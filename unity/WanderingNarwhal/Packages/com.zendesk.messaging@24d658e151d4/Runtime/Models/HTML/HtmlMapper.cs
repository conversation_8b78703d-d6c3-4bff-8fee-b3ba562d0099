using System.Collections.Generic;
using System.Linq;
using HtmlAgilityPack;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.Translations;

namespace Zendesk.Runtime.Models.HTML
{
    public static class HtmlMapper
    {
        private static List<HtmlComponent> _response;
        private static string _viewMediaTranslationKey = "messaging_unity_zmu_article_view_media";
        private static readonly Logger Logger = LoggerManager.GetLogger(nameof(HtmlMapper));

        /// <summary>
        /// Parse a HTML string to a list of <see cref="HtmlComponent">HTML Components</see>
        /// </summary>
        /// <param name="html">String which contains the html to be parsed</param>
        /// <returns>List of <see cref="HtmlComponent">HTML Components</see></returns>
        public static List<HtmlComponent> ToObject(string html)
        {
            //remove odd characters
            html = ParseInitialHtml(html);
            
            //load html doc from article body string
            var htmlDoc = LoadHtml(html);
            
            //Initiate response object
            _response = new List<HtmlComponent>();

            ParseListNodes(htmlDoc.DocumentNode.ChildNodes);

            return _response;
        }

        private static void ParseListNodes(HtmlNodeCollection nodeCollection)
        {
            foreach (var htmlNode in nodeCollection)
            {
                var item = ParseHtmlNode(htmlNode);
                if(item != null) _response.Add(item);
            }
        }

        /// <summary>
        /// Parse HTML node to a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">HTML Node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseHtmlNode(HtmlNode node)
        {
            switch (node.Name)
            {
                case "p":
                    return ParseParagraph(node);
                case "a":
                    return ParseLink(node);
                case "ul":
                case "ol":
                    return ParseList(node);
                case "h1":
                case "h2":
                case "h3":
                case "h4":
                    return ParseHeader(node);
                case "b":
                case "i":
                case "em":
                case "strong":
                    return ParseRichTextNode(node);
                case "#text":
                case "span":
                    return ParseText(node);
                case "img":
                    return ParseImage(node);
                case "iframe":
                    return ParseIFrame(node);
                default:
                    ParseListNodes(node.ChildNodes);
                    return null;
            }
        }
        
        /// <summary>
        /// Parse paragraph node to a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html paragraph node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseParagraph(HtmlNode node)
        {
            var parent = new HtmlComponent();

            if (!node.HasChildNodes)
            {
                var htmlNode = ParseHtmlNode(node);
                if(htmlNode == null) return null;

                parent.Type = htmlNode.Type == HtmlComponentType.Image ? HtmlComponentType.Image : HtmlComponentType.Text;
                parent.Children.Add(htmlNode);
            }
            else
            {
                foreach (var item in node.ChildNodes)
                {
                    var htmlNode = ParseHtmlNode(item);
                    if(htmlNode == null) continue;

                    parent.Children.Add(htmlNode);
                    parent.Type = htmlNode.Type == HtmlComponentType.Image ? HtmlComponentType.Image : HtmlComponentType.Text;
                }
            }

            return parent;
        }

        /// <summary>
        /// Parse rich text node to a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">HTML Node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseRichTextNode(HtmlNode node)
        {
            var parent = new HtmlComponent();
        
            if (!node.HasChildNodes)
            {
                parent.Type = HtmlComponentType.Text;
                parent.Value = node.InnerText;
            }
            else
            {
                var tag = CheckRichTextNode(node.Name);
                parent.Tags.Add(tag);
                parent.Type = HtmlComponentType.RichText;
                ParseRichTextChildren(node.ChildNodes, parent);
            }
            
            return parent;
        }
        
        /// <summary>
        /// Check rich text tag
        /// </summary>
        /// <param name="nodeName">Node name</param>
        /// <returns>Supported tag</returns>
        private static string CheckRichTextNode(string nodeName)
        {
            switch (nodeName)
            {
                case "em":
                case "i":
                    return "i";
                case "strong":
                case "b":
                    return "b";
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// Parse children from rich text component
        /// </summary>
        /// <param name="childNodes">List of child nodes</param>
        /// <param name="parent">ref: Parent HTML Component</param>
        private static void ParseRichTextChildren(HtmlNodeCollection childNodes, HtmlComponent parent)
        {
            foreach (var childNode in childNodes)
            {
                if (childNode.Name.Equals("#text"))
                {
                    parent.Children.Add(new HtmlComponent()
                    {
                        Value = childNode.InnerText,
                        Type = HtmlComponentType.Text
                    });
                }
                else
                {
                    var richTextChildren = new HtmlComponent()
                    {
                        Tags = new List<string>() {childNode.Name},
                        Type = HtmlComponentType.RichText
                    };

                    parent.Children.Add(richTextChildren);
                    
                    ParseRichTextChildren(childNode.ChildNodes, richTextChildren);
                }
            }
        }

        /// <summary>
        /// Parse #text node a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html text node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseText(HtmlNode node)
        {
            switch (node.ParentNode.Name)
            {
                case "h1":
                case "h2":
                case "h3":
                case "h4":
                    return ParseHeader(node.ParentNode);
                case "b":
                case "i":
                    return new HtmlComponent(HtmlComponentType.Text, $"<{node.ParentNode.Name}>{node.InnerText}</{node.ParentNode.Name}>");
                default:
                    return string.IsNullOrWhiteSpace(node.InnerText) ? null : new HtmlComponent(HtmlComponentType.Text, node.InnerText);
            }
        }

        /// <summary>
        /// Parse header node a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html header node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseHeader(HtmlNode node)
        {
            return new HtmlComponent(HtmlComponentType.Header, node.InnerText, new List<string>(){node.Name});
        }

        /// <summary>
        /// Parse list node a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html list node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseList(HtmlNode node)
        {
            HtmlComponentType type;
            switch (node.Name)
            {
                case "ul":
                    type = HtmlComponentType.UnorderedList;
                    break;
                case "ol":
                    type = HtmlComponentType.OrderedList;
                    break;
                default:
                    Logger.LogWarning("error");
                    return null;
            }

            var component = new HtmlComponent
            {
                Type = type
            };

            foreach (var item in node.ChildNodes)
            {
                component.Children.Add(ParseListItem(item));
            }
            
            return component;
        }
        
        /// <summary>
        /// Parse list items to a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html list item node</param>
        /// <returns>List of <see cref="HtmlComponent">HTML Components</see></returns>
        private static HtmlComponent ParseListItem(HtmlNode node)
        {
            var htmlComponent = new HtmlComponent();
            foreach (var child in node.ChildNodes)
            {
                htmlComponent.Children.Add(ParseHtmlNode(child));
            }
            return htmlComponent;
        }

        /// <summary>
        /// Parse image node a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html image node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseImage(HtmlNode node)
        {
            var imgSrc = string.Empty;
            try
            {
                imgSrc = node.Attributes.First(a => a.Name.Equals("src")).Value;
            }
            catch
            {
                // ignored
            }

            return new HtmlComponent(HtmlComponentType.Image, imgSrc);
        }
        
        /// <summary>
        /// Parse iframe node to a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html hyperlink node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseIFrame(HtmlNode node)
        {
            HtmlComponent htmlComponent = null;
            foreach (var attribute in node.Attributes)
            {
                if (!attribute.Name.ToUpper().Equals("SRC")) continue;

                var sourceValue = GetSourceAttributeValue(attribute.Value);
                var linkText = ZMTranslations.Current.GetString(_viewMediaTranslationKey);
                htmlComponent = new HtmlComponent(HtmlComponentType.Link, linkText, new List<string>(){sourceValue});
            }

            return htmlComponent;
        }
        
        /// <summary>
        /// Parse SRC attribute to a valid URL
        /// </summary>
        /// <param name="srcValue">SRC attribute</param>
        /// <returns>URL</returns>
        private static string GetSourceAttributeValue(string srcValue)
        {
            var sourceUrl = srcValue;
            while (sourceUrl.StartsWith("/"))
            {
                sourceUrl = sourceUrl.Remove(0, 1);
            }

            if (!sourceUrl.StartsWith("http"))
            {
                sourceUrl = "https://" + sourceUrl;
            }

            return sourceUrl;
        }

        /// <summary>
        /// Parse hyperlink node a <see cref="HtmlComponent">HTML Component</see>
        /// </summary>
        /// <param name="node">Html hyperlink node</param>
        /// <returns><see cref="HtmlComponent">HTML Component</see></returns>
        private static HtmlComponent ParseLink(HtmlNode node)
        {
            var href = string.Empty;
            try
            {
                href = node.Attributes.First(a => a.Name.Equals("href")).Value;
            }
            catch
            {
                // ignored
            }

            return new HtmlComponent(HtmlComponentType.Link, node.InnerText, new List<string>(){href});
        }
        
        /// <summary>
        /// Method to remove / replace some characters in the  html
        /// </summary>
        /// <param name="html">HTML</param>
        /// <returns>HTML string with characters removed / replaced</returns>
        private static string ParseInitialHtml(string html)
        {
            try
            {
                if (string.IsNullOrEmpty(html)) return html;
                
                //Bold
                html = html.Replace("<strong>", "<b>");
                html = html.Replace("</strong>", "</b>");
                
                //Italics
                html = html.Replace("<em>", "<i>");
                html = html.Replace("</em>", "</i>");
                
                //Span
                html = html.Replace("<span>", "");
                html = html.Replace("</span>", "");
                
                //Replace HTML characters
                html = html.Replace("&nbsp;", " ");
                html = html.Replace("&amp;", "&");
                html = html.Replace("&lt;", "<");
                html = html.Replace("&gt;", ">");
            }
            catch
            {
                // ignored
            }

            return html;
        }

        /// <summary>
        /// Load HTML document from string
        /// </summary>
        /// <param name="html">HTML string</param>
        /// <returns>HtmlDocument</returns>
        private static HtmlDocument LoadHtml(string html)
        {
            var htmlDoc = new HtmlDocument();
            htmlDoc.LoadHtml(html);

            return htmlDoc;
        }
    }
}