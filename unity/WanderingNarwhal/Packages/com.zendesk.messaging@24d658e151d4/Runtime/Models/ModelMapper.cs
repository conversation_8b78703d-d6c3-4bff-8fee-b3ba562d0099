using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.Messaging;
using Zendesk.MessagingCore.Messaging.DTO;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.RestClient.DTO.NativeMessaging;
using Zendesk.MessagingCore.RestClient.DTO.Zendesk;
using Zendesk.Runtime.Environment;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Messaging.Content;
using Zendesk.Runtime.Utils;
using FieldOptionsDto = Zendesk.MessagingCore.Messaging.DTO.FieldOptionsDto;
using MessageDto = Zendesk.MessagingCore.Messaging.DTO.MessageDto;

namespace Zendesk.Runtime.Models
{
    internal static class ModelMapper
    {
        private static readonly Lazy<MessageContentResolver> MessageContentResolver 
            = new Lazy<MessageContentResolver>(() => Messaging.Content.MessageContentResolver.Instance);
        public static User ToUser(AppUserResponseDto appUserDto)
        {
            return new User
            {
                Id = appUserDto.appUser._id,
                Locale = appUserDto.appUser.locale ?? "",
                ExternalId = appUserDto.appUser.userId ?? "",
                Conversations = ParseConversations(appUserDto.conversations, appUserDto.appUser._id),
                RealtimeSettings = ToRealtimeSettings(appUserDto.settings.realtime),
                Auth = new Authentication
                {
                    type = AuthType.sessionToken, token = appUserDto.sessionToken
                }
            };
        }
        
        internal static Conversation[] ParseConversations(ConversationDto[] conversationDto, string appUserId)
        {
            if (conversationDto.Length == 0)
                return Array.Empty<Conversation>();


            var conversations = new List<Conversation>();
            try
            {
                foreach (ConversationDto singleConvoDto in conversationDto)
                {
                    conversations.Add(ToConversation(singleConvoDto, appUserId));
                }
            }
            catch
            {
                return Array.Empty<Conversation>();
            }

            return conversations.ToArray();
        }

        private static RealtimeSettings ToRealtimeSettings(RealtimeSettingsDto realtimeSettings)
        {
            return new RealtimeSettings
            {
                baseUrl = realtimeSettings.baseUrl.Replace("https", "wss"),
                retryInterval = realtimeSettings.retryInterval,
                maxConnectionAttempts = realtimeSettings.maxConnectionAttempts,
                connectionDelay = realtimeSettings.connectionDelay,
                enabled = realtimeSettings.enabled
            };
        }

        // ConversationDto comes from initial user creation and has messages inside. This is currently used during User creation, is this correct?
        internal static Conversation ToConversation(ConversationDto conversationDto, string userId)
        {
            var parsedParticipants = conversationDto.participants.Select(ToParticipant).ToArray();
            return new Conversation
            {
                id = conversationDto._id,
                type = "",
                isDefault = conversationDto.isDefault,
                displayName = conversationDto.displayName,
                displayDescription = conversationDto.description,
                iconUrl = conversationDto.iconUrl,
                business = conversationDto.appMakers ?? new[] {string.Empty},
                businessLastRead =
                    conversationDto.appMakerLastRead,
                lastUpdatedAt = conversationDto.lastUpdatedAt,
                participants = parsedParticipants,
                messages = conversationDto.messages?.Select(ToMessage).ToList() ?? new List<Message>(),
                hasPrevious = conversationDto.hasPrevious ?? false,
                myself = parsedParticipants.First(participant => participant.appUserId == userId),
                metadata = conversationDto.metadata
            };
        }

        // ConversationResponseDto comes from GetConversation and has messages in a separate array alongside conversation instead of within 😩 the message array within is usually empty
        internal static Conversation ToConversation(ConversationResponseDto conversationResponseDto, string userId)
        {
            var conversationDto = conversationResponseDto.conversation;
            var messagesDto = conversationResponseDto.messages;

            var conversation = ToConversation(conversationDto, userId);
            conversation.messages = messagesDto?.Select(ToMessage).ToList() ?? new List<Message>();
            return conversation;
        }

        private static Participant ToParticipant(ParticipantDto participantDto)
        {
            return new Participant
            {
                id = participantDto._id,
                appUserId = participantDto.appUserId,
                unreadCount = participantDto.unreadCount,
                lastRead = participantDto.lastRead
            };
        }

        public static RestRetryPolicy ToRestRetryPolicy(RestRetryPolicyDto dto)
        {
            return new RestRetryPolicy
            {
                Regular = dto.intervals.regular,
                Aggressive = dto.intervals.aggressive,
                BackoffMultiplier = dto.backoffMultiplier,
                MaxRetries = dto.maxRetries
            };
        }

        internal static ZendeskTheme ToZendeskTheme(ThemeDto zendeskTheme)
        {
            return new ZendeskTheme
            {
                PrimaryColor = GetColorFromHexString(zendeskTheme.primary_color),
                MessageColor = GetColorFromHexString(zendeskTheme.message_color),
                ActionColor = GetColorFromHexString(zendeskTheme.action_color),
                SystemMessageColor = GetColorFromHexString(zendeskTheme.system_message_color),
                InboundMessageColor = GetColorFromHexString(zendeskTheme.inbound_message_color),
                BackgroundColor = GetColorFromHexString(zendeskTheme.background_color),
                ElevatedColor = GetColorFromHexString(zendeskTheme.elevated_color),
                NotifyColor = GetColorFromHexString(zendeskTheme.notify_color),
                SuccessColor = GetColorFromHexString(zendeskTheme.success_color),
                DangerColor = GetColorFromHexString(zendeskTheme.danger_color),
                DisabledColor = GetColorFromHexString(zendeskTheme.disabled_color),
                IconColor = GetColorFromHexString(zendeskTheme.icon_color),
                ActionBackgroundColor = GetColorFromHexString(zendeskTheme.action_background_color),
                OnPrimaryColor = GetColorFromHexString(zendeskTheme.on_primary_color),
                OnActionColor = GetColorFromHexString(zendeskTheme.on_action_color),
                OnMessageColor = GetColorFromHexString(zendeskTheme.on_message_color),
                OnBackgroundColor = GetColorFromHexString(zendeskTheme.on_background_color),
                OnDangerColor = GetColorFromHexString(zendeskTheme.on_danger_color),
                OnActionBackgroundColor = GetColorFromHexString(zendeskTheme.on_action_background_color)
            };
        }
        
        private static Color32? GetColorFromHexString(string hexString)
        {
            if (!ColorUtility.TryParseHtmlString(hexString, out Color result))
            {
                return null;
            }

            return result;
        }

        public static Message ToMessage(MessageDto messageDto)
        {
            if (messageDto == null)
            {
                Debug.LogWarning(ZMConstants.InvalidDto);
                return null;
            }

            var messageId = GetMessageId(messageDto);
            var content = MessageContentResolver.Value.GetContentFrom(messageDto);
            return new Message
            {
                MessageEventType = MessageEventType.Message,
                MessageId = messageId,
                LocalId = messageId,
                MessageType = content.Type,
                MessagePreviewText = content.PreviewText,
                Text = GetMessageText(messageDto),
                Form = GetMessageForm(messageDto),
                ImageUrl = GetImageUrl(messageDto),
                MediaFilename = messageDto.MediaUrl.GetMediaFileName().UrlDecode(),
                MediaType = StringExtension.GetMediaType(messageDto.MediaType),
                Author = Author.Create(messageDto.Name, messageDto.Role, messageDto.AvatarUrl, messageDto.Subroles),
                Actions = GetActions(messageDto.Actions),
                ArticleSuggestions = GetArticleSuggestions(messageDto.Items),
                AttachmentId = messageDto.AttachmentId
            };
        }

        public static Message ToMessage(ZendeskMessageRequest messageRequest)
        {
            if (messageRequest == null)
            {
                LoggerManager.GetLogger(nameof(ModelMapper)).LogWarning(ZMConstants.InvalidZendeskMessageRequest);
                return null;
            }

            var messageId = Guid.NewGuid().ToString();
            var result = new Message
            {
                MessageType = MessageType.Text,
                MessageEventType = MessageEventType.Message,
                Author = Author.Create(MessageRole.User),
                State = MessageState.Sending,
                LocalId = messageId,
                MessageId = messageId,
                CreatedAt = DateTime.UtcNow.GetUnixTimestampInMilliseconds(),
                ReceivedAt = DateTime.UtcNow.GetUnixTimestampInMilliseconds()
            };

            switch (messageRequest)
            {
                case ZendeskTextMessageRequest textMessageRequest:
                    result.MessageType = MessageType.Text;
                    result.Text = textMessageRequest.Message;
                    break;
                case ZendeskQuickRepliesMessageRequest quickRepliesMessage:
                    result.MessageType = MessageType.Text;
                    result.Text = quickRepliesMessage.Message;
                    break;
                case ZendeskImageUploadRequest fileRequest:
                    result.MessageType = MessageType.Image;
                    result.ImageUrl = fileRequest.MediaPath;
                    result.MediaFilename = fileRequest.MediaFilename;
                    result.MediaType = fileRequest.MediaType;
                    break;
            }

            return result;
        }

        internal static List<MessageAction> GetActions(IReadOnlyList<MessageActionDto> messageActions)
        {
            return messageActions?.Select(messageAction => new MessageAction
            {
                Id = messageAction.Id, 
                Payload = messageAction.Payload, 
                Text = messageAction.Text, 
                Type = messageAction.Type,
                Metadata = messageAction.Metadata, 
                Uri = messageAction.Uri, 
                Fallback = messageAction.Fallback
            }).ToList();
        }

        internal static List<MessageArticleSuggestion> GetArticleSuggestions(IReadOnlyList<MessageItemDto> itemDto)
        {
            return itemDto?.Select(messageSuggestionItem => new MessageArticleSuggestion
            {
                Id = messageSuggestionItem.Id, Title = messageSuggestionItem.Title, Description = messageSuggestionItem.Description, 
                MediaUrl = messageSuggestionItem.MediaUrl, MediaType = StringExtension.GetMediaType(messageSuggestionItem.MediaType),
                Actions = messageSuggestionItem.Actions?.Select(messageSuggestionItemAction => new MessageArticleSuggestionAction
                {
                    Id = messageSuggestionItemAction.Id,
                    Text = messageSuggestionItemAction.Text,
                    Type = messageSuggestionItemAction.Type,
                    Uri = messageSuggestionItemAction.Uri,
                    Fallback = messageSuggestionItemAction.Fallback
                }).ToList()
            }).ToList();
        }

        internal static MessageForm GetMessageForm(MessageDto messageDto)
        {
            if (messageDto.Fields == null || !messageDto.Fields.Any())
                return null;

            var messageForm = new MessageForm
            {
                MessageId = messageDto.RemoteId,
                Submitted = messageDto.Submitted.HasValue
                    ? messageDto.Submitted.Value
                    : false
            };

            foreach (var field in messageDto.Fields)
            {
                if (field == null) continue;

                var messageFormField = new MessageFormField
                {
                    Id = field.Id,
                    Label = field.Label,
                    Name = field.Name,
                    Payload = field.Payload,
                    Text = field.Text,
                    Email = field.Email,
                    Type = GetFormFieldType(field.Type),
                    Placeholder = field.Placeholder
                };
                if (messageFormField.Type == MessageFormFieldType.Select)
                {
                    messageFormField.Options = GetFormSelectOptions(field.Options);
                    messageFormField.Select = GetFormSelectOptions(field.Select);
                }

                messageForm.AddField(messageFormField);
            }

            //if no fields added into message form, return null
            if (messageForm.MessageFormFields == null || !messageForm.MessageFormFields.Any()) return null;

            messageForm.MessageId = messageDto.RemoteId;
            return messageForm;
        }

        private static MessageFormFieldType GetFormFieldType(string fieldType)
        {
            return fieldType switch
            {
                ZMConstants.MessageFormFieldText => MessageFormFieldType.Text,
                ZMConstants.MessageFormFieldEmail => MessageFormFieldType.Email,
                ZMConstants.MessageFormFieldSelect => MessageFormFieldType.Select,
                _ => MessageFormFieldType.Unknown
            };
        }

        private static List<MessageFormSelectField> GetFormSelectOptions(IReadOnlyList<FieldOptionsDto> fieldSelect)
        {
            var list = new List<MessageFormSelectField>();
            foreach (var option in fieldSelect)
            {
                list.Add(new MessageFormSelectField
                {
                    Id = option.Id,
                    Label = option.Label,
                    Name = option.Name
                });
            }

            return list;
        }
        
        private static MessageType GetActivityType(ActivityDto activityDto)
        {
            return activityDto.type switch
            {
                ZMConstants._ActivityStartTyping => MessageType.StartTyping,
                ZMConstants._ActivityStopTyping => MessageType.StopTyping,
                _ => MessageType.Unknown
            };
        }
        
        internal static string GetMessageText(MessageDto messageDto)
        {
            return messageDto.Text;
        }

        internal static string GetMessageId(MessageDto messageDto)
        {
            return messageDto.RemoteId;
        }

        internal static string GetImageUrl(MessageDto messageDto)
        {
            return messageDto.MediaUrl;
        }
    }
}