namespace Zendesk.Runtime.Storage
{
    public static class StorageFactory
    {
        public static IStorage Create(string ns, StorageType storageType = default)
        {
            return storageType switch
            {
                StorageType.Basic => new BasicStorage(ns),
                StorageType.Complex => new ComplexStorage(ns),
                _ => new BasicStorage(ns)
            };
        }
    }

    /// <summary>
    /// Supported storage types
    /// </summary>
    public enum StorageType
    {
        Basic,
        Complex
    }
}