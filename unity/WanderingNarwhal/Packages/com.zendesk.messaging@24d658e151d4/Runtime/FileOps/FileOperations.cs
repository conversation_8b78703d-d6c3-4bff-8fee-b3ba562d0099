using System;
using System.IO;
using Zendesk.MessagingCore.Logging;

namespace Zendesk.Runtime.FileOps
{
    internal static class FileOperations
    {
        private static readonly Logger Logger = LoggerManager.GetLogger(nameof(FileOperations));

        internal static string ReadFile(string filePath)
        {
            try
            {
                return File.ReadAllText(filePath);
            }
            catch (Exception e)
            {
                Logger.LogException(e);
                throw;
            }
        }

        internal static void WriteToFile(string filePath, string content, bool append = false)
        {
            try
            {
                if (append)
                {
                    File.AppendAllText(filePath, content);
                }
                else
                {
                    File.CreateText(filePath).Dispose();
                    File.WriteAllText(filePath, content);
                }
            }
            catch (Exception e)
            {
                Logger.LogException(e);
                throw;
            }
        }

        internal static void CreateFile(string path)
        {
            var fileInfo = new FileInfo(path);
            if (fileInfo.Exists) return;

            if (fileInfo.Directory != null)
            {
                CreateDirectory(fileInfo.Directory.FullName);
            }

            File.Create(path).Dispose();
        }

        internal static void CreateDirectory(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }
    }
}