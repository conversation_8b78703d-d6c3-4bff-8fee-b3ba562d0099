using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.Messaging.DTO;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Messaging.Contexts;

namespace Zendesk.Runtime.UI.Messaging
{
    internal static class MessageTransforms
    {
        private static readonly Logger Logger = LoggerManager.GetLogger(nameof(MessageTransforms));

        public static bool TryCreateContextForMessage(
            Message messageData, LayoutData layoutData,
            out IMessageWidgetContext messageWidgetContext)
        {
            messageWidgetContext = null;

            // Input validation
            if (messageData == null ||
                !layoutData.IsValid)
            {
                Logger.LogError("Invalid function params.");
                return false;
            }

            UIMessageType uiMessageType = MessageUtils.GetUIMessageType(messageData);
            messageWidgetContext = uiMessageType switch
            {
                UIMessageType.EndUserTextMessage => new EndUserTextMessageContext(messageData, layoutData),
                UIMessageType.RemoteUserTextMessage => new RemoteTextMessageContext(messageData, layoutData),
                UIMessageType.EndUserImage => new EndUserImageMessageContext(messageData, layoutData),
                UIMessageType.RemoteUserImage => new RemoteImageMessageContext(messageData, layoutData),
                UIMessageType.QuickReplies => new QuickRepliesMessageContext(messageData, layoutData),
                UIMessageType.Form => new FormMessageContext(messageData, layoutData),
                UIMessageType.Carousel => new CarouselMessageContext(messageData, layoutData),
                UIMessageType.RemoteUserUnsupportedAttachment => new RemoteUnsupportedAttachmentContext(messageData,
                    layoutData),
                UIMessageType.EndUserUnsupportedAttachment => new EndUserUnsupportedAttachmentContext(messageData,
                    layoutData),
                UIMessageType.ButtonLink => new ButtonLinkMessageContext(messageData, layoutData),
                _ => messageWidgetContext
            };

            return messageWidgetContext?.IsValid ?? false;
        }
    }
}