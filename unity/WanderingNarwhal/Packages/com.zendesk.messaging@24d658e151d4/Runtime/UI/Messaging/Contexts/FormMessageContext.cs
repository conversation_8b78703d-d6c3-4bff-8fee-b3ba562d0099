using System.Collections.Generic;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models;

namespace Zendesk.Runtime.UI.Messaging.Contexts
{
    public class FormMessageContext : RemoteMessageWidgetContext
    {
        public const string Id = "FormMessageContext";
        public int UserInputId => MessageId.GetHashCode();
        public override string IdRef => Id;
        public override bool IsValid =>
            base.IsValid &&
            AreFormFieldsValid();

        public string MessageId => Message?.MessageId;
        
        public List<MessageFormField> FormFields => Message?.Form?.MessageFormFields;
        
        public bool IsSubmitted => Message?.Form?.Submitted ?? false;

        public FormMessageContext(Message message, LayoutData layoutData) : base(message, layoutData) {}

        private bool AreFormFieldsValid()
        {
            if (FormFields == null ||
                FormFields.Count == 0)
                return false;

            foreach (MessageFormField messageFormField in FormFields)
            {
                if (messageFormField == null ||
                    !messageFormField.IsValid())
                    return false;
            }

            return true;
        }
    }
}
