using System.Collections.Generic;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models;

namespace Zendesk.Runtime.UI.Messaging.Contexts
{
    public class ButtonLinkMessageContext : RemoteMessageWidgetContext
    {
        public new const string Id = "ButtonLinkMessageContext";

        public override string IdRef => Id;

        public override bool IsValid =>
            base.IsValid &&
            AreButtonLinksValid();
        
        public string Text => Message?.Text;
        
        public List<MessageAction> ButtonLinkActions => Message?.Actions;


        public ButtonLinkMessageContext(Message message, LayoutData layoutData) : base(message, layoutData) {}

        private bool AreButtonLinksValid()
        {
            if (ButtonLinkActions == null ||
                ButtonLinkActions.Count == 0)
                return false;
            
            foreach (MessageAction buttonLinkAction in ButtonLinkActions)
            {
                if (buttonLinkAction == null ||
                    !buttonLinkAction.IsValidButtonLink)
                    return false;
            }

            return true;
        }
    }
}