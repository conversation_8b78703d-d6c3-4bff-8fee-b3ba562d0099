using System;
using MessageDto = Zendesk.Runtime.Models.Message;

namespace Zendesk.Runtime.UI.Messaging
{
    public interface IReadOnlyObservableMessageHistory
    {
        MessageDto this[int index] { get; }

        int Count { get; }

        event Action<MessageDto> ItemAddedEvent;
        event Action<int, MessageDto, MessageDto> MessageUpdatedEvent;
        event Action<int, MessageDto> ItemRemovedEvent;
        event Action InitializedEvent;
    }

    internal class ObservableMessageHistory : ObservableList<MessageDto>, IReadOnlyObservableMessageHistory
    {
        public event Action<int, MessageDto, MessageDto> _messageUpdatedEvent;
        public event Action<int, MessageDto, MessageDto> MessageUpdatedEvent
        {
            add
            {
                _messageUpdatedEvent -= value;
                _messageUpdatedEvent += value;
            }
            remove => _messageUpdatedEvent -= value;
        }
        

        public bool TryUpdateMessageAtIndex(int index, MessageDto messageDto)
        {
            MessageDto oldMessage = this[index];

            if (messageDto.MessageType != oldMessage.MessageType ||
                !messageDto.Author.IsEquivalentTo(oldMessage.Author))
            {
                return false;
            }

            _internalList[index] = messageDto;
            _messageUpdatedEvent?.Invoke(index, oldMessage, messageDto);
            return true;
        }
    }
}