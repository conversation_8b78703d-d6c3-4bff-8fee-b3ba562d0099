using UnityEngine;
using UnityEngine.UI;
using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.Messaging;
using Zendesk.Runtime.UI.Messaging.Contexts;
using Zendesk.Runtime.UI.Utilities;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Messaging.Widgets
{
    public class RemoteImageMessageWidget : RemoteMessageWidget<RemoteImageMessageContext>
    {
        public override string ContextId => RemoteImageMessageContext.Id;

        [SerializeField] private Button _tapArea;
        [SerializeField] private ImageBubbleComponent _imageBubbleComponent;
        private IZMImageViewer _imageViewer;
        private readonly Logger _logger = LoggerManager.GetLogger<RemoteImageMessageWidget>();

        public override bool IsWidgetConfigurationValid()
        {
            return base.IsWidgetConfigurationValid() && _imageBubbleComponent != null && _tapArea != null && _imageBubbleComponent.IsValid && Theme != null;
        }

        protected override void OnBindContext(out bool wasSuccess)
        {
            _imageViewer = gameObject.GetComponent<ZMImageViewer>();
            base.OnBindContext(out wasSuccess);
            _tapArea.onClick.AddListener(OnTapAreaPressedEventHandler);
            TextureHandlerStatusUpdatedEvent();
            Context.TextureHandle.StatusUpdatedEvent += TextureHandlerStatusUpdatedEvent;
        }

        private void TextureHandlerStatusUpdatedEvent()
        {
            if (Context.TextureHandle.Status == TextureHandleStatus.Failure)
            {
                _imageBubbleComponent.SetImageColor(Theme.NotifyColor);
                _imageBubbleComponent.SetImageAsFailed(true);
            }
            else if (Context.TextureHandle.Status == TextureHandleStatus.Ready)
            {
                _imageBubbleComponent.SetImageColor(Theme.OnMessageColor);
                _imageBubbleComponent.SetImageAsReady(Context.TextureHandle);
            }
        }

        protected override void OnReleaseContext()
        {
            _imageViewer = null;
            _tapArea.onClick.RemoveListener(OnTapAreaPressedEventHandler);
            base.OnReleaseContext();
        }

        private void OnTapAreaPressedEventHandler()
        {
            if (Context.TextureHandle.Status == TextureHandleStatus.Ready)
            {
                LoadImageInViewer();
            }
        }

        private void LoadImageInViewer()
        {
            if (_imageViewer == null)
            {
                _logger.LogError(ZMConstants.ImageViewerComponentMissing);
                return;
            }

            var texture = Context.TextureHandle.Texture;
            if (texture == null)
            {
                _logger.LogError(ZMConstants.ImageMissingUnableToOpenViewer);
                return;
            }

            _imageViewer.DisplayImage(texture);
        }
    }
}