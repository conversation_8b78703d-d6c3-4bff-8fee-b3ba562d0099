using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Messaging.Contexts;
using Zendesk.Runtime.UI.Messaging.WidgetComponents;
using Zendesk.Runtime.UI.Messaging.WidgetStateMachines;
using Zendesk.Runtime.UI.Utilities;
using Zendesk.Runtime.Utils;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Messaging.Widgets
{
    public class FormMessageWidget : RemoteMessageWidget<FormMessageContext>
    {
        private const string _FormValidationErrorMessageKey = "messaging_unity_zmu_form_submission_error";
        
        [Header("Prefabs")]
        [SerializeField] private GameObject _emailFieldPrefab; 
        [SerializeField] private GameObject _textFieldPrefab; 
        [SerializeField] private GameObject _dropdownFieldPrefab;
        [Header("UI Components")]
        [SerializeField] private Button _sendButton;
        [SerializeField] private Outline _outline;
        [Header("Transforms")]
        [SerializeField] private RectTransform _buttonRowTransform;
        [SerializeField] private RectTransform _activeComponentsParent;
        [SerializeField] private RectTransform _inactiveComponentsParent;
        [Header("FSM")]
        [SerializeField] private FormWidgetStateMachine _stateMachine;

        private readonly List<FieldComponent> _activeComponents = new List<FieldComponent>();
        private readonly List<FieldComponent> _inactiveComponents = new List<FieldComponent>();
        public override string ContextId => FormMessageContext.Id;

        private FormUIData _formUIData;
        private readonly Logger _logger = LoggerManager.GetLogger<FormMessageWidget>();

        public override bool IsWidgetConfigurationValid()
        {
            return
                _outline != null &&
                _stateMachine != null &&
                _activeComponentsParent != null &&
                _inactiveComponentsParent != null &&
                _buttonRowTransform != null &&
                _sendButton != null &&
                IsFieldPrefabValid<EmailFieldComponent>(_emailFieldPrefab) &&
                IsFieldPrefabValid<TextFieldComponent>(_textFieldPrefab) &&
                IsFieldPrefabValid<DropDownFieldComponent>(_dropdownFieldPrefab);
        }

        protected override void OnBindContext(out bool wasSuccess)
        {
            base.OnBindContext(out wasSuccess);

            IShortTermUIDataStore uiDataStore = ServiceLocator.Instance.GetService<IShortTermUIDataStore>();
            
            if (!uiDataStore.TryGetData(Context.UserInputId, out _formUIData))
            {
                _formUIData = new FormUIData()
                {
                    UserInput = new List<IFormFieldUserInput>(),
                    WasSubmitError = false
                };
            }

            _outline.effectColor = Theme.FormFieldDefaultOutlineColor;
            
            foreach (MessageFormField formField in Context.FormFields)
            {
                IFormFieldUserInput userInput = null;
                
                for (int i = 0; i < _formUIData.UserInput.Count; i++)
                {
                    if (_formUIData.UserInput[i].FormField != formField)
                        continue;

                    userInput = _formUIData.UserInput[i];
                    _formUIData.UserInput.RemoveAt(i);
                    break;
                }
                
                wasSuccess &= TryAddFieldComponent(formField, userInput);
            }
            
            // The submit button must be the last component in the message bubble.
            _buttonRowTransform.SetAsLastSibling();

            _sendButton.image.color = Theme.ActionColor;
            _sendButton.onClick.AddListener(OnSendPressedEventHandler);
            
            _stateMachine.TransitionEvent += FormStateChanged;
            wasSuccess &= _stateMachine.TryTransitionTo(Context.IsSubmitted
                ? FormWidgetState.Sending
                : FormWidgetState.CannotSend);
        }

        private void FormStateChanged()
        {
            switch (_stateMachine.CurrentState)
            {
                case FormWidgetState.CanSend:
                case FormWidgetState.CannotSend:
                    UpdateReceipt();
                    _buttonRowTransform.gameObject.SetActive(true);
                    foreach (FieldComponent activeComponent in _activeComponents)
                    {
                        activeComponent.ShowUnsentState();
                    }
                    break;
                
                case FormWidgetState.Sent:
                case FormWidgetState.Sending:
                    _formUIData.WasSubmitError = false;
                    UpdateReceipt();
                    _buttonRowTransform.gameObject.SetActive(false);
                    foreach (FieldComponent activeComponent in _activeComponents)
                    {
                        activeComponent.ShowSentState();
                    }
                    break;
            }
        }

        protected override void OnReleaseContext()
        {
            // We need user input data to persist independent of the widget for recycling to work as expected.
            foreach (FieldComponent activeComponent in _activeComponents)
                _formUIData.UserInput.Add(activeComponent.UserInputData); 
            
            IShortTermUIDataStore uiDataStore = ServiceLocator.Instance.GetService<IShortTermUIDataStore>();
            uiDataStore.StoreData(Context.UserInputId, _formUIData);
            _formUIData = null;
            
            // Rather than creating and destroying field components, we will store them for later use.
            foreach (FieldComponent activeComponent in _activeComponents)
            {
                activeComponent.transform.SetParent(_inactiveComponentsParent);
                activeComponent.FieldChangedEvent -= OnFieldChangedEventHandler;
                activeComponent.Clear();
            }

            _sendButton.onClick.RemoveAllListeners();

            _inactiveComponents.AddRange(_activeComponents);
            _activeComponents.Clear();

            _stateMachine.TransitionEvent -= FormStateChanged;
            _stateMachine.Reset();
            
            base.OnReleaseContext();
        }

        private bool TryAddFieldComponent(MessageFormField messageFormField, IFormFieldUserInput formFieldUserInput)
        {
            FieldComponent candidateFieldComponent = null;

            // Try fetch a component that already exists.
            for (int inactiveIndex = 0; inactiveIndex < _inactiveComponents.Count; inactiveIndex++)
            {
                FieldComponent inactiveComponent = _inactiveComponents[inactiveIndex];
                if (inactiveComponent.FieldType != messageFormField.Type)
                    continue;

                candidateFieldComponent = inactiveComponent;
                candidateFieldComponent.transform.SetParent(_activeComponentsParent);
                _inactiveComponents.RemoveAt(inactiveIndex);
            }

            // Create a new component if none already exist.
            if (candidateFieldComponent == null)
            {
                GameObject prefab;
                switch (messageFormField.Type)
                {
                    case MessageFormFieldType.Email:
                        prefab = _emailFieldPrefab;
                        break;
                    case MessageFormFieldType.Text:
                        prefab = _textFieldPrefab;
                        break;
                    case MessageFormFieldType.Select:
                        prefab = _dropdownFieldPrefab;
                        break;

                    default:
                        _logger.LogError($"Unhandled field type: {messageFormField.Type}");
                        return false;
                }

                candidateFieldComponent = Instantiate(prefab, _activeComponentsParent).GetComponent<FieldComponent>();
            }
            
            bool wasCreatedOk = candidateFieldComponent != null;
            if (wasCreatedOk)
            {
                candidateFieldComponent.SetContent(messageFormField, formFieldUserInput);
                candidateFieldComponent.FieldChangedEvent += OnFieldChangedEventHandler;
                _activeComponents.Add(candidateFieldComponent);
            }
            return wasCreatedOk;
        }

        static bool IsFieldPrefabValid<T>(GameObject fieldPrefab) where T : FieldComponent
        {
            return
                fieldPrefab != null &&
                fieldPrefab.TryGetComponent(out T fieldComponent) &&
                fieldComponent.IsValid;
        }

        #region [Event Handlers]
        
        protected override void OnMessageUpdatedEvent(Message message)
        {
            base.OnMessageUpdatedEvent(message);

            if (_stateMachine.CurrentState != FormWidgetState.Sending)
                return;
            
            _formUIData.WasSubmitError = _stateMachine.CurrentState == FormWidgetState.Sending && !Context.IsSubmitted;
            _stateMachine.TryTransitionTo(_formUIData.WasSubmitError 
                ? FormWidgetState.CanSend 
                : FormWidgetState.Sent);
        }

        protected override void UpdateReceipt()
        {
            if (!_formUIData.WasSubmitError)
            {
                base.UpdateReceipt();
                return;
            }
            
            ReceiptComponent.SetVisibility(true);
            ReceiptComponent.SetText(_FormValidationErrorMessageKey);
            ReceiptComponent.ApplyStyle(Theme.OnDangerColor, Theme.NotifyColor, true);
        }

        private void OnSendPressedEventHandler()
        {
            if (_stateMachine.CurrentState == FormWidgetState.CanSend)
            {
                // Send the form response using the existing backend, there is no error handling as per the old UI. 
                List<MessageFormField> userFormFields = new List<MessageFormField>();
                foreach (FieldComponent activeComponent in _activeComponents)
                {
                    userFormFields.Add(activeComponent.CreateFormFieldFromInput());
                }
                
                ZendeskMessaging.Instance.Kit.SendFormMessage(Context.Message, userFormFields);
                _stateMachine.TryTransitionTo(FormWidgetState.Sending);
            }
            else
            {
                foreach (FieldComponent activeComponent in _activeComponents)
                {
                    activeComponent.UpdateUserInputVisuals();
                }
            }
        }
        
        private void OnFieldChangedEventHandler()
        {
            // When the user has changed any field we need to check if we can submit the form or not.
            bool canSend = true;
            foreach (FieldComponent activeComponent in _activeComponents)
                canSend &= activeComponent.IsUserInputValid;

            _stateMachine.TryTransitionTo(canSend 
                ? FormWidgetState.CanSend 
                : FormWidgetState.CannotSend);
        }
        
        #endregion
        
        private class FormUIData
        {
            public List<IFormFieldUserInput> UserInput;
            public bool WasSubmitError;
        }
    }
}
