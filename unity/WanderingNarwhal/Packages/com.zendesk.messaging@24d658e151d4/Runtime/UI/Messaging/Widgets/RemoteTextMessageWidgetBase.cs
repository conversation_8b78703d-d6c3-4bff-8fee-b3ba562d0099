using UnityEngine;
using Zendesk.Runtime.UI.Messaging.Contexts;

namespace Zendesk.Runtime.UI.Messaging.Widgets
{
    public abstract class RemoteTextMessageWidgetBase<T> : RemoteMessageWidget<T> where T : RemoteTextMessageContext
    {
        [SerializeField] protected TextBubbleComponent _textBubbleComponent;

        public override bool IsWidgetConfigurationValid()
        {
            return
                base.IsWidgetConfigurationValid() &&
                _textBubbleComponent != null && _textBubbleComponent.IsValid;
        }
        
        protected override void OnBindContext(out bool wasSuccess)
        {
            base.OnBindContext(out wasSuccess);
            _textBubbleComponent.SetText(Context.Text, Context.Message.Author.HasAiDisclaimer());
        }

        protected override void OnReleaseContext()
        {
            _textBubbleComponent.ClearText();
            base.OnReleaseContext();
        }
    }
}
