using System;
using System.Threading.Tasks;

namespace Zendesk.Runtime.UI.Guide.Core.MVVM.Commands
{
    public class AsyncCommand : IAsyncCommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool> _canExecute;
        private bool _isExecuting;

        public AsyncCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            _execute = execute;
            _canExecute = canExecute ?? (() => true);
        }

        public async Task ExecuteAsync()
        {
            if (CanExecute && !_isExecuting)
            {
                try
                {
                    _isExecuting = true;
                    await _execute();
                }
                finally
                {
                    _isExecuting = false;
                }
            }
        }

        public bool CanExecute => _canExecute() && !_isExecuting;
    }
}