using System;
using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Enums;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;

namespace Zendesk.Runtime.UI.Guide.Screens.HelpCenterScreen.ViewModels
{
    internal class HelpCenterCategoryViewModel : ViewModelBase
    {
        public string CategoryName { get; }
        public long CategoryId { get; }

        public HelpCenterCategoryViewModel(
            INavigationService navigationService,
            string categoryName,
            long categoryId) 
            : base(navigationService)
        {
            CategoryName = categoryName ?? "";
            CategoryId = categoryId;
        }

        public static HelpCenterCategoryViewModel MapFrom(INavigationService navigationService, CategoryDto categoryDto)
        {
            if (categoryDto == null)
                throw new ArgumentNullException(nameof(categoryDto));

            return new HelpCenterCategoryViewModel(
                navigationService,
                categoryDto.Name,
                categoryDto.Id
            );
        }

        public void OnClick()
        {
            NavigationService?.NavigateTo(GuideScreen.Category, CategoryId);
        }
    }
}