using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.UI.Guide.Screens.HelpCenterScreen.ViewModels;

namespace Zendesk.Runtime.UI.Guide.Screens.HelpCenterScreen
{
    internal class HelpCenterCategoryButtonView : MonoBeh<PERSON>our, IHelpCenterCategoryButtonView
    {
        [SerializeField] private TMP_Text _categoryName;
        [SerializeField] private Button _button;

        public void SetData(HelpCenterCategoryViewModel model)
        {
            ClearData();
            _categoryName.text = model.CategoryName;
            _button.onClick.AddListener(model.OnClick);
        }

        public void ClearData()
        {
            _categoryName.text = string.Empty;
            _button.onClick.RemoveAllListeners();
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            gameObject.SetActive(false);
        }
    }
}