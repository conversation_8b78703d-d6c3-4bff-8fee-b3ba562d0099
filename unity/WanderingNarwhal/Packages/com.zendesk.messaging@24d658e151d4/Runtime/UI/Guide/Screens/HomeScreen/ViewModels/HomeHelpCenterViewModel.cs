using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.Runtime.Translations;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Enums;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;
using Zendesk.Runtime.UI.Guide.Services.Interfaces;
using Zendesk.Runtime.UI.Guide.Shared.ViewModels;

namespace Zendesk.Runtime.UI.Guide.Screens.HomeScreen.ViewModels
{
    /// <summary>
    /// ViewModel for the help center section of the home screen.
    /// Manages the display of articles and navigation to the full help center.
    /// </summary>
    internal class HomeHelpCenterViewModel : ViewModelBase
    {
        private readonly ITranslationService _translationService;

        public string Title => Translate(GuideTranslationKeys.GuideUnityZmuHelpCenterTitle);
        public string SeeAll => Translate(GuideTranslationKeys.GuideUnityZmuSeeAllButton);
        public string PromotedArticles => Translate(GuideTranslationKeys.GuideUnityZmuPromotedArticlesTitle);
        public string RecentArticles => Translate(GuideTranslationKeys.GuideUnityZmuRecentActivityTitle);

        public bool HasPromotedArticles { get; }
        public IReadOnlyList<ArticleViewModel> Articles { get; }

        private HomeHelpCenterViewModel(
            INavigationService navigationService,
            ITranslationService translationService,
            IReadOnlyList<ArticleViewModel> articles,
            bool hasPromotedArticles)
            : base(navigationService)
        {
            _translationService = translationService;
            Articles = articles;
            HasPromotedArticles = hasPromotedArticles;
        }

        public static HomeHelpCenterViewModel Create(
            INavigationService navigationService,
            ITranslationService translationService,
            List<CategoryDto> categoryDtos)
        {
            var (articleDtos, isPromoted) = CategoryDtoUtilities.GetPromotedOrRecentArticles(categoryDtos);

            var articles = articleDtos
                .Select(dto => ArticleViewModel.MapFrom(navigationService, dto))
                .ToList();

            return new HomeHelpCenterViewModel(navigationService, translationService, articles, isPromoted);
        }

        private string Translate(string key) => _translationService.GetString(key);

        public async Task OnClick()
        {
            await NavigationService.NavigateTo(GuideScreen.HelpCenter);
        }
    }
}