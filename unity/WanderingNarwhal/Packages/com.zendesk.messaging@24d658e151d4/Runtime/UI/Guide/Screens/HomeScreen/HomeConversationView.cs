using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Screens.HomeScreen.ViewModels;
using Zendesk.Runtime.UI.Messaging.WidgetComponents;
using Zendesk.Runtime.UI.Utilities;

namespace Zendesk.Runtime.UI.Guide.Screens.HomeScreen
{
    /// <summary>
    /// View component for displaying either an existing conversation preview or a new conversation prompt.
    /// Handles layout management and texture loading for conversation avatars.
    /// </summary>
    internal class HomeConversationView : BaseView
    {
        [SerializeField] private RectTransform _rectTransform;
        [SerializeField] private GameObject _newConversationGameObject;
        [SerializeField] private GameObject _existingConversationGameObject;
        [SerializeField] private TMP_Text _titleText;

        [Header("New Conversation Components")]
        [SerializeField] private Button _startConversationButton;
        [SerializeField] private TMP_Text _startConversationText;

        [Header("Conversation Components")]
        [SerializeField] private ContentSizeFitter _convoTopSizeFitter;
        [SerializeField] private ContentSizeFitter _convoBottomSizeFitter;
        [SerializeField] private TMP_Text _avatarNameText;
        [SerializeField] private TMP_Text _updateTimeText;
        [SerializeField] private TMP_Text _contentText;
        [SerializeField] private Button _openConversationButton;
        [SerializeField] private AvatarComponent _avatarComponent;
        
        [Header("Util")]
        [SerializeField] private Sprite _fallbackAvatarSprite;

        private GameObject _currentConversationGameObject;
        private ITextureHandle _textureHandle;

        private HomeConversationViewModel ViewModel => _viewModel as HomeConversationViewModel;
        
        protected override void OnViewModelSet()
        {
            if (ViewModel == null)
                return;
            
            ClearClickListeners();

            if (ViewModel.HasConversation)
            {
                SetupWithMessaging(ViewModel.ConversationPreview);
            }
            else
            {
                SetupWithoutMessaging(ViewModel.StartConversation);
            }
            
            SetTranslatedText();
        }

        private void ClearClickListeners()
        {
            _startConversationButton.onClick.RemoveAllListeners();
            _openConversationButton.onClick.RemoveAllListeners();
        }

        public void RebuildLayout()
        {
            if (_currentConversationGameObject == _existingConversationGameObject)
            {
                SetConversationPreview();
            }
        }

        private void SetActiveConversation(bool conversationExists)
        {
            _existingConversationGameObject.SetActive(conversationExists);
            _newConversationGameObject.SetActive(!conversationExists);
            _currentConversationGameObject =
                conversationExists ? _existingConversationGameObject : _newConversationGameObject;
        }

        private void SetupWithMessaging(HomeConversationPreviewViewModel viewModel)
        {
            SetActiveConversation(true);

            _avatarComponent.SetVisibility(true);
            _avatarComponent.SetAvatar(viewModel.TextureHandle);

            SetTextValues(viewModel);
            SetConversationPreview();

            _openConversationButton.onClick.AddListener(async () => await viewModel.OpenMessaging());
        }

        private void SetupWithoutMessaging(HomeStartConversationViewModel viewModel)
        {
            SetActiveConversation(false);
            _startConversationText.text = viewModel.StartConversationText;
            _startConversationButton.onClick.AddListener(async () => await viewModel.OpenMessaging());
        }

        private void SetTextValues(HomeConversationPreviewViewModel viewModel)
        {
            _avatarNameText.text = viewModel.AgentName;
            _updateTimeText.text = viewModel.ConversationTimestamp;
            _contentText.text = viewModel.ConversationDescription;
        }

        public void SetConversationPreview()
        {
            if (!gameObject.activeSelf || !gameObject.activeInHierarchy)
                return;
            StartCoroutine(ResizeTextAreas());
        }

        private IEnumerator ResizeTextAreas()
        {
            FillTextAreasMaxSize();
            yield return null;
            CompactTextAreas();

            LayoutRebuilder.ForceRebuildLayoutImmediate(_rectTransform);
        }
        
        public void SetTranslatedText()
        {
            _titleText.text = ViewModel.ConversationTitle;
        }

        private void FillTextAreasMaxSize()
        {
            _convoTopSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
            _convoBottomSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

            _avatarNameText.ForceMeshUpdate();
            _updateTimeText.ForceMeshUpdate();
            _contentText.ForceMeshUpdate();
        }

        private void CompactTextAreas()
        {
            if (_avatarNameText.textInfo.lineCount == 1 && _updateTimeText.textInfo.lineCount == 1)
            {
                _convoTopSizeFitter.verticalFit = ContentSizeFitter.FitMode.MinSize;
            }

            _convoBottomSizeFitter.verticalFit = _contentText.textInfo.lineCount == 1
                ? ContentSizeFitter.FitMode.MinSize
                : ContentSizeFitter.FitMode.PreferredSize;
        }
    }
}