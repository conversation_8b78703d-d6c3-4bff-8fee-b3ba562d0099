using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Application.DTOs;
using Zendesk.MessagingCore.Guide.Core.Controller.Interfaces;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.Localisation;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Guide.Core.MVVM.Base;
using Zendesk.Runtime.UI.Guide.Core.Navigation.Interfaces;
using Zendesk.Runtime.UI.Guide.Services.Interfaces;
using Zendesk.Runtime.UI.Header;
using Zendesk.Runtime.UI.Header.ViewModels;

namespace Zendesk.Runtime.UI.Guide.Screens.HomeScreen.ViewModels
{
    internal class HomeScreenViewModel : ViewModelBase, INotifyPropertyChanged, IHeaderStateUpdater
    {
        private readonly IGuideController _guideController;
        private readonly IMessagingService _messagingService;
        private readonly ITranslationService _translationService;
        private readonly ILocalisationService _localisationService;
        private readonly Action<Message> _messageReceivedHandler;
        private readonly Action<Message> _messageUpdatedHandler;
        private readonly Logger _logger;

        private HomeHelpCenterViewModel _helpCenter;

        public HomeHelpCenterViewModel HelpCenter
        {
            get => _helpCenter;
            private set => SetProperty(ref _helpCenter, value);
        }

        private HomeConversationViewModel _conversation;

        public HomeConversationViewModel Conversation
        {
            get => _conversation;
            private set => SetProperty(ref _conversation, value);
        }

        public bool ShowSearch { get; }

        public HomeScreenViewModel(
            INavigationService navigationService,
            IGuideController guideController,
            IMessagingService messagingService,
            ITranslationService translationService,
            ILocalisationService localisationService)
            : base(navigationService)
        {
            _guideController = guideController;
            _messagingService = messagingService;
            _translationService = translationService;
            _localisationService = localisationService;
            ShowSearch = false;
            _messageReceivedHandler = OnMessageReceived;
            _messageUpdatedHandler = OnMessageUpdated;
            _logger = LoggerManager.GetLogger<HomeScreenViewModel>();
        }

        public override async Task OnNavigatedTo(object parameter)
        {
            await InitializeGuideData();
        }

        public override async Task Initialize()
        {
            await InitializeMessaging();
        }

        private async Task InitializeGuideData()
        {
            try
            {
                LocaleInfo localeInfo =
                    await _guideController.GetSupportedLocaleAsync(_localisationService.GetCurrentLocale());
                var categoryDtos = await _guideController.GetArticles(localeInfo);
                HelpCenter = HomeHelpCenterViewModel.Create(NavigationService, _translationService, categoryDtos);
                await HelpCenter.Initialize();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to load guide data: {ex.Message}");
            }
        }

        private async Task InitializeMessaging()
        {
            try
            {
                var latestMessage = await _messagingService.GetLatestMessageAsync();
                UpdateConversation(latestMessage);
                _messagingService.AssignMessagingEventHandlers(_messageUpdatedHandler, _messageReceivedHandler);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - we can still show articles
                _logger.LogError($"Failed to initialize messaging: {ex.Message}");
            }
        }

        private void OnMessageReceived(Message message)
        {
            UpdateConversation(message);
        }

        private void OnMessageUpdated(Message message)
        {
            UpdateConversation(message);
        }

        private void UpdateConversation(Message message)
        {
            Conversation = HomeConversationViewModel.MapFrom(
                NavigationService, 
                _translationService, 
                _messagingService,
                message);
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(storage, value)) return false;
            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        public void Cleanup()
        {
            _messagingService?.Cleanup();
        }
        
        public void UpdateHeaderState(HeaderViewModel viewModel)
        {
            viewModel.ShowLeftButton = true;
            viewModel.ShowRightButton = false;
        }
    }
}