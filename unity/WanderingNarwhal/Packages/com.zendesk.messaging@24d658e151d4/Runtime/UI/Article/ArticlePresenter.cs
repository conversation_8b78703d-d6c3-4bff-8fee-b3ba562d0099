using System.Linq;
using System.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Models.HTML;

namespace Zendesk.Runtime.UI.Article
{
    internal class ArticlePresenter : MonoBehaviour, IArticlePresenter
    {
        [SerializeField] private TMP_Text _headerTitle;
        [SerializeField] private TMP_Text _articleTextGo;
        [SerializeField] private GameObject _articleImageGo;
        [SerializeField] private GameObject _content;

        public void PresentArticleContent(string articleTitle, string articleContent)
        {
            SetHeaderText(articleTitle);
            InstantiateHtmlComponents(articleContent);
        }
        
        private void InstantiateHtmlComponents(string articleContent)
        {
            var htmlComponents = HtmlMapper.ToObject(articleContent);
            foreach (var item in htmlComponents)
            {
                if (item.Type == HtmlComponentType.Image)
                {
                    InstantiateImagesForHtmlComponent(item).FireAndForget();
                }
                else
                {
                    InstantiateTextForHtmlComponent(HtmlRender.RenderItem(item));
                }
            }
        }

        #region Text

        private void SetHeaderText(string articleTitleText)
        {
            _headerTitle.text = articleTitleText ?? "";
        }

        private void InstantiateTextForHtmlComponent(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return;

            var articleTextGoInstance = Instantiate(_articleTextGo, _content.transform);
            articleTextGoInstance.SetText(text);
        }

        #endregion

        #region Image

        private async Task InstantiateImagesForHtmlComponent(HtmlComponent item)
        {
            if (item.Children.Any())
            {
                foreach (var child in item.Children)
                {
                    if (!string.IsNullOrWhiteSpace(child.Value))
                        await ConfigureImageComponent(child.Value);
                }
            }
            else
            {
                await ConfigureImageComponent(item.Value);
            }
        }

        private async Task ConfigureImageComponent(string imageUrl)
        {
            var articleImageGoInstance = Instantiate(_articleImageGo, _content.transform);

            //download image
            var texture = await ZendeskMessaging.Instance.Kit.DownloadImage(imageUrl);
            if (texture == null)
            {
                Destroy(articleImageGoInstance);
                return;
            }

            var rawImage = articleImageGoInstance.GetComponentInChildren<RawImage>();
            rawImage.texture = texture.Data;
            if (rawImage.gameObject.TryGetComponent<IZMAspectRatioFitter>(out var aspectRatioFitter))
            {
                aspectRatioFitter.SetAspectRatio(texture.Data);
                var layoutElement = articleImageGoInstance.GetComponent<LayoutElement>();
                SetImageSize(rawImage, layoutElement);
            }
        }

        private void SetImageSize(RawImage image, LayoutElement layoutElement)
        {
            var rect = image.rectTransform.rect;
            var height = rect.height;
            var width = rect.width;

            layoutElement.preferredWidth = width;
            layoutElement.preferredHeight = height;
        }

        #endregion
    }
}