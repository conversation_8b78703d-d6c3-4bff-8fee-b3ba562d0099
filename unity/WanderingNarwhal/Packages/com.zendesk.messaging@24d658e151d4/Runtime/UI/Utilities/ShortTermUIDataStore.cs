using System.Collections.Generic;

namespace Zendesk.Runtime.UI.Utilities
{
    public interface IShortTermUIDataStore
    {
        void StoreData<T>(int id, T data) where T : class;
        bool TryGetData<T>(int id, out T data) where T : class;
    }
    
    public class ShortTermUIDataStore : 
        MonoService<IShortTermUIDataStore, ShortTermUIDataStore>, IShortTermUIDataStore
    {
        private readonly Dictionary<int, object> _dataStore = new Dictionary<int, object>();

        public void StoreData<T>(int id, T data) where T : class
        {
            _dataStore[id] = data;
        }

        public bool TryGetData<T>(int id, out T data) where T : class
        {
            if (!_dataStore.ContainsKey(id) ||
                !(_dataStore[id] is T retrievedData))
            {
                data = null;
                return false;
            }

            data = retrievedData;
            return data != null;
        }
    }
}
