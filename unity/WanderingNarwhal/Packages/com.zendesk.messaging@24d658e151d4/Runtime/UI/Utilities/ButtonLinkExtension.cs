using System;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Messaging.WidgetComponents;
using Zendesk.Runtime.UI.Utilities.ClickActionHandlers;

namespace Zendesk.Runtime.UI.Utilities
{
    internal static class ButtonLinkExtension
    {
        public static void ConfigureButtonLinkAction(this IButtonLinkComponent buttonLinkComponent,
            MessageArticleSuggestionAction action, IClickActionHandlerFactory handlerFactory = null)
        {
            try
            {
                handlerFactory ??= new ClickActionHandlerFactory(buttonLinkComponent.LinkHandlerComponent);
                var handler = handlerFactory.CreateHandler(action);
                var actionText = action.GetActionData();

                if (handler == null || string.IsNullOrEmpty(actionText))
                {
                    buttonLinkComponent.CreateUnsupportedButton();
                    return;
                }

                buttonLinkComponent.TryCreateButtonLink(action.Text, actionText, handler);
            }
            catch (Exception)
            {
                buttonLinkComponent.CreateUnsupportedButton();
            }
        }

        private static void CreateUnsupportedButton(this IButtonLinkComponent buttonLinkComponent)
        {
            buttonLinkComponent.CreateOptionNotUnsupportedButtonLink();
        }
    }
}