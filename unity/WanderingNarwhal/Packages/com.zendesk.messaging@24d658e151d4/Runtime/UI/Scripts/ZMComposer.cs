using System.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.Messaging;
using Zendesk.Runtime.Plugins.ZMAttachmentPlugin;
using Zendesk.Runtime.UI.Scripts.Animators;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Scripts
{
    /// <summary>
    /// Composer state options
    /// </summary>
    internal enum ComposerState
    {
        Enabled,
        Disabled
    }

    /// <summary>
    /// Script attached to composer
    /// </summary>
    public class ZMComposer : MonoBehaviour
    {
        #region Game Objects

        [SerializeField] internal ZMTheme zmTheme;
        [SerializeField] internal TMP_InputField messageTextComponent;
        [SerializeField] internal RectTransform _messageComponentTransform;
        [SerializeField] internal Button attachmentButton;
        [SerializeField] internal Button sendButton;
        [SerializeField] internal GameObject sendButtonIconGo;
        [SerializeField] internal GameObject keyboardBackground;
        #endregion

        #region Properties
        private bool SendButtonInteractability
        {
            get => sendButton.interactable;
            set => sendButton.interactable = value;
        }
        internal string Placeholder { get; set; } = "Type a message";
        internal MessagingInstance zendeskMessaging;
        internal ISendButtonAnimator SendButtonAnimator;
        private readonly Logger _logger = LoggerManager.GetLogger<ZMComposer>();

        #endregion
        
        #region State Methods

        public void Awake()
        {
            SendButtonInteractability = false;
            SetThemeForComposer();
            SetPlaceholder();
            zendeskMessaging = zendeskMessaging ?? ZendeskMessaging.Instance;
            SendButtonAnimator = gameObject.GetComponent<ISendButtonAnimator>();
            ToggleAttachmentButton(zendeskMessaging.Kit.MessagingSettings.attachmentsEnabled);
        }

        public void OnEnable()
        {
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        internal void SetState(ComposerState state)
        {
            switch (state)
            {
                case ComposerState.Enabled:
                    messageTextComponent.interactable = true;
                    attachmentButton.interactable = true;
                    break;
                case ComposerState.Disabled:
                    messageTextComponent.interactable = false;
                    attachmentButton.interactable = false;
                    break;
            }
        }

        internal void ToggleAttachmentButton(bool attachmentsEnabled)
        {
            if (attachmentsEnabled)
            {
                attachmentButton.gameObject.SetActive(true);
                _messageComponentTransform.offsetMin = new Vector2(150, _messageComponentTransform.offsetMin.y);
            }
            else
            {
                attachmentButton.gameObject.SetActive(false);
                _messageComponentTransform.offsetMin = new Vector2(36, _messageComponentTransform.offsetMin.y);
            }
        }
        
        #endregion

        #region Events

        /// <summary>
        /// Subscribe to events
        /// </summary>
        private void SubscribeToEvents()
        {
            //Message Text Input Field actions
            if (messageTextComponent != null)
            {
                messageTextComponent.onValueChanged.AddListener(MessageTextComponentUpdated);
                messageTextComponent.onSelect.AddListener(MessageTextComponentSelected);
                messageTextComponent.onDeselect.AddListener(MessageTextComponentDeselected);
            }

            //Send Button: add action to send button onClick event
            sendButton.onClick.AddListener(SendButtonPressed);
            attachmentButton.onClick.AddListener(AttachmentButtonPressed);
        }

        /// <summary>
        /// Show background if input field is selected
        /// </summary>
        /// <param name="args">not used</param>
        private void MessageTextComponentSelected(string args)
        {
            #if !UNITY_EDITOR
            if (keyboardBackground != null) keyboardBackground.SetActive(true);
            #endif
        }

        /// <summary>
        /// Hide background if input field is deselected
        /// </summary>
        /// <param name="args">not used</param>
        private void MessageTextComponentDeselected(string args)
        {
            #if !UNITY_EDITOR
            if (keyboardBackground != null)
            {
                keyboardBackground.SetActive(false);
            }
            #endif

            ZMTypingIndicator.Instance.StopTyping();
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            sendButton.onClick.RemoveListener(SendButtonPressed);
            attachmentButton.onClick.RemoveListener(AttachmentButtonPressed);
            if (messageTextComponent != null)
            {
                messageTextComponent.onValueChanged.RemoveListener(MessageTextComponentUpdated);
                messageTextComponent.onSelect.RemoveListener(MessageTextComponentSelected);
                messageTextComponent.onDeselect.RemoveListener(MessageTextComponentDeselected);
            }
        }


        /// <summary>
        /// Event which is triggered when send button is pressed
        /// </summary>
        private void SendButtonPressed()
        {
            SendButtonInteractability = false;
            
            zendeskMessaging.Kit.SendMessage(messageTextComponent.text);

            SendButtonAnimator.FadeOut();

            SendButtonInteractability = true;
            ClearComposer();
        }

        /// <summary>
        ///  Event which is triggered when attachment button is pressed
        /// </summary>
        internal void AttachmentButtonPressed()
        {
            _logger.Log("Attachment button pressed");
            ZMAttachmentPicker.GetImageFromGallery(MediaPickCallback);
        }

        /// <summary>
        ///  Callback for when the user selects media from the attachment picker
        /// </summary>
        private async void MediaPickCallback(string path)
        {
            if (path == null)
                return;

            _logger.Log("Image path: " + path);
            await UploadFileAttachment(path);
        }

        /// <summary>
        /// Submit selected media as an end user file attachment
        /// </summary>
        /// <param name="mediaPath"> location of the file to upload </param>
        private async Task UploadFileAttachment(string mediaPath)
        {
            await zendeskMessaging.Kit.SendFile(mediaPath);
        }

        /// <summary>
        /// Action which will check the value in the input field component
        /// </summary>
        /// <param name="text">Text from input field component</param>
        private void MessageTextComponentUpdated(string text)
        {
            //if text from input field has non-whitespace content, then the send button is visible
            bool previousSendButtonInteractability = SendButtonInteractability;
            SendButtonInteractability = !string.IsNullOrWhiteSpace(text);

            //a typing event is sent as stop or start, respectively if the composer message is whitespace/empty/null or not
            if (!SendButtonInteractability)
            {
                ZMTypingIndicator.Instance.StopTyping();
                
                //the send button is hidden when the composer text is whitespace or empty
                sendButtonIconGo.SetImageOpacity(0);
            }
            else
            {
                ZMTypingIndicator.Instance.StartTyping();
                
                //the send button plays a fade in animation when its state changes from not interactable to interactable
                if (!previousSendButtonInteractability)
                    SendButtonAnimator.FadeIn();
            }
        }

        /// <summary>
        /// Method to clear the composer
        /// </summary>
        internal void ClearComposer()
        {
            messageTextComponent.text = string.Empty;
        }

        #endregion

        #region Theme/UI

        /// <summary>
        /// Set theme for composer components
        /// </summary>
        internal void SetThemeForComposer()
        {
            // //if game object is null, do nothing
            if (sendButtonIconGo == null)
            {
                _logger.LogWarning("Button icon component is null.");
                return;
            }

            sendButtonIconGo.SetImageColor(zmTheme.PrimaryColor);
            sendButtonIconGo.SetImageOpacity(0);
        }

        /// <summary>
        /// Set placeholder text
        /// </summary>
        internal void SetPlaceholder()
        {
            messageTextComponent.placeholder.GetComponent<TextMeshProUGUI>().text = Placeholder;
        }

        #endregion
    }
}