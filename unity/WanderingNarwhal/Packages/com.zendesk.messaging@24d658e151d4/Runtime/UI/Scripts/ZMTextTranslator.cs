using TMPro;
using UnityEngine;
using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.Messaging;
using Zendesk.Runtime.Translations;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Scripts
{
    internal interface IZMTextTranslator
    {
        void ChangeKeyAndSetText(string newKey, string textToFormat = null);
    }

    [RequireComponent(typeof(TMP_Text))]
    internal class ZMTextTranslator : MonoBehaviour, IZMTextTranslator
    {
        [SerializeField] private string key;
        private TMP_Text _textComponent;
        private IZMTranslations _zmTranslations;
        private readonly Logger _logger = LoggerManager.GetLogger<ZMTextTranslator>();

        # region Game Object Lifecycle

        private void Awake()
        {
            _textComponent = GetComponent<TMP_Text>();
            ZMTranslations.TranslationsUpdated += OnTranslationsUpdated;
            SetText();
        }

        private void OnDestroy()
        {
            ZMTranslations.TranslationsUpdated -= OnTranslationsUpdated;
        }

        #endregion
        
        public void ChangeKeyAndSetText(string newKey, string textToFormat = null)
        {
            key = newKey;
            SetText(textToFormat);
        }
        
        internal void SetTranslationsInstance(IZMTranslations zmTranslations)
        {
            _zmTranslations = zmTranslations;
        }
        
        private void OnTranslationsUpdated()
        {
            SetText();
        }
        
        private void SetText(string textToFormat = null)
        {
            if (_textComponent == null)
            {
                _textComponent = GetComponent<TMP_Text>();
            }
            if (string.IsNullOrWhiteSpace(key))
            {
                _textComponent.SetText(string.Empty);
                return;
            }
            
            var translatedString = GetTranslatedString();
            if (string.IsNullOrWhiteSpace(translatedString))
                _logger.LogWarning(string.Format(ZMConstants.TranslationStringKeyCouldNotBeFound, key));

            if (!string.IsNullOrWhiteSpace(translatedString) 
                && !string.IsNullOrWhiteSpace(textToFormat) 
                && translatedString.Contains("{"))
            {
                translatedString = string.Format(translatedString, textToFormat);
            }

            _textComponent.SetText(translatedString);
        }

        private string GetTranslatedString()
        {
            _zmTranslations ??= ZMTranslations.Current;
            var translatedString = _zmTranslations.GetString(key);
            return translatedString;
        }
    }
}