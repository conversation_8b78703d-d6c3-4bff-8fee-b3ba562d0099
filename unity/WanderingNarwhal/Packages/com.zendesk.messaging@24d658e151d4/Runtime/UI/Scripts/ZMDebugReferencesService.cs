using System;
using UnityEngine;
using Zendesk.Runtime.UI.Utilities;

namespace Zendesk.Runtime.UI.Scripts
{
    /// <summary>
    /// Stores references for debug menus which are not present in the SDK codebase.
    /// This class should not be used by code shipped with the SDK so a mocking interface for MonoService is not required
    /// </summary>
    public class ZMDebugReferencesService : MonoService<ZMDebugReferencesService, ZMDebugReferencesService>
    {
        [SerializeField] private ObjectPoolingReferences _objectPooling;
        public ObjectPoolingReferences ObjectPooling => _objectPooling;
        
        [Serializable]
        public class ObjectPoolingReferences
        {   
            [SerializeField] private RectTransform _scrollViewRoot;
            
            public RectTransform ScrollViewRoot => _scrollViewRoot;
        }
    }
}
