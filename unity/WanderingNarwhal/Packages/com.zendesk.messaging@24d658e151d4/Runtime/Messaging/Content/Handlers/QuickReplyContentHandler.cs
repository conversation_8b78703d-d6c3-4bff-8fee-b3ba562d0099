using System.Linq;
using Zendesk.MessagingCore.Messaging.DTO;

namespace Zendesk.Runtime.Messaging.Content.Handlers
{
    /// <summary>
    /// Interface for the quick reply type handler.
    /// </summary>
    internal interface IQuickReplyContentHandler : IMessageContentHandler
    {
    }

    /// <summary>
    /// Class to handle the quick reply message type.
    /// </summary>
    internal class QuickReplyContentHandler : IQuickReplyContentHandler
    {
        public MessageContent GetMessageContent(MessageDto message)
        {
            return message.Actions != null && message.Actions.All(action => action.Type == ActionType.Reply)
                ? MessageContent.Create(MessageType.QuickReplies, GeneratePreviewText(message))
                : MessageContent.Create(MessageType.Unknown, message.Text);
        }

        private static string GeneratePreviewText(MessageDto message) => 
            string.Join(", ", message.Actions.Select(action => action.Text));
    }
}