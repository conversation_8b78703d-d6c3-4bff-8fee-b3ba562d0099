using Zendesk.MessagingCore.Messaging.DTO;

namespace Zendesk.Runtime.Messaging.Content.Handlers
{
    /// <summary>
    /// Class to handle the text message type.
    /// </summary>
    internal class TextMessageContentHandler : IMessageContentHandler
    {
        private readonly IButtonLinkContentHandler _buttonLinkContentHandler;
        private readonly IQuickReplyContentHandler _quickReplyContentHandler;

        public TextMessageContentHandler(
            IButtonLinkContentHandler buttonLinkContentHandler,
            IQuickReplyContentHandler quickReplyContentHandler
        )
        {
            _buttonLinkContentHandler = buttonLinkContentHandler;
            _quickReplyContentHandler = quickReplyContentHandler;
        }

        public MessageContent GetMessageContent(MessageDto message)
        {
            if (HasNoActions(message))
                return MessageContent.Create(MessageType.Text, message.Text);

            MessageContent content = _buttonLinkContentHandler.GetMessageContent(message);

            return content.Type == MessageType.ButtonLink 
                ? content 
                : CheckIfQuickReply(message);
        }

        private MessageContent CheckIfQuickReply(MessageDto message) => _quickReplyContentHandler.GetMessageContent(message);

        private static bool HasNoActions(MessageDto messageDto) =>
            messageDto.Actions == null || messageDto.Actions.Count < 1;
    }
}