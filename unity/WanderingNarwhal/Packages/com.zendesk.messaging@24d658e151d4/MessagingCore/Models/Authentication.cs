using Newtonsoft.Json;
using Zendesk.MessagingCore.RestClient.Authenticator;

namespace Zendesk.MessagingCore.Models
{
    public class Authentication
    {
        [JsonProperty] public AuthType type;
        [JsonProperty] public string token;
        
        internal IAuthenticator GetAuthenticator(string userId)
        {
            IAuthenticator authenticator = type switch
            {
                AuthType.jwt => new JwtAuthenticator(token),
                AuthType.sessionToken => new BasicAuthenticator(userId, token),
                _ => null
            };

            return authenticator;
        }
    }

    public enum AuthType
    {
        unauthenticated = 0,
        sessionToken,
        jwt
    }
}