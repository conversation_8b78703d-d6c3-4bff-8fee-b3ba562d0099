using System.Collections.Generic;
using Zendesk.MessagingCore.FayeClient.DTO;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.Serialisation;

namespace Zendesk.MessagingCore.FayeClient.Client
{
    /// <summary>
    /// Builder for parsing message received from server 
    /// </summary>
    internal static class ZendeskFayeMessageResponseBuilder
    {
        private static readonly ISerialisationStrategy _serialiser = new JsonNetSerialisation();

        // Parse the message received from websocket server to object
        public static List<WsMessageReceivedDto> CreateMessageResponse(string message)
        {
            if (string.IsNullOrEmpty(message)) return null;
            
            // deserialise message to json
            var response = _serialiser.Deserialise<List<WsMessageReceivedDto>>(message);
            
            // check which channel the message is from, and add it into a enum
            response[0].ChannelType = response[0].channel switch
            {
                string handshake when handshake.Contains("/meta/handshake") => WsChannelType.Handshake,
                string disconnect when disconnect.Contains("/meta/disconnect") => WsChannelType.Disconnect,
                string connect when connect.Contains("/meta/connect") => WsChannelType.Connect,
                string subscribe when subscribe.Contains("/meta/subscribe") => WsChannelType.Subscribe,
                string sdkApps when sdkApps.Contains("/sdk/apps/") => WsChannelType.Message,
                _ => response[0].ChannelType
            };

            return response;
        }
    }
}