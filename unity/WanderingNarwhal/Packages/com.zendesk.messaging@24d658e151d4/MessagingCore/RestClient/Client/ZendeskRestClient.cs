using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Zendesk.MessagingCore.RestClient.Client.Request;
using Zendesk.MessagingCore.RestClient.DTO.Zendesk;
using Zendesk.MessagingCore.RestClient.Result;
using Zendesk.MessagingCore.Serialisation;

namespace Zendesk.MessagingCore.RestClient.Client
{
    internal sealed class ZendeskRestClient : IZendeskRestClient
    {
        private readonly IRestClient _restClient;
        private readonly Dictionary<string, string> _zendeskHeaders;
        private readonly string _baseUrl;
        private readonly JsonNetSerialisation _serialiser;

        internal ZendeskRestClient(
            string baseUrl,
            string sdkVersion,
            string osVersion,
            string platform,
            string locale)
        {
            _zendeskHeaders = GenerateZendeskHeaders(sdkVersion, osVersion, platform, locale);
            _baseUrl = baseUrl;
            _serialiser = new JsonNetSerialisation();
            _restClient = new RestClient();
        }

        public async Task<Result<SettingsResponseDto>> GetSettings(CancellationToken cancellationToken = default)
        {
            try
            {
                var requestBuilder = new RestRequest.Builder()
                    .WithMethod(HttpMethod.GET)
                    .WithUrl(_baseUrl)
                    .WithHeaders(_zendeskHeaders)
                    .Build();

                var result = await _restClient.PerformRequest(requestBuilder);

                if (result.IsSuccess)
                {
                    var settingsResponse = _serialiser.Deserialise<RootSettingsResponseDto>(result.Body);
                    return new SuccessResult<SettingsResponseDto>(settingsResponse.settings);
                }

                return new ErrorResult<SettingsResponseDto>("Get Settings Failed");    
            }
            catch (Exception e)
            {
                return new ErrorResult<SettingsResponseDto>("Exception: " + e.StackTrace);
            }
        }

        private static Dictionary<string, string> GenerateZendeskHeaders(string sdkVersion, string osVersion,
            string platform,
            string locale)
        {
            var zendeskHeaders = new Dictionary<string, string>
            {
                ["Content-Type"] = "application/json",
                ["Accept"] = "application/json",
                ["X-Zendesk-Api-Version"] = "2021-01-01"
            };

            var userAgentHeader = HeaderBuilder.CreateUserAgentHeader(sdkVersion, platform, osVersion);
            var zendeskClientHeader = HeaderBuilder.CreateZendeskClientHeader(platform);

            zendeskHeaders.Add(userAgentHeader.Key, userAgentHeader.Value);
            zendeskHeaders.Add(zendeskClientHeader.Key, zendeskClientHeader.Value);
            zendeskHeaders.Add("X-Zendesk-Client-Version", sdkVersion);
            zendeskHeaders.Add("Accept-Language", locale);

            return zendeskHeaders;
        }
    }
}