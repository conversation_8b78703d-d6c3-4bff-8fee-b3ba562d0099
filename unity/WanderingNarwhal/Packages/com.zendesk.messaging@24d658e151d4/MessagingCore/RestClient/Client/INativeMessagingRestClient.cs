using System.Collections.Generic;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Messaging;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.RestClient.DTO.NativeMessaging;
using Zendesk.MessagingCore.RestClient.Result;
using Zendesk.MessagingCore.RestClient.Services;

namespace Zendesk.MessagingCore.RestClient.Client
{
    public interface INativeMessagingRestClient
    {
        Task<Result<AppUserResponseDto>> CreateUser(string clientId,
            IReadOnlyDictionary<string, object> metadata = null,
            string intent = "conversation:start");

        Task<Result<SendMessageResponseDto>> SendMessage(ZendeskMessageRequest messageRequest, Authentication auth);

        Task<Result<SendFileResponseDto>> SendFile(string filePath, string conversationId,
            string userId, Authentication authentication, string clientId);
        
        Task<Result<bool>> SendPostback(PostbackParameters postbackParameters);

        Task<Result<AppUserResponseDto>> GetUser(string clientId, string userId, Authentication authentication);

        Task<Result<ConversationResponseDto>> GetConversation(string conversationId, string userId,
            Authentication authentication);

        Task<Result.Result> SetConversationActivity(string conversationId, string userId, string clientId, Authentication authentication,
            string intent = "conversation:read");

        Task<Result<ConversationResponseDto>> UpdateConversation(string conversationId, string userId, Authentication authentication, string clientId,
            IReadOnlyDictionary<string, object> metadata);
        
        Task<Result<AppUserResponseDto>> Login(string externalUserId, string jwtToken, string clientId, string currentUserId = null, string currentUserSessionToken = null);

        Task<Result.Result> Logout(string clientId, string userId, Authentication authentication);
        Task<Result<ConversationResponseDto>> CreateConversation(string clientId, string userId, Authentication authentication, Dictionary<string, object> metadata = null);
    }
}