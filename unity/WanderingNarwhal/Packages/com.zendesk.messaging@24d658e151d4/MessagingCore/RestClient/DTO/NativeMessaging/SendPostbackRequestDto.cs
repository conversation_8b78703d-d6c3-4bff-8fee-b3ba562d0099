namespace Zendesk.MessagingCore.RestClient.DTO.NativeMessaging
{
    public class SendPostbackRequestDto
    {
        public AuthorRequestDto author;
        public PostbackDto postback;

        public static SendPostbackRequestDto Build(string actionId, string userId, string clientId, string integrationId)
        {
            return new SendPostbackRequestDto
            {
                author = AuthorRequestDto.Build(userId, clientId, integrationId),
                postback = new PostbackDto(actionId)
            };
        }
    }

    public class PostbackDto
    {
        public string actionId;

        public PostbackDto(string actionId)
        {
            this.actionId = actionId;
        }
    }
}