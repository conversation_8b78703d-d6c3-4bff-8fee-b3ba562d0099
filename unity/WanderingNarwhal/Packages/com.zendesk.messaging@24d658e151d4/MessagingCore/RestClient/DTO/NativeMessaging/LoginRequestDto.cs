using Newtonsoft.Json;

namespace Zendesk.MessagingCore.RestClient.DTO.NativeMessaging
{
    public class LoginRequestDto
    {
        [JsonProperty("userId")]
        public string ExternalUserId { get; set; }

        [JsonProperty("client")]
        public ClientRequestDto Client { get; set; }

        [JsonProperty("appUserId", NullValueHandling = NullValueHandling.Ignore)]
        public string AnonymousUserId { get; set; }

        [JsonProperty("sessionToken", NullValueHandling = NullValueHandling.Ignore)]
        public string AnonymousUserSessionToken { get; set; }

        public static LoginRequestDto Build(string externalUserId, string clientId, string integrationId,
            string currentUserId = null, string currentSessionToken = null)
        {
            return new LoginRequestDto
            {
                ExternalUserId = externalUserId,
                Client = ClientRequestDto.Build(clientId, integrationId, ClientInfoDto.BuildDefault()),
                AnonymousUserId = currentUserId,
                AnonymousUserSessionToken = currentSessionToken
            };
        }
    }
}