using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Domain.Entities;
using Zendesk.MessagingCore.Guide.Domain.Repositories;
using Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.DTOs;
using Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.Services;
using Zendesk.MessagingCore.Logging;
using Zendesk.MessagingCore.RestClient.Client;
using Zendesk.MessagingCore.RestClient.Client.Request;
using Zendesk.MessagingCore.RestClient.Result;

namespace Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories
{
    internal class GetArticlesHttpRepository : IGetArticlesRepository
    {
        private readonly string _baseUrl;
        private readonly IPaginationService<ArticlesResponseDto> _paginationService;
        private const string _EndpointUrl = "api/v2/help_center/{0}/articles.json";
        private readonly Logger _logger = LoggerManager.GetLogger<GetArticlesHttpRepository>();
        
        public GetArticlesHttpRepository(
            string baseUrl,
            IPaginationService<ArticlesResponseDto> paginationService)
        {
            _baseUrl = baseUrl;
            _paginationService = paginationService;
        }

        public async Task<Result<IEnumerable<ArticleEntity>>> GetArticlesAsync(
            string locale, 
            Dictionary<string, string> headers,
            int pageSize = 100)
        {
            try
            {
                var httpRequest = new RestRequest.Builder()
                    .WithMethod(HttpMethod.GET)
                    .WithUrl(ParseUrl(locale))
                    .WithHeaders(headers)
                    .Build();

                var response = await _paginationService.PerformRequest(httpRequest, pageSize);

                if (!response.Success)
                {
                    _logger.LogError($"Failed to retrieve Guide articles. Status code: {response.StatusCode}");
                    return new ErrorResult<IEnumerable<ArticleEntity>>(
                        "Failed to retrieve Guide articles",
                        response.StatusCode);
                }

                var articlesResponseDto = response.Data.CombineResponses(
                    responses => responses._articles,
                    (dto, list) => dto._articles = new List<ArticleDto>(list)
                );

                return new SuccessResult<IEnumerable<ArticleEntity>>(articlesResponseDto.ToDomainArticles());
            } 
            catch (Exception e)
            {
                _logger.LogException(e);
                return new ErrorResult<IEnumerable<ArticleEntity>>("Failed to retrieve Guide articles", 500);
            }
        }
        
        private string ParseUrl(string locale)
        {
            var fullUrl = $"{_baseUrl}{_EndpointUrl}";
            return string.Format(fullUrl, locale);
        }
    }
}