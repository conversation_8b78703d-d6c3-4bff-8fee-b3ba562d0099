using System.Collections.Generic;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.DTOs;
using Zendesk.MessagingCore.RestClient.Client.Request;
using Zendesk.MessagingCore.RestClient.Result;

namespace Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.Services
{
    internal interface IPaginationService<T> where T : PaginationDto
    {
        Task<Result<IEnumerable<T>>> PerformRequest(IRestRequest request, int pageSize);
    }
}