using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace Zendesk.MessagingCore.Guide.Infrastructure.HttpRepositories.DTOs
{
    internal class PaginationDto
    {
        [JsonProperty("meta")] internal Meta _meta;
    }

    internal class Meta
    {
        [JsonProperty("has_more")] internal bool _hasMore;
        [JsonProperty("after_cursor")] internal string _afterCursor;
    }

    internal static class PaginationDtoExtensions
    {
        public static T CombineResponses<T, TItem>(
            this IEnumerable<T> dto,
            Func<T, IEnumerable<TItem>> getResponses,
            Action<T, IEnumerable<TItem>> addCombinedResponses
        ) where T : PaginationDto, new()
        {
            var flattenedResponses = dto.SelectMany(
                paginationDto => getResponses(paginationDto) ?? Enumerable.Empty<TItem>()
            ).ToList();

            var combinedResponses = new T();
            addCombinedResponses(combinedResponses, flattenedResponses);
            return combinedResponses;
        }
    }
}