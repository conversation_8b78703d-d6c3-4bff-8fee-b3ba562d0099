namespace Zendesk.MessagingCore.Guide.Application.DTOs
{
    /// <summary>
    /// Data Transfer Object for getting Articles.
    /// </summary>
    public class GetArticleRequestDto
    {
        /// <summary>
        /// Gets the Locale for which the article should be retrieved.
        /// </summary>
        public string Locale { get; private set; }

        /// <summary>
        /// The article ID to retrieve.
        /// </summary>
        public long Id { get; private set; }

        /// <summary>
        /// Creates a new instance
        /// </summary>
        /// <returns>A new instance.</returns>
        public static GetArticleRequestDto Create(long id, string locale)
        {
            return new GetArticleRequestDto
            {
                Id = id,
                Locale = locale
            };
        }
    }
}