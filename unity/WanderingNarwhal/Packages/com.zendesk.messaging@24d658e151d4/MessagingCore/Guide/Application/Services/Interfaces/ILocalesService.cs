using System.Threading.Tasks;
using Zendesk.MessagingCore.Guide.Application.DTOs;

namespace Zendesk.MessagingCore.Guide.Application.Services.Interfaces
{
    /// <summary>
    /// Service interface for managing locales in Guide.
    /// </summary>
    internal interface ILocalesService
    {
        /// <summary>
        /// Asynchronously gets the supported locale from the Guide server.
        /// </summary>
        /// <param name="requestDto">The request data transfer object containing the request parameters.</param>
        /// <param name="headers">The headers to include in the request.</param>
        /// <returns>A task result containing the supported locale as a string.</returns>
        Task<LocaleInfo> GetSupportedLocaleAsync(GetLocalesRequestDto requestDto, Headers headers);
    }
}