using System.Collections.Generic;
using System.Linq;
using Zendesk.MessagingCore.Logging;

namespace Zendesk.MessagingCore.Guide.Domain.Entities
{
    internal sealed class CategoryEntity
    {
        private readonly Logger _logger = LoggerManager.GetLogger<CategoryEntity>();
        private CategoryEntity()
        {
        }

        public long Id { get; private set; }
        public string Name { get; private set; }
        internal string Description { get; private set; }
        internal string Locale { get; private set; }
        internal string Url { get; private set; }

        private readonly List<SectionEntity> _sections = new List<SectionEntity>();
        public IReadOnlyCollection<SectionEntity> Sections => _sections.AsReadOnly();

        public static CategoryEntity Create(
            long id,
            string name,
            string description,
            string locale,
            string url
        )
        {
            if (!IsCategoryEntityValid(id, name))
            {
                return new CategoryEntity();
            }

            return new CategoryEntity
            {
                Id = id,
                Name = name,
                Description = description,
                Locale = locale,
                Url = url
            };
        }
        
        public void AddSection(SectionEntity section)
        {
            if (section == null
                || _sections.Any(s => s.Id == section.Id)
                || section.CategoryId != Id)
            {
                _logger.LogWarning($"Failed to add section to category with id {Id}");
                return;
            }
            
            _sections.Add(section);
        }
        
        private static bool IsCategoryEntityValid(long id, string name) => id > 0 && !string.IsNullOrWhiteSpace(name);
    }
}