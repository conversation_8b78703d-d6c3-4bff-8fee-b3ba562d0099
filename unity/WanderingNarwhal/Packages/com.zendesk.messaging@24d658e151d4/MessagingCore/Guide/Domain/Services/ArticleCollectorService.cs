using System.Collections.Generic;
using System.Linq;
using Zendesk.MessagingCore.Guide.Domain.Entities;
using Zendesk.MessagingCore.Logging;

namespace Zendesk.MessagingCore.Guide.Domain.Services
{
    internal sealed class ArticleCollectorService : IArticleCollectorService
    {
        private readonly Logger _logger = LoggerManager.GetLogger<ArticleCollectorService>();

        public List<CategoryEntity> CollectArticles(
            IEnumerable<CategoryEntity> categories,
            IEnumerable<SectionEntity> sections,
            IEnumerable<ArticleEntity> articles
        )
        {
            var sectionEntities = sections.Where(s => s.Id != default).ToList();
            var articleEntities = articles.Where(a => a.Id != default).ToList();
            var sectionsDictionary = new Dictionary<long, SectionEntity>();
            var categoriesDictionary = categories.ToDictionary(categoryEntity => categoryEntity.Id);
            AddSectionsToCategories(sectionEntities, sectionsDictionary, categoriesDictionary);
            AddArticlesToSections(articleEntities, sectionsDictionary);
            SortArticlesInSections(sectionEntities);

            return categoriesDictionary.Values.ToList();
        }

        private void AddSectionsToCategories(
            List<SectionEntity> sections,
            Dictionary<long, SectionEntity> sectionsDict,
            Dictionary<long, CategoryEntity> categoriesDict
        )
        {
            foreach (SectionEntity sectionEntity in sections)
            {
                sectionsDict.Add(sectionEntity.Id, sectionEntity);
                AddSectionToCategory(categoriesDict, sectionEntity);
            }
        }

        private void AddSectionToCategory(Dictionary<long, CategoryEntity> categoriesDict, SectionEntity section)
        {
            if (categoriesDict.TryGetValue(section.CategoryId, out CategoryEntity category))
            {
                category.AddSection(section);
            }
            else
            {
                _logger.LogWarning(
                    $"Category with id {section.CategoryId} not found for section with id {section.Id}");
            }
        }

        private void AddArticlesToSections(IEnumerable<ArticleEntity> articles,
            Dictionary<long, SectionEntity> sectionsDict)
        {
            foreach (var articleEntity in articles.Where(a => a.Id != default))
            {
                AddArticleToSection(sectionsDict, articleEntity);
            }
        }

        private void AddArticleToSection(Dictionary<long, SectionEntity> sectionsDict, ArticleEntity article)
        {
            if (sectionsDict.TryGetValue(article.SectionId, out var section))
            {
                section.AddArticle(article);
            }
            else
            {
                _logger.LogWarning(
                    $"Section with id {article.SectionId} not found for article with id {article.Id}");
            }
        }

        private static void SortArticlesInSections(IEnumerable<SectionEntity> sections)
        {
            foreach (var sectionEntity in sections)
            {
                sectionEntity.SortArticlesByLastUpdated();
            }
        }
    }
}