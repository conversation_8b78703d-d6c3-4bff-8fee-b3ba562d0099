using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using BBB.InfixExpressionEvaluator;
using LuaInterface;
using UnityEditor;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace BBBEditor
{
    /// <summary>
    /// Unit tests runner, which is created to replace Unity Test Runner window,
    /// because it is not accessible due to requirement for special Assembly Definition file with test mode enabled (currently this project can't have any Assembly Definitions).
    /// </summary>
    public class CustomUnitTestsWindow : EditorWindow
    {
        private enum RunStatusType
        {
            None = 0,
            Success,
            Fail,
            Exception,
        }

        private class AssertionException : Exception
        {
            public AssertionException(string message) : base(message)
            {
            }

            public AssertionException(string message, Exception innerException) : base(message, innerException)
            {
            }
        }

        private static string _exprInput;
        private static string _floatExprInput;
        private static bool _logs;

        private static string InputExpr
        {
            get { return EditorPrefs.GetString("expr", ""); }
            set { EditorPrefs.SetString("expr", value); }
        }

        private static string InputFloatExpr
        {
            get { return EditorPrefs.GetString("expr_f", ""); }
            set { EditorPrefs.SetString("expr_f", value); }
        }

        private static List<(string name, Delegate method)> _testMethods = new List<(string, Delegate)>(10);
        private static Dictionary<Delegate, RunStatusType> _runStatus = new Dictionary<Delegate, RunStatusType>(10);

        private Vector2 _scroll;

        [MenuItem("BebopBee/Debug/Custom Unit Tests Runner", priority = 5000)]
        public static void CreateWindow()
        {
            EditorWindow.GetWindow<CustomUnitTestsWindow>("Custom Unit Tests Runner");
        }

        private void OnEnable()
        {
            _exprInput = InputExpr;
            _floatExprInput = InputFloatExpr;
            GetTestMethods();
        }

        /// <summary>
        /// Cache all methods that have 'test' in the end of their names and no arguments. -VK
        /// </summary>
        private void GetTestMethods()
        {
            _testMethods.Clear();

            AddMethodsForType(typeof(InfixEvaluatorTests));
            //AddMethodsForType(someOtherTestsClassType);

            void AddMethodsForType(Type type)
            {
                var methods = type.GetMethods(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);

                foreach (var m in methods)
                {
                    if (m.Name.EndsWith("test", StringComparison.InvariantCultureIgnoreCase))
                    {
                        var parameters = m.GetParameters();
                        if (parameters.Length == 0)
                        {
                            //var methodDelegate = Util.CreateDelegate(null, m);
                            //var args = m.GetParameters().Select(p => p.ParameterType).Concat(new[] { m.ReturnType }).ToArray();
                            //var t = Expression.GetDelegateType(args);
                            var t = typeof(Action);
                            var methodDelegate = m.CreateDelegate(t);

                            if (methodDelegate != null)
                            {
                                _testMethods.Add((m.Name, methodDelegate));
                            }
                        }
                    }
                }
            }
        }

        private void OnGUI()
        {
            _logs = GUILayout.Toggle(_logs, "Verbose Logs");
            var str = GUILayout.TextField(_exprInput);
            if (str != _exprInput)
            {
                _exprInput = str;
                InputExpr = _exprInput;
            }

            if (GUILayout.Button("Evaluate bool expression"))
            {
                InfixEvaluatorTests.TestExpr(_exprInput, false, _logs);
            }

            str = GUILayout.TextField(_floatExprInput);
            if (str != _floatExprInput)
            {
                _floatExprInput = str;
                InputFloatExpr = _floatExprInput;
            }

            if (GUILayout.Button("Evaluate float expression"))
            {
                InfixEvaluatorTests.TestExpr(_floatExprInput, true, _logs);
            }

            GUILayout.Space(15);
            if (GUILayout.Button("Run quick performance benchmark test"))
            {
                InfixEvaluatorTests.PerformanceBenchmarkTestMethod();
            }

            GUILayout.Space(15);

            GUILayout.Label("Unit tests:");

            if (GUILayout.Button("Run all"))
            {
                foreach (var m in _testMethods)
                {
                    RunMethod(m.name, m.method);
                }
            }

            _scroll = EditorGUILayout.BeginScrollView(_scroll);
            {
                for (int i = 0; i < _testMethods.Count; i++)
                {
                    var item = _testMethods[i];
                    EditorGUILayout.BeginHorizontal("box");
                    {
                        EditorGUILayout.LabelField(item.name);

                        if (GUILayout.Button("Run", GUILayout.Width(100)))
                        {
                            RunMethod(item.name, item.method);
                        }

                        RunStatusType status = RunStatusType.None;

                        if (_runStatus.ContainsKey(item.method))
                        {
                            status = _runStatus[item.method];
                        }

                        GUILayout.Label(status == RunStatusType.Success ? "Success" : (status == RunStatusType.Fail ? "Fail" : (status == RunStatusType.Exception ? "Exception" : "-")),
                            GUILayout.Width(100));
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
            EditorGUILayout.EndScrollView();
        }

        private void RunMethod(string testName, Delegate method)
        {
            try
            {
                method.DynamicInvoke();
                Debug.Log($"Test '{testName}' success");
                _runStatus[method] = RunStatusType.Success;
            }
            catch (AssertionException assert)
            {
                Debug.LogError($"Test '{testName}' failed. Assertion: " + assert.Message + "\n\n" + assert.StackTrace);
                _runStatus[method] = RunStatusType.Fail;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Test '{testName}' failed. Exception: " + ex.Message + (ex.InnerException == null ? "" : "\n" + ex.InnerException.Message) + "\n\n" + ex.StackTrace);
                _runStatus[method] = ex.InnerException != null && ex.InnerException is AssertionException ? RunStatusType.Fail : RunStatusType.Exception;
            }
        }

        /// <summary>
        /// Simple assertion that throw an exception.
        /// </summary>
        public static void Assert(bool isTrue, string failMessage = null)
        {
            if (isTrue)
            {
            }
            else
            {
                throw new AssertionException("Assertion failed, value was false." + (failMessage.IsNullOrEmpty() ? "" : " Msg=" + failMessage));
            }
        }
    }

    public static class InfixEvaluatorTests
    {
        public static void PerformanceBenchmarkTestMethod()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var w = new Stopwatch();
            evaluator.RegisterObject("locs", new LocationMock(), typeof(ILocation));

            var expr =
                "locs:IsLocationUnlocked('p0') || locs:IsLocationUnlocked('p2') && not locs:IsLocationUnlocked('p3') || (locs:GetLevelStage('p4')>0 && locs:GetLevelStage('p5')>=100 || locs:GetLevelStage('p7') < (50+20.5)) || (locs:GetTotalLevelsAtMinStage('s0', 2*3) > 5/5)";

            w.Start();
            for (int i = 0; i < 500; i++)
            {
                evaluator.EvaluatePredicate(expr);
                evaluator.ClearCachedExpressions();
            }

            w.Stop();
            Debug.Log("elapsed: " + w.ElapsedMilliseconds);
        }

        public static void TestExpr(string expr, bool isFloat, bool logs)
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: logs);
            evaluator.RegisterObject("player", new PlayerMock(), typeof(IPlayer));
            evaluator.RegisterObject("locs", new LocationMock(), typeof(ILocation));
            evaluator.RegisterObject("levels", new LevelsMock(), typeof(ILevels));
            evaluator.RegisterObject("promotion", new PromotionMock(), typeof(IPromotion));
            evaluator.RegisterObject("lives", new LivesMock(), typeof(ILives));
            evaluator.RegisterObject("utils", new UtilsMock(), typeof(IUtils));
            evaluator.RegisterObject("un_promo", new UnPromoMock(), typeof(IUnPromo));
            evaluator.RegisterExternalFunction<Func<int>>("method0", () => { return 0; });
            evaluator.RegisterExternalFunction<Func<int>>("method1", () => { return 1; });
            evaluator.RegisterExternalFunction<Func<int>>("method5", () => { return 5; });
            evaluator.RegisterExternalFunction<Func<int>>("method9", () => { return 9; });
            evaluator.RegisterExternalFunction<Func<bool>>("methodT", () => true);
            evaluator.RegisterExternalFunction<Func<bool>>("methodF", () => false);
            if (isFloat)
            {
                var result = evaluator.EvaluateFloatExpression(expr);
                Debug.Log("End Result = " + result);
            }
            else
            {
                var result = evaluator.EvaluatePredicate(expr);
                Debug.Log("End Result = " + result);
            }

            // var expr0 = "A(5) || B('x') && (!C() && D('s') <= 0 && ( E('p') > 0 || (F() + 1) ))";
        }

        private static void InvokeVoidMethodTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int a = 0;
            evaluator.RegisterExternalFunction<Action>("method0", () => { a = 1; });
            var expr = "method0()";
            evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(a == 1);
        }

        private static void ConstAndBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => true);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => true);
            var expr = "method0() AND method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstAndBinaryOp1Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => true);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => false);
            var expr = "method0() && method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void ConstAndBinaryOp2Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => false);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => true);
            var expr = "method0() AND method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void ConstAndBinaryOp3Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => false);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => false);
            var expr = "method0() && method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void ConstOrBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => true);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => true);
            var expr = "method0() OR method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstOrBinaryOp1Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => true);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => false);
            var expr = "method0() || method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstOrBinaryOp2Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => false);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => true);
            var expr = "method0() OR method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstOrBinaryOp3Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => false);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => false);
            var expr = "method0() || method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void ConstOpPriority0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var expr = "false or true and (not false or false and true)";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void DynamicAndBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            bool val0 = true;
            bool val1 = true;
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => val0);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => val1);
            var expr = "method0() && method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = false;
            val1 = true;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = true;
            val1 = false;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = false;
            val1 = false;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void DynamicOrBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            bool val0 = true;
            bool val1 = true;
            evaluator.RegisterExternalFunction<Func<bool>>("method0", () => val0);
            evaluator.RegisterExternalFunction<Func<bool>>("method1", () => val1);
            var expr = "method0() || method1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = false;
            val1 = true;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = true;
            val1 = false;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = false;
            val1 = false;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void DynamicGreaterThanBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int val0 = 2;
            int val1 = 1;
            evaluator.RegisterExternalFunction<Func<int>>("methodInt0", () => val0);
            evaluator.RegisterExternalFunction<Func<int>>("methodInt1", () => val1);
            var expr = "methodInt0() > methodInt1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = 1;
            val1 = 50;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = 3;
            val1 = 3;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = 1152924;
            val1 = 124523;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void DynamicGreaterThanOrEqualBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int val0 = 2;
            int val1 = 1;
            evaluator.RegisterExternalFunction<Func<int>>("methodInt0", () => val0);
            evaluator.RegisterExternalFunction<Func<int>>("methodInt1", () => val1);
            var expr = "methodInt0() >= methodInt1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = 1;
            val1 = 50;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = 3;
            val1 = 3;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = 1524694;
            val1 = 198526;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void DynamicLessThanBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int val0 = 2;
            int val1 = 1;
            evaluator.RegisterExternalFunction<Func<int>>("methodInt0", () => val0);
            evaluator.RegisterExternalFunction<Func<int>>("methodInt1", () => val1);
            var expr = "methodInt0() < methodInt1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = 1;
            val1 = 50;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);

            val0 = 3;
            val1 = 3;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);

            val0 = 1152924;
            val1 = 124523;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void DynamicLessThanOrEqualBinaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int val0 = 2;
            int val1 = 1;
            evaluator.RegisterExternalFunction<Func<int>>("methodInt0", () => val0);
            evaluator.RegisterExternalFunction<Func<int>>("methodInt1", () => val1);
            var expr = "methodInt0() <= methodInt1()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result, "2 <= 1 is true");

            val0 = 1;
            val1 = 50;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result, "1 <= 50 is false");

            val0 = 3;
            val1 = 3;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result, "3 <= 3 is false");

            val0 = 1524694;
            val1 = 198526;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result, "1524694 <= 198526 is true");
        }

        private static void DynamicNotUnaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var val0 = true;
            evaluator.RegisterExternalFunction<Func<bool>>("methodInt0", () => val0);
            var expr = "not methodInt0()";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result, "'not true' is true");

            val0 = false;
            result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result, "'not false' is false");
        }

        private static void TruthTable0Test()
        {
            var evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var oldEvaluator = new Lua();

            var promotion = new PromotionMock();
            var unPromo = new UnPromoMock();
            var locs = new LocationMock();

            oldEvaluator["promotion"] = promotion;
            oldEvaluator["un_promo"] = unPromo;
            oldEvaluator["locs"] = locs;

            evaluator.RegisterObject("promotion", promotion, typeof(IPromotion));
            evaluator.RegisterObject("un_promo", unPromo, typeof(IUnPromo));
            evaluator.RegisterObject("locs", locs, typeof(ILocation));

            bool operand0 = false;
            bool operand1 = false;
            bool operand2 = false;
            int operand3 = 0;
            int operand4 = 0;

            promotion.isCountryTier = (str) => str == "tier_1" ? operand0 : false;
            unPromo.wasActive = (str) => str == "promo_t1_o2_s2" ? operand1 : false;
            unPromo.wasActiveWithNameStartingFrom = (str) => str == "promo_t1_o2_s" ? operand2 : false;
            locs.getLevelStage = (str) =>
            {
                if (str == "paris34") return operand3;
                if (str == "paris77") return operand4;
                return 0;
            };

            var exp = "promotion:IsCountryTier('tier_1') and " +
                "(un_promo:WasActive('promo_t1_o2_s2') or " +
                "not un_promo:WasActiveWithNameStartingFrom('promo_t1_o2_s') and " +
                "locs:GetLevelStage('paris34') > 0 and " +
                "locs:GetLevelStage('paris77') <= 0)";

            var truthTable = new ExpressionTruthTable()
            {
                expression = exp,
                operands = new[]
                {
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "promotion:IsCountryTier('tier_1')",
                        makeOperandTrueOrFalse = (f) => operand0 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "un_promo:WasActive('promo_t1_o2_s2')",
                        makeOperandTrueOrFalse = (f) => operand1 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "un_promo:WasActiveWithNameStartingFrom('promo_t1_o2_s')",
                        makeOperandTrueOrFalse = (f) => operand2 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "locs:GetLevelStage('paris34') > 0",
                        makeOperandTrueOrFalse = (f) => operand3 = f ? 1 : 0,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "locs:GetLevelStage('paris77') <= 0",
                        makeOperandTrueOrFalse = (f) => operand4 = f ? 0 : 1,
                    },
                },
            };

            AssertTruthTable(truthTable, evaluator, oldEvaluator, enableLogs: false);
        }

        private static void TruthTable1Test()
        {
            var evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var oldEvaluator = new Lua();

            var promotion = new PromotionMock();
            var unPromo = new UnPromoMock();
            var locs = new LocationMock();

            oldEvaluator["promotion"] = promotion;
            oldEvaluator["un_promo"] = unPromo;
            oldEvaluator["locs"] = locs;

            evaluator.RegisterObject("promotion", promotion, typeof(IPromotion));
            evaluator.RegisterObject("un_promo", unPromo, typeof(IUnPromo));
            evaluator.RegisterObject("locs", locs, typeof(ILocation));

            var operand0 = false;
            var operand1 = false;
            var operand2 = false;
            var operand3 = 0;
            var operand4 = 0;
            var operand5 = false;
            var operand6 = false;

            promotion.isCountryTier = (str) => str == "tier_1" ? operand0 : false;
            unPromo.wasActive = (str) => str == "promo_t1_o3_s3" ? operand1 : false;
            unPromo.wasActiveWithNameStartingFrom = (str) => str == "promo_t1_o3_s" ? operand2 : false;
            locs.getLevelStage = (str) =>
            {
                if (str == "paris77") return operand3;
                if (str == "newyork7") return operand4;
                return 0;
            };
            promotion.hasPurchasedWithinPromotion = (str0, str1) => str0 == "com.bebopbee.rl.others.promopack.tier1.stage4" && str1 == "promo_t1_o2_s4" ? operand5 : false;
            promotion.hasNotPurchasedWithinPromotion = (str0, str1) => str0 == "com.bebopbee.rl.others.promopack.tier1.stage2" && str1 == "promo_t1_o2_s2" ? operand6 : false;

            var expr = "promotion:IsCountryTier('tier_1') and " +
                "(un_promo:WasActive('promo_t1_o3_s3') or " +
                "not un_promo:WasActiveWithNameStartingFrom('promo_t1_o3_s') and " +
                "locs:GetLevelStage('paris77') > 0 and " +
                "locs:GetLevelStage('newyork7') <= 0 and " +
                "(promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier1.stage4','promo_t1_o2_s4') or " +
                "promotion:HasNotPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier1.stage2','promo_t1_o2_s2')))";

            var truthTable = new ExpressionTruthTable()
            {
                expression = expr,
                operands = new[]
                {
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "promotion:IsCountryTier('tier_1')",
                        makeOperandTrueOrFalse = (f) => operand0 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "un_promo:WasActive('promo_t1_o3_s3')",
                        makeOperandTrueOrFalse = (f) => operand1 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "un_promo:WasActiveWithNameStartingFrom('promo_t1_o3_s')",
                        makeOperandTrueOrFalse = (f) => operand2 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "locs:GetLevelStage('paris77') > 0",
                        makeOperandTrueOrFalse = (f) => operand3 = f ? 1 : 0,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "locs:GetLevelStage('newyork7') <= 0",
                        makeOperandTrueOrFalse = (f) => operand4 = f ? 0 : 1,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier1.stage4','promo_t1_o2_s4')",
                        makeOperandTrueOrFalse = (f) => operand5 = f,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "promotion:HasNotPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier1.stage2','promo_t1_o2_s2')",
                        makeOperandTrueOrFalse = (f) => operand6 = f,
                    },
                },
            };

            AssertTruthTable(truthTable, evaluator, oldEvaluator, enableLogs: false);
        }

        private static void TruthTable2Test()
        {
            bool a = false;
            bool b = false;
            bool c = false;
            bool d = false;
            bool e = false;
            bool f = false;

            var evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var oldEvaluator = new Lua();

            evaluator.RegisterExternalFunction<Func<bool>>("A", () => a);
            evaluator.RegisterExternalFunction<Func<bool>>("B", () => b);
            evaluator.RegisterExternalFunction<Func<bool>>("C", () => c);
            evaluator.RegisterExternalFunction<Func<bool>>("D", () => d);
            evaluator.RegisterExternalFunction<Func<bool>>("E", () => e);
            evaluator.RegisterExternalFunction<Func<bool>>("F", () => f);

            // Currently old evaluator can't handle lambda binding like this, it will trigger error.
            // TODO: resolve this. -VK
            oldEvaluator["A"] = (Func<bool>)(() => a);
            oldEvaluator["B"] = (Func<bool>)(() => b);
            oldEvaluator["C"] = (Func<bool>)(() => c);
            oldEvaluator["D"] = (Func<bool>)(() => d);
            oldEvaluator["E"] = (Func<bool>)(() => e);
            oldEvaluator["F"] = (Func<bool>)(() => f);

            var expr = "A() AND B() OR (NOT C() OR D() AND (E() OR not F()))";

            var truthTable = new ExpressionTruthTable()
            {
                expression = expr,
                operands = new[]
                {
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "A()",
                        makeOperandTrueOrFalse = (flag) => a = flag,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "B()",
                        makeOperandTrueOrFalse = (flag) => b = flag,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "C()",
                        makeOperandTrueOrFalse = (flag) => c = flag,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "D()",
                        makeOperandTrueOrFalse = (flag) => d = flag,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "E()",
                        makeOperandTrueOrFalse = (flag) => e = flag,
                    },
                    new ExpressionTruthTable.OperandEntry()
                    {
                        operandExpression = "F()",
                        makeOperandTrueOrFalse = (flag) => f = flag,
                    },
                },
            };

            AssertTruthTable(truthTable, evaluator, oldEvaluator, enableLogs: false);
        }

        private static void MethodTypeEval0Test()
        {
            Action method = () => {};
            var type = method.GetType();
            var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
            if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType))
            {
                CustomUnitTestsWindow.Assert(methodType == typeof(Action));
            }
            else
            {
                CustomUnitTestsWindow.Assert(false, "Method type 'Action' not found");
            }
        }

        private static void MethodTypeEval1Test()
        {
            Action<int> method0 = (n) => {};
            Func<int> method1 = () => 0;
            Func<long> method2 = () => 0;
            Func<float> method3 = () => 0f;
            Func<double> method4 = () => 0.0;
            Func<bool> method5 = () => false;
            Func<string> method6 = () => null;
            object[] testArray = new object[]
            {
                method0,
                method1,
                method2,
                method3,
                method4,
                method5,
                method6,
            };

            Type[] results = new Type[]
            {
                typeof(Action<int>),
                typeof(Func<int>),
                typeof(Func<long>),
                typeof(Func<float>),
                typeof(Func<double>),
                typeof(Func<bool>),
                typeof(Func<string>),
            };

            int index = 0;
            foreach (var method in testArray)
            {
                var type = method.GetType();
                var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
                if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType))
                {
                    CustomUnitTestsWindow.Assert(methodType == results[index]);
                }
                else
                {
                    CustomUnitTestsWindow.Assert(false, $"Method type '{type.Name}' not found");
                }

                index++;
            }
        }

        private static void MethodTypeEval2Test()
        {
            Action<int, int> method0 = (n0, n1) => {};
            Action<string, float> method1 = (s, f) => {};
            Action<float, string> method2 = (f, s) => {};

            Func<int, int> method3 = (n) => 0;
            Func<string, float> method4 = (s) => 0f;
            Func<float, string> method5 = (f) => null;

            object[] testArray = new object[]
            {
                method0,
                method1,
                method2,
                method3,
                method4,
                method5,
            };

            Type[] results = new Type[]
            {
                typeof(Action<int, int>),
                typeof(Action<string, float>),
                typeof(Action<float, string>),
                typeof(Func<int, int>),
                typeof(Func<string, float>),
                typeof(Func<float, string>),
            };

            int index = 0;
            foreach (var method in testArray)
            {
                var type = method.GetType();
                var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
                if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType))
                {
                    CustomUnitTestsWindow.Assert(methodType == results[index]);
                }
                else
                {
                    CustomUnitTestsWindow.Assert(false, $"Method type '{type.Name}' not found");
                }

                index++;
            }
        }

        private static void MethodTypeEval3Test()
        {
            Action<int, int> method0 = (n0, n1) => {};
            Action<string, string> method1 = (n0, n1) => {};
            Action<string, float> method2 = (n0, n1) => {};
            Action<float, long> method3 = (n0, n1) => {};
            Action<long, string> method4 = (n0, n1) => {};
            Func<int, int, int> method5 = (n0, n1) => 0;
            Func<string, string, string> method6 = (n0, n1) => null;
            Func<string, float, bool> method7 = (n0, n1) => false;
            Func<long, string, float> method8 = (n0, n1) => 0f;
            Func<bool, double, string> method9 = (n0, n1) => null;

            object[] testArray = new object[]
            {
                method0,
                method1,
                method2,
                method3,
                method4,
                method5,
                method6,
                method7,
                method8,
                method9,
            };

            Type[] results = new Type[]
            {
                typeof(Action<int, int>),
                typeof(Action<string, string>),
                typeof(Action<string, float>),
                typeof(Action<float, long>),
                typeof(Action<long, string>),
                typeof(Func<int, int, int>),
                typeof(Func<string, string, string>),
                typeof(Func<string, float, bool>),
                typeof(Func<long, string, float>),
                typeof(Func<bool, double, string>),
            };

            int index = 0;
            foreach (var method in testArray)
            {
                var type = method.GetType();
                var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
                if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType))
                {
                    CustomUnitTestsWindow.Assert(methodType == results[index], "Failed on method[" + index + "]: " + results[index].Name);
                }
                else
                {
                    CustomUnitTestsWindow.Assert(false, $"Method type '{type.Name}' not found");
                }

                index++;
            }
        }

        private static void MethodTypeEval4Test()
        {
            Action<object> method = (n) => {};
            var type = method.GetType();
            var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
            if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType, enableLogs: false))
            {
                CustomUnitTestsWindow.Assert(false, "Incorrect method type 'Action<object>' has been found found: " + methodType.Name);
            }
        }

        private static void MethodTypeEval5Test()
        {
            Action<char> method = (n) => {};
            var type = method.GetType();
            var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
            if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType, enableLogs: false))
            {
                CustomUnitTestsWindow.Assert(false, "Incorrect method type 'Action<char>' has been found found: " + methodType.Name);
            }
        }

        private static void MethodTypeEval6Test()
        {
            Action<int[]> method = (n) => {};
            var type = method.GetType();
            var m = type.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
            if (InfixExpressionEvaluator.TryGetDelegateType(m, out var methodType, enableLogs: false))
            {
                CustomUnitTestsWindow.Assert(false, "Incorrect method type 'Action<int[]>' has been found found: " + methodType.Name);
            }
        }

        private static void ConstNotUnaryOp0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var expr = "not (1 > 0)";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(!result, "'not (1 > 0)' is true");
        }

        private static void ConstNotUnaryOp1Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var expr = "(2 >= 3) OR NOT (1 < 0)";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstNotUnaryOp2Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var result = evaluator.EvaluatePredicate("not false");
            CustomUnitTestsWindow.Assert(result);
            result = evaluator.EvaluatePredicate("not true");
            CustomUnitTestsWindow.Assert(!result);
        }

        private static void ConstNotUnaryOp3Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var result = evaluator.EvaluatePredicate("not not false");
            CustomUnitTestsWindow.Assert(!result);
            result = evaluator.EvaluatePredicate("not not true");
            CustomUnitTestsWindow.Assert(result);
        }

        private static void ConstNotUnaryOp4Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<bool, bool>>("foo", (b) => b);
            evaluator.RegisterExternalFunction<Func<bool>>("T", () => true);
            var result = evaluator.EvaluatePredicate("not foo(not T())");
            CustomUnitTestsWindow.Assert(result);
        }

        private static void MethodWithSingleStringParamEvalTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<string, bool>>("class:method", (str) => str.Length > 0);
            var expr = "class:method('some param')";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void MethodWithSingleIntParamEvalTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<int, bool>>("class:method", (num) => num > 0);
            var expr = "class:method(7)";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void MethodWithTwoStringParamsEvalTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterExternalFunction<Func<string, string, bool>>("class:method", (str0, str1) => (str0.Length + str1.Length) > 0);
            var expr = "class:method('text 0', 'text 1')";
            var result = evaluator.EvaluatePredicate(expr);
            CustomUnitTestsWindow.Assert(result);
        }

        private static void BindAllMethodInInterface0Test()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            var playerMock = new PlayerMock();
            evaluator.RegisterObject("player", playerMock, typeof(IPlayer));

            var expr = "player:IsVisited('some modal name')";
            evaluator.EvaluatePredicate(expr);
        }

        /// <summary>
        /// Evaluate each existing predicate in game to make sure it works without exceptions.
        /// This test doesn't verify results of each test, actual result of evaluation is tested separately.
        /// </summary>
        private static void ExistingInGameExpressionsFormatEvaluationTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            evaluator.RegisterObject("player", new PlayerMock(), typeof(IPlayer));
            evaluator.RegisterObject("locs", new LocationMock(), typeof(ILocation));
            evaluator.RegisterObject("levels", new LevelsMock(), typeof(ILevels));
            evaluator.RegisterObject("promotion", new PromotionMock(), typeof(IPromotion));
            evaluator.RegisterObject("lives", new LivesMock(), typeof(ILives));
            evaluator.RegisterObject("utils", new UtilsMock(), typeof(IUtils));
            evaluator.RegisterObject("un_promo", new UnPromoMock(), typeof(IUnPromo));

            // Pre-selected subset of real existing predicates in the configs.
            string[] predicates = new[]
            {
                "player:IsVisited('SpecialPackClaim')",
                "player:HasUnlockedPOI('newyork_unisphere')",
                "locs:GetLevelStage('paris26') > 0 and utils:IsCurrentVersionBiggerThan('0.8.27')",
                "player.IsPayer and not player:HasPurchasedAnyOf('com.bebopbee.rl.basket') and promotion:PromotionShowedCountTotal('promo_baskets_nonpayer') <= 0 and locs:GetLevelStage('paris12') > 0",
                "(not player.IsPayer or promotion:PromotionShowedCountTotal('promo_baskets_nonpayer') > 0) and locs:GetLevelStage('paris12') > 0",
                "locs:GetLevelStage('newyork8') >= 1",
                "locs:GetTotalLevelsAtMinStage('newyork', 2) >= 100",
                "promotion:IsCountryTier('tier_2') and (un_promo:WasActive('promo_t2_o2_s1') or not un_promo:WasActiveWithNameStartingFrom('promo_t2_o2_s') and locs:GetLevelStage('paris34') > 0 and locs:GetLevelStage('paris77') <= 0 and (promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage1','promo_t2_o1_s1') or promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage2','promo_t2_o1_s2')))",
                "promotion:IsCountryTier('tier_2') and (un_promo:WasActive('promo_t2_o4_s1') or not un_promo:WasActiveWithNameStartingFrom('promo_t2_o4_s') and locs:GetLevelStage('newyork7') > 0 and locs:GetLevelStage('newyork52') <= 0 and (promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage1','promo_t2_o3_s1') or promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage2','promo_t2_o3_s2')))",
                "promotion:IsCountryTier('tier_2') and (un_promo:WasActive('promo_t2_o9_s1') or not un_promo:WasActiveWithNameStartingFrom('promo_t2_o9_s') and locs:GetLevelStage('tokyo67') > 0 and (promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage1','promo_t2_o8_s1') or promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage2','promo_t2_o8_s2')))",
                "promotion:IsCountryTier('tier_2') and (un_promo:WasActive('promo_t2_o9_s4') or not un_promo:WasActiveWithNameStartingFrom('promo_t2_o9_s') and locs:GetLevelStage('tokyo67') > 0 and (promotion:HasNotPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage4','promo_t2_o8_s4') or promotion:HasNotPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage3','promo_t2_o8_s3')))",
                "promotion:IsCountryTier('tier_2') and (un_promo:WasActive('promo_t2_o9_s2') or not un_promo:WasActiveWithNameStartingFrom('promo_t2_o9_s') and locs:GetLevelStage('tokyo67') > 0)",
                "not lives:alreadyAppliedLifeLifter()",
                "promotion:HasPurchasedWithinPromotion('com.bebopbee.rl.others.promopack.tier2.stage3','promo_t2_o4_s3')",
                "promotion:PromotionShowedCountThisSession('promo_t2_o4_s3') < 1 and levels:GetNumberOfPlayedLevelsInSession() >= 1",
                "locs:GetLevelStage('newyork9') >= 1",
            };

            foreach (var item in predicates)
            {
                try
                {
                    evaluator.EvaluatePredicate(item);
                }
                catch
                {
                    Debug.LogError("Failed on expression: " + item);
                    throw;
                }
            }

            // No assertions, test success means no exceptions happened.
        }

        private static void DynamicFloatExpressionTest()
        {
            InfixExpressionEvaluator evaluator = new InfixExpressionEvaluator(enableVerboseLogs: false);
            int num = 3;
            evaluator.RegisterExternalFunction<Func<int, int>>("class:method", (n) => n + num);
            var expr = "class:method(7) - 1";
            var result = (int)evaluator.EvaluateFloatExpression(expr);
            CustomUnitTestsWindow.Assert(result == 9);
            num = -6;
            result = (int)evaluator.EvaluateFloatExpression(expr);
            CustomUnitTestsWindow.Assert(result == 0);
        }

        private static void AssertTruthTable(ExpressionTruthTable truthTable, InfixExpressionEvaluator evaluator, Lua lua, bool enableLogs = false)
        {
            int totalCombinations = 1 << (truthTable.operands.Length - 1);
            (bool oldResult, bool newResult, string operands)[] testsResults = new (bool oldResult, bool newResult, string operands)[totalCombinations];
            for (int n = 0; n < totalCombinations; n++)
            {
                bool[] flags = new bool[truthTable.operands.Length];
                for (int i = 0; i < truthTable.operands.Length; i++)
                {
                    // For example, for 3 operands the number N changes in this range (totalCombinations is 2^3=8):
                    // 000
                    // 001
                    // 010
                    // 011
                    // 100
                    // 101
                    // 110
                    // 111
                    // and for each of this variants we take one bit and convert it into boolean value for operand on same index.
                    // this allows to test every possible combinations of values for boolean operands in any expression.

                    var operandIndexBit = 1 << i;
                    var operandTargetValue = (operandIndexBit & n) > 0;
                    var operand = truthTable.operands[i];
                    operand.makeOperandTrueOrFalse(operandTargetValue);
                    var operandEval = evaluator.EvaluatePredicate(operand.operandExpression);
                    if (operandEval != operandTargetValue)
                    {
                        operandEval = evaluator.EvaluatePredicate(operand.operandExpression);
                    }

                    CustomUnitTestsWindow.Assert(operandEval == operandTargetValue, $"Operand is not evaluated correctly: '{operand.operandExpression}'");
                    flags[i] = operandTargetValue;
                }

                bool oldEvalFailed;
                var expectedResult = EvaluateUsingOldLuaEvaluator(lua, truthTable.expression, out oldEvalFailed);
                var exprEval = evaluator.EvaluatePredicate(truthTable.expression);
                if (oldEvalFailed)
                {
                    // Old evaluator error happened, so keep overal test result as success and print error in the log.
                    expectedResult = exprEval;
                }

                if (enableLogs)
                {
                    Debug.Log($"TEST: Iteration {n}, Result: {expectedResult}, Operands values: {string.Join(";", flags.Select(f => f ? 1 : 0).ToArray())}");
                }

                testsResults[n] = (oldResult: expectedResult, newResult: exprEval, operands: Convert.ToString(n, 2));
            }

            int failedCount = 0;
            foreach (var item in testsResults)
            {
                if (item.oldResult != item.newResult) failedCount++;
            }

            if (failedCount == 1)
            {
                var item = testsResults[0];
                CustomUnitTestsWindow.Assert(item.oldResult == item.newResult, $"Truth table test failed on iteration 0, operands: {item.operands}, Expression:\n" + truthTable.expression);
            }
            else if (failedCount > 1)
            {
                for (int i = 0; i < testsResults.Length; i++)
                {
                    var item = testsResults[i];
                    CustomUnitTestsWindow.Assert(item.oldResult == item.newResult, $"Truth table test first failed on iteration {i} (total failed iterations count={failedCount})," +
                        $" operands: {item.operands}, Expression:\n" + truthTable.expression);
                }
            }
        }

        private static bool EvaluateUsingOldLuaEvaluator(Lua evaluator, string expression, out bool isException)
        {
            try
            {
                var funcStr = "return (function () return (" + expression + "); end)";
                var luaFunc = evaluator.DoString(funcStr)[0] as LuaFunction;
                isException = false;
                return (bool)luaFunc.Call()[0];
            }
            catch (Exception ex)
            {
                Debug.LogError($"Old lua evaluation exception on expr: '{expression}':\n{ex.Message}\n{ex.StackTrace}");
                isException = true;
                return false;
            }
        }

        private class ExpressionTruthTable
        {
            public string expression;
            public OperandEntry[] operands;

            public class OperandEntry
            {
                public string operandExpression;
                public Action<bool> makeOperandTrueOrFalse;
            }
        }

        #region MOCKS

        /// <summary>
        /// We don't have access to NSubstitute so we just keep it simple
        /// and declare mock types explicitly with delegates inside (for optional customization).
        /// </summary>
        private class PlayerMock : IPlayer
        {
            public Func<bool> isPayerDelegate = () => false;
            public Func<string, bool> isVisited = (str) => false;
            public Func<string, bool> hasPurchasedAnyOf = (str) => false;
            public Func<string, bool> hasUnlockedPOI = (str) => false;
            public Func<string, int> getNumberOfSessionsSinceLastShown = (str) => 0;

            public bool IsPayer
            {
                get { return isPayerDelegate(); }
            }

            public bool IsVisited(string str)
            {
                return isVisited(str);
            }

            public bool HasPurchasedAnyOf(string str)
            {
                return hasPurchasedAnyOf(str);
            }

            public bool HasUnlockedPOI(string str)
            {
                return hasUnlockedPOI(str);
            }

            public int GetNumberOfSessionsSinceLastShown(string str)
            {
                return getNumberOfSessionsSinceLastShown(str);
            }
        }

        private interface IPlayer
        {
            bool IsPayer { get; }
            bool IsVisited(string str);
            bool HasPurchasedAnyOf(string str);
            bool HasUnlockedPOI(string str);
            int GetNumberOfSessionsSinceLastShown(string str);
        }

        public class LocationMock : ILocation
        {
            public Func<string, int> getLevelStage = (str) => 0;
            public Func<string, bool> isLocationUnlocked = (str) => false;
            public Func<int> getUnlockedLocationsCount = () => 0;
            public Func<string, int, int> getTotalLevelsAtMinStage => (str, n) => 0;

            public int GetLevelStage(string str)
            {
                return getLevelStage(str);
            }

            public bool IsLocationUnlocked(string str)
            {
                return isLocationUnlocked(str);
            }

            public int GetUnlockedLocationsCount()
            {
                return getUnlockedLocationsCount();
            }

            public int GetTotalLevelsAtMinStage(string str, int n)
            {
                return getTotalLevelsAtMinStage(str, n);
            }
        }

        public interface ILocation
        {
            int GetLevelStage(string str);
            bool IsLocationUnlocked(string str);
            int GetUnlockedLocationsCount();
            int GetTotalLevelsAtMinStage(string str, int n);
        }

        public class LevelsMock : ILevels
        {
            public Func<int> getNumberOfPlayedLevelsInSession = () => 0;

            public int GetNumberOfPlayedLevelsInSession()
            {
                return getNumberOfPlayedLevelsInSession();
            }
        }

        public interface ILevels
        {
            int GetNumberOfPlayedLevelsInSession();
        }

        public class UtilsMock : IUtils
        {
            public Func<string, bool> isCurrentVersionBiggerThanDelegate = (str) => false;

            public bool IsCurrentVersionBiggerThan(string str)
            {
                return isCurrentVersionBiggerThanDelegate(str);
            }
        }

        public interface IUtils
        {
            bool IsCurrentVersionBiggerThan(string str);
        }

        public class PromotionMock : IPromotion
        {
            public Func<string, int> promotionShowedCountTotal = (str) => 0;
            public Func<string, bool> isCountryTier = (str) => false;
            public Func<string, string, bool> hasPurchasedWithinPromotion = (str0, str1) => false;
            public Func<string, string, bool> hasNotPurchasedWithinPromotion = (str0, str1) => false;
            public Func<string, int> promotionShowedCountThisSession = (str) => 0;

            public int PromotionShowedCountTotal(string str)
            {
                return promotionShowedCountTotal(str);
            }

            public bool IsCountryTier(string str)
            {
                return isCountryTier(str);
            }

            public bool HasPurchasedWithinPromotion(string str0, string str1)
            {
                return hasPurchasedWithinPromotion(str0, str1);
            }

            public bool HasNotPurchasedWithinPromotion(string str0, string str1)
            {
                return hasNotPurchasedWithinPromotion(str0, str1);
            }

            public int PromotionShowedCountThisSession(string str)
            {
                return promotionShowedCountThisSession(str);
            }
        }

        public interface IPromotion
        {
            int PromotionShowedCountTotal(string str);
            bool IsCountryTier(string str);
            bool HasPurchasedWithinPromotion(string str0, string str1);

            bool HasNotPurchasedWithinPromotion(string str0, string str1);
            int PromotionShowedCountThisSession(string str);
        }

        public class LivesMock : ILives
        {
            public Func<bool> alreadyAppliedLifeLifterDelegate = () => false;
            public Func<bool> notAppliedLifeLifterDelegate = () => false;

            public bool alreadyAppliedLifeLifter()
            {
                return alreadyAppliedLifeLifterDelegate();
            }

            public bool notAppliedLifeLifter()
            {
                return notAppliedLifeLifterDelegate();
            }

            public void applyLifeLifter()
            {
            }
        }

        public interface ILives
        {
            bool alreadyAppliedLifeLifter();

            bool notAppliedLifeLifter();

            void applyLifeLifter();
        }

        public class UnPromoMock : IUnPromo
        {
            public Func<string, bool> wasActive = (str) => false;
            public Func<string, bool> wasActiveWithNameStartingFrom = (str) => false;

            public bool WasActive(string str)
            {
                return wasActive(str);
            }

            public bool WasActiveWithNameStartingFrom(string str)
            {
                return wasActiveWithNameStartingFrom(str);
            }
        }

        public interface IUnPromo
        {
            bool WasActive(string str);
            bool WasActiveWithNameStartingFrom(string str);
        }

        #endregion
    }
}