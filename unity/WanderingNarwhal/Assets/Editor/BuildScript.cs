using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using Amazon;
#if UNITY_CLOUD_BUILD || UNITY_JENKINS_BUILD
using System.Collections.Specialized;
using System.Net;
using System.Text;
#endif
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using BBB;
using BBB.AssetBundles;
using BBB.Core.AssetBundles;
using BBB.EditorConfigs;
using BebopBee;
using GameAssets.Scripts.Utils;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using Google.Apis.Util.Store;
using JetBrains.Annotations;
using Newtonsoft.Json;
using UnityEditor;
using UnityEditor.Compilation;
using UnityEngine;
using UnityEngine.AssetGraph;
using UnityEngine.Networking;

public class BuildScript
{
    //UCB enabled
    private const string GraphCollectionEnvVarName = "GRAPH_COLLECTION";
    private const string EnvironmentEnvVarName = "ENVIRONMENT";
    private const string BuildName = "BUILD_NAME";
    private const string SlackToken = "SLACK_TOKEN";
    private const string SlackChannel = "SLACK_CHANNEL";

    //Local only
    private const string ScriptingDefineSymbols = "SYMBOLS";
    private const string IncludeScenes = "INCLUDE_SCENES";
    private const string BuildOptionsEnv = "BUILD_OPTIONS";

    private static Environments _environment;
    private static readonly BuildTarget BuildTarget;

    private enum GraphCollections
    {
        FastBuild = 0,
        Build
    }
    
    static BuildScript()
    {
        BuildTarget = EditorUserBuildSettings.activeBuildTarget;
        SetEnvironment();
    }

    private static void SetEnvironment()
    {
        //Environment variables setup
        var environmentAsString = GetEnvironmentVariable(EnvironmentEnvVarName, Environments.Dev.ToString());
        if (!Enum.TryParse(environmentAsString, out _environment))
        {
            throw new ArgumentException("Invalid environment name");
        }
    }

    private static void SetDevEnvVariables()
    {
        Environment.SetEnvironmentVariable(BuildOptionsEnv, "CompressWithLz4HC");
        Environment.SetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;BBB_DEBUG;BBB_LOG;USE_NUNU_SDK;DEVELOPMENT_BUILD;UNODE_PRO;");
        Environment.SetEnvironmentVariable(IncludeScenes, "Main.unity");
        Environment.SetEnvironmentVariable(EnvironmentEnvVarName, "Dev");
        SetEnvironment();
        SetScriptingDefineSymbols();
    }

    private static void SetDevProfilerEnvVariables()
    {
        Environment.SetEnvironmentVariable(BuildOptionsEnv, "CompressWithLz4HC,Development,AllowDebugging,ConnectWithProfiler");
        Environment.SetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;UNODE_PRO;");
        Environment.SetEnvironmentVariable(IncludeScenes, "Main.unity");
        Environment.SetEnvironmentVariable(EnvironmentEnvVarName, "Dev");
        SetEnvironment();
        SetScriptingDefineSymbols();
    }

    private static void SetStagingEnvVariables()
    {
        Environment.SetEnvironmentVariable(BuildOptionsEnv, "CompressWithLz4HC");
        Environment.SetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;BBB_DEBUG;BBB_LOG;BBB_STAGING;USE_NUNU_SDK;DEVELOPMENT_BUILD;UNODE_PRO;");
        Environment.SetEnvironmentVariable(IncludeScenes, "(Main.unity|EnvSelection.unity)");
        Environment.SetEnvironmentVariable(EnvironmentEnvVarName, "Staging");
        SetEnvironment();
        SetScriptingDefineSymbols();
    }

#if BBB_MAKE_PROD
    private static void SetProdEnvVariables()
    {
        Environment.SetEnvironmentVariable(BuildOptionsEnv, "CompressWithLz4HC");
        Environment.SetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;BBB_PROD;UNODE_PRO;");
        Environment.SetEnvironmentVariable(IncludeScenes, "Main.unity");
        Environment.SetEnvironmentVariable(EnvironmentEnvVarName, "Prod");
        SetEnvironment();
        SetScriptingDefineSymbols();
    }

    private static void SetProdDebugEnvVariables()
    {
        Environment.SetEnvironmentVariable(BuildOptionsEnv, "CompressWithLz4HC");
        Environment.SetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;BBB_DEBUG;BBB_LOG;BBB_PROD;USE_NUNU_SDK;DEVELOPMENT_BUILD;UNODE_PRO;");
        Environment.SetEnvironmentVariable(IncludeScenes, "Main.unity");
        Environment.SetEnvironmentVariable(EnvironmentEnvVarName, "Prod");
        SetEnvironment();
        SetScriptingDefineSymbols();
    }
#endif

    private static void SetScriptingDefineSymbols()
    {
        //Scripting define symbols
        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildPipeline.GetBuildTargetGroup(BuildTarget),
            GetEnvironmentVariable(ScriptingDefineSymbols, "I2_TMPro;"));
        CompilationPipeline.RequestScriptCompilation();
    }

    [UnityEditor.Callbacks.DidReloadScripts]
    private static void OnScriptsReloaded()
    {
        var buildMethodName = EditorPrefs.GetString("BuildMethodName", null);
        if (buildMethodName.IsNullOrEmpty()) return;

        EditorPrefs.SetString("BuildMethodName", null);

        var methodInfo = typeof(BuildScript).GetMethod(buildMethodName, BindingFlags.NonPublic | BindingFlags.Static);
        if (methodInfo != null)
        {
            methodInfo.Invoke(null, null);
        }
    }

#if UNITY_ANDROID
    [MenuItem("BebopBee/Build/Android/Create Dev Build")]
    private static void AndroidDevBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildAndroid");
        SetDevEnvVariables();
    }

    [MenuItem("BebopBee/Build/Android/Create Dev Profiler Build")]
    private static void AndroidDevProfilerBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildAndroid");
        SetDevProfilerEnvVariables();
    }

    [MenuItem("BebopBee/Build/Android/Create Staging Build")]
    private static void AndroidStagingBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildAndroid");
        SetStagingEnvVariables();
    }

#if BBB_MAKE_PROD
    [MenuItem("BebopBee/Build/Android/Create Prod Build")]
    private static void AndroidProdBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildAndroid");
        SetProdEnvVariables();
    }

    [MenuItem("BebopBee/Build/Android/Create Prod-Debug Build")]
    private static void AndroidProdDebugBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildAndroid");
        SetProdDebugEnvVariables();
    }
#endif
#endif

#if UNITY_IOS
    [MenuItem("BebopBee/Build/iOS/Create Dev Build")]
    private static void IosDevBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildiOS");
        SetDevEnvVariables();
    }
    
    [MenuItem("BebopBee/Build/iOS/Create Dev Profiler Build")]
    private static void IosDevProfilerBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildiOS");
        SetDevProfilerEnvVariables();
    }

    [MenuItem("BebopBee/Build/iOS/Create Staging Build")]
    private static void IosStagingBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildiOS");
        SetStagingEnvVariables();
    }

#if BBB_MAKE_PROD
    [MenuItem("BebopBee/Build/iOS/Create Prod Build")]
    private static void IosProdBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildiOS");
        SetProdEnvVariables();
    }

    [MenuItem("BebopBee/Build/iOS/Create Prod-Debug Build")]
    private static void IosProdDebugBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildiOS");
        SetProdDebugEnvVariables();
    }
#endif
#endif

#if UNITY_STANDALONE
    [MenuItem("BebopBee/Build/PC/Create Dev Build")]
    private static void WindowsDevBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildWindows");
        SetDevEnvVariables();
    }
    
    [MenuItem("BebopBee/Build/PC/Create Staging Build")]
    private static void WindowsStagingBuild()
    {
        EditorPrefs.SetString("BuildMethodName", "BuildWindows");
        SetStagingEnvVariables();
    }
#endif

#if UNITY_CLOUD_BUILD
    public static void PreExport(UnityEngine.CloudBuild.BuildManifestObject manifest)
    {
        DebugSymbolsPostProcess.PreExport(manifest);
        PreExport();
    }
#endif

    public static void PreExport()
    {
        try
        {
            BuildAndUploadAssetBundles();
        }
        catch (Exception e)
        {
            LogError($"{e.Message}\n{e.StackTrace}");
#if UNITY_CLOUD_BUILD || UNITY_JENKINS_BUILD
            NotifySlack($"Asset bundles ERROR on Unity Cloud Build\n{e.Message}\n{e.StackTrace}");
#endif
            throw e;
        }

        GenerateAssetBundlesConfig();

        //Cache configs
        ConfigsMenu.CacheConfigsForBuild();

        //Splash Image
        PlayerSettings.SplashScreen.showUnityLogo = false;

        switch (BuildTarget)
        {
            case BuildTarget.Android:
                PreExportAndroid();
                break;
            case BuildTarget.iOS:
                PreExportIOS();
                break;
            case BuildTarget.StandaloneWindows:
            case BuildTarget.StandaloneWindows64:
                PreExportWindows();
                break;
        }

        AssetDatabase.Refresh();
    }

    private static void LogError(string message)
    {
        Debug.LogError($"[AUTOMATED BUILD][ERROR] {message}");
    }

    private static void Log(string message)
    {
        Debug.Log($"[AUTOMATED BUILD] {message}");
    }

    private static void BuildAndUploadAssetBundles()
    {
        BuildAssetBundles();
        UploadAssetBundles();
    }

    private static void BuildAssetBundles()
    {
#if UNITY_CLOUD_BUILD
        if (Caching.ClearCache()) 
        {
            Debug.Log("Successfully cleaned the cache");
        }
        else 
        {
            Debug.Log("Cache is being used");
        }
#endif

        //Build Asset Bundles
        var collectionNameAsString = GetEnvironmentVariable(GraphCollectionEnvVarName, GraphCollections.Build.ToString());
        if (!Enum.TryParse(collectionNameAsString, out GraphCollections graphCollection))
        {
            throw new ArgumentException("Invalid graph collection name");
        }

        Log("[ASSET GRAPH] Executing Graph Collection");
        var result = AssetGraphUtility.ExecuteGraphCollection(BuildTarget, graphCollection.ToString());
        Log("[ASSET GRAPH] Build results");
        foreach (var r in result)
        {
            if (r.Graph == null) continue;
            Log("[ASSET GRAPH] Graph " + r.Graph.name + " status: " + (r.IsAnyIssueFound ? "Failed" : "Success"));
            if (!r.IsAnyIssueFound)
                continue;
            
            foreach (var e in r.Issues)
            {
                LogError(r.Graph.name + ":" + e.Reason);
            }

            throw new Exception("Executing Graph Collection failed. See errors above");
        }
    }

    // Requires AWS credentials to be present in the Environment variables
    private static void UploadAssetBundles()
    {
        var bucketRegion = RegionEndpoint.USWest2;
        var bucketName = GameConstants.BucketNames[_environment];
        Log($"[ASSET BUNDLES] Uploading Asset Bundles to {bucketName}");

        var platform = BuildTargetIsMobile() ? BuildTarget.ToString() : "Windows";
        var baseDir = Path.Combine("AssetBundles", platform, "downloadable");
        IAmazonS3 s3Client = new AmazonS3Client("********************", "huhBOn0h/qFLG/j29KxJsgX5ZxWvxv+Iu9wtmyzd", bucketRegion);

        //Retrieve
        var filesOnBucket = GetS3BucketFileList(bucketName, s3Client);
        Log($"[ASSET BUNDLES] Got files from S3 bucket:\n{string.Join("\n", filesOnBucket)}");

        //Transfer
        var allBundles = Directory.GetFiles(baseDir, "*.*", SearchOption.AllDirectories);
        var fileTransferUtility = new TransferUtility(s3Client);

        foreach (var bundlePath in allBundles)
        {
            //Take into account Windows path separator
            var key = bundlePath.Replace("\\", "/").Replace("AssetBundles/", $"v{CachedBundleInfoProvider.DefaultBundleVersion}/");
            if (filesOnBucket.Contains(key))
            {
                //Log($"File {bundlePath} already exists in bucket {bucketName}");
                continue;
            }

            var fileTransferUtilityRequest = new TransferUtilityUploadRequest
            {
                BucketName = bucketName,
                FilePath = bundlePath,
                //StorageClass = S3StorageClass.StandardInfrequentAccess,
                Key = key,
                CannedACL = S3CannedACL.PublicRead
            };

            fileTransferUtilityRequest.UploadProgressEvent += OnFileUploaded;
            //Log($"Uploading {bundlePath}");
            fileTransferUtility.Upload(fileTransferUtilityRequest);
            Log($"[ASSET BUNDLES] Uploaded {bundlePath} --> {Path.Combine(bucketName, key)}");
            continue;

            void OnFileUploaded(object sender, UploadProgressArgs uploadProgressArgs)
            {
                Log($"[ASSET BUNDLES] Upload status {key} %:{uploadProgressArgs.PercentDone} t:{uploadProgressArgs.TotalBytes} tb:{uploadProgressArgs.TransferredBytes}");
            }
        }
#if UNITY_CLOUD_BUILD || UNITY_JENKINS_BUILD
        NotifyAssetBundleSizeInfo(allBundles);
#endif
    }
    
#if UNITY_CLOUD_BUILD || UNITY_JENKINS_BUILD
    private static void NotifyAssetBundleSizeInfo(string[] bundlesPaths)
    {
        var info = GetAssetBundlesSizeInfo(bundlesPaths);
        NotifySlack($"Please check the sizes for the built Asset Bundles below for the Build: {GetBuildName()} . \n \n{info}");
    }

    private static string GetAssetBundlesSizeInfo(string[] bundlesPaths)
    {
        var stringBuilder = new StringBuilder();

        AssetBundleReporterConfig config = null;
        var configFilePath = Path.Combine(Application.dataPath, "Editor", "asset_bundles_reporter_config.json");
        if (File.Exists(configFilePath))
        {
            var configContent = File.ReadAllText(configFilePath);
            config = JsonConvert.DeserializeObject<AssetBundleReporterConfig>(configContent);
        }

        var bundlesToReport = new List<(string BundleName, string Emoji, float SizeMb)>();

        foreach (var bundlePath in bundlesPaths)
        {
            if (!File.Exists(bundlePath) || !bundlePath.EndsWith(".unity3d"))
                continue;
            
            var fileInfo = new FileInfo(bundlePath);
            var fileSizeInMb = (float)fileInfo.Length / (1000 * 1000);
            
            if (config is { SizeThresholdInMb: > 0 } && fileSizeInMb <= config.SizeThresholdInMb)
                continue;
            
            var matchingConfig = config?.AssetBundleSizeLimitConfigs?.FirstOrDefault(b => bundlePath.Contains(b.Name));

            if (matchingConfig != null && fileSizeInMb <= matchingConfig.MaxSizeMb)
                continue;

            var emoji = fileSizeInMb switch
            {
                <= 2 => "🟩",    // 1–2 MB
                <= 4 => "🟨",    // 2–4 MB
                <= 5 => "🟧",    // 4–5 MB
                _ => "🟥"        // 5+ MB
            };

            bundlesToReport.Add((Path.GetFileName(bundlePath), emoji, fileSizeInMb));
        }

        var sortedBundles = bundlesToReport.OrderByDescending(b => b.SizeMb);

        foreach (var bundle in sortedBundles)
        {
            stringBuilder.AppendLine($"{bundle.BundleName} : {bundle.Emoji} {bundle.SizeMb:F1} MB");
        }

        return stringBuilder.ToString();
    }
#endif
    
    private static List<string> GetS3BucketFileList(string bucketName, IAmazonS3 s3Client)
    {
        var filesOnBucket = new List<string>();
        var request = new ListObjectsV2Request
        {
            BucketName = bucketName,
        };
        ListObjectsV2Response response;
        do
        {
            response = s3Client.ListObjectsV2(request);

            foreach (var entry in response.S3Objects)
            {
                filesOnBucket.Add(entry.Key);
            }

            request.ContinuationToken = response.NextContinuationToken;
        } while (response.IsTruncated);

        return filesOnBucket;
    }

    private static string GetEnvironmentVariable(string envVarName, string defaultValue = "")
    {
        var value = Environment.GetEnvironmentVariable(envVarName);
        if (string.IsNullOrEmpty(value))
        {
            value = defaultValue;
        }

        return value;
    }

    private static void PreExportAndroid()
    {
        //Scripting Backend
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);

        //Target architectures
        if (_environment is Environments.Prod or Environments.Staging)
        {
            PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
        }
        else
        {
            EditorUserBuildSettings.connectProfiler = true;
            EditorUserBuildSettings.buildWithDeepProfilingSupport = true;
            PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64;
        }

        EditorUserBuildSettings.androidCreateSymbols = AndroidCreateSymbols.Debugging;
    }

    private static void PreExportIOS()
    {
        if (_environment == Environments.Prod)
            return;
        
        EditorUserBuildSettings.connectProfiler = true;
        EditorUserBuildSettings.buildWithDeepProfilingSupport = true;
    }

    private static void PreExportWindows()
    {
    }

    [UsedImplicitly]
    private static void BuildAndroid()
    {
        PreExport();

        var shouldBuildAab = AppDefinesConverter.Prod;
        var ext = shouldBuildAab ? ".aab" : ".apk";
        EditorUserBuildSettings.buildAppBundle = shouldBuildAab;

        PlayerSettings.Android.keystorePass = "17D3c3mb3r1989";
        PlayerSettings.Android.keyaliasPass = "17D3c3mb3r1989";

        var buildName = $"{GetBuildName()}{ext}";

        var buildPlayerOptions = new BuildPlayerOptions
        {
            target = BuildTarget,
            options = GetBuildOptions(),
            scenes = GetScenes(),
            locationPathName = $"./Builds/Android/{buildName}"
        };
        BuildPipeline.BuildPlayer(buildPlayerOptions);
    }
    
    private static string GetBuildName()
    {
        var buildName = GetEnvironmentVariable(BuildName);
        if (!string.IsNullOrEmpty(buildName)) return buildName;

#if UNITY_ANDROID
        return $"{Regex.Replace(PlayerSettings.productName, @"[^a-zA-Z0-9]+", "_")}_{Application.version}_{PlayerSettings.Android.bundleVersionCode}_{_environment}_{CurrentBundleVersion.GetCommitHash()}";
#elif UNITY_IOS
        return $"{Regex.Replace(PlayerSettings.productName, @"[^a-zA-Z0-9]+", "_")}_{Application.version}_{PlayerSettings.iOS.buildNumber}_{_environment}_{CurrentBundleVersion.GetCommitHash()}";
#else
        return string.Empty;
#endif
    }

    [UsedImplicitly]
    private static void BuildiOS()
    {
        PreExport();
        var buildName = GetBuildName();

        var buildPlayerOptions = new BuildPlayerOptions
        {
            target = BuildTarget,
            options = GetBuildOptions(),
            scenes = GetScenes(),
            locationPathName = $"./Builds/iOS/{buildName}"
        };
        BuildPipeline.BuildPlayer(buildPlayerOptions);
    }

    [UsedImplicitly]
    private static void BuildWindows()
    {
        PreExport();

        //No commit hash on Windows build for now
        var buildName = $"RL-v{Application.version}({PlayerSettings.Android.bundleVersionCode})-{_environment}";
        var buildPlayerOptions = new BuildPlayerOptions
        {
            target = BuildTarget,
            options = GetBuildOptions(),
            scenes = GetScenes(),
            locationPathName = $"./Builds/Windows/{buildName}/Puzzles_Passports.exe"
        };
        BuildPipeline.BuildPlayer(buildPlayerOptions);
    }

    private static string[] GetScenes()
    {
        var includeScene = GetEnvironmentVariable(IncludeScenes);
        return EditorBuildSettings.scenes.Where(s => s.enabled && Regex.IsMatch(s.path, includeScene)).Select(s => s.path).ToArray();
    }

    private static BuildOptions GetBuildOptions()
    {
        var buildOptions = GetEnvironmentVariable(BuildOptionsEnv, "None");
        Log($"[BUILD OPTIONS] {buildOptions}");
        var opt = buildOptions.Split(',');
        var options = (BuildOptions)Enum.Parse(typeof(BuildOptions), opt[0]);
        for (var i = 1; i < opt.Length; i++)
        {
            options |= (BuildOptions)Enum.Parse(typeof(BuildOptions), opt[i]);
        }

        Log($"[BUILD OPTIONS] {options}");
        return options;
    }

#if UNITY_CLOUD_BUILD || UNITY_JENKINS_BUILD
    private static void NotifySlack(string message)
    {
        var data = new NameValueCollection
        {
            ["token"] = GetEnvironmentVariable(SlackToken, "******************************************************"),
            ["channel"] = GetEnvironmentVariable(SlackChannel, "builds"),
            ["text"] = message
        };

        var client = new WebClient();
        var response = client.UploadValues("https://slack.com/api/chat.postMessage", "POST", data);
        var responseInString = Encoding.UTF8.GetString(response);
        Console.WriteLine(responseInString);
    }
#endif

    private static void GenerateAssetBundlesConfig()
    {
        var prevValue = AssetBundlesMenuItems.ShowDisplayDialog;
        AssetBundlesMenuItems.ShowDisplayDialog = false;
        GenerateBundleInfoConfig();
        GenerateBundleIndexConfig();
        GenerateSpriteAtlasIndexConfig();
        AssetBundlesMenuItems.ShowDisplayDialog = prevValue;
    }

    private static void GenerateBundleInfoConfig()
    {
        Log("[BUNDLE INFO] Generating Bundle Info Config...");
        switch (BuildTarget)
        {
            case BuildTarget.Android:
                AssetBundlesMenuItems.GenerateAndroidInfo();
                break;
            case BuildTarget.iOS:
                AssetBundlesMenuItems.GenerateiOsInfo();
                break;
            case BuildTarget.StandaloneWindows:
            case BuildTarget.StandaloneWindows64:
                AssetBundlesMenuItems.GeneratePcInfo();
                break;
            default:
                Log($"[BUNDLE INFO] Did NOT generate Asset Bundle Info Config for {BuildTarget} build target");
                return;
        }

        // Define request parameters.
        var spreadsheetId = GameConstants.BundleInfoConfigSpreadsheetId;
        var platform = BuildTargetIsMobile() ? BuildTarget.ToString() : "PC";
        var sheetName = $"BundleInfoConfig_{platform} ({_environment.ToString().ToUpper()} - {Application.version})";

        PublishConfig(spreadsheetId, sheetName, AssetBundlesMenuItems.SerializedInfoConfig);
    }

    private static void GenerateBundleIndexConfig()
    {
        Log("Generating Bundle Index Config...");
        switch (BuildTarget)
        {
            case BuildTarget.Android:
                AssetBundlesMenuItems.GenerateAndroidIndex();
                break;
            case BuildTarget.iOS:
                AssetBundlesMenuItems.GenerateiOsIndex();
                break;
            case BuildTarget.StandaloneWindows:
            case BuildTarget.StandaloneWindows64:
                AssetBundlesMenuItems.GeneratePcIndex();
                break;
            default:
                Log($"Did NOT generate Asset Bundle Index Config for {BuildTarget} build target");
                return;
        }

        // Define request parameters.
        var spreadsheetId = GameConstants.BundleIndexConfigSpreadsheetId;
        var platform = BuildTargetIsMobile() ? BuildTarget.ToString() : "PC";
        var sheetName = $"BundleIndexConfig_{platform} ({_environment.ToString().ToUpper()} - {Application.version})";

        PublishConfig(spreadsheetId, sheetName, AssetBundlesMenuItems.SerializedIndexConfig);
    }

    private static bool BuildTargetIsMobile()
    {
        return BuildTarget == BuildTarget.Android || BuildTarget == BuildTarget.iOS;
    }

    private static void GenerateSpriteAtlasIndexConfig()
    {
        Log("Generating Atlas Index Config...");

        AssetBundlesMenuItems.GenerateAtlasIndexConfig();

        // Define request parameters.
        var spreadsheetId = GameConstants.SpriteAtlasIndexConfigSpreadsheetId;
        var sheetName = $"SpriteAtlasIndexConfig ({_environment.ToString().ToUpper()} - {Application.version})";

        PublishConfig(spreadsheetId, sheetName, AssetBundlesMenuItems.SerializedAtlasConfig, false);
    }

    private static void PublishConfig(string spreadsheetId, string sheetName, string data, bool updatesPlatform = true)
    {
#if BBB_PROD && BBB_DEBUG
        // Do not push config for Prod-Debug builds
        return;
#endif

        var service = SetupSheetsService();
        PasteConfigInSheet(service, spreadsheetId, sheetName, data);

        //Update version
        var updateRequest = service.Spreadsheets.Values.Update(
            new ValueRange { Values = new List<IList<object>> { new List<object> { Application.version } } }, spreadsheetId,
            $"{sheetName}!D1");
        updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.RAW;
        var response = updateRequest.Execute();
        Log($"[GDocs Upload] PublishConfig: {spreadsheetId} {sheetName} response:{JsonConvert.SerializeObject(response)}");

        //Update platform
        if (updatesPlatform)
        {
            var platform = BuildTarget.ToString();
            if (!BuildTargetIsMobile())
            {
                platform = "Editor,Unknown";
            }

            updateRequest = service.Spreadsheets.Values.Update(
                new ValueRange { Values = new List<IList<object>> { new List<object> { platform } } }, spreadsheetId,
                $"{sheetName}!F1");
            updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.RAW;
            response = updateRequest.Execute();
            Log($"[GDocs Upload] PublishConfig Platforms: {spreadsheetId} {sheetName} response:{JsonConvert.SerializeObject(response)}");
        }

        //Upload config to server
        var host = GameConstants.ConfigHosts[_environment];

        var loginUrl = $"{host}/login";
        var loginCookie = LoginToServer(loginUrl);

        var uploadUrl = $"{host}/config/uploadconfig";

        SendConfigToServer(service, spreadsheetId, sheetName, uploadUrl, loginCookie);
    }

    private static SheetsService SetupSheetsService()
    {
        string[] scopes = { SheetsService.Scope.Spreadsheets };
        var applicationName = "BebopBee GS API";

        UserCredential credential;

        using (var stream =
               new FileStream("credentials.json", FileMode.Open, FileAccess.Read))
        {
            // The file token.json stores the user's access and refresh tokens, and is created
            // automatically when the authorization flow completes for the first time.
            var credPath = "token.json";
            credential = GoogleWebAuthorizationBroker.AuthorizeAsync(
                GoogleClientSecrets.Load(stream).Secrets,
                scopes,
                "user",
                CancellationToken.None,
                new FileDataStore(credPath, true)).Result;
            //Log("Credential file saved to: " + credPath);
        }

        // Create Google Sheets API service.
        var service = new SheetsService(new BaseClientService.Initializer()
        {
            HttpClientInitializer = credential,
            ApplicationName = applicationName,
        });
        return service;
    }

    private static void SendConfigToServer(SheetsService service, string spreadsheetId, string sheetName,
        string uploadUrl, string loginCookie)
    {
        var request =
            service.Spreadsheets.Values.Get(spreadsheetId, $"{sheetName}!A:H");

        var response = request.Execute();
        var values = response.Values;

        var configName = (string)values[0][1];
        if (string.IsNullOrEmpty(configName))
        {
            throw new Exception($"Config name is missing for spreadsheet {spreadsheetId}, sheet name {sheetName}");
        }

        var versions = ((string)values[0][3]).Split(',');
        var platforms = Array.Empty<string>();
        if (values[0].Count >= 6)
        {
            var p = (string)values[0][5];
            if (!p.IsNullOrEmpty())
            { 
                platforms = p.Split(',');
            }
        }

        var modifier = string.Empty;
        if (values[0].Count >= 8)
        {
            modifier = (string)values[0][7];
        }

        const string userEmail = "automated build";

        var formData = new List<IMultipartFormSection>
        {
            new MultipartFormDataSection("user_email", userEmail)
        };

        foreach (var version in versions)
        {
            if (string.IsNullOrEmpty(version))
            {
                var stateStr = version == null ? "null" : "empty";
                throw new Exception($"BuildScript Exception: platform is {stateStr} for spreadsheetId={spreadsheetId} sheetName={sheetName}");
            }

            formData.Add(new MultipartFormDataSection("version", version));
        }

        foreach (var platform in platforms)
        {
            if (string.IsNullOrEmpty(platform))
            {
                var stateStr = platform == null ? "null" : "empty";
                throw new Exception($"BuildScript Exception: platform is {stateStr} for spreadsheetId={spreadsheetId} sheetName={sheetName}");
            }

            formData.Add(new MultipartFormDataSection("platform", platform));
        }

        var config = GetJson(values);
        Log($"[CONFIG] {config}");
        if (string.IsNullOrEmpty(config))
        {
            var stateStr = config == null ? "null" : "empty";
            throw new Exception($"BuildScript Exception: platform is {stateStr} for spreadsheetId={spreadsheetId} sheetName={sheetName}");
        }

        formData.Add(new MultipartFormDataSection("config", config));

        if (values[0].Count >= 9) formData.Add(new MultipartFormDataSection("jsonify", "TRUE"));

        if (!modifier.Equals("@flatbufferonly"))
        {
            var newFormData = new List<IMultipartFormSection>(formData)
            {
                new MultipartFormDataSection("config_name", configName)
            };

            // Can't use UnityWebRequest.Post since it adds 2 bytes before form-data boundary, which leads to Tornadoweb not being able to parse it
            // UnityWebRequest www = UnityWebRequest.Post(uploadUrl, newFormData);
            using var www = RequestUtils.GenerateHttpPost(uploadUrl, "POST", newFormData);
            www.SetRequestHeader("Cookie", loginCookie);
            var e = CallURL(www);
            while (e.MoveNext()) ;

            if (www.result == UnityWebRequest.Result.ConnectionError || www.result == UnityWebRequest.Result.ProtocolError)
            {
                LogError(
                    $"[CONFIG] Could update config on Config server {uploadUrl}\n{www.error}\n{www.downloadHandler.text}");
                throw new Exception("Failed to update config on config Server");
            }

            Log($"[CONFIG] Server responded: {www.downloadHandler.text}");
        }

        if (modifier.Equals("@flatbuffer") || modifier.Equals("@flatbufferonly"))
        {
            var fbFormData = new List<IMultipartFormSection>(formData)
            {
                new MultipartFormDataSection("config_name", $"FB{configName}")
            };

            // Can't use UnityWebRequest.Post since it adds 2 bytes before form-data boundary, which leads to Tornadoweb not being able to parse it
            // UnityWebRequest www = UnityWebRequest.Post(uploadUrl, fbFormData);
            using var www = RequestUtils.GenerateHttpPost(uploadUrl, "POST", fbFormData);
            www.SetRequestHeader("Cookie", loginCookie);
            var e = CallURL(www);
            while (e.MoveNext()) ;

            if (www.result == UnityWebRequest.Result.ConnectionError || www.result == UnityWebRequest.Result.ProtocolError)
            {
                LogError(
                    $"[CONFIG] Could update config on Config server {uploadUrl}\n{www.error}\n{www.downloadHandler.text}");
                throw new Exception("Failed to update config on config Server");
            }

            Log($"[CONFIG] Server responded: {www.downloadHandler.text}");
        }
    }

    private static string LoginToServer(string loginUrl)
    {
        var formData = new List<IMultipartFormSection>
        {
            new MultipartFormDataSection("email", "<EMAIL>"),
            new MultipartFormDataSection("password", "h4yCar4amba")
        };

        // UnityWebRequest www = UnityWebRequest.Post(loginUrl, formData);
        using var www = RequestUtils.GenerateHttpPost(loginUrl, "POST", formData);
        www.redirectLimit = 0;
        var e = CallURL(www);
        while (e.MoveNext()) ;

        var loginCookie = www.GetResponseHeader("Set-Cookie");
        if (String.IsNullOrEmpty(loginCookie))
        {
            LogError(
                $"Could get login cookie from Config server {loginUrl}\n{www.error}\n{www.downloadHandler.text}");
            throw new Exception("Could get login cookie from Config server");
        }

        Log($"[CONFIG] Got Login Cookie from the server");
        return loginCookie;
    }

    private static void PasteConfigInSheet(SheetsService service, string spreadsheetId, string sheetName, string data)
    {
        var sheet = GetSheet(service, spreadsheetId, sheetName);
        var requests = new List<Request>();

        if (sheet == null)
        {
            Log($"[CONFIG] Sheet {sheetName} does not exist in {spreadsheetId}. Creating...");
            sheet = CreateNewSheet(service, spreadsheetId, sheetName);
        }
        else
        {
            Log($"[CONFIG] Sheet {sheetName} already exists in {spreadsheetId}! Cleaning up...");

            var cleanSheet = new Request();
            var cleanSheetReq = new UpdateCellsRequest
            {
                Range = new GridRange { StartRowIndex = 3, SheetId = sheet.Properties.SheetId },
                Fields = "userEnteredValue"
            };
            cleanSheet.UpdateCells = cleanSheetReq;

            requests.Add(cleanSheet);
        }

        var pasteData = new Request();
        var pasteDataReq = new PasteDataRequest
        {
            Coordinate = new GridCoordinate
                { ColumnIndex = 0, RowIndex = 3, SheetId = sheet.Properties.SheetId },
            Data = data,
            Type = "PASTE_NORMAL",
            Delimiter = "\t"
        };
        pasteData.PasteData = pasteDataReq;
        requests.Add(pasteData);
        Log($"[GDocs Upload] Paste len:{data.Length} data:{data}");
        var body = new BatchUpdateSpreadsheetRequest { Requests = requests };
        var batchUpdateRequest = service.Spreadsheets.BatchUpdate(body, spreadsheetId);
        var response = batchUpdateRequest.Execute();
        Log($"[GDocs Upload] PasteConfigInSheet: {spreadsheetId} {sheetName} response:{JsonConvert.SerializeObject(response)}");
    }

    private static Sheet CreateNewSheet(SheetsService service, string spreadsheetId, string sheetName)
    {
        var sheet = GetSheet(service, spreadsheetId, "Template");
        var copyToReq = new CopySheetToAnotherSpreadsheetRequest
        {
            DestinationSpreadsheetId = spreadsheetId
        };
        var copyToRequest = service.Spreadsheets.Sheets.CopyTo(copyToReq, spreadsheetId, (int)sheet.Properties.SheetId);
        var prop = copyToRequest.Execute();

        var sheetProp = new Request();
        var sheetPropReq = new UpdateSheetPropertiesRequest
        {
            Fields = "Title",
            Properties = new SheetProperties { SheetId = prop.SheetId, Title = sheetName }
        };
        sheetProp.UpdateSheetProperties = sheetPropReq;
        var addSheetBody = new BatchUpdateSpreadsheetRequest { Requests = new List<Request> { sheetProp } };
        var addSheetUpdateRequest = service.Spreadsheets.BatchUpdate(addSheetBody, spreadsheetId);
        addSheetUpdateRequest.Execute();

        return GetSheet(service, spreadsheetId, sheetName);
    }

    private static IEnumerator CallURL(UnityWebRequest www)
    {
        yield return www.SendWebRequest();
        while (!www.isDone)
        {
            yield return true;
        }
    }

    private static string GetJson(IList<IList<object>> values)
    {
        var types = values[1];
        var headers = values[2];

        var records = new List<string>();

        for (var i = 3; i < values.Count; i++)
        {
            var newRecord = new List<string>();
            if (!String.IsNullOrEmpty(values[i][0].ToString()) && !values[i][0].Equals("#"))
            {
                string uid = null;
                for (var j = 0; j < values[i].Count; j++)
                {
                    var value = values[i][j].ToString();
                    if (string.IsNullOrEmpty(value))
                    {
                        continue;
                    }

                    string castedValue = null;
                    switch (types[j].ToString())
                    {
                        case "String":
                            castedValue = $"\"{value}\"";
                            break;
                        case "Number":
                        case "Object":
                        case "List":
                        case "Dict":
                            castedValue = value;
                            break;
                        case "Bool":
                            castedValue = Boolean.Parse(value).ToString();
                            break;
                    }

                    if (castedValue != null)
                    {
                        if (headers[j].Equals("Uid")) uid = castedValue;
                        newRecord.Add($"\"{headers[j]}\": {castedValue}");
                    }
                }

                if (uid != null)
                {
                    records.Add($"{uid}: {{{string.Join(",", newRecord)}}}");
                }
            }
        }

        return $"{{{string.Join(",", records)}}}";
    }

    private static Sheet GetSheet(SheetsService service, string spreadsheetId, string sheetName)
    {
        var req = service.Spreadsheets.Get(spreadsheetId);
        var spreadsheet = req.Execute();
        var sheet = spreadsheet.Sheets.FirstOrDefault(s => s.Properties.Title.Equals(sheetName));
        return sheet;
    }
}