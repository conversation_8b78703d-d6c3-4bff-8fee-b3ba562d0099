using UnityEngine;

namespace Bebopbee.Core.Utility
{
    public static class SliderUtility
    {
        public static float ToPercent(float sliderValue)
        {
            return ToNewRangeValue(sliderValue, 0, 100);
        }

        public static float ToNewRangeValue(float currentRangeValue, float newRangeMin, float newRangeMax)
        {
            return ToNewRangeValue(currentRangeValue, 0, 1, newRangeMin, newRangeMax);
        }

        public static float ToNewRangeValue(float currentRangeValue, float currentRangeMin, float currentRangeMax, float newRangeMin, float newRangeMax)
        {
            return MathUtility.ConvertRange(currentRangeValue, currentRangeMin, currentRangeMax, newRangeMin, newRangeMax);
        }

        //@TODO: Move to math utility. Generalize approach.
        //@TODO: Cover by tests.
        //http://answers.unity3d.com/questions/283192/how-to-convert-decibel-number-to-audio-source-volu.html
        //http://www.playdotsound.com/portfolio-item/decibel-db-to-float-value-calculator-making-sense-of-linear-values-in-audio-tools/
        public static float LinearToDecibel(float sliderValue, float decibelMinValue, float decibelMaxValue)
        {
            if (sliderValue < 0.01f)
                return decibelMinValue;
           
            var multiplier = (decibelMaxValue - decibelMinValue) / 4f;
            return Mathf.Log10(sliderValue) * multiplier;
        }
    }
}