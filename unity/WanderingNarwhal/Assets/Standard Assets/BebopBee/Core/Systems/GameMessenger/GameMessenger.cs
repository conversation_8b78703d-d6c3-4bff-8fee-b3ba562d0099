using System;
using System.Collections.Generic;
using Bebopbee.Core.Components;
using Bebopbee.Core.Systems.Gamemessenger;
using Bebopbee.Core.Systems.GamemessengerBase;
using Bebopbee.Core.Systems.Ticksystem;
using UnityEngine;

namespace BBB.Core.Systems.GameMessenger
{
    public class GameMessenger : IGameMessenger, ITickable
    {
        private readonly Dictionary<Type, LazyList<WeakReference>> _listeners;
        private readonly Queue<IGameMessage> _messages;

        public GameMessenger()
        {
            _listeners = new Dictionary<Type, LazyList<WeakReference>>();
            _messages = new Queue<IGameMessage>();
        }

        /// <summary>
        /// Subscribe to receiving messages of given type
        /// </summary>
        /// <typeparam name="T"></typeparam> type of message that will be delivered to message listener
        /// <param name="listener"></param> message listener that will receive messages
        /// <returns></returns>
        public IDisposable Subscribe<T>(IGameMessageListener listener) where T : IGameMessage
        {
            return Subscribe(listener, typeof(T));
        }

        /// <summary>
        /// Subscribe to receiving messages of given type
        /// </summary>
        /// <param name="listener"></param> message listener that will receive messages
        /// <param name="messageType"></param> type of message that will be delivered to message listener
        /// <returns></returns>
        public IDisposable Subscribe(IGameMessageListener listener, Type messageType)
        {
            if (!_listeners.ContainsKey(messageType))
            {
                _listeners.Add(messageType, new LazyList<WeakReference>());
            }

            var result = _listeners[messageType].Get()
                .Find(reference => reference.IsAlive && ((IGameMessageListener) reference.Target).Equals(listener));
            if (result == null)
            {
                result = new WeakReference(listener);
                _listeners[messageType].Add(result);
            }

            return new DisposableActionSafe(() =>
            {
                if (listener != null && _listeners != null && _listeners.ContainsKey(messageType))
                {
                    _listeners[messageType].Remove(result);
                }
            });
        }

        /// <summary>
        /// Message is delivered to all subscribers immediately. Use it only when immediate delivery is needed.
        /// </summary>
        /// <param name="message"></param>
        public void TriggerMessage(IGameMessage message)
        {
            Process(message);
        }

        /// <summary>
        /// Message is added to message queue and be delivered to subscribers on next messenger "process" tick. 
        /// Use it in all cases when immediate delivery is not needed.
        /// </summary>
        /// <param name="message"></param>
        public void QueueMessage(IGameMessage message)
        {
            _messages.Enqueue(message);
        }

        /// <summary>
        /// DO NOT INVOKE THIS MANUALLY. Starts "process" tick.
        /// </summary>
        public void Tick()
        {
            while (_messages.Count > 0)
            {
                Process(_messages.Dequeue());
            }
        }

        private void Process(IGameMessage message)
        {
            if (!message.IsValid())
            {
                return;
            }

            var messageType = message.GetType();
            if (!_listeners.ContainsKey(messageType))
            {
                return;
            }

            try
            {
                var listeners = _listeners[messageType].Get();
                for (int i = 0; i < listeners.Count; i++)
                {
                    var result = listeners[i];
                    if (result.IsAlive)
                    {
                        ((IGameMessageListener) result.Target).OnMessage(message);
                    }
                    else
                    {
                        _listeners[messageType].Remove(result);
                    }
                }
            }
            catch (Exception e)
            {
                // External exception should not stop the messanging system (i.e. if logging failed)
                Debug.LogException(e);
            }
        }
    }
}