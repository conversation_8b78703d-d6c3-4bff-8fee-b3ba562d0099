using Bebopbee.Core.Utility;
using UnityEngine;

namespace Bebopbee.Core.Extensions
{
    public static class FloatExtensions
    {
        public static bool IsEqual(this float compared, float compareTo)
        {
            return FloatUtility.IsEqual(compared, compareTo);
        }

        public static bool IsInteger(this float value)
        {
            return Mathf.Round(value).Equals(value);
        }

        public static int GetDimension(this float value)
        {
            return Mathf.RoundToInt(Mathf.Log10(value));
        }

        public static float GetLerpParameter(this float current, float min, float max)
        {
            return (current - min) / (max - min);
        }

       
    }

    public static class LongExtensions
    {
        public static float GetLerpParameter(this long current, long min, long max)
        {
            return ((float)(current - min)) / (max - min);
        }
    }



}