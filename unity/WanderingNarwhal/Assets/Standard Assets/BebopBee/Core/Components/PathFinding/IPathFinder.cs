using System;

namespace BebopBee.Core.PathFinding
{
    public interface IPathFinder<TVertex> where TVertex : class, IVertex
    {
        void SetGraph(IGraph graph);

        Path<TVertex> FindPathToDestination(TVertex origin, TVertex destination, bool forced,
            Func<TVertex, TVertex, PassibilityType> checkPassibiliy = null);

        Path<TVertex> FindPathWithCondition(TVertex origin, Func<TVertex, bool> isTarget, bool forced,
            Func<TVertex, TVertex, PassibilityType> checkPassibiliy = null);
    }
}