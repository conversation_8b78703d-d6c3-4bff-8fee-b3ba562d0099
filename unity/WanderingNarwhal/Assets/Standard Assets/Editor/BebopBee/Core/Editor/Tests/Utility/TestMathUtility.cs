using Bebopbee.Core.Utility;
using NUnit.Framework;

namespace Bee<PERSON><PERSON>bee.Core.Tests
{
    public class TestMathUtility
    {
        #region ConvertRange
        [Test]
        [TestCase(0, 0, 1, 0, 100, 0)]
        [TestCase(1, 0, 1, 0, 100, 100)]
        [TestCase(0.5f, 0, 1, 0, 100, 50)]
        [TestCase(0.75f, 0, 1, 0, 100, 75)]
        [TestCase(0.95f, 0, 1, 0, 100, 95)]
        [TestCase(0.999f, 0, 1, 0, 100, 99.9f)]
        public void ConvertRange_Range_0_100(float sliderValue, float minValue, float maxValue, float outputRangeMin, float outputRangeMax, float resultValue)
        {
            ConvertRange(sliderValue, minValue, maxValue, outputRangeMin, outputRangeMax, resultValue);
        }

        [Test]
        [TestCase(0, 0, 1, -80, 0, -80)]
        [TestCase(1, 0, 1, -80, 0, 0)]
        [TestCase(0.5f, 0, 1, -80, 0, -40)]
        [TestCase(0.75f, 0, 1, -80, 0, -20)]
        [TestCase(0.95f, 0, 1, -80, 0, -4)]
        [TestCase(0.99f, 0, 1, -80, 0, -0.8f)]
        [TestCase(0.995f, 0, 1, -80, 0, -0.4f)]
        [TestCase(0.9995f, 0, 1, -80, 0, -0.04f)]
        [TestCase(0.99995f, 0, 1, -80, 0, -0.004f)]
        [TestCase(0.999f, 0, 1, -80, 0, -0.07999897f)]
        public void ConvertRange_Range_Minus80_0(float sliderValue, float minValue, float maxValue, float outputRangeMin, float outputRangeMax, float resultValue)
        {
            ConvertRange(sliderValue, minValue, maxValue, outputRangeMin, outputRangeMax, resultValue);
        }

        [Test]
        [TestCase(0, 0, 1, 0, 80, 0)]
        [TestCase(1, 0, 1, 0, 80, 80)]
        [TestCase(0.5f, 0, 1, 0, 80, 40)]
        [TestCase(0.75f, 0, 1, 0, 80, 60)]
        [TestCase(0.95f, 0, 1, 0, 80, 76)]
        [TestCase(0.99f, 0, 1, 0, 80, 79.2f)]
        [TestCase(0.995f, 0, 1, 0, 80, 79.6f)]
        [TestCase(0.9995f, 0, 1, 0, 80, 79.96f)]
        [TestCase(0.99995f, 0, 1, 0, 80, 79.996f)]
        [TestCase(0.9799103f, 0, 1, 0, 80, 78.392824f)]
        public void ConvertRange_Range_0_80(float sliderValue, float minValue, float maxValue, float outputRangeMin, float outputRangeMax, float resultValue)
        {
            ConvertRange(sliderValue, minValue, maxValue, outputRangeMin, outputRangeMax, resultValue);
        }

        [Test]
        [TestCase(0, 0, 1, -80, 80, -80)]
        [TestCase(1, 0, 1, -80, 80, 80)]
        [TestCase(0.25f, 0, 1, -80, 80, -40)]
        [TestCase(0.5f, 0, 1, -80, 80, 0)]
        [TestCase(0.75f, 0, 1, -80, 80, 40)]
        public void ConvertRange_Range_Minus80_80(float sliderValue, float minValue, float maxValue, float outputRangeMin, float outputRangeMax, float resultValue)
        {
            ConvertRange(sliderValue, minValue, maxValue, outputRangeMin, outputRangeMax, resultValue);
        }

        private static void ConvertRange(float value, float minRangeValue, float maxRangeValue, float outputRangeMin, float outputRangeMax, float resultValue)
        {
            var result = MathUtility.ConvertRange(value, minRangeValue, maxRangeValue, outputRangeMin, outputRangeMax);
            Assert.IsTrue(FloatUtility.IsEqual(result, resultValue), "SliderValue : [" + value + "], Result : [" + result + "]");
        }
        #endregion
    }
}