using System;

namespace BBB
{
    [Flags]
    public enum LogCat : long
    {
        All = 0xFFFFFFFFFFFFFFF,
        General = 1 << 1,
        City = 1 << 2,
        WorldCity = 1 << 3,
        LogWatch = 1 << 4,
        Poi = 1 << 5,
        Config = 1 << 6,
        Store = 1 << 7,
        FileLogger = 1 << 8,
        Social = 1 << 9,
        Rpc = 1 << 10,
        Tutorial = 1 << 11,
        Gacha = 1 << 12,
        Iap = 1 << 13,
        Profile = 1 << 14,
        Quest = 1 << 15,
        AsyncCommand = 1 << 16,
        Login = 1 << 17,
        Match3 = 1 << 18,
        Ui = 1 << 19,
        AssetBundle = 1 << 20,
        Notification = 1 << 21,
        Player = 1 << 22,
        Wallet = 1 << 23,
        CoreViews = 1 << 24,
        Resources = 1 << 25,
        Narrative = 1 << 26,
        Analytics = 1 << 27,
        Input = 1 << 28,
        Spine = 1 << 29,
        ScriptActions = 1 << 30,
        Lua = 1L << 31,
        Globe = 1L << 32,
        Promotions = 1L << 33,
        SimpleAction= 1L << 34,
        DailyEvent= 1L << 35,
        Audio =  1L << 36,
        CrashService =  1L << 37,
        Hud = 1L << 38,
        Modals = 1L << 39,
        HelpDesk = 1L << 40,
        Ads = 1L << 41,
        DeepLink = 1L << 42,
        Events = 1L << 43,
        Vibration = 1L << 44,
        Reward = 1L << 45,
        Scheduling = 1L << 46,
        Leaderboards = 1L << 47,
        Leanplum = 1L << 48,
        Consent = 1L << 49,
        Flow = 1L << 50,
        Collection = 1L << 51,
        Graph = 1L << 52,
    }
}