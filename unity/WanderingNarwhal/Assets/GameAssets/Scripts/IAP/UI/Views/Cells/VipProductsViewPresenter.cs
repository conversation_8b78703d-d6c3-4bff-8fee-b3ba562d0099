using BBB.Screens;
using VipProducts;

namespace BBB.UI
{
    public class VipProductsViewPresenter : ModalsViewPresenter, IVipProductsViewPresenter
    {
        protected override void OnShow()
        {
            base.OnShow();
            VipProductsClient.Instance.UI.gameObject.SetActive(true);
        }

        protected override void OnCloseButton()
        {
            base.OnCloseButton();
            VipProductsClient.Instance.UI.ProductPage.CloseProductPage();
        }

        protected override void OnHide()
        {
            base.OnHide();
            VipProductsClient.Instance.UI.gameObject.SetActive(false);
        }
    }
}