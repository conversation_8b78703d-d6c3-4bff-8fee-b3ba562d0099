using System;
using BBB.DailyTrivia;
using BBB.DI;
using BBB.UI.Core;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class IAPDailyTriviaView : ContextedUiBehaviour, IClosableStoreAction
    {
        [SerializeField] private LocalizedTextPro _streakDays;
        [SerializeField] private Animator _animator;
        [SerializeField] private Animator _checkmarkAnimator;

        [SerializeField] private Button _goButton;
        [SerializeField] private GameObject _answeredHolder;
        [SerializeField] private GameObject _notAnsweredHolder;
        [SerializeField] private GameObject[] _stars;
        [SerializeField] private NotifierWidget _notifierWidget;

        private Action _closeStoreAction;
        private DailyTriviaManager _dailyTriviaManager;

        private static bool _shouldCheckForAnsweredState = false;
        
        private void Start()
        {
            if (!Initialized)
            {
                LazyInit();
                Refresh();
            }
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            if (Initialized)
                Refresh();
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _dailyTriviaManager = context.Resolve<DailyTriviaManager>();
            _goButton.ReplaceOnClick(GoButtonHandler);
            var notificationManager = context.Resolve<GameNotificationManager>();
            _notifierWidget.Init(notificationManager.GetDailyTriviaNotifier());
        }

        public void SetupStoreCloseAction(Action closeStoreAction)
        {
            _closeStoreAction = closeStoreAction;
        }

        private void GoButtonHandler()
        {
            _shouldCheckForAnsweredState = !_dailyTriviaManager.IsAnswered;
            _closeStoreAction?.Invoke();
            _dailyTriviaManager.Show(true);
        }

        private void Refresh()
        {
            var answered = _dailyTriviaManager.IsAnswered;
            _answeredHolder.SetActive(answered);
            _notAnsweredHolder.SetActive(!answered);
            
            if (_shouldCheckForAnsweredState)
            {
                if (_dailyTriviaManager.IsAnswered)
                    _checkmarkAnimator.SetTrigger("JustFilled");
                
                _shouldCheckForAnsweredState = false;
            }

            var currentStreak = _dailyTriviaManager.Streak;
            for (var i = 0; i < _stars.Length; i++)
            {
                _stars[i].SetActive(i < currentStreak);
            }

            _streakDays.FormatSetArgs(_dailyTriviaManager.Streak, _dailyTriviaManager.MaxStreak);
            _animator.SetBool("Answered", answered);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _closeStoreAction = null;
        }
    }
}