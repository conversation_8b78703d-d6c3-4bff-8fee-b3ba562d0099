using BebopBee.Core.UI;

namespace BBB.UI.IAP.Controllers
{
    public static class StoreModalHelper
    {
        public static void ShowBasedOnNotifications(this StoreModalController storeModalController, GameNotificationManager notificationManager, StoreCategory categoryToShow = StoreCategory.Regular)
        {
            var basketNotifier = notificationManager.GetBasketNotifier();
            if (basketNotifier.GetStatus() > 0)
            {
                storeModalController.Show(StoreCategory.Featured, ShowMode.Delayed);
                return;
            }

            var dailyTriviaNotifier = notificationManager.GetDailyTriviaNotifier();
            if (dailyTriviaNotifier.GetStatus() > 0)
            {
                storeModalController.Show("DailyTrivia");
                return;
            }

            storeModalController.Show(categoryToShow);
        }
    }
}