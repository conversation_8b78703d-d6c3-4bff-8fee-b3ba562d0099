using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.DI;
using BBB.IAP;
using BBB.Screens;
using BBB.UI;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.IAP.Baskets
{
    public class BasketViewPresenter : ModalsViewPresenter, IBasketViewPresenter
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private SkeletonGraphic[] _basketSkeletonGraphics;

        [Header("Reward")]
        [SerializeField] private LocalizedTextPro _titleReward;
        [SerializeField] private LocalizedTextPro _daysNumberText;

        [SerializeField] private GameObject _rewardHolder;
        [SerializeField] private GameObject _singleRewardHolder;
        [SerializeField] private GameObject _doubleRewardHolder;

        [SerializeField] private UICurrencyFXedComponent[] _mainRewards;
        [SerializeField] private UICurrencyFXedComponent _specialReward;

        [SerializeField] private SkeletonGraphic _giftBox;
        [SerializeField] private string _skeletonAnimationName = "Opening";
        [SerializeField] private GameObject _comeBackTomorrowTextHolder;


        [Header("Info")]
        [SerializeField] private LocalizedTextPro _title;
        [SerializeField] private LocalizedTextPro _subTitle;

        [SerializeField] private GameObject _infoHolder;
        [SerializeField] private GameObject[] _regularRewardHolders;
        [SerializeField] private GameObject[] _specialRewardHolders;

        [SerializeField] private UIRewardComponent _rewardComponent;
        [SerializeField] private Button _buyButton;
        [SerializeField] private TextMeshProUGUI _priceText;

        [SerializeField] private GameObject[] _basketCategoryHolders;
        [SerializeField] private TextMeshProUGUI[] _daysNumberTexts;
        [SerializeField] private SaleRibbon _saleRibbon;

        private Action _iapBuyCallback;
        private IapManager _iapManager;
        private static readonly int Reward = Animator.StringToHash("Reward");

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _rewardComponent.Init(context);
            _iapManager = context.Resolve<IapManager>();

            _buyButton.ReplaceOnClick(BuyButtonHandler);
        }

        private void BuyButtonHandler()
        {
            _iapBuyCallback?.Invoke();
        }

        public void SetupInfo(IAPBasket basket, string price, Action iapBuyCallback)
        {
            _animator.ResetTrigger(Reward);

            _title.SetTextId(basket.TitleLocId);
            _subTitle.SetTextId(basket.Subtitle);
            SetupThumbnail(basket);

            _iapBuyCallback = iapBuyCallback;
            _rewardHolder.SetActive(false);
            _infoHolder.SetActive(true);

            _priceText.text = price;
            _regularRewardHolders.Enable(!basket.HasSpecialReward);
            _specialRewardHolders.Enable(basket.HasSpecialReward);

            _rewardComponent.SetupReward(basket.Reward);

            foreach (var daysNumberText in _daysNumberTexts)
            {
                daysNumberText.SetText(basket.DurationInDays.ToString());
            }

            if (_basketCategoryHolders is { Length: 3 })
            {
                foreach (var basketCategoryHolder in _basketCategoryHolders)
                {
                    basketCategoryHolder.SetActive(false);
                }

                _basketCategoryHolders[(int)basket.BasketCategory].SetActive(true);
            }

            var iapStoreMarketItemConfig = _iapManager.GetIapConfig(basket.MarketItemUid);
            if (iapStoreMarketItemConfig.SaleBadgeText.IsNullOrEmpty())
            {
                _saleRibbon.SetTitle(iapStoreMarketItemConfig.DiscountOfId);
            }
        }

        public void SetupReward(IAPBasket basket, Tuple<string, int> specialReward)
        {
            _titleReward.SetTextId(basket.TitleLocId);
            SetupThumbnail(basket);
            _rewardHolder.SetActive(true);
            _infoHolder.SetActive(false);

            _giftBox.Initialize(true);
            _giftBox.AnimationState.SetAnimation(0, _skeletonAnimationName, false);

            var passedDays = basket.GetNumberOfPassedDays();
            _daysNumberText.FormatSetArgs(passedDays.ToOrdinal(), basket.DurationInDays.ToString());

            _comeBackTomorrowTextHolder.SetActive(passedDays < basket.DurationInDays);

            var singleReward = specialReward == null;
            _singleRewardHolder.SetActive(singleReward);
            _doubleRewardHolder.SetActive(!singleReward);

            if (basket.Reward != null)
            {
                KeyValuePair<string, int> reward = default;

                foreach (var item in basket.Reward)
                {
                    reward = item;
                    break;
                }

                foreach (var mainReward in _mainRewards)
                {
                    if (mainReward == null)
                        continue;

                    mainReward.Setup(reward.Key, reward.Value);
                }
            }

            if (!singleReward)
            {
                _specialReward.Setup(specialReward.Item1, specialReward.Item2);
            }

            _animator.SetTrigger(Reward);
            AudioProxy.PlaySound(GenericSoundIds.GiftboxFallingAndOpenning);
        }

        private void SetupThumbnail(IAPBasket basket)
        {
            foreach (var skeletonGraphic in _basketSkeletonGraphics)
            {
                if (skeletonGraphic == null)
                    continue;

                skeletonGraphic.Initialize(true);
                skeletonGraphic.AnimationState.SetAnimation(0, basket.SkinName, false);
            }
        }
    }
}