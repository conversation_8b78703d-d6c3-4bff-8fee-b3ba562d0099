using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Quests;
using BBB.Wallet;
using BebopBee.Core.UI;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using UnityEngine;

namespace GameAssets.Scripts.IAP.EndlessTreasure
{
    public class EndlessTreasureController : BaseModalsController<IEndlessTreasureViewPresenter>
    {
        private readonly List<EndlessTreasure> _currentTreasures = new();
        private List<EndlessTreasure> _endlessTreasures;

        private IWalletManager _walletManager;
        private EndlessTreasureManager _endlessTreasureManager;
        private IapPurchaseProcessor _iapPurchaseProcessor;
        private IUIWalletManager _uiWalletManager;
        private IModalsBuilder _modalsBuilder;

        private bool _switching;
        private string _endlessTreasureUid;
        private bool _onModalCurrencyAnimationInProgress;

        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);

            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _endlessTreasureManager = context.Resolve<EndlessTreasureManager>();
            _iapPurchaseProcessor = context.Resolve<IapPurchaseProcessor>();
        }

        public void Setup(string endlessTreasureUid, List<EndlessTreasure> endlessTreasures)
        {
            _endlessTreasureUid = endlessTreasureUid;
            _endlessTreasures = endlessTreasures;
        }

        private void SetupAsSwitching()
        {
            _switching = true;
        }

        public override void OnShow()
        {
            base.OnShow();
            Subscribe();
            RefreshTreasures(true);
        }

        public override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _switching = false;
            _onModalCurrencyAnimationInProgress = false;
        }

        private void Subscribe()
        {
            Unsubscribe();

            View.Claimed += ClaimHandler;
        }

        private void Unsubscribe()
        {
            if (View != null)
            {
                View.Claimed -= ClaimHandler;
            }
        }

        private void RefreshWithoutModalHiding()
        {
            // check for can be shown happens before each call to Show
            // calling HideModal in OnShow could lead to issues
            if (!_endlessTreasureManager.CanBeShown(_endlessTreasureUid))
            {
                HideModal();
                return;
            }

            RefreshTreasures(false);
        }

        private void RefreshTreasures(bool fullRefresh)
        {
            var lastClaimed = _endlessTreasureManager.GetLastClaimed(_endlessTreasureUid);
            var indexOfLastClaimed = _endlessTreasures.IndexOf(x => x.Uid == lastClaimed);

            _currentTreasures.Clear();
            if (indexOfLastClaimed >= 0 && _switching)
            {
                _currentTreasures.Add(_endlessTreasures[indexOfLastClaimed]);
            }

            for (var i = indexOfLastClaimed + 1; i < Math.Min(_endlessTreasures.Count, indexOfLastClaimed + 1 + EndlessTreasureViewPresenter.NumberOfTreasuresAtATime); i++)
            {
                _currentTreasures.Add(_endlessTreasures[i]);
            }

            View.Setup(_currentTreasures, _switching, fullRefresh);
        }

        private void CurrencyAnimationFinished()
        {
            _onModalCurrencyAnimationInProgress = false;
        }

        private void ClaimHandler(EndlessTreasure endlessTreasure)
        {
            if (_onModalCurrencyAnimationInProgress)
                return;

            if (endlessTreasure.Reward != null)
            {
                var transaction = new Transaction()
                    .SetAnalyticsData(CurrencyFlow.EndlessTreasure.Name, endlessTreasure.EndlessTreasureUid, endlessTreasure.Uid)
                    .AddTag(TransactionTag.EndlessTreasure)
                    .Earn(endlessTreasure.Reward);

                WalletTransactionController.MakeTransaction(transaction);

                if (endlessTreasure.VisualType == VisualType.None)
                {
                    _onModalCurrencyAnimationInProgress = true;
                    View.UpdateCurrencyInstances();

                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.EndlessTreasure, animCallback: CurrencyAnimationFinished);
                    _endlessTreasureManager.RegisterAsPurchased(endlessTreasure);

                    if (_endlessTreasureManager.CanBeShown(_endlessTreasureUid))
                    {
                        SetupAsSwitching();
                        RefreshWithoutModalHiding();
                    }
                }
                else
                {
                    var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
                    rewardModal.SetupInitialParams(
                        new CurrenciesRewardViewModel()
                        {
                            RewardDict = endlessTreasure.Reward,
                            TitleText = EndlessTreasure.RewardTitle,
                            SubtitleText = EndlessTreasure.RewardSubtitle,
                            RewardViewOverride = endlessTreasure.VisualType == VisualType.None
                                ? null
                                : new RewardViewOverride()
                                {
                                    IconPrefab = View.TreasureVisualConfig.GetViewOverride(endlessTreasure.VisualType),
                                }
                        }, skippedCurrencies =>
                        {
                            _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.EndlessTreasure, skippedCurrencies);
                            _endlessTreasureManager.RegisterAsPurchased(endlessTreasure);

                            if (_endlessTreasureManager.CanBeShown(_endlessTreasureUid))
                            {
                                SetupAsSwitching();
                                ShowModal(ShowMode.Delayed, ModalsTags.EndlessTreasure);
                            }
                        });

                    rewardModal.ShowModal(ShowMode.Delayed, ModalsTags.EndlessTreasure);
                    HideModal();
                }
            }
            else
            {
                if (endlessTreasure.HasStoreItem)
                {
                    var purchaseParam = new IapPurchaseParams()
                    {
                        PurchaseSource = PurchaseSource.EndlessTreasure,

                        PurchaseCompletedCallback = () => { _endlessTreasureManager.RegisterAsPurchased(endlessTreasure); },
                        FlowCompletedCallback = () =>
                        {
                            if (_endlessTreasureManager.CanBeShown(_endlessTreasureUid))
                            {
                                SetupAsSwitching();
                                ShowModal(ShowMode.Delayed, ModalsTags.EndlessTreasure);
                            }
                        },
                        FlowFailedCallback = () =>
                        {
                            Debug.LogError($"Couldn't purchase product {endlessTreasure.ProductData.ID} for endless treasure {endlessTreasure.Uid}");
                            ShowModal(ShowMode.Delayed, ModalsTags.EndlessTreasure);
                        },

                        ShowGacha = false,
                        Tag = ModalsTags.EndlessTreasure,
                        TransactionTag = TransactionTag.EndlessTreasure,
                    };

                    if (endlessTreasure.VisualType == VisualType.None)
                    {
                        purchaseParam.ShowGenericRewardScreen = false;
                    }
                    else
                    {
                        purchaseParam.ShowGenericRewardScreen = true;
                        purchaseParam.RewardViewOverride = new RewardViewOverride()
                        {
                            IconPrefab = View.TreasureVisualConfig.GetViewOverride(endlessTreasure.VisualType),
                        };
                    }

                    _iapPurchaseProcessor.Purchase(endlessTreasure.ProductData.ID, purchaseParam);
                    HideModal();
                }
                else
                {
                    Debug.LogError($"Couldn't purchase product for endless treasure {endlessTreasure.Uid}");
                }
            }
        }
    }
}