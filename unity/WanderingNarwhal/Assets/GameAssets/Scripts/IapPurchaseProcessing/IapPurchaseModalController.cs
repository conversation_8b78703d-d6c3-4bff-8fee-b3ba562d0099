using System;
using BBB.Core;
using UnityEngine;

namespace BBB
{
    public class IapPurchaseModalController : BaseModalsController<IIapPurchaseViewPresenter>
    {
        private Action _closedCallback;
        private Action _onDimmerVisible;
        
        private GameObject _successViewPrefab;
        private GameObject _failedViewPrefab;

        public void SetupCloseCallback(Action closedCallback)
        {
            _closedCallback = closedCallback;
        }

        public void SetupCustomViews(GameObject successViewPrefab, GameObject failedViewPrefab)
        {
            _successViewPrefab = successViewPrefab;
            _failedViewPrefab = failedViewPrefab;
        }
        
        public void ShowDimmer(Action onDimmerVisible = null)
        {
            _onDimmerVisible = onDimmerVisible;
            DoWhenReady(() =>
            {
                View.StartDimmer();
            });
        }

        public override void OnShow()
        {
            base.OnShow();
            
            View.OnDimmerVisible -= OnDimmerVisible;
            View.OnDimmerVisible += OnDimmerVisible;
            
            View.SetupCustomViews(_successViewPrefab, _failedViewPrefab);
        }
        
        // When Dimmer is visible and Animation is done, _onDimmerVisible needs to be trigger only once
        private void OnDimmerVisible()
        {
            _onDimmerVisible?.Invoke();
            _onDimmerVisible = null;
        }

        public override void OnHide()
        {
            base.OnHide();
            
            _closedCallback?.Invoke();
            View.OnDimmerVisible -= OnDimmerVisible;

            _successViewPrefab = null;
            _failedViewPrefab = null;
        }

        public void ShowSuccess(FBConfig.IAPStoreMarketItemConfig storeMarketItemConfig)
        {
            DoWhenReady(() =>
            {
                View.StopDimmer();
                View.ShowSuccess(storeMarketItemConfig);
            });
        }

        public void ShowFailed()
        {
            DoWhenReady(() =>
            {
                View.StopDimmer();
                View.ShowFailed();
            });
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            View.OnDimmerVisible -= OnDimmerVisible;
            _closedCallback = null;
            _onDimmerVisible = null;
        }
    }
}