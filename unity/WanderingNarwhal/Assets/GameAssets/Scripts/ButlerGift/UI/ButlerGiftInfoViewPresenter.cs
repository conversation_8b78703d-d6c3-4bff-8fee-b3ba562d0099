using System;
using System.Collections.Generic;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.ButlerGift.UI
{
    public class ButlerGiftInfoViewPresenter : ModalsViewPresenter, IButlerGiftInfoViewPresenter
    {
        public event Action PlayStartButtonPressedEvent;
        [SerializeField] private Button _startButton;
        [SerializeField] private List<GameObject> _ribbons;
        
        
        public override void Show()
        {
            base.Show();
            _startButton.ReplaceOnClick(() =>
            {
                PlayStartButtonPressedEvent?.Invoke();
            });
        }

        public void Refresh(int tier)
        {
            if(_ribbons.IsNullOrEmpty())
                return;
            foreach (var ribbon in _ribbons)
            {
                ribbon.SetActive(false);
            }
            //Don't show any ribbons if tier < 0 since no rewards are active
            if (tier < 0) return;
            var currentRibbon = _ribbons[tier];
            if(currentRibbon != null)
                currentRibbon.SetActive(true);
        }
    }
}