using BBB.Audio;
using BBB.DI;
using BebopBee.Core.Audio;
using FBConfig;

namespace BBB
{
    public class PassportTabAchievementsController : PassportTabController<PassportTabAchievementsView>
    { 
        private QuestManager _questManager;
        private GameNotificationManager _notificationManager;

        protected override void OnContextInitialized(IContext context)
        {
            _questManager = context.Resolve<QuestManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
        }

        protected override void OnShow()
        {
            Subscribe();
        }

        protected override void OnHide()
        {
            Unsubscribe();
        }

        private void Unsubscribe()
        {
            InternalView.ClaimButtonClicked -= OnClaimButtonClicked;
        }
        
        private void Subscribe()
        {
            Unsubscribe();
            InternalView.ClaimButtonClicked += OnClaimButtonClicked;
        }

        private void OnClaimButtonClicked(QuestObjectiveConfig questObjectiveConfig)
        {
            AudioProxy.PlaySound(GenericSoundIds.ClaimOrRevealButtonTap);
            _questManager.ClaimObjective(questObjectiveConfig);
            UpdateNotifierStatus();
            InternalView.RefreshRanksQuest();
        }

        private void UpdateNotifierStatus()
        {
            var badgeQuestNotifier = _notificationManager.GetBadgeQuestNotifier();
            var rewardStatus = badgeQuestNotifier.GetRewardStatus() - 1;
            var status = badgeQuestNotifier.GetStatus() - (rewardStatus <= 0 ? 1 : 0);
    
            badgeQuestNotifier.SetNotifier(status, rewardStatus);
        }
    }
}