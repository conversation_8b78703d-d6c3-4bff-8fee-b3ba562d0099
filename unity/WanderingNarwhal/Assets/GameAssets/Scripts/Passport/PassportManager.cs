using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Modals;
using BebopBee.Core.UI;
using UnityEngine;
using Core.Configs;
using QuestObjectiveConfig = FBConfig.QuestObjectiveConfig;

namespace BBB
{
    public class PassportManager : IContextInitializable
    {
        private const string RanksQuestUid = "travel_badge";
        public const string LockPassportUid = "passport";

        private IPlayerManager _playerManager;
        private PassportModalController _modalController;
        private IModalsBuilder _modalsBuilder;
        private ILocationManager _locationManager;
        private IModalsManager _modalsManager;

        private QuestManager _questManager;
        public Quest RanksQuest { get; private set; }
        public readonly HashSet<string> LocationsAlreadyShown = new();


        public void InitializeByContext(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _locationManager = context.Resolve<ILocationManager>();
            _modalsManager = context.Resolve<IModalsManager>();
            _questManager = context.Resolve<QuestManager>();
            var questAccessor = context.Resolve<IQuestAccessor>();
            RanksQuest = questAccessor.GetQuest(RanksQuestUid);

            if (_playerManager.Player == null)
            {
                Debug.LogError($"_playerManager.Player is null");
            }
            else if (_playerManager.Player.ClaimedLocations == null)
            {
                Debug.LogError($"_playerManager.Player.ClaimedLocations");
            }
            else
            {
                foreach (var location in _playerManager.Player.ClaimedLocations)
                    LocationsAlreadyShown.Add(location);
            }
        }

        private bool IsLocationAlreadyShown(string locationId)
        {
            return _playerManager.Player.ClaimedLocations.Contains(locationId) || LocationsAlreadyShown.Contains(locationId);
        }

        public void ShowAnnouncement()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.Passport, string.Empty));
            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.PassportClick, DauInteractions.TapOnAutoPopups.PassportClose);
            ShowPassportModal();
        }

        public void ShowPassportModal(PassportTab passportTab = PassportTab.Info, Action hideCallback = null, string tag = "", ShowMode showMode = ShowMode.Delayed)
        {
            _modalController ??= _modalsBuilder.CreateModalView<PassportModalController>(ModalsType.Passport);

            _modalController.Setup(passportTab);
            _modalController.SetHideCallback(hideCallback);
            _modalsManager.PrioritizeNextModalWithTag(ModalsTags.Passport);
            _modalController.ShowModal(showMode, tag: tag.IsNullOrEmpty() ? ModalsTags.Passport : tag);
        }

        public void GetRankQuestProgress(out int startProgress, out int currentProgress, out int maxProgress)
        {
            startProgress = 0;
            currentProgress = 0;
            maxProgress = 0;
            if (RanksQuest == null)
                return;

            _questManager.QuestsConditionsRefresh();
            if (RanksQuest.IsFinished())
            {
                Debug.LogError("Undefined behaviour on rank quest completion");
                return;
            }

            if (RanksQuest.GetState() == QuestState.WaitingForCondition)
            {
                Debug.LogError("Rank quest is not started, that should not be the case");
                currentProgress = 1;
                maxProgress = 3;
                return;
            }

            QuestObjectiveConfig lastDoneOrClaimed = default;
            QuestObjectiveConfig nextObjective = default;
            var objectives = RanksQuest.GetObjectives();
            foreach (var objectiveConfig in objectives)
            {
                if (RanksQuest.GetObjectiveState(objectiveConfig.Uid) == ObjectiveState.Done || RanksQuest.GetObjectiveState(objectiveConfig.Uid) == ObjectiveState.Claimed)
                {
                    lastDoneOrClaimed = objectiveConfig;
                }
                else
                {
                    nextObjective = objectiveConfig;
                    break;
                }
            }

            var startObjectiveProgress = 0;
            var maxObjectiveProgress = 0;
            // fallback
            var currentObjectiveProgress = _locationManager.GetUnlockedLocationsCount(false);

            if (nextObjective.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig) || lastDoneOrClaimed.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig))
            {
                if (lastDoneOrClaimed.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig))
                {
                    if (nextObjective.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig))
                    {
                        BDebug.LogError(LogCat.Quest, "None of the ranks quest objectives are valid");
                    }
                    else
                    {
                        startObjectiveProgress = nextObjective.StartProgress;
                        currentObjectiveProgress = RanksQuest.GetObjectiveProgressNumber(nextObjective);
                        maxObjectiveProgress = nextObjective.ProgressiveObjective;
                    }
                }
                else
                {
                    startObjectiveProgress = lastDoneOrClaimed.StartProgress;
                    currentObjectiveProgress = RanksQuest.GetObjectiveProgressNumber(lastDoneOrClaimed);
                    maxObjectiveProgress = lastDoneOrClaimed.ProgressiveObjective;
                }
            }
            else
            {
                startObjectiveProgress = lastDoneOrClaimed.StartProgress;
                currentObjectiveProgress = RanksQuest.GetObjectiveProgressNumber(nextObjective);
                maxObjectiveProgress = nextObjective.ProgressiveObjective;
            }

            startProgress = startObjectiveProgress;
            currentProgress = currentObjectiveProgress;
            maxProgress = maxObjectiveProgress;
        }
    }
}