using System;
using BBB.DI;
using TMPro;
using UnityEngine;

namespace BBB
{
    public abstract class PassportTabView : BbbMonoBehaviour
    {
        public bool CanBeFlipped { get; protected set; } = true;

        protected Action HideModal;
        
        public Animator PageAnimator;
        [SerializeField] private TextMeshProUGUI _pageText;

        public void Init(IContext context, Action hideModal)
        {
            OnContextInitialized(context);
            HideModal = hideModal;
        }

        protected abstract void OnContextInitialized(IContext context);

        public void Show()
        {
            gameObject.SetActive(true);
            OnShow();
        }

        public void SetupPageNumber(int pageNumber)
        {
            _pageText.text = pageNumber.ToString();
        }
        
        public void Hide()
        {
            OnHide();
            gameObject.SetActive(false);
        }

        public abstract void PrepareForHideFlipping();

        protected abstract void OnShow();
        protected abstract void OnHide();
        public abstract void UserInteractionChanged(bool state);

        protected override void OnDestroy()
        {
            base.OnDestroy();
            HideModal = null;
        }
    }
}