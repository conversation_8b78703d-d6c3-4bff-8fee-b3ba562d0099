using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    /// <summary>
    /// Component for Passport modal speech bubble.
    /// </summary>
    public class SpeechBubbleActivator : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private string _showAnimState = "Show";
        [SerializeField] private string _hideAnimState = "Hide";
        [SerializeField] private LocalizedTextPro _locText;
        [SerializeField] private TextMeshProUGUI _rawText;
        [SerializeField] private bool _doNotAutoClose = false;

        private Button _selfHideButton;
        private bool _shown;

        private void Awake()
        {
            _selfHideButton = gameObject.GetComponent<Button>();
            if (_selfHideButton != null)
                _selfHideButton.ReplaceOnClick(Hide);
        }

        public void ShowWithText(string textLocKey, Vector3 position)
        {
            var forceDifferentSource = position != transform.position;
            transform.position = position;
            ShowWithText(textLocKey, forceDifferentSource);
        }

        public void ShowWithText(string textLocKey, bool forceDifferentSource = false)
        {
            gameObject.SetActive(true);

            if ((forceDifferentSource || (_locText != null && _locText.TextId != textLocKey) || (_rawText != null && _rawText.text != textLocKey) || !_shown))
            {
                if (_locText != null)
                    _locText.SetTextId(textLocKey);
                else if (_rawText != null)
                    _rawText.text = textLocKey;

                Show();
            }
            else
            {
                Hide();
            }
        }

        public void Show()
        {
            gameObject.SetActive(true);
            _animator.enabled = false;
            _animator.Play(_showAnimState, 0, 0);
            _animator.enabled = true;
            _animator.SetBool("DoNotAutoClose", _doNotAutoClose);
            _shown = true;
        }

        public void Hide()
        {
            if (_shown)
                _animator.Play(_hideAnimState);
        }

        private void HideAnimationStarted()
        {
            _shown = false;
        }

        /// <summary>
        /// Called from hide animation.
        /// </summary>
        public void DisableObject()
        {
            gameObject.SetActive(false);
            _shown = false;
        }

        protected override void OnDisable()
        {
            DisableObject();
        }
    }
}