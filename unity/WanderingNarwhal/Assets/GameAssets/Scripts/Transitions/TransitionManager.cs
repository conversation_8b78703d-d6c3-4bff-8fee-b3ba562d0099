using UnityEngine;
using BBB.Core.ResourcesManager;
using System.Collections.Generic;
using System;
using System.Collections;
using BBB.Core;
using BBB.Core.ResourcesManager.Asset;
using BBB.DI;
using RSG;
using TMPro;
using UnityEngine.Serialization;
using BBB.UI.Transitions.Impl;
using BBB.Core.AssetBundles;
using BBB.Screens;
using BebopBee.Core;
using DG.Tweening;
using GameAssets.Scripts.Theme;
using Google.FlatBuffers;
using UnityEngine.Profiling;

namespace BBB.UI.Transitions
{
    public class TransitionManager : BbbMonoBehaviour, ITransitionScreen, IContextInitializable
    {
        private const string ProgressBarPrefabName = "ProgressBar_P";
        private const string TipsTextPrefabName = "TipsText";
        private const float ShowTipsTime = 7f;

        [FormerlySerializedAs("_screenHolder")] [SerializeField]
        private RectTransform _transitionsHolder;

        [SerializeField] private RectTransform _progressBarHolder;
        [SerializeField] private Ease _progressEase = Ease.OutCubic;
        [SerializeField] private float _progressTweenTime = 1f;
        [SerializeField] private RectTransform _loadingBarHolder;
        [SerializeField] private RectTransform _tipsTextHolder;
        [SerializeField] private float _timeBeforeShowingProgressBar = 0.5f;
        [SerializeField] private float _timeBeforeShowingTips = 0.5f;

        [SerializeField] private string[] _tipsIds;
        private TextMeshProUGUI _tipsText;

        private GameObject _loadingBar;
        private Animator _loadingBarAnimator;
        private AnimationEventCatcher _loadingBarAnimationEventCatcher;

        private float _loadingStartTime;
        private const float PreFadeOutPause = 0f;
        private const float FadeOutTime = 0.5f;

        private readonly Dictionary<string, ITransition> _implementationsByKey = new();

        private ProgressBar _progressBar;
        private AnimatorShowable _curtain;

        private ILocalizationManager _localizationManager;
        private bool _isReady;
        private IConfig _config;
        private IAssetsManager _assetsManager;
        private IThemeManager _themeManager;
        private IPlayerManager _playerManager;
        private IGameEventManager _gameEventManager;
        private ITransition _currentTransition;
        private ILocationManager _locationManager;
        private LevelLocationManager _levelLocationManager;
        private IDisposable _showTipsTween;
        private ITransition _noTransition;
        private Tweener _currentProgressTween;
        private float _progressTarget;
        private string _overridenTransitionScreenUid;
        private static readonly int Outro = Animator.StringToHash("Outro");

        public float ProgressFillDuration => _progressTweenTime;

        public void SetDefaultCurtain(AnimatorShowable curtain)
        {
            _curtain = curtain;
        }

        public void InitializeByContext(IContext context)
        {
            _config = context.Resolve<IConfig>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _themeManager = context.Resolve<IThemeManager>();

            Profiler.BeginSample("NoTransition");
            _noTransition = new NoTransition();
            _implementationsByKey["LoadingScreen"] = _noTransition;
            Profiler.EndSample();
            Profiler.BeginSample("DefaultTransition");
            var defaultTransition = new DefaultTransition();
            defaultTransition.Init(new TransitionData("LandingScreen", "landing", "Transition_Generic"), _config, _transitionsHolder, null);
            _implementationsByKey["LandingScreen"] = defaultTransition;
            Profiler.EndSample();

            _assetsManager.LoadAsync<GameObject>("VideoLoadingIndicator", AssetLoadPriority.InQueue).Then(_ =>
            {
                Profiler.BeginSample($"Instantiate[VideoLoadingIndicator]");
                _loadingBar = Instantiate(_.Get(), _loadingBarHolder);
                _loadingBarAnimator = _loadingBar.GetComponent<Animator>();
                _loadingBarAnimationEventCatcher = _loadingBar.GetComponent<AnimationEventCatcher>();
                Profiler.EndSample();
            });
        }

        public void SetLocalizationManager(ILocalizationManager localizationManager)
        {
            _localizationManager = localizationManager;
        }

        public void Preload()
        {
            foreach (var pair in _implementationsByKey)
            {
                var transition = pair.Value;
                var promise = transition.Preload(_assetsManager, _themeManager);
                promise.Done();
            }
        }

        public void OverrideNextTransitionScreen(string overridenTransitionScreenUid)
        {
            _overridenTransitionScreenUid = overridenTransitionScreenUid;
        }

        public void Init(IGameEventManager gameEventManager, IPlayerManager playerManager)
        {
            _playerManager = playerManager;
            _gameEventManager = gameEventManager;
            var screenConfigs = _config.Get<FBConfig.ScreenConfig>();

            var transitionByKey = new Dictionary<string, ITransition>();
            if (!screenConfigs.IsNull())
            {
                if (screenConfigs.Count <= 0)
                {
                    Debug.LogWarning("No screen configs found, creating default transition");
                    screenConfigs = CreateDefaultScreenConfigs();
                }

                foreach (var sc in screenConfigs.Values)
                {
                    if (string.IsNullOrEmpty(sc.Transition) || string.IsNullOrEmpty(sc.Uid))
                        continue;

                    ITransition transition;
                    var key = $"{sc.Transition}_{sc.TransitionPrefab}";
                    if (transitionByKey.TryGetValue(key, out var transitionForKey))
                    {
                        transition = transitionForKey;
                    }
                    else
                    {
                        transition = InstantiateTransitionImpl(sc.Transition);

                        if (transition != null)
                        {
                            transition.Init(new TransitionData(sc), _config, _transitionsHolder, _gameEventManager);
                            transitionByKey.Add(key, transition);
                        }
                    }

                    if (transition != null)
                    {
                        _implementationsByKey[sc.Uid] = transition;
                    }
                }
            }

            _assetsManager.LoadAsync<GameObject>(ProgressBarPrefabName).Then(OnProgressBarLoaded).Done();
            _assetsManager.LoadAsync<GameObject>(TipsTextPrefabName).Then(OnTipTextLoaded).Done();
        }

        private void OnTipTextLoaded(IAssetLoaded<GameObject> obj)
        {
            if (obj == null)
                return;

            Profiler.BeginSample($"Instantiate[{TipsTextPrefabName}]");
            _tipsText = Instantiate(obj.Get(), _tipsTextHolder).GetComponent<TextMeshProUGUI>();
            Profiler.EndSample();
            SetTipsTextVisibility(false);
        }

        public bool IsReady(string uid)
        {
            var transition = _implementationsByKey.GetSafe(uid);
            return _isReady || (transition != null && transition.IsLoaded());
        }

        public bool IsShown()
        {
            return (_currentTransition != null && _currentTransition.IsShown()) || _curtain.IsVisible();
        }

        private IDictionary<string, FBConfig.ScreenConfig> CreateDefaultScreenConfigs()
        {
            var result = new Dictionary<string, FBConfig.ScreenConfig>();

            var fbb = new FlatBufferBuilder(0x100);

            var offset = FBConfig.ScreenConfig.CreateScreenConfig(fbb, fbb.CreateString("HelpingHands"), fbb.CreateString("CityTransition"));
            fbb.Finish(offset.Value);

            var bytes = fbb.SizedByteArray();
            var buf = new ByteBuffer(bytes);
            var obj = FBConfig.ScreenConfig.GetRootAsScreenConfig(buf);
            result.Add("HelpingHands", obj);

            fbb.Clear();
            var offset2 = FBConfig.ScreenConfig.CreateScreenConfig(fbb, fbb.CreateString("LevelScreen"), fbb.CreateString("CityTransition"));
            fbb.Finish(offset2.Value);

            bytes = fbb.SizedByteArray();
            buf = new ByteBuffer(bytes);
            obj = FBConfig.ScreenConfig.GetRootAsScreenConfig(buf);
            result.Add("LevelScreen", obj);

            return result;
        }

        private ITransition InstantiateTransitionImpl(string transitionClassName)
        {
            var type = Type.GetType("BBB.UI.Transitions.Impl." + transitionClassName, false, true);
            if (type?.GetInterface("BBB.UI.Transitions.ITransition", true) == null)
                return null;

            if (Activator.CreateInstance(type) is not ITransition result)
            {
                throw new Exception("Transition implementation '" + transitionClassName + "' is declared but cannot be found!");
            }

            return result;
        }

        public void ProvideLocationManager(ILocationManager locationManager)
        {
            _locationManager = locationManager;
        }

        private void OnProgressBarLoaded(IAssetLoaded<GameObject> obj)
        {
            if (obj == null)
                return;

            Profiler.BeginSample($"Instantiate[{ProgressBarPrefabName}]");
            _progressBar = Instantiate(obj.Get(), _progressBarHolder).GetComponent<ProgressBar>();
            Profiler.EndSample();
            SetProgressBarVisibility(false);
        }

        public void Load(IContext context)
        {
            foreach (var impl in _implementationsByKey.Values)
            {
                impl.Setup(context);
            }

            var preloadPromises = new List<IPromise>(_implementationsByKey.Count);
            var transitionInProgress = new HashSet<ITransition>();
            foreach (var pair in _implementationsByKey)
            {
                var transition = pair.Value;
                if (transitionInProgress.Add(transition))
                {
                    preloadPromises.Add(transition.Preload(_assetsManager, _themeManager));
                }
            }

            Promise.All(preloadPromises).Then(() => { _isReady = true; }
            ).Done();
        }

        private void SetProgressBarVisibility(bool isVisible)
        {
            if (_progressBar != null)
            {
                _progressBar.gameObject.SetActive(isVisible);
            }
        }

        private void SetTipsTextVisibility(bool isVisible)
        {
            if (_tipsText != null)
            {
                _tipsText.gameObject.SetActive(isVisible);
            }
        }

        public void BeginTransition(string uid, ScreenType previousScreen, out ScreenFadeOutParams fadeOutParams, bool noTransition = false)
        {
            BDebug.Log(LogCat.CoreViews, $"{Time.frameCount}: BeginTransition");
            var useCurtain = true;
            _loadingStartTime = Time.realtimeSinceStartup;
            fadeOutParams = new ScreenFadeOutParams();

            if (!_overridenTransitionScreenUid.IsNullOrEmpty())
            {
                uid = _overridenTransitionScreenUid;
                _overridenTransitionScreenUid = string.Empty;
            }

            if (previousScreen == ScreenType.LoadingScreen)
            {
                _currentTransition = _noTransition;
                fadeOutParams = new ScreenFadeOutParams
                {
                    PreFadePauseTime = PreFadeOutPause,
                    FadeOutTime = FadeOutTime
                };

                useCurtain = !_currentTransition.Begin(this, uid, previousScreen);
            }
            else
            {
                if (noTransition)
                {
                    _currentTransition = _noTransition;
                    useCurtain = false;
                } else if (_implementationsByKey.TryGetValue(uid, out _currentTransition))
                {
                    useCurtain = !_currentTransition.Begin(this, uid, previousScreen);
                }
                TryEnableLoadingBar(uid);
            }

            if (useCurtain)
            {
                DestroyCurrentTransition();
                _curtain.Show();
            }

            SetTipsTextVisibility(false);
            if (ShouldShowTips())
            {
                _showTipsTween = Rx.InvokeRepeating(_timeBeforeShowingTips, ShowTipsTime, _ => ShowNewTip());
            }

            if (_progressBar != null)
            {
                _progressBar.SetProgress(0f);
                _progressTarget = 0f;
            }
        }

        private bool ShouldShowTips()
        {
            return _currentTransition?.AllowShowingTips ?? false;
        }

        public bool CanShowProgressBar()
        {
            return Time.realtimeSinceStartup > _loadingStartTime + _timeBeforeShowingProgressBar
                   && (_currentTransition?.AllowShowingProgressBar ?? false)
                   && (_currentTransition?.IsShown() ?? _curtain.IsVisible());
        }

        private void TryEnableLoadingBar(string screenUid)
        {
            if (_loadingBar == null) return;

            if (screenUid != "MapScreen")
                return;

            var lastUnlockedLocation = _locationManager.GetLastUnlockedLocation();

            if (lastUnlockedLocation == null || lastUnlockedLocation == _playerManager.CurrentLocationUid)
                return;

            _loadingBar.SetActive(true);
            _loadingBarAnimationEventCatcher.AnimationEvent -= HideLoadingBar;
            _loadingBarAnimationEventCatcher.AnimationEvent += HideLoadingBar;
        }

        public IEnumerator PreLoadScreen()
        {
            _currentTransition?.PreLoadScreen();
            while (!ReadyToLoadScreen())
            {
                yield return null;
            }
        }

        private bool ReadyToLoadScreen()
        {
            return _currentTransition is { ReadyToLoadScreen: true } or null;
        }

        public bool CanBeEnded()
        {
            return _currentTransition is { CanBeEnded: true } or null;
        }

        public IEnumerator EndTransition()
        {
            BDebug.Log(LogCat.CoreViews, $"{Time.frameCount}: EndTransition");

            foreach (var value in _implementationsByKey.Values)
            {
                if (value is CityTransition transition)
                {
                    transition.LoadTransitionScreen(_assetsManager, _playerManager.CurrentLocationUid);
                }
            }

            while (!CanBeEnded())
            {
                yield return null;
            }

            _currentTransition?.End();
            SetProgressBarVisibility(false);
            SetTipsTextVisibility(false);

            if (_currentTransition == null)
            {
                _curtain.Hide();
            }

            DestroyCurrentTransition();

            _loadingBarAnimator?.SetTrigger(Outro);

            _showTipsTween?.Dispose();
            _showTipsTween = null;

            _currentProgressTween?.Kill();
            _currentProgressTween = null;
            _progressTarget = 0f;
        }

        private void HideLoadingBar()
        {
            _loadingBar.SetActive(false);
            _loadingBarAnimationEventCatcher.AnimationEvent -= HideLoadingBar;
        }

        public void SetProgressValue(float value)
        {
            if (_progressBar != null)
            {
                if (!CanShowProgressBar())
                    return;

                SetProgressBarVisibility(true);
                if (Math.Abs(_progressTarget - value) < float.Epsilon)
                    return;

                _progressTarget = value;
                _currentProgressTween?.Kill();
                if (value != 0f)
                {
                    _currentProgressTween = DOTween.To(() => _progressBar.GetProgress(),
                        p => _progressBar.SetProgress(p), value,
                        _progressTweenTime).SetEase(_progressEase);
                }
                else
                {
                    _progressBar.SetProgress(value);
                }
            }
        }

        private void ShowNewTip()
        {
            if (_tipsText == null || _tipsIds == null || _tipsIds.Length == 0)
                return;

            var tipId = _tipsIds.GetRandomItem();
            var text = _localizationManager?.getLocalizedText(tipId);
            if (text.IsNullOrEmpty())
            {
                _tipsText.gameObject.SetActive(false);
            }
            else
            {
                _tipsText.text = text;
                _tipsText.gameObject.SetActive(true);
            }
        }

        private void DestroyTransitions()
        {
            if (_implementationsByKey != null)
            {
                foreach (var transition in _implementationsByKey)
                {
                    transition.Value?.Destroy();
                }

                _implementationsByKey.Clear();
            }

            DestroyCurrentTransition();
        }

        private void DestroyCurrentTransition()
        {
            _currentTransition?.Destroy();
            _currentTransition = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            _showTipsTween?.Dispose();
            _showTipsTween = null;

            _currentProgressTween?.Kill();
            _currentProgressTween = null;
            if (_loadingBarAnimationEventCatcher != null)
            {
                _loadingBarAnimationEventCatcher.AnimationEvent -= HideLoadingBar;
            }

            DestroyTransitions();

            _progressBar?.Unsubscribe();
            _progressBar = null;
            _loadingBar = null;

            _assetsManager?.UnloadAsset(ProgressBarPrefabName);
            _assetsManager?.UnloadAsset(TipsTextPrefabName);
        }

        public IEnumerator EnsureCityTransitionReady(string uid)
        {
            var transition = _implementationsByKey.GetSafe(uid);
            if (transition is CityTransition cityTransition)
            {
                var timeout = 120; // At 60 fps, this would be 2 seconds
                cityTransition.LoadTransitionScreen(_assetsManager, _playerManager.CurrentLocationUid);
                while (!cityTransition.IsCityTransitionLoaded(_playerManager.CurrentLocationUid) && timeout > 0)
                {
                    yield return null;
                    timeout--;
                }
            }
        }
    }
}