using System.Collections.Generic;
using UnityEngine;
using BBB.DI;
using BebopBee.Core;
using UnityEngine.EventSystems;

namespace BBB.Inputs
{
    public sealed partial class InputManager : IInputManager, IContextReleasable
    {
        private readonly HashSet<object> _currentLocks = new HashSet<object>();

        private EventSystem _lastKnownEventSystem;

        public void AddGlobalLock(object source)
        {
            Debug.Log("#GLOCK# Add lock");
            if (_currentLocks.Contains(source))
            {
                Debug.LogWarning("already contains this lock. ignoring");
                return;
            }

            _currentLocks.Add(source);
            if (_lastKnownEventSystem == null)
            {
                _lastKnownEventSystem = EventSystem.current;
            }

            _lastKnownEventSystem.enabled = false;
        }

        public void ReleaseGlobalLock(object source)
        {
            Debug.Log("#GLOCK# Release lock");
            if (!_currentLocks.Contains(source))
            {
                Debug.LogWarning("Can't find this lock. ignoring");
            }
            else
            {
                _currentLocks.Remove(source);
            }

            if (_lastKnownEventSystem != null)
            {
                _lastKnownEventSystem.enabled = _currentLocks.Count == 0;
            }
        }

        public void AddAutoReleaseLock(object source, float lockDuration)
        {
            if (lockDuration <= 0)
            {
                return;
            }

            _currentLocks.Add(source);
            if (_lastKnownEventSystem == null)
            {
                _lastKnownEventSystem = EventSystem.current;
            }

            _lastKnownEventSystem.enabled = false;

            Rx.Invoke(Mathf.Min(lockDuration, 10f), (_) =>
            {
                if (_currentLocks != null)
                {
                    _currentLocks.Remove(source);
                }

                if (_lastKnownEventSystem != null)
                {
                    _lastKnownEventSystem.enabled = true;
                }
            });
        }
    }
}