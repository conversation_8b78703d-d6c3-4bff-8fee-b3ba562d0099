using System;
using BBB;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace GameAssets.Scripts.GlobeModal
{
    public class ChallengeLocationMarker : BbbMonoBehaviour
    {
        private const string CountryPrefix = "COUNTRY_";
        private const string LocationMarkerCountryText = "LOCATION_MARKER_COUNTRY_TEXT";
        private const string MissingGreeting = "MISSING_GREETING";

        private static readonly int Visible = Animator.StringToHash("Visible");

        public event Action<string> Selected;
        public event Action<string, bool> CardShown;

        [SerializeField] private Canvas _canvas;
        [SerializeField] private Button _markerButton;

        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private AsyncLoadableImage _pinFlagImage;

        [SerializeField] private TextMeshProUGUI _playerName;
        [SerializeField] private LocalizedTextPro _countryText;
        [SerializeField] private LocalizedTextPro _challengesPlayed;
        [SerializeField] private GenericSpeechBubble _greetingSpeechBubble;

        [SerializeField] private bool _flipScale;

        [SerializeField] private Animator _markerAnimator;
        [SerializeField] private Animator _cardAnimator;
        [SerializeField] private AnimationEventHandler _animationEventHandler;

        [SerializeField] private float _autoSelectionDelay = 0.5f;

        [SerializeField] private string _selectedSoundId = "GenericButtonTap";
        [SerializeField] private bool _haptic = true;
        [SerializeField] private ImpactPreset _impactPreset = ImpactPreset.MediumImpact;

        [SerializeField] private GameObject _selectionParticlesHolder;

        // Needed to make sure hiding card doesn't overlap visually with appearing
        [SerializeField] private Canvas _cardCanvas;
        [SerializeField] private int _shownCardSortOrder;
        [SerializeField] private int _hiddenCardSortOrder;

        private IVibrationsWrapper _vibrationsWrapper;
        private Camera _camera;
        private ILocalizationManager _localizationManager;

        private ChallengeInfo _challengeInfo;
        private bool _visible;
        private bool _showCard;
        private Tweener _autoSelectionTweener;

        public bool Active { get; private set; }

#if UNITY_EDITOR
        [SerializeField] private string _coordsText;

        [ContextMenu("GetCoordsForConfig")]
        private void GetCoordsForConfig()
        {
            var position = transform.localPosition;
            position = position.normalized * 53f;
            transform.localPosition = position;
            var magnitude = position.magnitude;

            var yaw = Mathf.Atan2(position.x, position.z) * Mathf.Rad2Deg;
            var pitch = -Mathf.Asin(position.y / magnitude) * Mathf.Rad2Deg;

            _coordsText = $"{{\"X\":{yaw}, \"Y\":{pitch}, \"Z\":{0}}}";
        }
#endif

        private void Start()
        {
            if (_flipScale)
            {
                var localScale = transform.localScale;
                localScale.x *= -1;
                transform.localScale = localScale;
            }

            _animationEventHandler.AnimationEvent += SpeechBubbleEventHandler;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            Selected = null;
            CardShown = null;
            _animationEventHandler.AnimationEvent -= SpeechBubbleEventHandler;
        }

        public void Init(ILocalizationManager localizationManager, Camera camera, IVibrationsWrapper vibrationsWrapper)
        {
            _localizationManager = localizationManager;
            _camera = camera;
            _vibrationsWrapper = vibrationsWrapper;

            _canvas.worldCamera = _camera;
            _markerButton.ReplaceOnClick(MarkerButtonHandler);
            _selectionParticlesHolder.SetActive(false);
        }

        public void Reset()
        {
            Active = false;
            _challengeInfo = default;
            _visible = false;
            _showCard = false;

            _cardCanvas.sortingOrder = _hiddenCardSortOrder;
            _cardAnimator.SetBool(Visible, false);
            _markerAnimator.SetBool(Visible, false);

            _greetingSpeechBubble.HideSpeechBubble();
            KillAutoSelectionTweener();
        }

        public void Setup(ChallengeInfo challengeInfo)
        {
            Active = true;

            _challengeInfo = challengeInfo;
            var locationConfig = challengeInfo.ChallengeLocationConfig;
            _asyncAvatar.Setup(new AvatarInfo(challengeInfo.AvatarUrl, challengeInfo.ChallengeLocationConfig.CountryCode));
            _pinFlagImage.Show(challengeInfo.ChallengeLocationConfig.FlagImage);

            _playerName.text = challengeInfo.PlayerName;

            if (_greetingSpeechBubble != null)
            {
                if (locationConfig.GreetingsLength > 0)
                {
                    var greetingText = locationConfig.Greetings(Random.Range(0, locationConfig.GreetingsLength));
                    _greetingSpeechBubble.SetLocalizedText(greetingText);
                }
                else
                {
                    _greetingSpeechBubble.SetLocalizedText(MissingGreeting);
                }
            }

            var countryName = _localizationManager.getLocalizedText(CountryPrefix + locationConfig.CountryCode);
            _countryText.SetTextId(LocationMarkerCountryText, countryName);

            if (challengeInfo.ChallengesPlayed > 0)
            {
                _challengesPlayed.FormatSetArgs(challengeInfo.ChallengesPlayed);
                _challengesPlayed.gameObject.SetActive(true);
            }
            else
            {
                _challengesPlayed.gameObject.SetActive(false);
            }
        }

        public void SetVisible(bool visible)
        {
            if (_visible == visible)
                return;

            if (!visible)
            {
                _cardCanvas.sortingOrder = _hiddenCardSortOrder;
                CardShown?.Invoke(_challengeInfo.ChallengeLocationConfig.Uid, false);
                _cardAnimator.SetBool(Visible, false);
                _markerAnimator.SetBool(Visible, false);
                _greetingSpeechBubble.HideSpeechBubble();
            }
            else
            {
                _markerAnimator.SetBool(Visible, true);

                // if already was selected, but only now auto-appears, we can should make with controllable delay
                if (_showCard)
                {
                    _autoSelectionTweener = Rx.Invoke(_autoSelectionDelay, _ => { SetShowCard(true); });
                }
            }

            _visible = visible;
        }

        public void TriggerSelection()
        {
            _selectionParticlesHolder.SetActive(false);
            _selectionParticlesHolder.SetActive(true);
        }

        public void SetShowCard(bool showCard)
        {
            KillAutoSelectionTweener();

            _showCard = showCard;
            if (_showCard && _visible)
            {
                if (!_selectedSoundId.IsNullOrEmpty())
                {
                    AudioProxy.PlaySound(_selectedSoundId);
                }

                if (_haptic)
                {
                    _vibrationsWrapper?.PlayHaptic(_impactPreset);
                }

                transform.SetAsLastSibling();
                _cardCanvas.sortingOrder = _shownCardSortOrder;
                CardShown?.Invoke(_challengeInfo.ChallengeLocationConfig.Uid, true);
                _cardAnimator.SetBool(Visible, true);
            }
            else if (!_showCard)
            {
                _cardCanvas.sortingOrder = _hiddenCardSortOrder;
                CardShown?.Invoke(_challengeInfo.ChallengeLocationConfig.Uid, false);
                _cardAnimator.SetBool(Visible, false);
                _greetingSpeechBubble.HideSpeechBubble();
            }
        }

        public Vector3 GetUserAvatarPosition()
        {
            return _asyncAvatar.transform.position;
        }

        private void KillAutoSelectionTweener()
        {
            if (_autoSelectionTweener != null)
            {
                _autoSelectionTweener.Kill();
                _autoSelectionTweener = null;
            }
        }

        private void MarkerButtonHandler()
        {
            Selected?.Invoke(_challengeInfo.ChallengeLocationConfig.Uid);
        }

        private void SpeechBubbleEventHandler()
        {
            if (_showCard && _visible)
            {
                _greetingSpeechBubble.ShowSpeechBubble();
            }
        }

        private void LateUpdate()
        {
            if (_camera != null)
            {
                transform.LookAt(_camera.transform);
            }
        }
    }
}