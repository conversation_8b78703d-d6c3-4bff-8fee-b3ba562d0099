using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.MMVibrations;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using UnityEngine;

namespace GameAssets.Scripts.GlobeModal
{
    public class GlobeController : BbbMonoBehaviour
    {
        private static readonly Vector3 FallbackMarkerCoordinates = new(88.97085f, -17.70879f);

        public event Action IntroFinished = delegate { };
        public event Action<string> MarkerSelected = delegate { };
        public event Action<bool> AnyCardShown;

        [SerializeField] private GlobeRotationController _globeRotationController;
        [SerializeField] private GlobeInputController _globeInputController;
        [Space]
        [SerializeField] private Animator _animator;
        [SerializeField] private Camera _mainCamera;
        [SerializeField] private Transform _cameraTarget;
        [SerializeField] private Transform _rotationRoot;
        [SerializeField] private float _yawOffsetCenter;
        [SerializeField] private float _pitchOffsetCenter;
        [SerializeField] private bool _introSkippable = true;
        [SerializeField] float _skipAnimationSpeed;
        [Space]
        [SerializeField] private GameObject _challengeLocationMarkerPrefab;
        [SerializeField] private Transform _challengeLocationMarkersRoot;
        [Space]
        [SerializeField] private GameObject[] _introObjects;
        [Space]
        [SerializeField] private float _globeWidth;
        [SerializeField] private float _selectThreshold;
        [Space]
        [SerializeField] private float _markersVisibilityDot = 0.7f;
        [SerializeField] private LayerMask _selectionLayerMask;
        [SerializeField] private float _markerHeightAboveGlobe = 55f;
        [SerializeField] private float _delayOnGraduallyAppearingOfMarkers = 0.4f;

        private readonly Dictionary<string, Vector3> _defaultMarkerPositionByGroup = new();
        private readonly Dictionary<string, GlobeLocationData> _locationByUid = new();
        private readonly Dictionary<string, ChallengeLocationMarker> _markerByGroupCache = new();
        private readonly List<ChallengeLocationMarker> _activeMarkers = new();
        private readonly HashSet<ChallengeLocationMarker> _visibleMarkers = new();
        private readonly HashSet<string> _cardShownLocations = new();

        private ILocalizationManager _localizationManager;
        private IVibrationsWrapper _vibrationsWrapper;

        private ChallengeLocationMarker _currentlySelectedMarker;

        private bool _markersEnabled;
        private string _currentLocationUid;
        private string _targetLocationUid;
        private bool _isAnimationSkipped;

        //to reset the state on second opening
        private Vector3 _earthAnimInitialPosition;
        private Quaternion _earthAnimInitialRotation;

        public Dictionary<string, GlobeLocationData> LocationByUid => _locationByUid;

        private void Start()
        {
            _earthAnimInitialPosition = _rotationRoot.localPosition;
            _earthAnimInitialRotation = _rotationRoot.localRotation;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            IntroFinished = null;
            MarkerSelected = null;
            AnyCardShown = null;

            foreach (var locationMarker in _markerByGroupCache.Values)
            {
                locationMarker.Selected -= MarkerSelectedHandler;
                locationMarker.CardShown -= CardShownHandler;
            }

            Unsubscribe();
        }

        public void Init(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _vibrationsWrapper = context.Resolve<IVibrationsWrapper>();
            _introObjects.Enable(false);
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _globeRotationController.RotationFinished += RotationFinishedHandler;
            _globeInputController.LocationSelectedAt += SelectLocationAt;
        }

        private void Unsubscribe()
        {
            _globeRotationController.RotationFinished -= RotationFinishedHandler;
            _globeInputController.LocationSelectedAt -= SelectLocationAt;
        }

        public void SetupChallenges(IReadOnlyCollection<ChallengeInfo> challenges)
        {
            _cardShownLocations.Clear();
            _activeMarkers.Clear();

            foreach (var locationMarker in _markerByGroupCache.Values)
            {
                locationMarker.Reset();
            }

            foreach (var location in _locationByUid.Values)
            {
                location.Marker = null;
            }

            foreach (var challengeInfo in challenges)
            {
                var locationConfig = challengeInfo.ChallengeLocationConfig;

                if (locationConfig.DefaultInRegion)
                {
                    var markerPointValue = locationConfig.MarkerPoint.Value;
                    var markerPoint = new Vector3(markerPointValue.X, markerPointValue.Y, markerPointValue.Z);

                    _defaultMarkerPositionByGroup[locationConfig.GroupUid] = markerPoint;
                }
            }

            UnityEngine.Profiling.Profiler.BeginSample($"Globe.InitLocations - LocationMarks");
            foreach (var challengeInfo in challenges)
            {
                var locationConfig = challengeInfo.ChallengeLocationConfig;

                if (!_locationByUid.TryGetValue(locationConfig.Uid, out var location))
                {
                    location = new GlobeLocationData();

                    Vector3 markerPoint;
                    if (locationConfig.MarkerPoint != null)
                    {
                        var markerPointValue = locationConfig.MarkerPoint.Value;
                        markerPoint = new Vector3(markerPointValue.X, markerPointValue.Y, markerPointValue.Z);
                    }
                    else
                    {
                        if (_defaultMarkerPositionByGroup.TryGetValue(locationConfig.GroupUid, out var defaultGroupPosition))
                        {
                            markerPoint = defaultGroupPosition;
                        }
                        else
                        {
                            markerPoint = FallbackMarkerCoordinates;
                            BDebug.LogError(LogCat.Globe, $"MarkerPoint is null for ChallengeLocationConfig: {locationConfig.Uid}");
                        }
                    }

                    location.Yaw = markerPoint.x;
                    location.Pitch = markerPoint.y;
                    location.Position = Quaternion.Euler(location.Pitch, location.Yaw, 0f) * Vector3.forward * _markerHeightAboveGlobe;

                    _locationByUid.Add(locationConfig.Uid, location);
                }

                var groupUid = locationConfig.GroupUid;
                if (_markerByGroupCache.TryGetValue(groupUid, out var locationMarker))
                {
                    locationMarker.transform.localPosition = location.Position;
                }
                else
                {
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_challengeLocationMarkerPrefab.name}]");
                    var go = Instantiate(_challengeLocationMarkerPrefab, location.Position, Quaternion.identity, _challengeLocationMarkersRoot);
                    UnityEngine.Profiling.Profiler.EndSample();

                    go.transform.localPosition = location.Position;
                    locationMarker = go.GetComponent<ChallengeLocationMarker>();
                    locationMarker.Init(_localizationManager, _mainCamera, _vibrationsWrapper);

                    locationMarker.Selected += MarkerSelectedHandler;
                    locationMarker.CardShown += CardShownHandler;

                    _markerByGroupCache.Add(groupUid, locationMarker);
                }

                _activeMarkers.Add(locationMarker);

                locationMarker.Reset();
                locationMarker.gameObject.name = locationConfig.Uid;
                locationMarker.Setup(challengeInfo);
                location.Marker = locationMarker;
            }

            UpdateLocationMarksSelectionState(false);
            UnityEngine.Profiling.Profiler.EndSample();
        }

        private void CardShownHandler(string locationUid, bool shown)
        {
            var anyCardShown = _cardShownLocations.Count > 0;

            if (shown)
            {
                _cardShownLocations.Add(locationUid);
            }
            else
            {
                _cardShownLocations.Remove(locationUid);
            }

            var anyCardShownAfterUpdate = _cardShownLocations.Count > 0;
            if (anyCardShown != anyCardShownAfterUpdate)
            {
                AnyCardShown?.Invoke(anyCardShownAfterUpdate);
            }
        }

        private void MarkerSelectedHandler(string locationUid)
        {
            MarkerSelected?.Invoke(locationUid);
        }

        public void Reset()
        {
            _animator.enabled = false;
            _animator.Rebind();
            _introObjects.Enable(false);
            _currentLocationUid = null;
            _currentlySelectedMarker = null;
            _targetLocationUid = null;
            _isAnimationSkipped = false;

            _rotationRoot.localPosition = _earthAnimInitialPosition;
            _rotationRoot.localRotation = _earthAnimInitialRotation;
        }

        public void RotateToLocationWithSpeed(string uid, float yawSmoothTime, float pitchSmoothTime)
        {
            if (_locationByUid.TryGetValue(uid, out var location))
            {
                _targetLocationUid = uid;
                _globeRotationController.RotateToCoordsWithSpeed(location.Yaw + _yawOffsetCenter, location.Pitch + _pitchOffsetCenter,
                    yawSmoothTime, pitchSmoothTime, _targetLocationUid);
            }

            UpdateLocationMarksSelectionState(true);
        }

        public void RotateToLocation(string uid)
        {
            if (!_locationByUid.ContainsKey(uid))
            {
                BDebug.LogError(LogCat.General, $"Couldn't find location with uid: {uid}, please report full stack trace");
                return;
            }

            _targetLocationUid = uid;
            _globeRotationController.RotateToCoords(_locationByUid[uid].Yaw + _yawOffsetCenter, _locationByUid[uid].Pitch + _pitchOffsetCenter, _targetLocationUid);

            UpdateLocationMarksSelectionState(true);
        }

        public void SetTargetLocation(string uid)
        {
            if (!_locationByUid.ContainsKey(uid))
            {
                BDebug.LogError(LogCat.General, $"Couldn't find location with uid: {uid}, please report full stack trace");
                return;
            }

            _targetLocationUid = uid;
            _currentLocationUid = uid;
            UpdateLocationMarksSelectionState(false);
        }

        public void EnableMarkersGradually()
        {
            _visibleMarkers.Clear();
            SetMarkersEnabled(true);

            GradualAppearing();

            async void GradualAppearing()
            {
                foreach (var marker in _activeMarkers)
                {
                    _visibleMarkers.Add(marker);
                    await UniTask.Delay(Mathf.RoundToInt(_delayOnGraduallyAppearingOfMarkers * 1000));
                }

                _visibleMarkers.Clear();
            }
        }

        public void SetMarkersEnabled(bool markersEnabled)
        {
            _markersEnabled = markersEnabled;
        }

        public void PlayIntro(string locationUid)
        {
            _globeRotationController.ResetTargetZoomToDefault();
            _currentLocationUid = locationUid;
            _introObjects.Enable(true);
            _animator.enabled = true;

            if (_locationByUid.TryGetValue(locationUid, out var location))
            {
                // ignoring pitch for better intro animation
                _globeRotationController.SetTargetCoordOffset(location.Yaw, 0f);
            }
        }

        public void SkipAnimation()
        {
            if (!_introSkippable)
                return;

            if (_isAnimationSkipped)
                return;

            _animator.speed = _skipAnimationSpeed;
            _isAnimationSkipped = true;
        }

        public Vector2 GetShownPlayerScreenPoint()
        {
            if (_currentlySelectedMarker == null)
                return new Vector2(Screen.width / 2, Screen.height / 2);

            return _mainCamera.WorldToScreenPoint(_currentlySelectedMarker.GetUserAvatarPosition());
        }

        [UsedImplicitly]
        private void OnIntroAnimFinished()
        {
            _animator.enabled = false;
            _globeRotationController.ResetTargetZoomToDefault();
            _globeRotationController.ResetTargetCoordOffset();

            _rotationRoot.rotation = Quaternion.identity;
            FinishIntro();
        }

        private void FinishIntro()
        {
            _introObjects.Enable(false);
            _animator.enabled = false;
            IntroFinished();
        }

        private float SphericalDistance(Vector3 a, Vector3 b)
        {
            return Mathf.Acos(Vector3.Dot(a.normalized, b.normalized)) * _globeWidth;
        }

        private void SelectLocationAt(Vector2 position)
        {
            string selectedLocationUid = null;

            var ray = _mainCamera.ScreenPointToRay(position);
            if (Physics.Raycast(ray, out var hit, Mathf.Infinity, _selectionLayerMask))
            {
                var minDist = float.MaxValue;
                foreach (var (locationUid, location) in _locationByUid)
                {
                    var distance = SphericalDistance(location.Marker.transform.position, hit.point);
                    if (distance < minDist && distance < _selectThreshold)
                    {
                        minDist = distance;
                        selectedLocationUid = locationUid;
                    }
                }
            }

            if (!selectedLocationUid.IsNullOrEmpty())
            {
                MarkerSelected?.Invoke(selectedLocationUid);
            }
        }

        public void UpdateLocationMarksSelectionState(bool showCard)
        {
            UnityEngine.Profiling.Profiler.BeginSample("Globe.InitLocations - Update LocationMarks");
            if (_targetLocationUid != null && _locationByUid.TryGetValue(_targetLocationUid, out var targetLocation) && targetLocation.Marker != null)
            {
                targetLocation.Marker.SetShowCard(showCard);
                if (_currentlySelectedMarker != targetLocation.Marker)
                {
                    _currentlySelectedMarker = targetLocation.Marker;
                    _currentlySelectedMarker.TriggerSelection();
                }
            }
            
            foreach (var (locationUid, location) in _locationByUid)
            {
                if (location.Marker == null || locationUid == _targetLocationUid)
                    continue;

                location.Marker.SetShowCard(false);
            }

            UnityEngine.Profiling.Profiler.EndSample();
        }

        private void RotationFinishedHandler(string locationUid, bool canceled)
        {
            if (locationUid != _targetLocationUid)
                return;

            if (canceled)
            {
                _targetLocationUid = _currentLocationUid;
            }
            else
            {
                _currentLocationUid = _targetLocationUid;
            }
        }

        private void Update()
        {
            if (!_markersEnabled)
            {
                foreach (var marker in _activeMarkers)
                {
                    marker.SetVisible(false);
                }
            }
            else
            {
                var cameraDirection = (_mainCamera.transform.position - _cameraTarget.position).normalized;

                foreach (var marker in _activeMarkers)
                {
                    if (_visibleMarkers.Count > 0 && !_visibleMarkers.Contains(marker))
                    {
                        marker.SetVisible(false);
                        continue;
                    }

                    var markerToCameraDirection = (marker.transform.position - _cameraTarget.position).normalized;
                    marker.SetVisible(Vector3.Dot(markerToCameraDirection, cameraDirection) > _markersVisibilityDot);
                }
            }
        }
    }
}