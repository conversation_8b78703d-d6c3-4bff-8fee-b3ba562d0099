using System;
using System.Collections.Generic;
using System.Globalization;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.Screens;
using BBB.UI;
using BebopBee.Core;
using Bebopbee.Core.Extensions.Unity;
using DG.Tweening;
using GameAssets.Scripts.Core;
using Spine;
using Spine.Unity;
using UI;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.GlobeModal
{
    public class GlobeViewPresenter : ModalsViewPresenter, IGlobeViewPresenter
    {
        private static readonly int ShowSwipeTooltip = Animator.StringToHash("ShowSwipeTooltip");
        private static readonly int StopSwipeTooltip = Animator.StringToHash("StopSwipeTooltip");

        private const string LastArrowPromptTimeKey = "LastArrowPromptTime";
        private const int SpineBaseTrackIndex = 0;

        private static readonly HashSet<string> PrioritizedFirstSelectedGroups = new() { "EU", "AF" };
        public event Action<ChallengeInfo> SendChallengeButton;
        public event Action OtherPlayersButton;
        public event Action OnCancelButton;

        [SerializeField] private GlobeController _globeController;
        [SerializeField] private GlobeInputController _globeInputController;
        [SerializeField] private GlobeRotationController _globeRotationController;
        [SerializeField] private AsyncAvatar _sendChallengeAvatar;
        [SerializeField] private Button _sendChallengeButton;
        [SerializeField] private Button _otherPlayersButton;
        [SerializeField] private Button _cancelButton;

        [SerializeField] private GameObject[] _toHideOnRotationHolders;
        [SerializeField] private GameObject[] _challengeSentHolders;
        [SerializeField] private SkeletonGraphic _characterSkeleton;
        [SerializeField] private string _characterAppearAnimationName;
        [SerializeField] private string _characterIdleAnimationName;
        [SerializeField] private string _characterHideAnimationName;
        [SerializeField] private string _characterChallengeSentAnimationName;
        [SerializeField] private string _giftThrowStartEventName;
        [SerializeField] private string _giftThrowEndEventName;
        [SerializeField] private RectTransform _giftAnimationParent;
        [SerializeField] private RectTransform _giftIdleParent;
        [SerializeField] private RectTransform _giftTargetPoint;
        [SerializeField] private RectTransform _giftRoot;
        [SerializeField] private RectTransform _giftFx;
        [SerializeField] private FloatingTextEffect _floatingTextEffect;
        [SerializeField] private Transform _floatingTextAnchor;
        [SerializeField] private GenericSpeechBubble _characterSpeechBubble;

        [SerializeField] private float _safeRewardDelayTime = 0.7f;
        [SerializeField] private float _challengeSentDelay = 2f;

        [SerializeField] private float _introFinishedSmoothTime = 0.5f;
        [SerializeField] private float _rotateSmoothTimeOnSelection = 0.3f;
        [SerializeField] private float _preIntroZoom = 300f;

        [SerializeField] private float _maxRotationAngleToKeepSelectionSqr = 900f;

        [SerializeField] private Transform _rootEnvironment;
        [SerializeField] private GameObject _loadingScreenHolder;
        [SerializeField] private GameObject _touchBlocker;
        [SerializeField] private Animator _animator;

        private readonly List<ChallengeInfo> _displayedChallenges = new();
        private readonly Dictionary<string, ChallengeInfo> _displayedChallengeByGroup = new();

        private bool _transitionIsHappening;
        private string _currentlySelectedLocation;
        private ChallengeInfo _currentlySelectedChallenge;

        private bool _characterShown;
        private bool _characterForceShown;
        private float _safeTimeToReward;
        private bool _canStopSwipeAnimation;
        private bool _shouldTrackMarkersPositions;
        private Tweener _rewardWaitTweener;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _globeController.Init(context);
            _globeRotationController.Init();
            _globeInputController.Init(context.Resolve<IOrientationTracker>());

            _sendChallengeButton.ReplaceOnClick(SendChallengeButtonHandler);
            _otherPlayersButton.ReplaceOnClick(OtherPlayersButtonHandler);
            _cancelButton.ReplaceOnClick(CancelButtonHandler);
        }

        public void Setup(List<ChallengeInfo> challenges)
        {
            _displayedChallenges.Clear();
            _displayedChallengeByGroup.Clear();

            foreach (var challengeInfo in challenges)
            {
                var groupUid = challengeInfo.ChallengeLocationConfig.GroupUid;
                if (_displayedChallengeByGroup.ContainsKey(groupUid))
                {
                    BDebug.LogError(LogCat.Globe, $"Attempt to add challenge with already occupied group: {groupUid}, it will be skipped");
                    continue;
                }

                _displayedChallenges.Add(challengeInfo);
                _displayedChallengeByGroup.Add(groupUid, challengeInfo);
            }

            _globeController.SetupChallenges(_displayedChallenges);

            // according to design we need to select random marker from one of the predefined groups (eu or af) because they are in the middle
            // as a fallback using first item if we get not expected distribution of data from server
            var selectedLocation = _displayedChallenges[0].ChallengeLocationConfig.Uid;
            foreach (var challengeInfo in _displayedChallenges)
            {
                if (PrioritizedFirstSelectedGroups.Contains(challengeInfo.ChallengeLocationConfig.GroupUid))
                {
                    selectedLocation = challengeInfo.ChallengeLocationConfig.Uid;
                    break;
                }
            }

            _globeController.PlayIntro(selectedLocation);
            SetCurrentlySelectedLocation(selectedLocation);
        }

        public void SelectChallenge(ChallengeInfo challengeInfo)
        {
            foreach (var challenge in _displayedChallenges)
            {
                if (challenge.PlayerUid != challengeInfo.PlayerUid) continue;

                SelectLocation(challenge.ChallengeLocationConfig.Uid);
                return;
            }

            var groupUid = challengeInfo.ChallengeLocationConfig.GroupUid;
            if (_displayedChallengeByGroup.ContainsKey(groupUid))
            {
                if (_displayedChallengeByGroup[groupUid].PlayerUid == challengeInfo.PlayerUid)
                    return;

                _displayedChallenges.Remove(_displayedChallengeByGroup[groupUid]);
                _displayedChallenges.Add(challengeInfo);
                _displayedChallengeByGroup[groupUid] = challengeInfo;
            }
            else
            {
                _displayedChallenges.Add(challengeInfo);
                _displayedChallengeByGroup[groupUid] = challengeInfo;
            }

            _globeController.SetupChallenges(_displayedChallenges);
            SelectLocation(challengeInfo.ChallengeLocationConfig.Uid);
        }

        public void ShowLoadingScreen(bool show)
        {
            _loadingScreenHolder.SetActive(show);
        }

        public void SetChallengeSentState(Action animationCompletedCallback)
        {
            _globeInputController.SetInteractable(false);
            _toHideOnRotationHolders.Enable(false);
            _challengeSentHolders.Enable(true);
            _touchBlocker.SetActive(true);

            if (Time.time < _safeTimeToReward)
            {
                ResetRewardWaitTweener();
                _rewardWaitTweener = Rx.Invoke(_safeTimeToReward - Time.time, _ => PlayRewardAnimation());
            }
            else
            {
                PlayRewardAnimation();
            }

            void PlayRewardAnimation()
            {
                PlayCharacterAnimation(_characterChallengeSentAnimationName, false);
                AddCharacterAnimation(_characterIdleAnimationName, true);
                ResetRewardWaitTweener();
                _rewardWaitTweener = Rx.Invoke(_challengeSentDelay, _ =>
                {
                    _giftRoot.gameObject.SetActive(false);
                    animationCompletedCallback?.Invoke();
                });
            }
        }

        private void StartGiftAnimation()
        {
            _giftRoot.SetParent(_giftAnimationParent);
            _giftRoot.localPosition = Vector3.zero;
            _giftRoot.localScale = Vector3.one / _characterSkeleton.transform.localScale.x;
            _giftRoot.localRotation = Quaternion.identity;

            _giftRoot.gameObject.SetActive(true);
            _giftFx.gameObject.SetActive(false);

            var targetScreenPosition = _globeController.GetShownPlayerScreenPoint();
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(_giftTargetPoint.parent as RectTransform, targetScreenPosition, null, out var localPoint))
            {
                _giftTargetPoint.localPosition = localPoint;
            }
        }

        private void EndGiftAnimation()
        {
            _giftRoot.SetParent(_giftIdleParent);
            _giftRoot.localScale = Vector3.one;
            _giftFx.gameObject.SetActive(true);
        }

        private void PlayCharacterAnimation(string animationName, bool loop)
        {
            if (_characterSkeleton == null)
                return;

            _characterSkeleton.Initialize(true);
            // initialize resets subscriptions
            _characterSkeleton.AnimationState.Event += CharacterEventHandler;

            _characterSkeleton.AnimationState.SetAnimation(SpineBaseTrackIndex, animationName, loop);
        }

        private void AddCharacterAnimation(string animationName, bool loop, float delay = 0)
        {
            if (_characterSkeleton == null)
                return;

            _characterSkeleton.AnimationState.AddAnimation(SpineBaseTrackIndex, animationName, loop, delay);
        }

        private void CharacterEventHandler(TrackEntry trackEntry, Spine.Event e)
        {
            var eventName = e.Data.Name;
            if (eventName == _giftThrowStartEventName)
            {
                StartGiftAnimation();
            }

            if (eventName == _giftThrowEndEventName)
            {
                EndGiftAnimation();
            }
        }

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();

            _globeController.Reset();
            _globeInputController.Reset();
            _globeRotationController.Reset();
            _globeRotationController.ZoomIn(_preIntroZoom, true);

            _currentlySelectedLocation = string.Empty;
            _currentlySelectedChallenge = default;
            _globeInputController.SetInteractable(false);

            _challengeSentHolders.Enable(false);
            _toHideOnRotationHolders.Enable(false);
            _globeController.SetMarkersEnabled(false);
            _touchBlocker.SetActive(false);

            _rootEnvironment.SetParent(null);
            _rootEnvironment.localPosition = Vector3.zero;
            _rootEnvironment.localScale = Vector3.one;

            _giftRoot.gameObject.SetActive(false);
            _canStopSwipeAnimation = false;
            _shouldTrackMarkersPositions = false;

            HideCharacter(true);
            ShowCharacter(false);
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();

            _rootEnvironment.SetParent(transform);
            _loadingScreenHolder.SetActive(false);

            _animator.ResetTrigger(ShowSwipeTooltip);
            _animator.ResetTrigger(StopSwipeTooltip);

            ResetRewardWaitTweener();
        }

        private void ResetRewardWaitTweener()
        {
            if (_rewardWaitTweener == null) return;

            _rewardWaitTweener.Kill();
            _rewardWaitTweener = null;
        }

        private void Subscribe()
        {
            Unsubscribe();

            _globeInputController.NonInteractableInput += NonInteractableInputHandler;
            _globeInputController.Rotated += RotatedHandler;
            _globeInputController.TouchingEnded += TouchingEndedHandler;

            _globeController.IntroFinished += IntroFinishedHandler;
            _globeController.MarkerSelected += MarkerSelectedHandler;
            _globeController.AnyCardShown += AnyCardShownHandler;
        }

        private void Unsubscribe()
        {
            _globeInputController.NonInteractableInput -= NonInteractableInputHandler;
            _globeInputController.Rotated -= RotatedHandler;
            _globeInputController.TouchingEnded -= TouchingEndedHandler;

            _globeController.IntroFinished -= IntroFinishedHandler;
            _globeController.MarkerSelected -= MarkerSelectedHandler;
            _globeController.AnyCardShown -= AnyCardShownHandler;

            _characterSkeleton.AnimationState.Event -= CharacterEventHandler;
        }

        private void SendChallengeButtonHandler()
        {
            StopSwipeAnimation();

            if (TryShowNoConnectionFloatingText())
                return;

            _safeTimeToReward = Time.time + (_characterShown ? _rotateSmoothTimeOnSelection : Mathf.Max(_rotateSmoothTimeOnSelection, _safeRewardDelayTime));
            ShowCharacter(true);
            _characterSpeechBubble.gameObject.SetActive(false);
            _globeController.RotateToLocationWithSpeed(_currentlySelectedLocation, _rotateSmoothTimeOnSelection, _rotateSmoothTimeOnSelection);
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Didi.Name, DauInteractions.Didi.SendScreen, DauInteractions.Didi.Send));
            SendChallengeButton?.Invoke(_currentlySelectedChallenge);
        }

        private void OtherPlayersButtonHandler()
        {
            StopSwipeAnimation();

            if (TryShowNoConnectionFloatingText())
                return;

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Didi.Name, DauInteractions.Didi.SendScreen,
                DauInteractions.Didi.OtherPlayers));
            OtherPlayersButton?.Invoke();
        }

        private bool TryShowNoConnectionFloatingText()
        {
            if (ConnectivityStatusManager.ConnectivityReachable) return false;

            _floatingTextEffect.PlayFloatingAnimation(_floatingTextAnchor);
            return true;
        }

        private void CancelButtonHandler()
        {
            OnCancelButton?.Invoke();
        }

        private void RotatedHandler(float x, float y)
        {
            _shouldTrackMarkersPositions = true;

            StopSwipeAnimation();

            _globeController.UpdateLocationMarksSelectionState(false);
            _toHideOnRotationHolders.Enable(false);
        }

        private void Update()
        {
            // We can't rely on RotatedHandler because it is input driven and we have non tirvial inertia system
            // In most of the cases this check will end on base math check for _maxRotationAngleToKeepSelection tolerance,
            // so no nested iterations will happen
            if (_shouldTrackMarkersPositions)
            {
                const float fullDegree = 360f;

                // check currently selected, if moved too far - switch to closest to current center
                var currentLocation = _globeController.LocationByUid[_currentlySelectedLocation];
                var yaw = NormalizeAngle(_globeRotationController.Yaw);
                var pitch = NormalizeAngle(_globeRotationController.Pitch);

                var nearestLocation = _currentlySelectedLocation;

                float NormalizeAngle(float angle)
                {
                    angle %= fullDegree;
                    if (angle < 0)
                        angle += fullDegree;

                    return angle;
                }

                float GetMagnitude(GlobeLocationData location)
                {
                    var yawDifference = Mathf.Abs(yaw - NormalizeAngle(location.Yaw));
                    yawDifference = Math.Min(yawDifference, fullDegree - yawDifference);

                    var pitchDifference = Mathf.Abs(pitch - NormalizeAngle(location.Pitch));
                    pitchDifference = Math.Min(pitchDifference, fullDegree - pitchDifference);

                    return yawDifference * yawDifference + pitchDifference * pitchDifference;
                }

                var currentMagnitude = GetMagnitude(currentLocation);
                if (currentMagnitude > _maxRotationAngleToKeepSelectionSqr)
                {
                    var minDistance = currentMagnitude;

                    foreach (var (locationUid, location) in _globeController.LocationByUid)
                    {
                        if (location.Marker == null)
                            continue;

                        if (_currentlySelectedLocation == locationUid)
                            continue;

                        currentMagnitude = GetMagnitude(location);

                        if (currentMagnitude < minDistance)
                        {
                            minDistance = currentMagnitude;
                            nearestLocation = locationUid;
                        }
                    }
                }

                if (_currentlySelectedLocation != nearestLocation)
                {
                    SetCurrentlySelectedLocation(nearestLocation);
                    _globeController.SetTargetLocation(nearestLocation);
                }
            }
        }

        private void TouchingEndedHandler()
        {
            if (_touchBlocker.activeSelf) return;

            _toHideOnRotationHolders.Enable(true);
        }

        private void NonInteractableInputHandler()
        {
            _globeController.SkipAnimation();
        }

        private void IntroFinishedHandler()
        {
            if (ShouldShowSwipeAnimation())
            {
                _animator.SetTrigger(ShowSwipeTooltip);
                UpdateSwipeAnimationLastShownTime();
                _canStopSwipeAnimation = true;
            }

            _globeInputController.SetInteractable(true);
            _globeController.RotateToLocationWithSpeed(_currentlySelectedLocation, _introFinishedSmoothTime, _introFinishedSmoothTime);
            _globeController.EnableMarkersGradually();
            _toHideOnRotationHolders.Enable(true);
        }

        protected override void OnTransitionStarted()
        {
            _transitionIsHappening = true;
        }

        protected override void OnTransitionCompleted()
        {
            _transitionIsHappening = false;
        }

        private void MarkerSelectedHandler(string locationUid)
        {
            if (_transitionIsHappening)
                return;

            StopSwipeAnimation();

            if (locationUid.IsNullOrEmpty())
                return;

            SelectLocation(locationUid);
        }

        private void AnyCardShownHandler(bool shown)
        {
            if (shown)
            {
                HideCharacter(false);
            }
            else
            {
                ShowCharacter(false);
            }
        }

        private void SelectLocation(string locationUid)
        {
            _shouldTrackMarkersPositions = false;

            _globeInputController.Reset();
            _globeController.RotateToLocation(locationUid);
            SetCurrentlySelectedLocation(locationUid);
        }

        private void SetCurrentlySelectedLocation(string locationUid)
        {
            _currentlySelectedLocation = locationUid;
            _currentlySelectedChallenge = default;

            foreach (var challengeInfo in _displayedChallenges)
            {
                if (challengeInfo.ChallengeLocationConfig.Uid == locationUid)
                {
                    _currentlySelectedChallenge = challengeInfo;
                    break;
                }
            }

            // should be impossible in real scenario, just for safety
            if (_currentlySelectedChallenge.PlayerUid.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Globe, $"Couldn't find displayed challenge with location uid: {_currentlySelectedLocation}");
            }
            else
            {
                _sendChallengeAvatar.Setup(new AvatarInfo(_currentlySelectedChallenge.AvatarUrl, _currentlySelectedChallenge.ChallengeLocationConfig.CountryCode));
            }
        }

        private void ShowCharacter(bool force)
        {
            if (force)
            {
                _characterForceShown = true;
            }

            if (_characterShown)
                return;

            _characterSpeechBubble.gameObject.SetActive(true);
            _characterSpeechBubble.ShowSpeechBubble();

            _characterSkeleton?.gameObject.SetActive(true);
            PlayCharacterAnimation(_characterAppearAnimationName, false);
            AddCharacterAnimation(_characterIdleAnimationName, true);
            _characterShown = true;
        }

        private void HideCharacter(bool force)
        {
            if (_characterForceShown && !force)
                return;

            if (force)
            {
                _characterSpeechBubble.gameObject.SetActive(false);
                _characterSkeleton?.gameObject.SetActive(false);
                _characterShown = false;
                _characterForceShown = false;
                return;
            }

            if (!_characterShown)
                return;

            _characterSpeechBubble.HideSpeechBubble();
            PlayCharacterAnimation(_characterHideAnimationName, false);
            _characterShown = false;
        }

        private bool ShouldShowSwipeAnimation()
        {
            if (!PlayerPrefs.HasKey(LastArrowPromptTimeKey))
                return true;

            var lastPromptTime = long.Parse(PlayerPrefs.GetString(LastArrowPromptTimeKey));
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            return currentTime - lastPromptTime >= TimeSpan.FromHours(24).TotalSeconds;
        }

        private void UpdateSwipeAnimationLastShownTime()
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            PlayerPrefs.SetString(LastArrowPromptTimeKey, currentTime.ToString(CultureInfo.InvariantCulture));
            PlayerPrefs.Save();
        }

        private void StopSwipeAnimation()
        {
            if (!_canStopSwipeAnimation)
                return;

            _animator.SetTrigger(StopSwipeTooltip);
            _canStopSwipeAnimation = false;
        }
    }
}