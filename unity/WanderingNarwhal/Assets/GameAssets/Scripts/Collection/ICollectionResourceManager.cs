using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.U2D;

namespace GameAssets.Scripts.Collection
{
    public interface ICollectionResourceManager
    {
        bool IsLoaded { get; }
        Sprite GetSprite(string spriteName);
        Sprite GetSetIcon(string setId);
        UniTask<Sprite> GetCardIconAsync(string setId, string spriteName);
        UniTask<SpriteAtlas> LoadCardsAtlas(string setId);
        void UnloadResources();
        void PreWarm();
    }
}
