using System;
using BBB;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace GameAssets.Scripts.Collection.UI
{
    public class WildCardButtonWidget : BbbMonoBehaviour
    {
        [SerializeField] private Image _icon;
        [SerializeField] private ParticleSystem _shineEffect;
        [SerializeField] private Canvas _canvas;
        [SerializeField] private Button _wildCardButton;
        private Transform _transformParent;

        public void ActivateCanvas()
        {
            _canvas.overrideSorting = true;
        }

        public void PutOnHUD(Transform trans)
        {
            _transformParent = transform.parent;
            transform.SetParent(trans);
            _shineEffect.gameObject.SetActive(true);
            _icon.raycastTarget = false;
        }

        public void PutBack()
        {
            _shineEffect.gameObject.SetActive(false);
            _canvas.overrideSorting = false;
            transform.SetParent(_transformParent);
            _icon.raycastTarget = true;
        }

        public void ReplaceOnClick(UnityAction action)
        {
            _wildCardButton.ReplaceOnClick(action);
        }
    }
}
