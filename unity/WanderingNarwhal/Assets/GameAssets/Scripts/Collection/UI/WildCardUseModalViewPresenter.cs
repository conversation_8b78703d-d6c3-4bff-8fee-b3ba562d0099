using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Screens;
using BBB.UI;
using BebopBee.UnityEngineExtensions;
using Cysharp.Threading.Tasks;
using FBConfig;
using UnityEngine;
using UnityEngine.Events;
using Button = UnityEngine.UI.Button;

namespace GameAssets.Scripts.Collection.UI
{
    public class WildCardUseModalViewPresenter : ModalsViewPresenter, IWildCardUseModalViewPresenter
    {
        [SerializeField] private GameObject _lockedCardsPrefab;
        [SerializeField] private Button _useButton;
        [SerializeField] private MaterialSwapper _useButtonMaterialSwapper;
        [SerializeField] private Transform _scrimTransform;
        [SerializeField] private Transform _content;
        private static readonly int Enabled = Animator.StringToHash("Enabled");
        private static readonly int Disabled = Animator.StringToHash("Disabled");
        private Animator _buttonAnimator;
        private ICollectionResourceManager _collectionResourceManager;
        private Dictionary<CollectionSetConfigT, List<CollectionCardsConfigT>> _lockedCards;
        private readonly Dictionary<string, LockedCardsWidget> _lockedCardsWidgets = new();
        private ICollectionManager _collectionManager;
        private WildCardButtonWidget _wildCardButtonWidget;

        public event UnityAction OnUseButton;
        public event Action<string> OnCardItemPressed; 

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _useButtonMaterialSwapper.StoreOriginalValues();
            _buttonAnimator ??= _useButton.GetComponent<Animator>();
            _collectionResourceManager = context.Resolve<ICollectionResourceManager>();
            _collectionManager = context.Resolve<ICollectionManager>();
        }

        public async UniTask SetupAsync(Dictionary<CollectionSetConfigT, List<CollectionCardsConfigT>> lockedCards)
        {
            _lockedCards = lockedCards;

            foreach (var (setConfig, cards) in _lockedCards)
            {
                var setId = setConfig.Uid;
                if (cards.Count == 0)
                {
                    if (_lockedCardsWidgets.TryGetValue(setId, out var widget))
                    {
                        Destroy(widget.gameObject);
                        _lockedCardsWidgets.Remove(setId);
                    }
                    continue;
                }
                if (!_lockedCardsWidgets.TryGetValue(setId, out var lockedCardsWidget))
                {
                    lockedCardsWidget = Instantiate(_lockedCardsPrefab).GetComponent<LockedCardsWidget>();
                    _lockedCardsWidgets.Add(setId, lockedCardsWidget);
                    lockedCardsWidget.Init(_collectionResourceManager);
                }
                lockedCardsWidget.transform.SetParent(_content);
                lockedCardsWidget.transform.localScale = Vector3.one;
                await LockedWidgetSetupAsync(lockedCardsWidget, setConfig, cards);
            }
        }
        
        private async UniTask LockedWidgetSetupAsync(LockedCardsWidget widget, CollectionSetConfigT setConfig, List<CollectionCardsConfigT> cards)
        {
            var isValid = await widget.SetupAsync(setConfig, cards, _collectionManager);
            if (!isValid)
            {
                _lockedCardsWidgets.Remove(setConfig.Uid);
                Destroy(widget.gameObject);
                return;
            }
            widget.OnCardItemPressed -= OnCardPressed;
            widget.OnCardItemPressed += OnCardPressed;
        }

        public void EnableUseButton()
        {
            _useButton.ReplaceOnClick(OnUseButton);
            _buttonAnimator.ResetAllParameters();
            _buttonAnimator.SetTrigger(Enabled);
            _useButton.interactable = true;
            _useButtonMaterialSwapper.SetOriginalValue();
        }

        public void DisableUseButton()
        {
            _buttonAnimator.ResetAllParameters();
            _buttonAnimator.SetTrigger(Disabled);
            _useButton.interactable = false;
            _useButtonMaterialSwapper.SetNewValue();
        }

        public void ShowWildCardIconWithEffect(WildCardButtonWidget wildCardButtonWidget)
        {
            _wildCardButtonWidget = wildCardButtonWidget;
            _wildCardButtonWidget.PutOnHUD(transform.parent);
        }

        private void OnCardPressed(string cardId)
        {
            foreach (var cardsWidget in _lockedCardsWidgets.Values)
            {
                cardsWidget.DeselectCurrentCard(cardId);
            }
            OnCardItemPressed?.Invoke(cardId);
        }

        private void Unsubscribe()
        {
            foreach (var lockedCardsWidget in _lockedCardsWidgets.Values)
            {
                lockedCardsWidget.OnHide();
                lockedCardsWidget.OnCardItemPressed -= OnCardPressed;
            }
        }
        
        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _wildCardButtonWidget.PutBack();
            _wildCardButtonWidget = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
        }
    }
}
