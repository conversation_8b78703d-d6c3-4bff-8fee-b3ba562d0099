using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;

namespace GameAssets.Scripts.Collection.UI
{
    public class CollectionIntroModalController : BaseModalsController<ICollectionIntroModalViewPresenter>
    {
        private ICollectionManager _collectionManager;
        private ICollectionResourceManager _collectionResourceManager;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _collectionManager = context.Resolve<ICollectionManager>();
            _collectionResourceManager = context.Resolve<ICollectionResourceManager>();
        }

        public override void OnShow()
        {
            base.OnShow();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.CollectionIntro, DauInteractions.AutoPopups.Intro));
            _collectionResourceManager.PreWarm();
            Subscribe();
        }

        public override void OnHide()
        {
            base.OnHide();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnAutoPopups.Name, DauInteractions.TapOnAutoPopups.CollectionIntroClicks, DauInteractions.AutoPopups.Intro));
            _collectionManager.OnIntroModalClosed();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnViewHide += ViewHideHandler;
        }

        private void Unsubscribe()
        {
            View.OnViewHide -= ViewHideHandler;
        }

        private void ViewHideHandler()
        {
            base.OnPostHide();
            Unsubscribe();
        }
    }
}
