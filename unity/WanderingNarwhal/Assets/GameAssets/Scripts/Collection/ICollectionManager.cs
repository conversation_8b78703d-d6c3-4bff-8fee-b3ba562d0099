using System;
using System.Collections.Generic;
using BBB;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Collection.UI;
using UnityEngine;

namespace GameAssets.Scripts.Collection
{
    public interface ICollectionManager
    {
        Dictionary<CollectionCardRarity, int> WildCardTokenAmountByRarity { get; }
        int WildCardTokenAmount { get; }
        int WildCardTokenAmountDelta { get; }
        bool ShouldShowIntro { get; }
        bool IsResourcesLoaded { get; }
        ISpecializedResourceManager CollectionResourceProvider { get;}
        void ShowIntroModal(Action onIntroHide);
        void ShowWildCardTokenModal();
        bool CanUnlockSet(string setUid);
        void ShowCollectionsModal();
        void HideCollectionModal();
        void GetProgress(string setUid, out int currentProgress, out int targetProgress);
        void OnWildCardTokenModalClosed();
        void OnIntroModalClosed();
        UniTask StartCardsRewardAnimation();
        void ResetWildCardTokenDelta();
        UniTask ShowWildCardModalAsync(WildCardButtonWidget wildCardButton);
        void OnWildCardUseClosed();
        void UseWildCard(Transform wildCardIcon);
        void SelectedForWildCard(string cardId);
        void MarkSetAsSeen(string setUid);
        int CountNewCardsInSet(string setUid);
        event Action<string> CollectionSetChanged;
        bool IsClaimed(string setUid);
        void SetClaimed(string setUid);
        IEnumerable<string> GetCardsForSet(string setUid);
        bool IsCollected(string cardUid, out bool isNew);
        void MarkCardAsSeen(string setUid, string cardUid);
        void ShowWildCardRewardScreen(WildCardItem wildCard);
        string GetSetUnlockLevel(string setUid);
        bool HasCardPackReward(Dictionary<string,int> rewards, out string currencyUid);
        bool IsCollectionReward(string uid);
        UniTask ShowCardsRewardScreenAsync(Action onStart, Action onComplete);
        bool IsSetStateSeen(string setUid);
        void MarkSetStateAsSeen(string setUid);
        HashSet<CollectionCardsConfigT> GetCardsOfSet(string setUid);
        bool IsComingSoon(string setId);
        int GetCompletedSetsCount();
        int TotalWildCards { get; }
    }
}
