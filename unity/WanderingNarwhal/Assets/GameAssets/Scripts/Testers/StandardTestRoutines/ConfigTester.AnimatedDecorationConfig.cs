using System.Collections;
using PBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private IEnumerator TestAnimatedDecorationConfig()
        {
            var configName = "AnimatedDecorationConfig";
            Debug.LogWarningFormat("Checking {0}", configName);
            var animatedDecorationConfig = _config.Get<AnimatedDecorationConfig>();
            var locationConfig = _config.Get<FBConfig.LocationConfig>();
            
            var index = 0f;
            foreach (var entry in animatedDecorationConfig.Values)
            {
                var uid = entry.Uid;

                if (entry.LocationUid.IsNullOrEmpty())
                {
                    LogError("LocationUid is not set for AnimatedDecorationConfig {0}", uid);
                }
                else
                {
                    if (!locationConfig.ContainsKey(entry.LocationUid))
                    {
                        LogError("Location {0} from AnimatedDecorationConfig {1} was not found in LocationConfig", entry.LocationUid, uid);
                    }
                }
                
                if (entry.Position == null)
                {
                    LogError("Position is not set for AnimatedDecorationConfig {0}", uid);
                }
                
                if (entry.Size == null)
                {
                    LogError("Size is not set for AnimatedDecorationConfig {0}", uid);
                }
                
                if (entry.VisualBoxSize == null)
                {
                    LogError("VisualBoxSize is not set for AnimatedDecorationConfig {0}", uid);
                }
                
                if (entry.Prefab.IsNullOrEmpty())
                {
                    LogError("Prefab is not set for AnimatedDecorationConfig {0}", uid);
                }

                if (entry.AnimationData == null)
                {
                    LogError("AnimationData is not set for AnimatedDecorationConfig {0}", uid);
                }
                else
                {
                    if (entry.AnimationData.StartAnimation.IsNullOrEmpty())
                    {
                        LogError("StartAnimation is not set for AnimationData in AnimatedDecorationConfig {0}", uid);
                    }
                }

                _progressCallback.SafeInvoke(index / animatedDecorationConfig.Values.Count, configName);
                index++;
                yield return null;
            }
        }
    }
}