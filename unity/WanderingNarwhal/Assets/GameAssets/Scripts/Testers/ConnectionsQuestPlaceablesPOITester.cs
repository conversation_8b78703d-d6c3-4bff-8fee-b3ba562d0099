using Core.Configs;
using PBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public static class ConnectionsQuestPlaceablesPOITester
    {
        public static void Test(IConfig configs)
        {
            var questConfig = configs.Get<FBConfig.QuestConfig>();
            var dailyEventConfig = configs.Get<DailyEventConfig>();
            var mapPlaceableConfig = configs.Get<FBConfig.MapPlaceableConfigT>();
            var mapBackgroundPlaceableConfig = configs.Get<MapBackgroundMaskConfig>();
            var poiConfig = configs.Get<FBConfig.POIEntityConfigT>();

            Debug.Log("Testing Background Masks started");
            var result = "";
            var counter = 0;
            foreach (var config in mapBackgroundPlaceableConfig.Values)
            {
                if (config.PlaceableUid.IsNullOrEmpty())
                {
                    result += $"\nPlaceableUid is empty for background mask {config.Uid}";
                    counter++;
                }
                else if (!mapPlaceableConfig.ContainsKey(config.PlaceableUid))
                {
                    result += $"\nCouldn't find placeable {config.PlaceableUid} for background mask {config.Uid}";
                    counter++;
                }
            }

            result = $"Testing Background Masks finished, total issues: {counter}{result}";
            if (counter <= 0)
                Debug.Log(result);
            else
                Debug.LogError(result);

            Debug.Log("Testing Placeables started");
            result = string.Empty;
            counter = 0;
            foreach (var config in mapPlaceableConfig.Values)
            {
                if (config.BlueprintUid.IsNullOrEmpty())
                {
                    result += $"\nBlueprintUid is empty for background mask {config.Uid}";
                    counter++;
                }
                else if (!poiConfig.ContainsKey(config.BlueprintUid))
                {
                    result += $"\nCouldn't find POIEntity {config.BlueprintUid} for placeable {config.Uid}";
                    counter++;
                }

                if (config.QuestUid.IsNullOrEmpty())
                {
                    result += $"\nQuestUid is empty for background mask {config.Uid}";
                    counter++;
                }
                else if (!questConfig.ContainsKey(config.QuestUid))
                {
                    result += $"\nCouldn't find Quest {config.QuestUid} for placeable {config.Uid}";
                    counter++;
                }
                else if (!FlatBufferHelper.Contains(questConfig[config.QuestUid].ObjectivesFb,
                        questConfig[config.QuestUid].ObjectivesFbLength, x => x?.Uid == config.BlueprintObjectiveUid))
                {
                    result += $"\nCouldn't find Objective {config.BlueprintObjectiveUid} for placeable {config.Uid}";
                    counter++;
                }
            }

            result = string.Format("Testing Placeables finished, total issues: {0}{1}", counter, result);
            if (counter <= 0)
                Debug.Log(result);
            else
                Debug.LogError(result);

            Debug.Log("Testing Quests started");
            result = string.Empty;
            counter = 0;
            foreach (var config in questConfig.Values)
            {
                if (config.ObjectivesFbLength == 2)
                {
                    var objective1 = config.ObjectivesFb(0).Value;
                    var objective2 = config.ObjectivesFb(1).Value;

                    if (objective1.ObjectiveCategory == "Blueprint" || objective2.ObjectiveCategory == "Building")
                    {
                        if (objective1.ObjectiveCategoryParameter.IsNullOrEmpty())
                        {
                            result += $"\nObjectiveCategoryParameter is empty for {config.Uid}:{objective1.Uid}";
                            counter++;
                        }
                        else if (!poiConfig.ContainsKey(objective1.ObjectiveCategoryParameter))
                        {
                            result += $"\nCouldn't find POI Entity {objective1.ObjectiveCategoryParameter} for {config.Uid}:{objective1.Uid}";
                            counter++;
                        }

                        if (objective2.ObjectiveCategoryParameter.IsNullOrEmpty())
                        {
                            result += $"\nObjectiveCategoryParameter is empty for {config.Uid}:{objective2.Uid}";
                            counter++;
                        }
                        else if (!mapPlaceableConfig.ContainsKey(objective2.ObjectiveCategoryParameter))
                        {
                            result += $"\nCouldn't find Placeable {objective2.ObjectiveCategoryParameter} for {config.Uid}:{objective1.Uid}";
                            counter++;
                        }
                    }
                }
            }

            result = $"Testing Quests finished, total issues: {counter}{result}";
            if (counter <= 0)
                Debug.Log(result);
            else
                Debug.LogError(result);

            Debug.Log("Testing Daily Events started");
            result = string.Empty;
            counter = 0;
            foreach (var config in dailyEventConfig.Values)
            {
                if (!poiConfig.ContainsKey(config.POIUid))
                {
                    result += $"\nCouldn't find POI Entity {config.POIUid} for daily event {config.Uid}";
                    counter++;
                }
            }

            result = $"Testing Daily Events finished, total issues: {counter}{result}";
            Debug.Log(result);
        }

        // [MenuItem("BebopBee/Config Test/Validate connections between Quest, Placeables, POI and DailyEvents")]
        // private static void TestConnectionsQuestPlaceablesPOI()
        // {
        //     ConfigLoader.Load(Test);
        // }
    }
}