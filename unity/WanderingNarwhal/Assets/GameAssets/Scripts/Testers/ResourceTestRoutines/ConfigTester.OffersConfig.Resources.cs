using System.Collections;
using BBB.Core.ResourcesManager;
using PBConfig;
using UnityEngine;

namespace BBB.Testers
{
    public partial class ConfigTesterBehaviour
    {
        private IEnumerator TestOffersConfigResources(IAssetsManager assetsManager)
        {
            var configName = "OfferConfig";
            Debug.LogWarningFormat("Checking {0}", configName);
            var offerConfig = _config.Get<OfferConfig>();

            var index = 0f;
            foreach (var entry in offerConfig.Values)
            {
                var uid = entry.Uid;

                yield return CheckIfAssetIsMissing<Sprite>(assetsManager, entry.Thumbnail, configName, uid, "Thumbnail");
                
                _progressCallback.SafeInvoke(index / offerConfig.Values.Count, configName);
                index++;
            }
        }
    }
}