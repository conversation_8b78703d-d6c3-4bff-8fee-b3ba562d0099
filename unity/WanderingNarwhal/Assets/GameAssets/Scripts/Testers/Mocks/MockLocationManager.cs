using System;
using System.Collections.Generic;
using BBB.Map.Location;
using GameAssets.Scripts.Player;
using PBConfig;
using PBGame;
using LocationConfig = FBConfig.LocationConfig;

namespace BBB.Testers.Mocks
{
    public class MockLocationManager : ILocationManager
    {
        public event Action<LevelState, bool, bool> LevelPassed = delegate { };

        public void Init(ILockManager lockManager, IConfig config)
        {
        }

        public bool IsCoinsFlightActive { get; }

        public bool IsInitialized { get; }

        public void SetupLocations(IDictionary<string, PBLocation> locations)
        {
            throw new NotImplementedException();
        }

        public event Action<string> LocationUnlocked;
        public event Action LocationsDataUpdated;

        public IDictionary<string, FBConfig.ProgressionLevelConfig> LevelsConfigs { get; set; }

        public void OnLevelPassed(string levelUid, bool nextStageReached, int grindReplaysNumber, bool isFirstTry)
        {
        }

        public ILocation GetLocationByUid(string locationUid)
        {
            return null;
        }

        public int GetUnlockedLocationsCount(bool includeSideMap = true)
        {
            throw new NotImplementedException();
        }

        public int GetUnclaimedLocationsCount()
        {
            return 0;
        }
        
        public int GetCompletedLocationsCount()
        {
            return 0;
        }

        public int GetUnlockedLocationsCount()
        {
            return 0;
        }

        public bool IsLocationUnlocked(string uid)
        {
            return false;
        }

        public bool IsLocationAvailableForPreview(string locationUid)
        {
            return false;
        }

        public bool AreAllLocationsPassed()
        {
            return false;
        }

        public int GetElderPoints()
        {
            return 0;
        }

        public int GetLevelStage(string levelUid)
        {
            return 1;
        }

        public int GetLevelStage(string locationUid, string levelUid)
        {
            return 0;
        }

        public int GetTotalLevelsAtStage(string locationUid, int stage)
        {
            return 0;
        }

        public int GetTotalWins(string locationUid)
        {
            return 0;
        }

        public int GetTotalLoses(string locationUid)
        {
            return 0;
        }

        public bool HasLevelsByPredicate(string locationUid, Func<LevelState, bool> predicate)
        {
            return true;
        }

        public List<string> GetLocationsWithPredicate(Func<LevelState, bool> predicate)
        {
            return null;
        }

        public List<string> GetLocationsWithLocationPredicate(Func<ILocation, bool> predicate)
        {
            return null;
        }

        public int GetTotalLevelNumber(string locationUid)
        {
            return 0;
        }

        public bool HasLevelLocationByHashcode(int hashCode)
        {
            throw new NotImplementedException();
        }

        public LevelLocationInfo GetLevelLocationByHashCode(int hashCode)
        {
            return new LevelLocationInfo();
        }

        public string GetLastUnlockedLocation(bool includeOutGlobe = true)
        {
            return "";
        }

        public LevelState GetLevelState(string levelUid)
        {
            return null;
        }

        public void UpdateListOfUnlockedLocations()
        {
            throw new NotImplementedException();
        }

        public void CheckForUnlockedLocation()
        {
            throw new NotImplementedException();
        }

        public int GetLocationOrder(string locationUid)
        {
            return 0;
        }

        public string GetNextLevelToPlayByProgression(string locationUid, string requiredReward, out int stage)
        {
            stage = 0;
            return "";
        }

        public int GetTotalLevelsAtMinStage(string locationUid, int stage)
        {
            return 0;
        }

        public string GetNextLevelToPlayByProgression(string locationUid, bool ignoreGrindLevels = false)
        {
            return string.Empty;
        }

        public string GetLocationUidByOrder(int order)
        {
            return "";
        }

        public ILocation GetLocationByLevelUid(string levelUid)
        {
            return null;
        }

        public string GetHighestPassedLevelUid(string locationUid)
        {
            return "";
        }

        public void OnLevelPassed(string levelUid, bool nextStageReached)
        {
            throw new NotImplementedException();
        }

        public int GetMaxPlayedLevelInt(string locationUid) => throw new NotImplementedException();

        public string GetLocationUidByLevelUid(string levelUid)
        {
            throw new NotImplementedException();
        }

        public HashSet<string> GetListOfUnlockedLocations()
        {
            throw new NotImplementedException();
        }

        public ILocation GetFirstUnclaimedLocation()
        {
            throw new NotImplementedException();
        }

        public string GetFirstUncompletedLocationUid()
        {
            throw new NotImplementedException();
        }

        public string LastStartedLevelUidThisSession { get; set; }

        public bool IsDeferredAnimationInProgress => throw new NotImplementedException();

        public void InitStateOfLocation(string locationUid)
        {
            throw new NotImplementedException();
        }

        public bool IsSideMapLocation(string locationUid)
        {
            throw new NotImplementedException();
        }

        public void RegisterLocation(Location location)
        {
            throw new NotImplementedException();
        }

        public void UnregisterLocation(Location location)
        {
            throw new NotImplementedException();
        }

        public bool IsOutGlobeLocation(string locationUid)
        {
            throw new NotImplementedException();
        }

        public int GetGlobeLocationOrder(string locationUid)
        {
            throw new NotImplementedException();
        }
        
        public void DebugUnlockLocation(ILocation location)
        {
            throw new NotImplementedException();
        }
        
    }
}