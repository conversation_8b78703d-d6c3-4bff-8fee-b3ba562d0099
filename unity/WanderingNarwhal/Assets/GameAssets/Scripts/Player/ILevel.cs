using BBB;
using PBConfig;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.Match3.Systems;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using Grid = BBB.Grid;

public interface ILevel
{
    bool DoesUseTileKind(TileKinds kind);
    string GetLevelFullName();
    bool HasAnyResultRecords { get; }
    GoalState AverageResult { get; }
    POIMarkerStateType POIMarkerState { get; set; }
    List<TileKinds> UsedKinds { get; set; }
    GoalState Goals { get; set; }
    Grid Grid { get; set; }
    int TurnsLimit { get; set; }
    GoalType OptionalGoalsEnabled { get; set; }
    Hash128 AssetHash { get; }
    string VersionHash { get; }

    void SetupRemoteData(LevelRemoteData remoteData, SpawnerSettings[] globalSpawners, bool allowAutoFixGoals = true);

    bool IsWonderLevel { get; }

    FBConfig.ProgressionLevelConfig Config { get; }
    FBConfig.LevelViewConfig? ViewConfig { get; }

    string LevelName { get; }
    int TargetWinRate { get; }
    int TargetWinRateT2 { get; }
    int TargetWinRateT3 { get; }
    int Stage { get; }
    int GrindReplays { get; }
    bool IsReplay { get; set; }
    Dictionary<string, int> RewardsDictionary { get; }
    string GetStageCurrency();
    LitterSkin LitterSkin { get; set; }
    StickerSkin StickerSkin { get; set; }
    string AssistSystemUid { get; set; }
    AnimalSkin AnimalSkin { get; }
    string LevelUid { get; }
    int LossStreak { get; }
    void WinLevel(IPlayerManager playerManager);
    void LoseLevel(IConfig config);
    void ExitWithoutPlaying();
    void IncrementNumPlayed();
    void DecrementNumPlayed();
    int GetNumPlayed();
    float GetNum(IConfig config);
    string GetDifficultyLocalizationID();
    void IncreaseWinRate(int amount);
}