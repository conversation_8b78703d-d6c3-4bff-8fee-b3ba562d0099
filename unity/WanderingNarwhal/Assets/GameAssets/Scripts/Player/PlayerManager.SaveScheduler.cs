using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;

namespace GameAssets.Scripts.Player
{
    public partial class PlayerManager
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(SystemConfig)
        };

        private const int FallBackDebounceDelay = 15;
        private const int FallBackMaxDelay = 30;

        private DateTime _lastChangeTime;
        private DateTime _lastServerSaveTime;
        private bool _isDirty;
        private bool _isSaving;
        private int _debounceDelay = FallBackDebounceDelay;
        private int _maxDelay = FallBackMaxDelay;
        private PlayerSavedGame _lastSavedGame;
        private IUpdateDispatcher _updateDispatcher;
        
        public async void MarkDirty(bool force = false)
        {
            var result = await TryToSave(force);
            
            if (result.Item1)
            {
                _lastSavedGame = result.Item2;
                _isDirty = true;
                _lastChangeTime = DateTime.UtcNow;
            }
        }
        
        private void Update()
        {
            if (!_isDirty)
                return;

            var debounceCondition = (DateTime.UtcNow - _lastChangeTime).TotalSeconds >= _debounceDelay;

            var maxDelayCondition = (DateTime.UtcNow - _lastServerSaveTime).TotalSeconds >= _maxDelay;

            if (debounceCondition || maxDelayCondition)
            {
                ExecuteServerSave().Forget();
            }
        }

        private void ForceSave()
        {
            if (!_isDirty)
                return;
            
            ExecuteServerSave().Forget();
        }
        
        private async UniTaskVoid ExecuteServerSave()
        {
            if (_isSaving)
                return;

            _isSaving = true;
            try
            {
                await SaveDataToRemote(_lastSavedGame);
                _lastServerSaveTime = DateTime.UtcNow;
                _isDirty = false;
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.General,$"Error during scheduled server save: {ex.Message}");
            }
            finally
            {
                _isSaving = false;
            }
        }
        
        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupConfigs;
            _updateDispatcher.OnUpdate -= Update;
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += SetupConfigs;
            _updateDispatcher.OnUpdate += Update;
        }
        
        private void SetupConfigs(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            
            var saveGameProgressConfig = config.TryGetDefaultFromDictionary<SystemConfig>().SaveGameProgressConfig;
            if (!saveGameProgressConfig.HasValue) return;
            
            var debounceDelay = saveGameProgressConfig.Value.DebounceDelay;
            var maxDelay = saveGameProgressConfig.Value.MaxDelay;
            if (debounceDelay != 0)
            {
                _debounceDelay = debounceDelay;
            }
            if (maxDelay != 0)
            {
                _maxDelay = maxDelay;
            }
        }

        private void Reset()
        {
            _isDirty = false;
            _isSaving = false;
            _lastChangeTime = default;
            _lastServerSaveTime = default;
            _lastSavedGame = default;
        }
    }
}