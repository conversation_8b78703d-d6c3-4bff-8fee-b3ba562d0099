using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Map;
using BebopBee;
using Core.Configs;
using FBConfig;
using PBGame;

namespace GameAssets.Scripts.Map
{
    public class EpisodeTaskManager : IEpisodeTaskManager, IContextInitializable, IContextReleasable
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(SceneTaskConfig)
        };

        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private IEventDispatcher _eventDispatcher;
        private ILocationManager _locationManager;

        private IDictionary<string, SceneTaskConfig> _sceneTaskConfig;
        private readonly Dictionary<string, List<SceneTaskConfig>> _sceneToTasks = new();
        private HashSet<DependencyGroup<string>> _groupedTasksByDependencies;
        private readonly Dictionary<string, SceneTaskConfig> _cachedTasksByUid = new();

        //temp containers
        private readonly List<string> _phaseTasks = new();
        private readonly List<SceneTaskConfig> _availableTasksToFinish = new();
        private readonly List<string> _emptyList = new();

        public void InitializeByContext(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _locationManager = context.Resolve<ILocationManager>();
            var config = context.Resolve<IConfig>();
            InitFromConfig(config);
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += InitFromConfig;
            _accountManager.ProfileUpdated += ProfileUpdatedHandler;
            _eventDispatcher.AddListener<LevelResultPredicted>(OnLevelResultPredicted);
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= InitFromConfig;
            _accountManager.ProfileUpdated -= ProfileUpdatedHandler;
            _eventDispatcher.RemoveListener<LevelResultPredicted>(OnLevelResultPredicted);
        }

        private void InitFromConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs)) return;

            _sceneTaskConfig = config.Get<SceneTaskConfig>();
            CacheGroupsOfSameDependencies(_playerManager.Player.CurrentEpisodeScene);
        }

        public void CacheGroupsOfSameDependencies(string sceneUid)
        {
            var sceneTaskConfigs = GetOrCreateConfigsOf(sceneUid);
            _groupedTasksByDependencies = ConfigUtils.GroupTasksByDependencies(sceneTaskConfigs);
        }

        private void ProfileUpdatedHandler(IPlayer player)
        {
            CacheGroupsOfSameDependencies(player.CurrentEpisodeScene);
            IEpisodicScenesManager.SceneToLoad = player.CurrentEpisodeScene;
        }

        public List<SceneTaskConfig> GetAvailableTasksToFinishOf(string sceneUid)
        {
            _availableTasksToFinish.Clear();

            if (IsSceneCompleted(sceneUid)) return _availableTasksToFinish;

            var starCount = _playerManager.StarCount;
            var progress = GetProgressOf(sceneUid);
            var tasks = GetOrCreateConfigsOf(sceneUid);
            var currentPhaseTasks = GetCurrentPhaseTasksOf(sceneUid);

            if (currentPhaseTasks.Count > 1)
            {
                _cachedTasksByUid.Clear();
                foreach (var sceneTaskConfig in tasks)
                {
                    _cachedTasksByUid[sceneTaskConfig.Uid] = sceneTaskConfig;
                }

                int GetStarCost(string uid)
                {
                    if (_cachedTasksByUid.TryGetValue(uid, out var sceneTaskConfig))
                        return sceneTaskConfig.StarCost;

                    BDebug.LogError(LogCat.General, $"Couldn't find task: {uid} in {sceneUid}");
                    return 0;
                }

                currentPhaseTasks.Sort((a, b) =>
                {
                    var taskAStarCost = GetStarCost(a);
                    var taskBStarCost = GetStarCost(b);

                    return taskAStarCost > taskBStarCost ? 1 : taskAStarCost < taskBStarCost ? -1 : 0;
                });
            }

            foreach (var taskUid in currentPhaseTasks)
            {
                if (!CheckToAddTask(taskUid)) break;
            }

            if (starCount > 0)
            {
                foreach (var taskConfig in tasks)
                {
                    if (currentPhaseTasks.Contains(taskConfig.Uid)) continue;
                    if (!CheckToAddTask(taskConfig.Uid)) break;
                }
            }

            return _availableTasksToFinish;

            bool CheckToAddTask(string taskUid)
            {
                if (progress.Contains(taskUid)) return true;
                var taskConfig = tasks.Find(t => t.Uid == taskUid);
                starCount -= taskConfig.StarCost;
                if (starCount < 0) return false;
                _availableTasksToFinish.Add(taskConfig);
                return true;
            }
        }

        private void OnLevelResultPredicted(LevelResultPredicted levelResultPredicted)
        {
            if (levelResultPredicted.Arg0 == LevelOutcome.Win)
            {
                HandleWin(levelResultPredicted.ScreenType, levelResultPredicted.level);
            }
        }

        private void HandleWin(ScreenType screenType, ILevel level)
        {
            if ((screenType & ScreenType.SideMap) != 0) return;

            var stagePlayed = level.Stage;
            var levelActualStage = _locationManager.GetLevelState(level.LevelUid).Stage;
            if (levelActualStage > stagePlayed) return;
            
            _playerManager.AddStar();
        }

        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();
        }

        public List<string> GetCurrentPhaseTasksOf(string sceneUid)
        {
            var sceneProgress = GetOrCreateProgressOf(sceneUid);
            var completedTasks = sceneProgress.CompletedTasks;

            if (completedTasks.Count == GetOrCreateConfigsOf(sceneUid).Count) return _emptyList;
            _phaseTasks.Clear();
            ConfigUtils.GetUnlockedTasksFromDependencyGroups(_groupedTasksByDependencies, completedTasks, _phaseTasks);
            return _phaseTasks;
        }

        public List<SceneTaskConfig> GetOrCreateConfigsOf(string sceneUid)
        {
            if (_sceneToTasks.TryGetValue(sceneUid, out var tasks)) return tasks;

            tasks = new List<SceneTaskConfig>();
            _sceneToTasks.Add(sceneUid, tasks);
            foreach (var (key, taskConfig) in _sceneTaskConfig)
            {
                const char uidSeparator = '_';
                var keySpan = key.AsSpan();
                var underscoreIndex = keySpan.IndexOf(uidSeparator);

                var locationUid = underscoreIndex >= 0 ? keySpan[..underscoreIndex].ToString() : key;
                if (locationUid == sceneUid)
                {
                    tasks.Add(taskConfig);
                }
                else if (tasks.Count > 0)
                {
                    break;
                }
            }

            return tasks;
        }

        private PBEpisodeSceneProgress GetOrCreateProgressOf(string sceneUid)
        {
            if (_playerManager.Player.EpisodeScenesProgress.TryGetValue(sceneUid, out var sceneProgress))
                return sceneProgress;

            sceneProgress = new PBEpisodeSceneProgress
            {
                SceneId = sceneUid,
                CompletedTasks = new List<string>()
            };
            _playerManager.Player.EpisodeScenesProgress.Add(sceneUid, sceneProgress);

            return sceneProgress;
        }

        public List<string> GetProgressOf(string sceneUid)
        {
            return GetOrCreateProgressOf(sceneUid).CompletedTasks;
        }

        public void UpdateProgressOf(string sceneUid, string taskUid)
        {
            var sceneProgress = GetOrCreateProgressOf(sceneUid);
            sceneProgress.CompletedTasks.Add(taskUid);

            var episodicTaskCompletedEvent = _eventDispatcher.GetMessage<EpisodicTaskCompletedEvent>();
            episodicTaskCompletedEvent.Set(taskUid);
            _eventDispatcher.TriggerEvent(episodicTaskCompletedEvent);

            CheckCompletionOf(sceneUid);
        }

        public int GetRemainingSceneCost(string sceneUid)
        {
            var tasks = GetOrCreateConfigsOf(sceneUid);
            var progress = GetProgressOf(sceneUid);
            var cost = 0;
            foreach (var task in tasks)
            {
                if (progress.Contains(task.Uid)) continue;

                cost += task.StarCost;
            }

            return cost;
        }

        public bool IsSceneCompleted(string sceneUid)
        {
            var configs = GetOrCreateConfigsOf(sceneUid);
            if (configs.Count == 0)
            {
                BDebug.LogError(LogCat.General, $"No tasks found for scene {sceneUid}");
                return false;
            }

            var completedTasks = GetOrCreateProgressOf(sceneUid).CompletedTasks;
            foreach (var config in configs)
            {
                if (!completedTasks.Contains(config.Uid)) return false;
            }

            return true;
        }

        public int GetCompletedScenesCount()
        {
            var result = _playerManager.Player.OpenedEpisodeScenes.Count;
            var lastSceneUid = _playerManager.Player.CurrentEpisodeScene;
            if (!IsSceneCompleted(lastSceneUid))
            {
                result--;
            }

            return result;
        }

        private void CheckCompletionOf(string sceneUid)
        {
            if (!IsSceneCompleted(sceneUid)) return;

            var sceneCompletedEvent = _eventDispatcher.GetMessage<AllSceneTasksCompletedEvent>();
            sceneCompletedEvent.Set(sceneUid);
            _eventDispatcher.TriggerEvent(sceneCompletedEvent);
        }

        public void HandleDebugWin(ScreenType screenType, ILevel level)
        {
            HandleWin(screenType, level);
        }

        public int GetNumberOfRemainingTasks(string sceneUid)
        {
            return sceneUid == _playerManager.Player.CurrentEpisodeScene 
                ? GetOrCreateConfigsOf(sceneUid).Count - GetProgressOf(sceneUid).Count 
                : GetOrCreateConfigsOf(sceneUid).Count;
        }
    }
}