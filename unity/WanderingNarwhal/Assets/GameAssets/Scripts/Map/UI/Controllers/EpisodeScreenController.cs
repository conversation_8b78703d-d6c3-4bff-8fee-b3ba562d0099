using System;
using System.Collections.Generic;
using BBB;
using BBB.Audio;
using BBB.Controller;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Map;
using BBB.Modals;
using BBB.UI;
using BBB.UI.BottomBar.Views;
using BBB.Wallet;
using BebopBee.Core.Audio;
using Bebopbee.Core.Utility;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Generic.Controllers;
using GameAssets.Scripts.Hud;
using GameAssets.Scripts.Loading.Modals;
using GameAssets.Scripts.Map.Location.FlowActions;
using GameAssets.Scripts.Map.Events;
using GameAssets.Scripts.Map.UI.Views;
using GameAssets.Scripts.Tripstagram;
using GameAssets.Scripts.UI;
using UnityEngine;

namespace GameAssets.Scripts.Map.UI.Controllers
{
    public class EpisodeScreenController : FlowActionsScreenController<IEpisodeScreenViewPresenter>,
        IEpisodeScreenController
    {
#if BBB_DEBUG
        public static bool ForceSkipAutoPopups { get; set; }
#endif
        public const string IntroName = "intro_";

        private GenericHudManager _genericHudManager;
        private IEpisodeTaskManager _episodeTaskManager;
        private EpisodeResourceManager _episodeAssetsProvider;
        private IEpisodicScenesManager _episodicScenesManager;
        private IScreensBuilder _screensBuilder;
        private readonly EpisodeScreenViewModel _episodeScreenViewModel = new();
        private IContext _context;

        private IModalsBuilder _modalsBuilder;
        private IEventDispatcher _eventDispatcher;
        private IGameEventManager _gameEventManager;

        private Action _onReplayComplete;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private TimeManager _timeManager;

        private readonly Queue<Action<string, Action>> _sceneCompletionActions = new();

        private bool _skipScene;
        private bool _sceneCompleted;
        private SceneCompletionModalController _sceneCompletionModal;
        private SnapshotTaskModalController _snapshotTaskModalController;
        private EpisodicSceneOpenData _episodicSceneOpenData;
        private bool _hasNewScene;
        private bool _progressBarFillReady;
        private bool _isLoadingModalShown;
        private bool _loadingModalClosed;
        private bool _showingIntro;
        private bool _keepPostCard;
        private bool _showingReward;
        private bool _scheduleSpeechBubbles;

        private readonly HashSet<string> _currentTasks = new();
        private TapToSkipModalController _tapToSkipModalController;
        private IGenericModalsBlocker _genericModalsBlocker;

        private bool ShouldShowIntro => PlayerManager.Player.GetLastInteractionTimestamp(
                $"{IntroName}{IEpisodicScenesManager.SceneToLoad}") < 0;
        
        public override bool ShouldHideHud => _episodicScenesManager.ScreenMode is EpisodeScreenMode.Replay;

        protected override void OnInitializeByContext(UnityContext context)
        {
            base.OnInitializeByContext(context);
            context.AddServiceToRegister<IEpisodeScreenController>(this);
        }

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _context = previousContext;
            _genericHudManager = previousContext.Resolve<GenericHudManager>();
            _episodeTaskManager = previousContext.Resolve<IEpisodeTaskManager>();
            _episodeAssetsProvider = previousContext.Resolve<EpisodeResourceManager>();
            _episodicScenesManager = previousContext.Resolve<IEpisodicScenesManager>();
            _screensBuilder = previousContext.Resolve<IScreensBuilder>();
            _modalsBuilder = previousContext.Resolve<IModalsBuilder>();
            _eventDispatcher = previousContext.Resolve<IEventDispatcher>();
            _walletManager = previousContext.Resolve<IWalletManager>();
            _uiWalletManager = previousContext.Resolve<IUIWalletManager>();
            _timeManager = previousContext.Resolve<TimeManager>();
            _gameEventManager = previousContext.Resolve<IGameEventManager>();
            _genericModalsBlocker = previousContext.Resolve<IGenericModalsBlocker>();

            RegisterPostFXAction(new FlowAction(PlayTutorial));
            RegisterPostFXAction(new FlowAction(ExecuteDeeplink));
            RegisterPostFXAction(new FlowAction(InvokeParamsCallback));
            RegisterPostFXAction(new FlowAction(ShowHighPriorityAutoPopups));
            RegisterPostFXAction(new FlowAction(InvokeInterruptionCallback));
            RegisterPostFXAction(new FlowAction(HandleLevelExit));
            RegisterPostFXAction(new FlowAction(HandleLevelLose));
            RegisterPostFXAction(new FlowAction(PostFxInterruptionAwaiter));
            RegisterPostFXAction(new FlowAction(InvokeInterruptionCallback));
            RegisterPostFXAction(new FlowAction(ShowAutoPopups));
            RegisterPostFXAction(new FlowAction(ContinueLevelFlow));
            //TODO migrate to Deep Links
            RegisterPostFXAction(new FlowAction(CheckAndProceedQuickActions));

            IEpisodicScenesManager.SceneToLoad ??= PlayerManager.Player.CurrentEpisodeScene;
            _episodicScenesManager.UICurrentSceneUid = PlayerManager.Player.CurrentEpisodeScene;
        }

        public override bool IsReady()
        {
            if (!base.IsReady()) return false;

            if (_episodeAssetsProvider.AreBundlesLoaded(ScreenType.EpisodeScreen.ToString(), ScreenType.EpisodeScreen))
                return true;

            if (_screensBuilder.PreviousScreenType == ScreenType.LoadingScreen) return true;
            if (_isLoadingModalShown) return _loadingModalClosed;

            _isLoadingModalShown = true;
            ShowAssetsLoadingModal();
            return false;
        }

        private void ShowAssetsLoadingModal()
        {
            var assetsLoadingModalController = _modalsBuilder.CreateModalView<AssetsLoadingModalController>(ModalsType.AssetsLoadingModal);
            assetsLoadingModalController.Setup(_episodeAssetsProvider, () => _loadingModalClosed = true);
            assetsLoadingModalController.ConfigureForTransition();
            assetsLoadingModalController.ShowModal();
        }

        public override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _isLoadingModalShown = false;
            _loadingModalClosed = false;
            _showingReward = false;
        }

        private void Subscribe()
        {
            Unsubscribe();

            FlowEndedEvent += FxFlowEndedEventHandler;
            
            View.OnExitButtonClicked += ExitButtonClickHandler;
            View.OnTaskStart += TaskStartHandler;
            View.OnTaskComplete += TaskCompleteHandler;
            View.OnBatchTasksComplete += BatchTasksCompleteHandler;
            View.OnPostcardClicked += PostcardClickedHandler;

            _genericHudManager.HudShown += HudShownHandler;

            ModalsManager.ModalShown += OnModalShown;
            ModalsManager.ModalHidden += OnModalHidden;

            _eventDispatcher.AddListener<ViewEpisodicSceneEvent>(ViewSceneHandler);
            _eventDispatcher.AddListener<BuildEpisodicSceneEvent>(BuildSceneHandler);
            _eventDispatcher.AddListener<OnClaimDone>(ClaimDoneHandler);
            _eventDispatcher.AddListener<EpisodicProgressBarClickEvent>(EpisodicProgressBarClickHandler);
            _eventDispatcher.AddListener<AllSceneTasksCompletedEvent>(AllTasksCompletedHandler);
            _eventDispatcher.AddListener<EpisodicProgressBarAnimationFinishEvent>(EpisodicProgressBarAnimationFinishEventHandler);
            _eventDispatcher.AddListener<TransitionIrisOutroEvent>(TransitionIrisOutroCompletedEventHandler);
            _eventDispatcher.AddListener<SweepstakesMilestoneReachedEvent>(SweepstakesMilestoneReachedHandler);
        }

        private void Unsubscribe()
        {
            FlowEndedEvent -= FxFlowEndedEventHandler;
            
            if (View != null)
            {
                View.OnExitButtonClicked -= ExitButtonClickHandler;
                View.OnTaskStart -= TaskStartHandler;
                View.OnTaskComplete -= TaskCompleteHandler;
                View.OnBatchTasksComplete -= BatchTasksCompleteHandler;
                View.OnPostcardClicked -= PostcardClickedHandler;
                _eventDispatcher?.Unsubscribe(View);
            }

            if (_genericHudManager != null)
            {
                _genericHudManager.HudShown -= HudShownHandler;
            }

            if (ModalsManager != null)
            {
                ModalsManager.ModalShown -= OnModalShown;
                ModalsManager.ModalHidden -= OnModalHidden;
            }

            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<ViewEpisodicSceneEvent>(ViewSceneHandler);
                _eventDispatcher.RemoveListener<BuildEpisodicSceneEvent>(BuildSceneHandler);
                _eventDispatcher.RemoveListener<OnClaimDone>(ClaimDoneHandler);
                _eventDispatcher.RemoveListener<EpisodicProgressBarClickEvent>(EpisodicProgressBarClickHandler);
                _eventDispatcher.RemoveListener<AllSceneTasksCompletedEvent>(AllTasksCompletedHandler);
                _eventDispatcher.RemoveListener<EpisodicProgressBarAnimationFinishEvent>(EpisodicProgressBarAnimationFinishEventHandler);
                _eventDispatcher.RemoveListener<TransitionIrisOutroEvent>(TransitionIrisOutroCompletedEventHandler);
                _eventDispatcher.RemoveListener<SweepstakesMilestoneReachedEvent>(SweepstakesMilestoneReachedHandler);
            }
        }
        
        private void SweepstakesMilestoneReachedHandler(SweepstakesMilestoneReachedEvent @event)
        {
            _showingReward = true;
            InterruptLevelRewarding();
        }

        private void FxFlowEndedEventHandler()
        {
            if (!_scheduleSpeechBubbles || _episodicScenesManager.ScreenMode is EpisodeScreenMode.Replay) return;
            View.ScheduleSpeechBubbles();
        }

        private void OnModalShown(IController controller)
        {
            if (View == null) return;
            View.HoldSpeechBubblesTimer(controller.GetType().Name);
        }

        private void OnModalHidden(IController controller)
        {
            if (View == null) return;
            View.ResumeSpeechBubblesTimer(controller.GetType().Name);
        }

        private void TransitionIrisOutroCompletedEventHandler(TransitionIrisOutroEvent irisEvent)
        {
            View.IrisOutroCompleted = irisEvent.Arg0;
        }

        private async void PostcardClickedHandler()
        {
            var tripstagramSnapshotModalController = _modalsBuilder.CreateModalView<TripstagramSnapshotModalController>(ModalsType.TripstagramSnapshotModal);
            var snapshot = await _episodicScenesManager.SnapshotOf(_episodeScreenViewModel.SceneUid);
            var city = _episodicScenesManager.CityNameOf(_episodeScreenViewModel.SceneUid);
            var country = _episodicScenesManager.CountryNameOf(_episodeScreenViewModel.SceneUid);
            var subtitle = _episodicScenesManager.SubTitleOf(_episodeScreenViewModel.SceneUid);
            var info = _episodicScenesManager.UniqueInfoOf(_episodeScreenViewModel.SceneUid);
            tripstagramSnapshotModalController.Setup(snapshot, city, country, subtitle, info);
            tripstagramSnapshotModalController.ShowModal();
        }

        private void EpisodicProgressBarAnimationFinishEventHandler(EpisodicProgressBarAnimationFinishEvent _)
        {
            _progressBarFillReady = true;
        }

        private void HudShownHandler(bool isShown)
        {
            if (isShown)
            {
                View?.HidePostcard();
                HideConstructionViewAsync().Forget(BDebug.LogDebugError);
            }
        }

        private void ExitButtonClickHandler()
        {
            Action<ScreenType> onScreenChange = null;
            ScreenType returnScreen = ScreenType.None;
            if (_episodicSceneOpenData.IsFromTripstagram)
            {
                onScreenChange = ShowTripstagram;
                returnScreen = _episodicSceneOpenData.FromScreen;
            }

            _episodicSceneOpenData = default;

            if (_episodicScenesManager.ScreenMode != EpisodeScreenMode.Replay)
            {
                if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.Construction && _gameEventManager.IsActiveSideMap(returnScreen))
                {
                    _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
                    HideConstructionViewAsync().Forget(BDebug.LogDebugError);
                    _screensBuilder.ShowScreen(returnScreen, onScreenChange);
                }
                else
                {
                    HideConstructionViewAsync().Forget(BDebug.LogDebugError);
                }

                return;
            }

            _genericHudManager.BlockByIntro(false);
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
            if (_onReplayComplete != null)
            {
                _onReplayComplete();
                _onReplayComplete = null;
            }
            else
            {
                //return to game event screen if any active
                if (_gameEventManager.IsActiveSideMap(returnScreen))
                {
                    _screensBuilder.ShowScreen(returnScreen, onScreenChange);
                }
                else
                {
                    IEpisodicScenesManager.SceneToLoad = PlayerManager.Player.CurrentEpisodeScene;
                    _episodicScenesManager.UICurrentSceneUid = PlayerManager.Player.CurrentEpisodeScene;
                    _screensBuilder.ReloadScreen(onScreenChange);
                }
            }
        }

        private void BuildSceneHandler(BuildEpisodicSceneEvent ev)
        {
            if (!_hasNewScene && _episodeTaskManager.IsSceneCompleted(PlayerManager.Player.CurrentEpisodeScene))
            {
                ShowEndOfContentModal();
            }
            else
            {
                _episodicSceneOpenData = new EpisodicSceneOpenData(ev.Arg1, ev.Arg2);
                ShowConstructionView().Forget();
            }
        }

        private void ViewSceneHandler(ViewEpisodicSceneEvent ev)
        {
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Replay;
            IEpisodicScenesManager.SceneToLoad = ev.Arg0;
            _episodicScenesManager.CheckToReloadSceneGameObject(IEpisodicScenesManager.SceneToLoad);
            _episodicScenesManager.OpenData = new EpisodicSceneOpenData(ev.Arg1, ev.Arg2);
            View.HoldSpeechBubblesTimer(nameof(EpisodeScreenMode.Replay));
            View.HideAllBubbles();
            _genericHudManager.ForceShowBottomBar(false);
            _screensBuilder.ReloadScreen();
        }

        private async void AllTasksCompletedHandler(AllSceneTasksCompletedEvent ev)
        {
            _episodicSceneOpenData = default;
            TryOpenNextScene().Forget();
            _sceneCompleted = true;
            _keepPostCard = false;

            GrantSceneRewards(ev.Arg0);
            if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.AutoTask)
            {
                _tapToSkipModalController = _modalsBuilder.CreateModalView<TapToSkipModalController>(ModalsType.TapToSkipModal);
                _tapToSkipModalController.OnSkipButtonPressed -= SkipSceneCompletion;
                _tapToSkipModalController.OnSkipButtonPressed += SkipSceneCompletion;
                _tapToSkipModalController.ShowModal();
            }
            
            await UniTask.WaitWhile(this, state => !state._skipScene && (state._episodicScenesManager.ScreenMode == EpisodeScreenMode.AutoTask || !state._progressBarFillReady));

            _tapToSkipModalController?.Hide();

            if (!_skipScene)
            {
                _episodicScenesManager.ScreenMode = EpisodeScreenMode.SceneCompletion;
                _snapshotTaskModalController = null;
                RegisterSceneCompletionActions();
                await RunSceneCompletionActions(ev.Arg0);
                _snapshotTaskModalController?.Hide();
            }

            _genericHudManager.BottomBarController.RefreshFlowButtons();
            await HideConstructionViewAsync();
            _episodicScenesManager.UICurrentSceneUid = PlayerManager.Player.CurrentEpisodeScene;
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
            if (_hasNewScene)
            {
                View.IrisOutroCompleted = false;
                ModalsManager.HideAllModals();
                _walletManager.TransactionsDeltaPool.RemoveDeltasWithTag(TransactionTag.SceneComplete);
                _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelRewardInterruptionEvent>());
                _screensBuilder.ReloadScreen();
            }
            else
            {
                ShowEndOfContentModal();
            }
        }

        private void SkipSceneCompletion()
        {
            _tapToSkipModalController?.Hide();
            View.SkipBatchTasks().Forget();
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.SceneCompletion;
        }

        private void ShowEndOfContentModal()
        {
            var endOfContentModal = _modalsBuilder.CreateModalView<EndOfContentInfoModalController>(ModalsType.EndOfContentInfo);
            endOfContentModal.ShowModal();
        }

        private async void ClaimDoneHandler(OnClaimDone message)
        {
            if (message.Arg0.GameplayType != GameEventGameplayType.EndOfContent)
                return;

            await TryOpenNextScene();
            if (_hasNewScene)
            {
                _screensBuilder.ReloadScreen();
            }
        }

        private async UniTask TryOpenNextScene()
        {
            await UniTask.RunOnThreadPool(async () =>
            {
                var nextScenes = _episodicScenesManager.GetReadyToOpenScenes();
                if (nextScenes.Count == 0)
                {
                    _hasNewScene = false;
                    return;
                }

                var nextScene = nextScenes[0]; //first available for now
                await UniTask.SwitchToMainThread();
                _episodicScenesManager.OpenScene(nextScene);
                await UniTask.SwitchToThreadPool();
                _episodeTaskManager.CacheGroupsOfSameDependencies(nextScene);
                _hasNewScene = true;
            });
        }

        private void EpisodicProgressBarClickHandler(EpisodicProgressBarClickEvent _)
        {
            ShowConstructionView().Forget();
        }

        private void GrantSceneRewards(string sceneUid)
        {
            var transaction = new Transaction().AddTag(TransactionTag.SceneComplete);
            transaction.Earn(_episodicScenesManager.RewardOf(sceneUid));
            transaction.SetAnalyticsData(CurrencyFlow.TravelJournal.Category, CurrencyFlow.TravelJournal.Family, sceneUid);
            _walletManager.TransactionController.MakeTransaction(transaction);
        }

        private void RegisterSceneCompletionActions()
        {
            _sceneCompletionActions.Enqueue(ShowSceneCompletionModal);
            _sceneCompletionActions.Enqueue(HideUI);
            _sceneCompletionActions.Enqueue(ShowScreenshotModal);
            _sceneCompletionActions.Enqueue(ShowSceneRewardModal);
            _sceneCompletionActions.Enqueue(ShowSpeechBubbles);
        }

        private async UniTask RunSceneCompletionActions(string sceneUid)
        {
            while (_sceneCompletionActions.Count > 0)
            {
                var state = new SceneActionState {
                    Completed = false,
                    Owner = this
                };
                var action = _sceneCompletionActions.Dequeue();
                action(sceneUid, () => state.Completed = true);
                await UniTask.WaitUntil(state,s => s.Completed || s.Owner._skipScene);
            }

            await UniTask.WaitUntil(this, self => self._skipScene);
        }

        private async void HideUI(string sceneUid, Action onComplete)
        {
            _genericHudManager.TryToHide();
            _genericHudManager.HideStarsWidget();
            await HideConstructionViewAsync();
            onComplete();
        }

        private async void ShowScreenshotModal(string sceneUid, Action onComplete)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(View.CameraMomentDuration));

            _snapshotTaskModalController = _modalsBuilder.CreateModalView<SnapshotTaskModalController>(ModalsType.SnapshotTaskModal);
            var snapshot = await _episodicScenesManager.SnapshotOf(sceneUid);

            var postcard = View.PostcardButton.transform;
            var postcardScreenPosition = RectTransformUtility.WorldToScreenPoint(((BaseNavigationViewPresenter)View).ScreenCamera, postcard.position);
            RectTransformUtility.ScreenPointToWorldPointInRectangle(postcard as RectTransform, postcardScreenPosition, null, out var position);
            _snapshotTaskModalController.Setup(snapshot, position);
            _snapshotTaskModalController.OnSceneCaptured -= SceneCaptureHandler;
            _snapshotTaskModalController.OnSceneCaptured += SceneCaptureHandler;
            _snapshotTaskModalController.OnPhotoStartsFlying -= ShowPostCardButton;
            _snapshotTaskModalController.OnPhotoStartsFlying += ShowPostCardButton;
            _snapshotTaskModalController.OnPhotoFinishFlying -= HideSnapshotModal;
            _snapshotTaskModalController.OnPhotoFinishFlying += HideSnapshotModal;
            _snapshotTaskModalController.OnPhotoImpact -= View.ImpactPostcard;
            _snapshotTaskModalController.OnPhotoImpact += View.ImpactPostcard;
            _snapshotTaskModalController.OnPhotoImpact -= onComplete;
            _snapshotTaskModalController.OnPhotoImpact += onComplete;
            _snapshotTaskModalController.ShowModal();
        }

        private void HideSnapshotModal()
        {
            _snapshotTaskModalController?.Hide();
            if (_keepPostCard) return;
            View.HidePostcard();
        }

        private void SceneCaptureHandler()
        {
            AudioProxy.PlaySound(View.SnapshotTaskSoundId);
        }

        private void ShowSpeechBubbles(string sceneUid, Action onComplete)
        {
            View.ShowSpeechBubbles(_episodeScreenViewModel.SceneTaskConfigs[^1].Uid);
            onComplete();
        }

        private void ShowSceneRewardModal(string sceneUid, Action onComplete)
        {
            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
            var rewardModel = new CurrenciesRewardViewModel
            {
                RewardDict = _episodicScenesManager.RewardOf(sceneUid)
            };
            rewardModal.SetupInitialParams(rewardModel, onHide: async skippedCurrencies =>
            {
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.SceneComplete, skippedCurrencies);
                await UniTask.Delay(TimeSpan.FromSeconds(View.RewardAnimationDuration));
                _sceneCompletionModal.ShowNextEpisodeButton(_hasNewScene);
                _keepPostCard = true;
                ShowPostCardButton();
                onComplete();
            });
            _sceneCompletionModal.HideNextEpisodeButton();
            rewardModal.ShowModal();
        }

        private void ShowSceneCompletionModal(string sceneUid, Action onComplete = null)
        {
            _genericHudManager.TryToHide();
            _sceneCompletionModal = _modalsBuilder.CreateModalView<SceneCompletionModalController>(ModalsType.SceneCompletionModal);
            _sceneCompletionModal.ShowModal();
            _sceneCompletionModal.OnModalHide -= OnSceneCompletionClicked;
            _sceneCompletionModal.OnModalHide += OnSceneCompletionClicked;
            onComplete.SafeInvoke();
        }

        private void OnSceneCompletionClicked()
        {
            _skipScene = true;
        }

        public override void OnShow()
        {
            base.OnShow();

            UpdateView();
            Subscribe();

            _scheduleSpeechBubbles =
                (ScreensManager.GetPreviousScreenType() & (ScreenType.Levels | ScreenType.LoadingScreen)) != 0;
            
            _episodicSceneOpenData = _episodicScenesManager.OpenData;
            _episodicScenesManager.OpenData = default;

            _skipScene = false;
            var isReplay = _episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay;
            _genericHudManager.BlockByIntro(isReplay);

            if (isReplay)
            {
                ShowPostCardButton();
                ReplayScene();
                return;
            }
            
            IEpisodicScenesManager.SceneToLoad = PlayerManager.Player.CurrentEpisodeScene;

            View.HidePostcard();
            _episodicScenesManager.UICurrentSceneUid = PlayerManager.Player.CurrentEpisodeScene;
            _hasNewScene = _episodicScenesManager.GetReadyToOpenScenes().Count > 0;
            View.SetupSpeechBubbles(_genericHudManager.SpeechBubblesContainer);
            _sceneCompleted = false;
            _episodicScenesManager.MarkVisited(IEpisodicScenesManager.SceneToLoad);
            if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.Construction)
            {
                _showingIntro = false;
                ShowConstructionView().Forget();
                View.ShowTasks();
                return;
            }

            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
            View.ResumeSpeechBubblesTimer(nameof(EpisodeScreenMode.Replay));
            if (ShouldShowIntro)
            {
                ShowIntro();
            }
            else
            {
                _showingIntro = false;
                View.ShowTasks();
                View.HideBuildButtons(true);
            }
        }

        private void ShowPostCardButton()
        {
            if (_sceneCompleted || _episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay)
            {
                View.ShowPostcard();
            }
            else
            {
                View.HidePostcard();
            }
        }

        private void ShowTripstagram(ScreenType _)
        {
            _genericHudManager.BottomBarController.ShowTripstagram();
        }

        private void ShowIntro()
        {
            View.IrisOutroCompleted = !_episodicScenesManager.ShouldWaitForOutro;
            _episodicScenesManager.ShouldWaitForOutro = false;
            _showingIntro = true;
            _genericHudManager.BlockByIntro(true);
            View.ShowIntro(ShowHud, IntroComplete).Forget(exception => BDebug.LogError(LogCat.Flow, exception));

            PlayerManager.Player.UpdateLastInteractionTimestamp(
                $"{IntroName}{IEpisodicScenesManager.SceneToLoad}",
                _timeManager.CurrentTimeStamp());
            return;

            void ShowHud()
            {
                _genericHudManager.BlockByIntro(false);
            }

            void IntroComplete()
            {
                ShowHud(); // in case a scene miss to call ShowHud

                PlayerManager.MarkDirty();
                if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.Default)
                {
                    View.HideBuildButtons();
                }
            }
        }

        private void ReplayScene(Action onComplete = null)
        {
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Replay;
            _onReplayComplete = onComplete;
            _genericHudManager.BlockByIntro(true);
            View.Replay();
        }

        private void UpdateView()
        {
            var currentEpisodicScene = IEpisodicScenesManager.SceneToLoad;

            _episodeScreenViewModel.SceneUid = currentEpisodicScene;
            _episodeScreenViewModel.SceneTaskConfigs = _episodeTaskManager.GetOrCreateConfigsOf(currentEpisodicScene);
            _episodeScreenViewModel.PhaseTasks = _episodeTaskManager.GetCurrentPhaseTasksOf(currentEpisodicScene);
            _episodeScreenViewModel.CompletedTasks = _episodeTaskManager.GetProgressOf(currentEpisodicScene);
            _episodeScreenViewModel.AvailableStars = PlayerManager.StarCount;

            var viewPrefab = _episodeAssetsProvider.GetGenericAsset<GameObject>(EpisodeResourceKeys.Scene);
            var shouldReloadScene = _episodicScenesManager is { ShouldReloadScene: true };
            View.Setup(currentEpisodicScene, _episodeScreenViewModel, viewPrefab, shouldReloadScene);
            if (shouldReloadScene)
            {
                _episodicScenesManager.ShouldReloadScene = false;
            }
        }

        private bool IsStoryModeBlocked()
        {
            return _episodicScenesManager.ScreenMode is EpisodeScreenMode.Replay ||
                   _genericModalsBlocker.IsBlocked() ||
                   ModalsManager.IsShowingAModal();
        }

        private async UniTask ShowConstructionView(bool withDelay = true)
        {
            if (IsStoryModeBlocked()) return;

            View.StartShowConstructionView();

            SkipAutoPopups = true;
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.Construction;
            _genericHudManager.BlockBy(nameof(EpisodeScreenMode.Construction), true);
            _genericHudManager.TryToHide(true);
            if (!_sceneCompleted && withDelay)
            {
                await UniTask.Delay((int)((View.EditModeTopBarDelay) * MathUtility.SecondsToMillis));
                _genericHudManager.ShowStarsInEditMode();
            }

            if (withDelay && _episodicScenesManager.ScreenMode == EpisodeScreenMode.Construction && !_sceneCompleted)
            {
                TryToShowBatchBuildButton();
                ShowExitButtonWithDelay();
            }

            _episodeScreenViewModel.PhaseTasks =
                _episodeTaskManager.GetCurrentPhaseTasksOf(PlayerManager.Player.CurrentEpisodeScene);
            _episodeScreenViewModel.AvailableStars = PlayerManager.StarCount;

            View.NotEnoughStarsClicked -= ShowNotEnoughStars;
            View.NotEnoughStarsClicked += ShowNotEnoughStars;
            View.OnConstructionHide -= OnConstructionHide;
            View.OnConstructionHide += OnConstructionHide;

            await View.ShowConstructionView(withDelay);
        }
        
        private async void OnConstructionHide() 
        {
                _genericHudManager.BlockBy(nameof(EpisodeScreenMode.Construction), false);
                if (_episodicScenesManager.ScreenMode is EpisodeScreenMode.AutoTask or EpisodeScreenMode.SceneCompletion) return;
                SkipAutoPopups = false;
                _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
                View.HideBuildButtons();
                if (!_genericHudManager.IsHudVisible)
                {
                    _genericHudManager.HideStarsWidget();
                    await UniTask.Delay((int)(View.EditModeTopBarDelay * MathUtility.SecondsToMillis));
                    _genericHudManager.TryToShow();
                }
                else
                {
                    _genericHudManager.ShowStarsWidget();
                } 
        }

        private async void ShowExitButtonWithDelay()
        {
            await UniTask.Delay((int)(View.DelayForBuildAllButton * MathUtility.SecondsToMillis));
            if (_currentTasks.Count > 0) return;
            View.ShowExitButton();
        }

        protected override void RefreshAutoPopupsSkip()
        {
#if BBB_DEBUG
            SkipLevelFlow = SkipAutoPopups = ForceSkipAutoPopups || _showingIntro || ShouldShowIntro || _episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay || _showingReward;
            ForceSkipAutoPopups = false;
#else
            SkipLevelFlow = SkipAutoPopups = _showingIntro || ShouldShowIntro || _episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay || _showingReward;
#endif
        }

        private void TryToShowBatchBuildButton()
        {
            if (_sceneCompleted || _currentTasks.Count > 0 || _episodicScenesManager.ScreenMode is not EpisodeScreenMode.Construction) return;
            var sceneUid = PlayerManager.Player.CurrentEpisodeScene;
            var availableTasksToFinish = _episodeTaskManager.GetAvailableTasksToFinishOf(sceneUid);
            var configs = _episodeTaskManager.GetOrCreateConfigsOf(sceneUid);

            var mode = BatchBuildMode.Moment;

            if (availableTasksToFinish.Count > 0)
            {
                mode = BatchBuildMode.Moments;

                if (availableTasksToFinish[^1].Uid == configs[^1].Uid) //camera task
                {
                    mode = BatchBuildMode.Scene;
                }
                else if (availableTasksToFinish.Count == 1) // 1 for a single task
                {
                    mode = BatchBuildMode.Moment;
                }
            }
            else
            {
                var progress = _episodeTaskManager.GetProgressOf(sceneUid).Count;
                if (progress == configs.Count - 1) //camera task
                {
                    mode = BatchBuildMode.Scene;
                }
            }

            View.ShowBatchBuildButton(availableTasksToFinish, mode);
        }

        private void ShowNotEnoughStars()
        {
            var notEnoughStarsController = _modalsBuilder.CreateModalView<NotEnoughStarsController>(ModalsType.NotEnoughStarsModal);
            notEnoughStarsController.Setup(onClose: () => ShowConstructionView().Forget());
            notEnoughStarsController.ShowModal();

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.EditMode, DauInteractions.TapOnHud.NotEnoughStars));
            HideConstructionViewAsync().Forget();
        }

        private async UniTask HideConstructionViewAsync()
        {
            _genericHudManager.BlockBy(nameof(EpisodeScreenMode.Construction), false);
            await View.HideConstruction();
        }

        private void TaskStartHandler(string taskUid)
        {
            if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay) return;
            _currentTasks.Add(taskUid);

            View.HideBatchBuildButton();
            View.HideExitButton();
            CompleteTask(taskUid);
            var index = _episodeScreenViewModel.SceneTaskConfigs.FindIndex(config => config.Uid == taskUid);
            PlayerManager.SpendStar(_episodeScreenViewModel.SceneTaskConfigs[index].StarCost);
            PlayerManager.MarkDirty();
            _episodeScreenViewModel.AvailableStars = PlayerManager.StarCount;
        }

        private async void TaskCompleteHandler(string taskUid)
        {
            if (_episodicScenesManager.ScreenMode == EpisodeScreenMode.Replay) return;

            ShowConstructionView(false).Forget();
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<UpdateSceneProgressUIEvent>());

            await UniTask.Delay(TimeSpan.FromSeconds(View.DelayBetweenTasks));

            _currentTasks.Remove(taskUid);
            if (_currentTasks.Count == 0 && _episodicScenesManager.ScreenMode is not EpisodeScreenMode.SceneCompletion)
            {
                ShowConstructionView().Forget();
            }
        }

        private async UniTask UpdateViewAfterDelay(float delaySeconds = 0, bool resetMode = false)
        {
            await UniTask.Delay((int)(delaySeconds * MathUtility.SecondsToMillis));

            if (View.IsBatchCancelled) return;

            if (resetMode)
            {
                _episodicScenesManager.ScreenMode = EpisodeScreenMode.Construction;
            }

            if (_episodicScenesManager.ScreenMode is EpisodeScreenMode.Construction)
            {
                ShowConstructionView().Forget();
            }
        }

        private async void BatchTasksCompleteHandler(List<SceneTaskConfig> tasks)
        {
            _episodicScenesManager.ScreenMode = EpisodeScreenMode.AutoTask;

            var starCount = 0;
            var waitTime = 0f;

            foreach (var task in tasks)
            {
                var delay = task.TaskTimeOverride > 0 ? task.TaskTimeOverride : View.DelayBetweenTasks;
                waitTime += delay;

                var index = _episodeScreenViewModel.SceneTaskConfigs.FindIndex(config => config.Uid == task.Uid);
                starCount += _episodeScreenViewModel.SceneTaskConfigs[index].StarCost;

                CompleteTask(task.Uid);
            }

            PlayerManager.SpendStar(starCount);
            PlayerManager.MarkDirty();
            _episodeScreenViewModel.AvailableStars = PlayerManager.StarCount;

            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<UpdateSceneProgressUIEvent>());

            await UpdateViewAfterDelay(waitTime, true);
            if (_episodicScenesManager.ScreenMode is EpisodeScreenMode.AutoTask)
            {
                _episodicScenesManager.ScreenMode = EpisodeScreenMode.Default;
            }
        }

        private void CompleteTask(string taskUid)
        {
            _progressBarFillReady = false;
            var index = _episodeScreenViewModel.SceneTaskConfigs.FindIndex(config => config.Uid == taskUid);

            var currentEpisodicScene = PlayerManager.Player.CurrentEpisodeScene;
            _episodeTaskManager.UpdateProgressOf(currentEpisodicScene, taskUid);
            _episodeScreenViewModel.CompletedTasks = _episodeTaskManager.GetProgressOf(currentEpisodicScene);

            Analytics.LogEvent(new FunnelEvent(Funnel.Category.MetaGame, currentEpisodicScene, index.ToString()));

            if (_episodicScenesManager.ScreenMode is not EpisodeScreenMode.AutoTask)
            {
                _episodeScreenViewModel.PhaseTasks = _episodeTaskManager.GetCurrentPhaseTasksOf(currentEpisodicScene);
            }
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
            if (View != null)
            {
                View.NotEnoughStarsClicked -= ShowNotEnoughStars;
                View.OnConstructionHide -= OnConstructionHide;  
            }
           
            _genericHudManager = null;
        }

        protected override void OnFxFlowStarted()
        {
            base.OnFxFlowStarted();
            
            if (_episodicScenesManager.ScreenMode is EpisodeScreenMode.Replay)
                return;

            var lastLevelPlayedData = PlayerProfileLocal.GetLastLevelPlayedData();
            if (!lastLevelPlayedData.LevelUidForCurrencyDropAnimation.IsNullOrEmpty())
            {
                SetupWinFlowActions(lastLevelPlayedData);
            }
            else if (lastLevelPlayedData.Lose)
            {
                SetupLoseFlowActions();
            }
        }

        private void SetupWinFlowActions(LastLevelPlayedData lastLevelPlayedData)
        {
            FXFlowActions.Add(new WaitForHudShownFlowAction(_context));
            FXFlowActions.Add(new TriggerCurrencyFlightFlowAction(_context, lastLevelPlayedData));
            FXFlowActions.Add(new WaitForLevelRewardTransactionCompletedFlowAction(_context));
        }

        private void SetupLoseFlowActions()
        {
            FXFlowActions.Add(new WaitForHudShownFlowAction(_context));
            FXFlowActions.Add(new TriggerProgressLoseFlowAction(_context));
            FXFlowActions.Add(new WaitForLevelRewardTransactionCompletedFlowAction(_context));
        }

        protected override void OnFxFlowEnded()
        {
            //todo use to release actions blocked in StartFlowActions()
        }

        protected override void OnTransitionCompleted()
        {
            var scenesDict = Config.Get<ScenesConfig>();
            var scenesConfig = scenesDict[IEpisodicScenesManager.SceneToLoad];

            if (scenesConfig.IsNull() || scenesConfig.BgMusicId.IsNullOrEmpty() || !AudioProxy.TryPlayMusic(scenesConfig.BgMusicId))
            {
                AudioProxy.TryPlayMusic(MusicIds.Main);
            }

            if (!LastLevelPlayedData.Lose)
            {
                AudioProxy.PlayRandomSound("VO_Opening_");
            }
        }
        
        private class SceneActionState
        {
            public bool Completed;
            public EpisodeScreenController Owner;
        }
    }
}