using System;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Map.Views
{
    public class ClickableOverlay
    {
        private readonly GameObject _gameObject;
        private Action _callback;

        public ClickableOverlay(GameObject gameObject, Button button)
        {
            _gameObject = gameObject;
            button.ReplaceOnClick(ButtonClickedHandler);
        }

        private void ButtonClickedHandler()
        {
            _callback?.Invoke();
        }

        public void Enable(Action callback = null)
        {
            _callback = callback;
            _gameObject.SetActive(true);
        }

        public void Disable()
        {
            _callback = null;
            _gameObject.SetActive(false);
        }
    }
}