using System;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Map.UI.Views
{
    public class TapToSkipModalViewPresenter : ModalsViewPresenter, ITapToSkipModalViewPresenter
    {
        [SerializeField] private Button _button;

        public event Action OnSkipButtonPressed;

        protected override void Awake()
        {
            base.Awake();
            _button.ReplaceOnClick(() =>
            {
                Hide();
                OnSkipButtonPressed.SafeInvoke();
            });
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnSkipButtonPressed = null;
        }
    }
}
