using BBB.UI.Core;
using System;
using System.Collections.Generic;
using PBGame;

namespace BBB.UI.Map.Views
{
    public interface INoMoreLivesViewPresenter : IViewPresenter
    {
        event Action BuyButtonClicked;
        event Action GetGiftsButtonClicked;
        event Action AdButtonClicked;

        void Setup(Dictionary<string, long> livesPrice, LevelState levelState);
        void RefreshAd();
    }
}