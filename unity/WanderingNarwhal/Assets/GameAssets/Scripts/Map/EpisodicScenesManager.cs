using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.AssetBundles;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.Map;
using Core.Configs;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Map.UI.Controllers;
using UnityEngine;

namespace GameAssets.Scripts.Map
{
    public class EpisodicScenesManager : IEpisodicScenesManager, IContextInitializable, IContextReleasable, IBundlePredownloadProvider
    {
        public const string DefaultEpisodicScene = "airport";
        public static bool IsNewSceneVisited = true;

        private static readonly Type[] RequiredConfigs =
        {
            typeof(ScenesConfig)
        };

        private IPlayerManager _playerManager;
        private IEventDispatcher _eventDispatcher;

        private readonly HashSet<string> _openedScenes = new();
        private readonly HashSet<string> _completedTasks = new();
        private readonly List<string> _readyScenes = new();
        private ICityAssetsProvider _cityAssetsProvider;
        private IEpisodeTaskManager _episodeTaskManager;
        private IConfig _config;
        private IGameEventManager _gameEventManager;
        private EpisodeResourceManager _episodeAssetsProvider;
        private ILocationManager _locationManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;
        public List<ScenesConfig> OrderedScenes { get; private set; }

        public IDictionary<string, ScenesConfig> ScenesConfigs { get; private set; }
        public EpisodicSceneOpenData OpenData { get; set; }
        public string UICurrentSceneUid { get; set; }
        public EpisodeScreenMode ScreenMode { get; set; }
        private readonly List<string> _remainingScenes = new();
        public bool ShouldReloadScene { get; set; }

        public bool ShouldWaitForOutro { get; set; }

        public void InitializeByContext(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _cityAssetsProvider = context.Resolve<ICityAssetsProvider>();
            _episodeTaskManager = context.Resolve<IEpisodeTaskManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _episodeAssetsProvider = context.Resolve<EpisodeResourceManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _bundlesBackgroundDownloaderManager = context.Resolve<BundlesBackgroundDownloaderManager>();
            _config = context.Resolve<IConfig>();

            _openedScenes.AddRange(_playerManager.Player.OpenedEpisodeScenes);
            foreach (var progress in _playerManager.Player.EpisodeScenesProgress.Values)
            {
                _completedTasks.AddRange(progress.CompletedTasks);
            }

            IEpisodicScenesManager.SceneToLoad = _playerManager.Player.CurrentEpisodeScene;
            UICurrentSceneUid = _playerManager.Player.CurrentEpisodeScene;

            Subscribe();
        }

        public void Setup()
        {
            InitFromConfig(_config);
        }

        private void InitOrderedScenes()
        {
            OrderedScenes ??= new();
            OrderedScenes.Clear();
            OrderedScenes.AddRange(ScenesConfigs.Values);
            OrderedScenes.Sort((a, b) => a.UiOrder.CompareTo(b.UiOrder));
        }

        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += OnConfigsUpdated;
            _eventDispatcher.AddListener<EpisodicTaskCompletedEvent>(OnEpisodicTaskCompleted);
            _bundlesBackgroundDownloaderManager.RegisterProvider(this);
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= OnConfigsUpdated;
            _eventDispatcher.RemoveListener<EpisodicTaskCompletedEvent>(OnEpisodicTaskCompleted);
            _bundlesBackgroundDownloaderManager.UnregisterProvider(this);
        }

        private void OnEpisodicTaskCompleted(EpisodicTaskCompletedEvent ev)
        {
            _completedTasks.Add(ev.Arg0);
        }

        private void OnConfigsUpdated(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs)) return;

            InitFromConfig(config);
        }

        private void InitFromConfig(IConfig config)
        {
            ScenesConfigs = config.Get<ScenesConfig>();
            InitOrderedScenes();
            CheckForNewContent();
        }

        private void CheckForNewContent()
        {
            if (!_episodeTaskManager.IsSceneCompleted(_playerManager.Player.CurrentEpisodeScene)) return;
            var readyToOpenScenes = GetReadyToOpenScenes();
            if (readyToOpenScenes.Count == 0 ||
                _gameEventManager.GetHighestPriorityEvent(e => e.GameplayType == GameEventGameplayType.EndOfContent, skipResourceCheck: true) is EndOfContentGameEvent ev
                && ev.IsLaunched() && ev.IntroductionAlreadyShown) return;

            var nextSceneUid = readyToOpenScenes[0]; //we are unlocking first available scene for now
            OpenScene(nextSceneUid);
            _episodeTaskManager.CacheGroupsOfSameDependencies(nextSceneUid);
        }

        public void OpenScene(string sceneUid)
        {
            if (!TryAddOpenedScene(sceneUid)) return;
            
            _playerManager.Player.CurrentEpisodeScene = sceneUid;
            IEpisodicScenesManager.SceneToLoad = sceneUid;
            _playerManager.MarkDirty();
            IsNewSceneVisited = false;
            var message = _eventDispatcher.GetMessage<NewEpisodicSceneOpenEvent>();
            message.Set(sceneUid);
            _eventDispatcher.TriggerEvent(message);
        }

        public List<string> GetReadyToOpenScenes()
        {
            _readyScenes.Clear();
            foreach (var sceneUid in ScenesConfigs.Keys)
            {
                if (IsReadyToOpen(sceneUid))
                {
                    _readyScenes.Add(sceneUid);
                }
            }

            return _readyScenes;
        }

        public string SceneNameOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.SceneName;
        }

        public string CityNameOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.Name;
        }

        public string CountryNameOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.CountryName;
        }

        public string SubTitleOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.SubTitle;
        }

        public string UniqueInfoOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.UniqueInfo;
        }

        public int OrderOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? 0 : sceneConfig.UiOrder;
        } 
        
        public string CityTitleNameOf(string sceneUid)
        {
            return !ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig) ? string.Empty : sceneConfig.TravelJournalTitleName;
        }

        public Dictionary<string, int> RewardOf(string sceneUid)
        {
            var rewards = new Dictionary<string, int>();
            if (!ScenesConfigs.TryGetValue(sceneUid, out var sceneConfig)) return rewards;

            for (var i = 0; i < sceneConfig.RewardLength; i++)
            {
                var reward = sceneConfig.Reward(i);
                if (reward == null)
                {
                    BDebug.LogError(LogCat.Config, $"Reward at index {i} is null for scene {sceneUid}");
                    continue;
                }

                var rewardDict = reward.Value;
                rewards.Add(rewardDict.Key, rewardDict.Value);
            }

            return rewards;
        }

        public UniTask<Sprite> SnapshotOf(string sceneUid)
        {
            return _cityAssetsProvider.GetSceneSnapshot(sceneUid);
        }

        public UniTask<Sprite> IconOf(string sceneUid)
        {
            return _cityAssetsProvider.GetSceneIcon(sceneUid);
        }

        public bool IsVisited(string sceneUid)
        {
            var openedScenes = _playerManager.Player.OpenedEpisodeScenes;
            var index = openedScenes.IndexOf(sceneUid);
            return index >= 0 && (index < openedScenes.Count - 1 || IsNewSceneVisited);
        }

        public void MarkVisited(string sceneUid)
        {
            //previous scenes count as visited anyway
            if (sceneUid == _playerManager.Player.CurrentEpisodeScene)
                IsNewSceneVisited = true;
        }

        public static bool IsReadyToOpen(string sceneUid, ICollection<string> openedScenes,
            IDictionary<string, ScenesConfig> scenesConfigs, ICollection<string> completedTasks)
        {
            if (openedScenes.Contains(sceneUid) || !scenesConfigs.TryGetValue(sceneUid, out var config))
                return false;

            var dependencies =
                ConfigUtils.ToListOfList(FlatBufferHelper.ToList(config.Dependencies, config.DependenciesLength));
            foreach (var dependency in dependencies)
            {
                if (dependency == null)
                    continue;

                foreach (var taskUid in dependency)
                {
                    if (!completedTasks.Contains(taskUid))
                        return false;
                }
            }

            return true;
        }

        private bool IsReadyToOpen(string sceneUid)
        {
            return IsReadyToOpen(sceneUid, _openedScenes, ScenesConfigs, _completedTasks);
        }

        // Debug call
        public void ResetSceneDebug()
        {
            ShouldReloadScene = true;
            _openedScenes.Clear();
            _completedTasks.Clear();
        }

        public void TryAddOpenedSceneDebug(string sceneUid)
        {
            TryAddOpenedScene(sceneUid);
        }

        private bool TryAddOpenedScene(string sceneUid)
        {
            if (_openedScenes.Contains(sceneUid))
            {
                BDebug.LogError(LogCat.City, $"Attempt to open already opened scene {sceneUid}");
                return false;
            }

            if (!IsReadyToOpen(sceneUid))
            {
                BDebug.LogError(LogCat.City, $"Attempt to open a scene {sceneUid} that is not ready");
                return false;
            }

            _openedScenes.Add(sceneUid);
            _playerManager.Player.OpenedEpisodeScenes.Add(sceneUid);
            _playerManager.MarkDirty();
            return true;
        }

        public List<string> GetListOfRemainingSceneUid()
        {
            _remainingScenes.Clear();
            _remainingScenes.Add(_playerManager.Player.CurrentEpisodeScene);
            foreach (var sceneConfig in OrderedScenes)
            {
                if (!_openedScenes.Contains(sceneConfig.Uid))
                {
                    _remainingScenes.Add(sceneConfig.Uid);
                }
            }
            return _remainingScenes;
        }

        public void CheckToReloadSceneGameObject(string sceneUid)
        {
            if (!_openedScenes.Contains(sceneUid)) return;
            ShouldReloadScene = OrderedScenes[^1].Uid == sceneUid;
        }

        public List<BackgroundDownloadData> GetBundlesToPredownload()
        {
            const int NextScenesToLoad = 2;

            List<BackgroundDownloadData> bundles = null;
            
            var curScene = _playerManager.Player.CurrentEpisodeScene;
            var curSceneIndex = -1;
            var nextScenesLeft = NextScenesToLoad;
            for (var i = 0; i < OrderedScenes.Count; i++)
            {
                var sceneConfig = OrderedScenes[i];
                if (curSceneIndex >= 0 && nextScenesLeft == 0) break;
                
                var priority = BackgroundDownloadPriority.Lowest;
                if (curScene == sceneConfig.Uid)
                {
                    curSceneIndex = i;
                    priority = BackgroundDownloadPriority.Highest;
                }
                else if (curSceneIndex >= 0 && nextScenesLeft > 0)
                {
                    nextScenesLeft--;
                    priority = BackgroundDownloadPriority.High;
                }
                
                var locationBundles = _episodeAssetsProvider.GetLocationBundleNames(sceneConfig.Uid);
                if (locationBundles == null || locationBundles.Count == 0) continue;
                
                bundles ??= new List<BackgroundDownloadData>();
                foreach (var bundle in locationBundles)
                {
                    if(bundle.IsNullOrEmpty()) continue;
                    bundles.Add(new BackgroundDownloadData{Priority = priority, Name = bundle});
                }
            }
            return bundles;
        }
    }
}