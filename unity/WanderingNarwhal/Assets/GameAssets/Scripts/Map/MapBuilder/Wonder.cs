using System.Collections.Generic;
using System.Web.UI.WebControls;
using FBConfig;
using PBGame;
using UnityEngine;

namespace BBB
{
    public class Wonder
    {
        private readonly MapPlaceableConfigT _config;
        private readonly PBMapPlaceable _wonderData;

        public MapPlaceableConfigT Config => _config;
        public string Uid => _config.Uid;
        public string LocationUid => _config.LocationUid;
        public string LevelUid => _config.LevelUid;
        public bool IsFake => _config.Fake;


        public Dictionary<string, int> Reward { get; }
        public Vector2[] CurveTangents { get; private set; }

        public string POIEntityUid => _config.BlueprintUid;
        public string FallbackName { get; set; }
        public string FallbackIcon { get; set; }

        public int StarsPrice { get; set; }
        public string UnlockLevelUid { get; set; }
        public int SortOrder { get; set; }
        public int TargetStars { get; set; }
        public bool UnlockLevelReached { get; set; }
        public bool TargetLevelReached { get; set; }

        public bool CanCollectStars => UnlockLevelReached && !TargetLevelReached && !Restored;
        public bool CanBeRestored => TargetLevelReached && !Restored;

        public bool Restored
        {
            get => _wonderData.Inaugurated;
            set => _wonderData.Inaugurated = value;
        }

        public bool JustRevealed { get; set; }

        public Wonder(MapPlaceableConfigT config, PBMapPlaceable wonderData)
        {
            _config = config;
            _wonderData = wonderData;

            Reward = new Dictionary<string, int>();
            
            if (config.Curve != null)
            {
                CurveTangents = new Vector2[config.Curve.Count];

                for (var i = 0; i < config.Curve.Count; i++)
                {
                    var point = config.Curve[i];
                    CurveTangents[i] = new Vector2(point.X, point.Y);
                }
            }
            
            if (config.RewardFb.IsNullOrEmpty())
            {
                Debug.LogError($"Wonder: {config.Uid} doesn't have configured reward");
            }
            else
            {
                foreach (var pair in config.RewardFb)
                {
                    Reward.Add(pair.Key, pair.Value);
                }
            }
        }
    }
}