using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using PBGame;

namespace GameAssets.Scripts.DailyTask
{
    public class DailyTasksController : BaseModalsController<IDailyTasksModalViewPresenter>
    {
        private IDailyTasksManager _dailyTasksManager;
        private int _lastStreak;
        private List<TaskState> _currentTasks;
        
        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _dailyTasksManager = previousContext.Resolve<IDailyTasksManager>();
            _lastStreak = _dailyTasksManager.CurrentStreakDay;
        }
        
        public override void OnShow()
        {
            base.OnShow();
            View.RefreshModalHeader();
            _dailyTasksManager.RefreshCycle();

            SetupView();
            Subscribe();
        }

        private void SetupView()
        {
            View.Setup(_dailyTasksManager.GetTimeLeft);
            View.SetupStreak(_dailyTasksManager.CurrentStreakDay, _lastStreak);
            _lastStreak = _dailyTasksManager.CurrentStreakDay;
            _currentTasks = _dailyTasksManager.GetCurrentTasks();
            View.SetupItems(_currentTasks);
        }

        public override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _dailyTasksManager.TryClaimStreakReward();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnClaimed += OnClaimed;
            View.OnSwapped +=  OnSwapped;
            View.OnFlowRequested += OnFlowRequested;
            
            _dailyTasksManager.TasksUpdated += OnTasksUpdatedHandler;
        }

        private void Unsubscribe()
        {
            View.OnClaimed -= OnClaimed;
            View.OnSwapped -=  OnSwapped;
            View.OnFlowRequested -= OnFlowRequested;
            
            _dailyTasksManager.TasksUpdated -= OnTasksUpdatedHandler;
        }

        private void OnTasksUpdatedHandler()
        {
            SetupView();
        }
        
        private void OnFlowRequested(string taskUid)
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.DailyTasks, 
                DauInteractions.DailyTasks.TapOnTask, taskUid));
            
            View.HideFromCloseButton();
            _dailyTasksManager.FlowRequested(taskUid);
        }
        
        private void OnClaimed(string taskUid)
        {
            var taskIndex = GetTaskIndex(taskUid);
            if (_dailyTasksManager.TryClaimTask(taskUid))
            {
                View.UpdateItem(taskUid, _currentTasks[taskIndex]);
                
                var newStreak = _dailyTasksManager.CurrentStreakDay;
                if (newStreak != _lastStreak)
                {
                    View.SetupStreak(newStreak, _lastStreak);
                    _lastStreak = newStreak;
                    _dailyTasksManager.TryClaimStreakReward();
                }
            }
            View.RefreshModalHeader();
        }

        private void OnSwapped(string taskUid)
        {
            var taskIndex = GetTaskIndex(taskUid);
            if (_dailyTasksManager.TrySwapTask(taskUid))
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.DailyTasks, 
                    DauInteractions.DailyTasks.RefreshTask, taskUid));
                
                View.UpdateItem(taskUid, _currentTasks[taskIndex]);
            }
        }

        private int GetTaskIndex(string taskUid)
        {
            for (int i = 0; i < _currentTasks.Count; i++)
            {
                if (_currentTasks[i].TaskUid == taskUid)
                {
                    return i;
                }
            }
            return 0; //first by default
        }
    }
}