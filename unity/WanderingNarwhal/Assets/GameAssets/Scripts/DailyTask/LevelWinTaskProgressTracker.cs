using BBB;
using BBB.DI;
using BBB.UI;

namespace GameAssets.Scripts.DailyTask
{
    public class LevelWinTaskProgressTracker : TaskProgressTracker
    {
        public override string TaskType => "LevelFirstTry";

        private IEventDispatcher _eventDispatcher;

        public override void Init(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            
            _eventDispatcher.RemoveListener<LevelWonEvent>(LevelWonHandler);
            _eventDispatcher.AddListener<LevelWonEvent>(LevelWonHandler);
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher.RemoveListener<LevelWonEvent>(LevelWonHandler);
        }
        
        public override void OnFlowRequested()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelFlowRequestedEvent>());
        }
        
        private void LevelWonHandler(LevelWonEvent ev)
        {
            if (ev.Arg0 == 1)
            {
                AddProgress(1);
            }
        }
    }
}