using System;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.DailyTask.UI
{
    public class DailyTaskIntroModalViewPresenter : ModalsViewPresenter, IDailyTasksIntroModalViewPresenter
    {
        public event Action OnViewHide;
        [SerializeField] private Button _startButton;

        protected override void OnShow()
        {
            base.OnShow();
            _startButton.ReplaceOnClick(OnCloseButton);
        }

        protected override void OnHide()
        {
            base.OnHide();
            OnViewHide.SafeInvoke();
        }
    }
}