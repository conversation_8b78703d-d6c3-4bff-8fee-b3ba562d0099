using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BebopBee;
using BebopBee.Social;
using UnityEngine;

namespace BBB
{

    public class AfterM3LeaderboardResultFilter : ILeaderboardResultFilter
    {
        private const float EasyEnemyScoreMultiplierMin = 0.8f;
        private const float EasyEnemyScoreMultiplierMax = 0.9f;
        private const float EnemyScoreMultiplierMin = 1.01f;
        private const float EnemyScoreMultiplierMax = 1.3f;
        
        public int FilteredCount { get; }
        private const string PlayerNameLoc = "YOU_STANDART";
        private const string StrangersLockUid = "strangers";
        private readonly string _currentPlayerUid;
        private List<LeaderboardItem> _cachedLeaderboard;
        private HashSet<string> _friends;

        private readonly ILocalizationManager _localizationManager;
        private readonly ILockManager _lockManager;
        
        //TODO add weights for picking player place in leaderboard to config 
        private readonly Dictionary<int, float> _leaderboardWeights = new ();
        private readonly StandardItemComparer _standardItemComparer = new ();

        public AfterM3LeaderboardResultFilter(IContext context)
        {
            FilteredCount = 10;
            _localizationManager = context.Resolve<ILocalizationManager>();
            _lockManager = context.Resolve<ILockManager>();

            var accountManager = context.Resolve<IAccountManager>();
            var player = accountManager.Profile;
            _currentPlayerUid = player.Uid;
        }

        public void Setup(List<LeaderboardItem> leaderboard, HashSet<string> friends)
        {
            _cachedLeaderboard = new List<LeaderboardItem>(leaderboard);
            _friends = friends;
        }

        public List<LeaderboardItem> GetLeaderboardItems(int ownScore)
        {
#if BBB_TEST
            return new List<LeaderboardItem>();
#endif
            var result = new List<LeaderboardItem>();

            var localPlayerItem = FindLocalPlayer();
            if (localPlayerItem == null) return result;
            localPlayerItem.Score = ownScore;
            
            AdjustScoresBy(localPlayerItem.Score);
            InsertPlayerAndSort(localPlayerItem, result);
            AssignRanks(result);
            return result;
        }

        private LeaderboardItem FindLocalPlayer()
        {
            var localPlayerIndex = _cachedLeaderboard.IndexOf(item => item.Uid == _currentPlayerUid);
            if (localPlayerIndex == -1)
            {
                BDebug.LogError(LogCat.Match3, "Local player index was not found");
                return null;
            }
            var localPlayerItem = _cachedLeaderboard[localPlayerIndex];
            localPlayerItem.Name = _localizationManager.getLocalizedText(PlayerNameLoc);
            _cachedLeaderboard.RemoveAt(localPlayerIndex);
            return localPlayerItem;
        }

        private void AdjustScoresBy(int localPlayerScore)
        {
            if (_cachedLeaderboard.Count == 0) return;
            
            _cachedLeaderboard.Sort(_standardItemComparer);
            var maxRealEnemyScore = _cachedLeaderboard[0].Score;

            var easyEnemyBaseScore = localPlayerScore * Random.Range(EasyEnemyScoreMultiplierMin, EasyEnemyScoreMultiplierMax);
            var easyMultiplier = easyEnemyBaseScore / maxRealEnemyScore;

            var isLocked = _lockManager.IsLocked(StrangersLockUid, LockItemType.Social);
            var playerIndex = Util.GetRandomWeightedNumber(0, FilteredCount, _leaderboardWeights);
            for (var i = 0; i < _cachedLeaderboard.Count; i++)
            {
                var item = _cachedLeaderboard[i];
                if (_friends.Contains(item.Uid)) continue;
                if (isLocked || i >= playerIndex)
                {
                    item.Score = (int)(easyMultiplier * item.Score);
                }
                else
                {
                    var multiplier = Random.Range(EnemyScoreMultiplierMin, EnemyScoreMultiplierMax);
                    item.Score = (int)(multiplier * localPlayerScore);
                }
            }
        }

        private void InsertPlayerAndSort(LeaderboardItem localPlayerItem, List<LeaderboardItem> leaderboard)
        {
            var count = _cachedLeaderboard.Count;
            if (count > FilteredCount)
            {
                _cachedLeaderboard.RemoveRange(FilteredCount, count - FilteredCount);
            }
            leaderboard.AddRange(_cachedLeaderboard);
            leaderboard.Add(localPlayerItem);
            leaderboard.Sort(_standardItemComparer);
        }

        private static void AssignRanks(List<LeaderboardItem> leaderboard)
        {
            for (var i = 0; i < leaderboard.Count; i++)
            {
                var leaderboardItem = leaderboard[i];
                leaderboardItem.Rank = i + 1;
            }
        }
    }

    public class StandardItemComparer : IComparer<LeaderboardItem>
    {
        public int Compare(LeaderboardItem x, LeaderboardItem y)
        {
            return x.Score != y.Score ? -x.Score.CompareTo(y.Score)
                : x.Rank.CompareTo(y.Rank);
        }
    }
}