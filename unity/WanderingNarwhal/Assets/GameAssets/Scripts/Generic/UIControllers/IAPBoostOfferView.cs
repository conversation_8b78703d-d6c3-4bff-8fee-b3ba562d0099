using System;
using BBB;
using BBB.Core.ResourcesManager;
using BBB.Core.ResourcesManager.Asset;
using PBConfig;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class IAPBoostOfferView : BbbMonoBehaviour
{

	[SerializeField] private Image _offerIcon;
	[SerializeField] private LocalizedTextPro _offerName;
	[SerializeField] private LocalizedTextPro _offerDescription;
	[SerializeField] private TextMeshProUGUI _offerPrice;
	[SerializeField] private Button _buyOfferButton;

	private IAssetLoaded<Sprite> _assetLoaded;
	private Action<IAPStoreVirtualItemPackConfig> OnOfferPurchaseClicked;
	
	
	public void Setup(IAssetsManager assetsManager, IAPStoreVirtualItemPackConfig offer, Action<IAPStoreVirtualItemPackConfig> onOfferPurchasedClicked)
	{
		OnOfferPurchaseClicked = onOfferPurchasedClicked;
		assetsManager.LoadAsync<Sprite>(offer.Icon).Then((Action<IAssetLoaded<Sprite>>) OnSpriteLoaded).Done();
		_offerName.SetTextId(offer.Name);
		_offerName.Text.text += " x9";
		_offerDescription.SetTextId(offer.Description);
		var offerPack = offer.Packs.Find(x => x.Amount == 9);
		_offerPrice.text = offerPack.Price.Currencies["regular"].ToString();
		_buyOfferButton.ReplaceOnClick(() => OnOfferPurchaseClicked.SafeInvoke(offer));
	}

	private void OnSpriteLoaded(IAssetLoaded<Sprite> assetLoaded)
	{
		ReleaseAsset();
		_assetLoaded = assetLoaded;
		_offerIcon.sprite = assetLoaded.Get();
		gameObject.SetActive(true);
	}

	private void ReleaseAsset()
	{
		if (_assetLoaded != null)
		{
			_assetLoaded.Dispose();
			_assetLoaded = null;
		}
	}

	protected override void OnDestroy()
	{
		base.OnDestroy();
		
		ReleaseAsset();
	}

	public void Hide()
	{
		gameObject.SetActive(false);
	}
}
