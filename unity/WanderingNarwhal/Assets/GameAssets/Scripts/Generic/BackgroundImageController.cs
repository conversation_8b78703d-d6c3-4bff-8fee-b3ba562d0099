using BBB;
using BBB.Core;
using BBB.DI;
using GameAssets.Scripts.Generic;
using UnityEngine;

public class BackgroundImageController : BbbMonoBehaviour, IContextInitializable
{
    public IBackgroundImageGetter BackgroundImageGetter { get; set; }
    private GameObject _imageGo;
    private Transform _defaultParent;
    private Transform _defaultParentParent;
    private const int DefaultSortOrder = -3;

    private void Awake()
    {
        _defaultParent = transform.parent;
        _defaultParentParent = _defaultParent.parent;
        var canvas = GetComponent<Canvas>();
        if (canvas == null)
            return;
        canvas.overrideSorting = true;
        canvas.sortingOrder = DefaultSortOrder;
    }
    
    public void InitializeByContext(IContext context)
    {
        BackgroundImageGetter.InitializeByContext(context);
    }

    public void Refresh()
    {
        if (!BackgroundImageGetter.ShouldRefreshBg())
            return;

        if (_imageGo != null)
        {
            Destroy(_imageGo);
            _imageGo = null;
        }

        var prefab = BackgroundImageGetter.GetPrefab();
        if (prefab == null) return;
        UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
        _imageGo = Instantiate(prefab, transform);
        UnityEngine.Profiling.Profiler.EndSample();
    }

    public void MoveUpInHierarchy()
    {
        if (transform.parent == null)
        {
            BDebug.LogError(LogCat.Match3, "BG Image is already at the root of hierarchy");
            return;
        }

        // Note: reason of why the transform is moved up is unknown, but there was added a restoration of default parent anyway. -VK
        transform.SetParent(_defaultParentParent, false);
        transform.SetSiblingIndex(0);
    }

    public void RestoreParentInHierarchy()
    {
        transform.SetParent(_defaultParent, false);
        transform.SetSiblingIndex(0);
    }

#if BBB_TEST
// Obsolete. Preserved for reference.
    // private void OnDestroy()
    // {
    //     EventDispatcher.RemoveListener<TileRevealFinishedEvent>(TileRevealFinishedCatcher);
    // }
    //
    // private void TileRevealFinishedCatcher(TileRevealFinishedEvent obj)
    // {
    //     AddTest();
    // }
    //
    // private void AddTest()
    // {
    //     if (gameObject.GetComponent<Button>() == null)
    //     {
    //         Button button = gameObject.AddComponent<Button>();
    //         button.onClick.AddListener(OnClick);
    //         Image image = gameObject.GetComponent<Image>();
    //         image.raycastTarget = true;
    //         button.targetGraphic = gameObject.GetComponent<Image>();
    //     }
    // }
    //
    // private void OnClick()
    // {
    //     Debug.Log("~~on On_SimpleTap ");
    //     var levelController = _levelController as IForceWinLosable;
    //
    //     levelController.ForceWin();
    //
    //     Button button = gameObject.AddComponent<Button>();
    //     Destroy(button);
    //     Image image = gameObject.GetComponent<Image>();
    //     Destroy(image);
    // }
#endif
}
