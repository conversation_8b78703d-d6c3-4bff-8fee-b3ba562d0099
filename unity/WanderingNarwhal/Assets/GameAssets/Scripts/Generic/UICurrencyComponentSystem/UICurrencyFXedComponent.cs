using BBB.DI;
using BBB.UI.Core;
using BBB.Wallet;
using TMPro;
using UnityEngine;

namespace BBB.UI
{
    public class UICurrencyFXedComponent : ContextedUiBehaviour
    {
        [SerializeField] private TextMeshProUGUI _number;
        [SerializeField] private GenericRewardItem _genericRewardItem;
        private CurrencyIconsLoader _currencyIconsLoader;

        protected override void InitWithContextInternal(IContext context)
        {
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
        }

        public void Setup(string currencyUid, int number)
        {
            LazyInit();

            var array = _currencyIconsLoader.GetCurrencySprites(currencyUid);

            if (array == null || (array.Length == 1 && array[0] == null))
            {
                Debug.LogError("Couldn't find icons for currency: " + currencyUid);
                return;
            }
            
            _genericRewardItem.Setup(array);
            _number.text = number.ToString();
        }
    }
}