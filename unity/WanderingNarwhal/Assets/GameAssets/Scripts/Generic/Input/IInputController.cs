using System;

namespace BBB.UI.Level.Input
{
    public interface IInputController
    {
        event Action<Coords> OnTileSelected;
        event Action<Coords> OnTileLongHoldEvent;
        event Action<Coords, int> OnStartTouchEvent;
        event Action<Coords> OnTileTappedEvent;
        event Action<Coords> OnEndTouchEvent;
        event Action OnTileUnselected;
        event Action BonusTimeSkipEvent;

        void SelectTile(Coords selectedCoords);
        void UnselectTileAndClearState();
        void AutoDoubleTap(Coords coords);
        void AutoSwap(Coords firstCellCoords, Coords secondCellCoords);
    }
}