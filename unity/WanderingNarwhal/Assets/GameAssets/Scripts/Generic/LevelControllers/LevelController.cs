using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using BBB.DI;
using BBB.UI.Level;
using BBB.UI.Level.Controllers;
using BBB.Map;
using BBB.Match3.Debug;
using BBB.Match3.Systems;
using BBB.Core.Wallet;
using BBB.UI.Level.Match3BoostProgressProvider;
using BBB.Wallet;
using BBB.Match3.Renderer;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Crash;
using BBB.POI;
using BebopBee.Core;
using GameAssets.Scripts.Match3.Logic;
using BBB.Audio;
using BBB.Core.Analytics;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.RaceEvents;
using BBB.Map.Location;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Narrative.Controllers;
using BBB.Social;
using BebopBee.Core.Audio;
using BebopBee.Core.UI;
using Core.Configs;
using Core.RPC;
using GameAssets.Scripts.Generic;
using TMPro;
using AssistSystem = BBB.GameAssets.UI.Level.Scripts.AssistSystem.AssistSystem;
using BBB.BrainCloud;
using BBB.EndGameEvents;
using BBB.TeamEvents;
using BBB.UI.Level.Scripts.Boosts;
using BBB.UI.Level.Views;
using BrainCloud;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Generic.Carrots;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Map.UI.Controllers;

namespace BBB.UI
{
    public class LevelController : LevelControllerBase, IForceWinLosable
    {
        private const bool SkipLevelSuccessController = false;
        private const string ItemUid = "shovel";
        private const int MovesAddCount = 5;

        public IBoostButtonsController BoostButtonsController => _boostButtons;

        protected virtual bool CanRetryLevelAfterLose => true;

        /// <summary>
        /// Delay before starting screen transition if grid fall animation is triggered by out of moves. -VK
        /// </summary>
        [SerializeField] private float _gridFallNextScreenTransitionDelay = 1.2f;

        //proxy references
        protected Camera LevelCamera;
        protected LevelTitle LevelTitle;
        protected WonderTitle WonderTitle;
        protected LevelControllerDebugMenu DebugMenu;
        protected Button MenuButton;
        protected TextMeshProUGUI EventTitle;
        protected PaletteApplier PaletteApplier;
        // repeated
        protected IModalsBuilder ModalsBuilder;
        protected IConfig Config;
        protected ILocationManager LocationManager;

        private IBoosterManager _boosterManager;
        private LevelExitMenuController _levelExitMenuController;
        private LevelRevealer _levelRevealer;
        private LevelSkipper _levelSkipper;
        private CurrencyIconsLoader _currencyIconsLoader;
        private PopupManager _popupManager;
        private AssistSystem _assistSystem;

        private IWalletManager _walletManager;

        protected IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;
        protected IEventDispatcher EventDispatcher;
        protected POIManager POIManager;
        protected WeeklyLeaderboardManager WeeklyLeaderboardManager;

        private IScreensBuilder _screensBuilder;
        private ILocalizationManager _localizationManager;
        private IRoyaleEventManager _royaleEventManager;
        private IGameEventManager _gameEventManager;

        private GoalState _progressAchievedWhenMovesBought;
        public GameEventMatch3ManagersCollection GameEventM3ManagerCollection;
        public IRaceEventMatch3Manager RaceEventMatch3Manager;
        protected IRoyaleEventMatch3Manager RoyaleEventMatch3Manager;
        protected ITeamCoopEventMatch3Manager TeamCoopEventMatch3Manager;
        protected IButlerGiftManager ButlerGiftManager;

        protected ChallengeTriviaManager ChallengeTriviaManager;
        protected SdbManager SdbManager;
        protected bool IsStartedLevelPlay;

        private Match3BoostProgressProvider _match3BoostProgress;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;
        private LevelNarrativeController _levelNarrativeController;
        private IEpisodeTaskManager _episodeTaskManager;
        private ICarrotsManager _carrotsManager;
        private readonly List<string> _spentAutoBoostersAtStart = new();

        /// <summary>
        /// Is player currently have lives above max limit.
        /// </summary>
        /// <remarks>
        /// Flag for situations when player started play with additional lives.
        /// (additional lives can be received from gifts and special rewards.)
        /// If true, then we must force restore live at victory (above regular limit),
        /// in other cases lives will be always capped to avoid bugs. -VK
        /// </remarks>
        protected bool IsStartedLevelWithAdditionalLives;
        protected bool ResultReceived;
        protected bool NextStageReached;
        protected bool CanSendChallenge;

        private Match3ResourceProvider _resourceProvider;
        private int _stageOnStart;
        private StagePaletteApplier _stagePaletteApplier;
        private CanvasScaler _canvasScaler;
        private Vector2 _referenceResolutionOnOpen;
        private int _purchasePlus5Counter;
        private StartLevelController _startLevelController;
        private IScreensManager _screensManager;
        private ILockManager _lockManager;
        private LevelSuccessAdManager _levelSuccessAdManager;
        private LevelStarter _levelStarter;

        public override bool IsEditor => false;
        public HintSystem HintSystem => HintSystemRef;

        protected virtual ScreenType FallbackScreen => ScreenType.EpisodeScreen;
        protected virtual bool ProcessNonGameEventManagers => true;

        public override void Init(IContext previousContext)
        {
            M3Debug.Disabled = true;
            POIManager = previousContext.Resolve<POIManager>();

            base.Init(previousContext);

            _walletManager = previousContext.Resolve<IWalletManager>();
            EventDispatcher = previousContext.Resolve<IEventDispatcher>();
            _currencyIconsLoader = previousContext.Resolve<CurrencyIconsLoader>();
            _popupManager = previousContext.Resolve<PopupManager>();
            _levelNarrativeController = previousContext.Resolve<LevelNarrativeController>();
            _royaleEventManager = previousContext.Resolve<IRoyaleEventManager>();
            _gameEventManager = previousContext.Resolve<IGameEventManager>();
            WeeklyLeaderboardManager = previousContext.Resolve<WeeklyLeaderboardManager>();
            ButlerGiftManager = previousContext.Resolve<IButlerGiftManager>();
            _screensManager = previousContext.Resolve<IScreensManager>();
            _lockManager = previousContext.Resolve<ILockManager>();
            _episodeTaskManager = previousContext.Resolve<IEpisodeTaskManager>();
            _levelStarter = previousContext.Resolve<LevelStarter>();
            var levelCameraInitialized = EventDispatcher.GetMessage<LevelCameraInitialized>();
            levelCameraInitialized.Set(LevelCamera);
            EventDispatcher.TriggerEvent(levelCameraInitialized);

            var levelContextInitialized = EventDispatcher.GetMessage<LevelContextInitialized>();
            levelContextInitialized.Set(Context);
            EventDispatcher.TriggerEvent(levelContextInitialized);
        }


        protected override void AggregateProxyDependencies()
        {
            base.AggregateProxyDependencies();
            var proxy = GetComponent<LevelControllerReferenceProxy>();
            LevelCamera = proxy.LevelCamera;
            _levelExitMenuController = proxy.LevelExitMenuController;
            LevelTitle = proxy.LevelTitle;
            WonderTitle = proxy.WonderTitle;
            DebugMenu = proxy.DebugMenu;
            MenuButton = proxy.MenuButton;
            _levelRevealer = proxy.LevelRevealer;
            _levelSkipper = proxy.LevelSkipper;
            _stagePaletteApplier = proxy.StagePaletteApplier;
            _canvasScaler = proxy.CanvasScaler;
            EventTitle = proxy.EventTitle;
            PaletteApplier = proxy.PaletteApplier;
        }

        protected override void RegisterSpecificServices(UnityContext unityContext, IContext previousContext)
        {
            _resourceProvider = previousContext.Resolve<Match3ResourceProvider>();
            ChallengeTriviaManager = previousContext.Resolve<ChallengeTriviaManager>();
            SdbManager = previousContext.Resolve<SdbManager>();
            _referenceResolutionOnOpen = _canvasScaler.referenceResolution;

            if (_assistSystem == null)
            {
                _assistSystem = new AssistSystem();
                _match3BoostProgress = new Match3BoostProgressProvider();
                _levelAnalyticsReporter = new LevelAnalyticsReporter();
            }

            NextStageReached = false;
            ResultReceived = false;
            IsStartedLevelPlay = false;
            IsStartedLevelWithAdditionalLives = false;

            var gameEventManager = previousContext.Resolve<IGameEventManager>();
            var raceEventManager = previousContext.Resolve<IRaceEventManager>();
            var royaleEventManager = previousContext.Resolve<IRoyaleEventManager>();
            var teamCoopEventManager = previousContext.Resolve<ITeamEventManager>();
            var screenManager = previousContext.Resolve<IScreensManager>();
            var currentScreen = screenManager.GetCurrentScreenType();
            GameEventM3ManagerCollection = GameEventMatch3ManagerFactory.CreateManagersCollection(gameEventManager, currentScreen);
            RaceEventMatch3Manager = GameEventMatch3ManagerFactory.CreateRaceEventManager(raceEventManager);
            RoyaleEventMatch3Manager = GameEventMatch3ManagerFactory.CreateRoyaleEventManager(royaleEventManager);
            TeamCoopEventMatch3Manager = GameEventMatch3ManagerFactory.CreateTeamCoopEventMatch3Manager(teamCoopEventManager);

            unityContext.AddServiceToRegisterOverride<IMatch3SharedResourceProvider>(_resourceProvider);
            unityContext.AddServiceToRegisterOverride<IMatch3BoostProgressProvider>(_match3BoostProgress);
            unityContext.AddServiceToRegisterOverride<IAssistParamsProvider>(_assistSystem);
            unityContext.AddServiceToRegisterOverride<ILevelRevealer>(_levelRevealer);
            unityContext.AddServiceToRegisterOverride<ILevelSkipper>(_levelSkipper);
            unityContext.AddServiceToRegisterOverride<Camera>(LevelCamera, Match3Constants.LevelCameraTag);

            unityContext.AddServiceToRegisterOverride<GameEventMatch3ManagersCollection>(GameEventM3ManagerCollection);
            unityContext.AddServiceToRegisterOverride<IRaceEventMatch3Manager>(RaceEventMatch3Manager);
            unityContext.AddServiceToRegisterOverride<IRoyaleEventMatch3Manager>(RoyaleEventMatch3Manager);
            unityContext.AddServiceToRegisterOverride<ITeamCoopEventMatch3Manager>(TeamCoopEventMatch3Manager);
            unityContext.AddServiceToRegisterOverride<ILevelAnalyticsReporter>(_levelAnalyticsReporter);
            _carrotsManager = new CarrotsManager();
            _carrotsManager.Init(previousContext);
        }

        protected override void RunGame(bool skipLevelStartNarrative = false)
        {
            base.RunGame(skipLevelStartNarrative);
            if (gameObject == null)
            {
                BDebug.LogError(LogCat.Match3, "RunGame: LevelController go was already destroyed");
                return;
            }

            if (_playerManager == null)
                throw new NullReferenceException("_playerManager is null");

            if (_playerManager.Player == null)
                throw new NullReferenceException("_playerManager.Player is null");

            var inventory = _playerManager.PlayerInventory;
            if (inventory == null)
                throw new NullReferenceException("Inventory is null");

            if (_levelAnalyticsReporter == null)
                throw new NullReferenceException("LevelAnalyticsReporter is null");

            LevelTitle.gameObject.SetActive(true);

            var level = _levelHolder.level;
            if (level.Stage == 0 && !skipLevelStartNarrative)
            {
                _levelNarrativeController.Setup(level.Config.Uid, NarrativeDialogsPlaces.LevelStartMatch3, () => HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(false));
            }
            else
            {
                _levelNarrativeController.CleanDialog();
            }

            ResetPurchasePlus5Counter();
            level.IncrementNumPlayed();

            _stageOnStart = level.Stage;
            ChallengeTriviaManager.ProcessOnLevelStart();
            SdbManager.ProcessOnLevelStart();

            if (_levelRevealer == null)
                throw new NullReferenceException("LevelRevealer is null");

            WonderTitle.enabled = false;
            EventTitle.enabled = false;

            var levelStarted = EventDispatcher.GetMessage<LevelStarted>();
            levelStarted.Set(level.Config.Uid, level.Stage);
            EventDispatcher.TriggerEvent(levelStarted);

            if (_levelRevealer != null)
            {
                _levelRevealer.Reveal(null);
            }

            if (GameController != null)
            {
                GameController.Run(onEventsManagersSetup: PrewarmEventsFx);
            }

            _assistSystem.RefreshForLevel(_levelHolder.level);

            //TODO game event parallelism - fix later after clarification regarding analytics for multiple events
            var firstActiveGameEventId = GameEventM3ManagerCollection.GetFirstActiveGameEventUid();
            _levelAnalyticsReporter.LevelStarted(level, Config, firstActiveGameEventId);

            var discoBallValue = M3Settings.DiscoBallEffectDefaultValue;
            if (SdbManager.SdbActiveWhenLevelStart)
            {
                discoBallValue = Mathf.Max(SdbManager.SdbEffectValue, discoBallValue);
            }

            SetSuperDiscoBallValue(discoBallValue);
        }

        private void PrewarmEventsFx()
        {
            if (GameEventM3ManagerCollection?.IsAnyManager(m => m.ActiveGameEvent is
                    { Status: GameEventStatus.Active, GameplayType: GameEventGameplayType.Collection }) ?? false)
            {
                _rendererContainers.PrewarmEventFx(FxType.CollectEventTile, 5);
            }

            if (RaceEventMatch3Manager?.IsAnyRaceEventOfType(RaceEventType.Collect, out _) ?? false)
            {
                _rendererContainers.PrewarmEventFx(FxType.DiscoRushCollect, 5);
            }
        }

        protected override void OnContextInitialized(IContext previousContext, IContext context)
        {
            BDebug.Log(LogCat.Match3, $"LevelController context initialized, InputLock = true");
            GameController.LockInput(true);

            if (Config == null)
            {
                Config = previousContext.Resolve<IConfig>();
                POIManager = previousContext.Resolve<POIManager>();
                _localizationManager = previousContext.Resolve<ILocalizationManager>();
                ModalsBuilder = previousContext.Resolve<IModalsBuilder>();
                _playerManager = previousContext.Resolve<IPlayerManager>();
                _boosterManager = previousContext.Resolve<IBoosterManager>();
                LocationManager = previousContext.Resolve<ILocationManager>();
                _livesManager = previousContext.Resolve<ILivesManager>();
                _screensBuilder = previousContext.Resolve<IScreensBuilder>();
            }

            MenuButton.ReplaceOnClick(OnControlMenuClicked);
            var level = _levelHolder.level;
            var assistValuePanel = context.Resolve<AssistValuesPanelController>();

            // do not allow menu button for nux-level
            MenuButton.interactable = level.LevelUid != "special0";

            LevelTitle.Setup(_localizationManager, level);
            WonderTitle.Init(_localizationManager, POIManager, level);

            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(true);
            DebugMenu.Init(this, assistValuePanel);
            _levelSkipper.Init(this);
            _levelRevealer.Init(this);

            _levelSuccessAdManager = new LevelSuccessAdManager(context);
        }

        protected virtual void OnFirstMove()
        {
            GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnFirstMove());
            if (ProcessNonGameEventManagers)
            {
                RaceEventMatch3Manager.ProcessOnFirstMove();
            }

            ButlerGiftManager.ProcessOnFirstMove();
            SdbManager.ProcessOnFirstMove();

            if (WeeklyLeaderboardManager.Status == WeeklyLeaderboardStatus.Active)
            {
                WeeklyLeaderboardManager.IncrementFailedAttempt();
            }
        }

        protected override void OnRemainingMovesChanged(RemainingMovesChanged ev)
        {
            base.OnRemainingMovesChanged(ev);

            if (ResultReceived)
            {
                TurnsPanel.StopAnimation();
            }

            if (!IsStartedLevelPlay && ShouldShowQuitWarning)
            {
                OnFirstMove();

                bool lifeSpend = false;
                if (!_livesManager.IsInfiniteLivesActive)
                {
                    // Spend live silently on first move on current level.
                    // this live will be restored if player wins.
                    IsStartedLevelWithAdditionalLives = _livesManager.NumberOfLives > _livesManager.MaxLives;

                    _livesManager.SpendLife(hidden: true);
                    lifeSpend = true;
                }

                var makeScorePenalizable = false;
                foreach (var manager in GameEventM3ManagerCollection)
                {
                    makeScorePenalizable |= manager.MakeScoresPenalizable();
                }

                var madePenalizable = makeScorePenalizable;
                if (ProcessNonGameEventManagers)
                {
                    var makeRaceEventsPenalizable = RaceEventMatch3Manager.MakeEventsPenalizable();
                    var makeRoyaleEventsPenalizable = RoyaleEventMatch3Manager.MakeEventsPenalizable();
                    madePenalizable |= makeRaceEventsPenalizable || makeRoyaleEventsPenalizable;
                }

                // Game needs to be saved to handle situation if player closes the app
                // during level play and then restarts.
                if (madePenalizable || lifeSpend)
                    EventDispatcher.TriggerEvent(EventDispatcher.GetMessage<LevelPlayStateChanged>());

                IsStartedLevelPlay = true;
            }
        }

        public override void OnShow()
        {
            base.OnShow();
            gameObject.SetActive(true);
            if (_levelRevealer != null)
            {
                _levelRevealer.HideInstant();
            }
            var level = _levelHolder.level;
            // Removed level intro for now, as requested - MF
            /*_startupLevelController.Setup(_resourceProvider, level, _goalViewHelper, RunGame);
            _startupLevelController.ShowModal(ShowMode.Immediate);*/

            StartCoroutine(RunGameCoroutine());
            FrameRateAdjuster.AdjustFrameRateInMatch3();
            CrashLoggerService.Log($"Starting Level: {level.Config.Uid} config:{Config.GetHash<FBConfig.ProgressionLevelConfig>()}");
            NextStageReached = false;
            ResultReceived = false;
            _stagePaletteApplier.Apply(level.Config.GetPaletteStage());
            Subscribe();
        }

        private IEnumerator RunGameCoroutine()
        {
            yield return null;
            RunGame();
        }

        private void IncrementPurchasePlus5Counter()
        {
            ++_purchasePlus5Counter;
        }

        private void ResetPurchasePlus5Counter()
        {
            _purchasePlus5Counter = 0;
        }

        protected override void RetryLevelAfterLose()
        {
            base.RetryLevelAfterLose();

            gameObject.SetActive(true);
            if (_levelRevealer != null)
                _levelRevealer.HideInstant();

            CrashLoggerService.Log($"Re-starting Level: {_levelHolder.level?.Config.Uid} config:{Config.GetHash<FBConfig.ProgressionLevelConfig>()}");
            NextStageReached = false;
            ResultReceived = false;
            IsStartedLevelPlay = false;
            IsStartedLevelWithAdditionalLives = false;
            _levelExitMenuController.ResetOnRetry(_levelHolder.level);

            SetupRendererContainers(_levelHolder.level);
            RunGame(true);
        }

        public override void OnHide()
        {
            base.OnHide();
            FrameRateAdjuster.AdjustFrameRateInGame();
            GameController.GridController.Clear();
            GameController.Clear();
            Unsubscribe();
            _canvasScaler.referenceResolution = _referenceResolutionOnOpen;
            BackgroundImageController.RestoreParentInHierarchy();
        }

        private void Subscribe()
        {
            Unsubscribe();
            EventDispatcher.AddListener<MoreMovesAddedEvent>(MoreMovesAddedHandler);
            EventDispatcher.AddListener<MoreMovesCancelledEvent>(MoreMovesCancelledHandler);
            EventDispatcher.AddListener<EquippedAutoBoostAppliedEvent>(AutoBoostEquippedHandler);
            EventDispatcher.AddListener<BoardRevealStarted>(BoardRevealStartedHandler);
        }

        private void Unsubscribe()
        {
            EventDispatcher.RemoveListener<MoreMovesAddedEvent>(MoreMovesAddedHandler);
            EventDispatcher.RemoveListener<MoreMovesCancelledEvent>(MoreMovesCancelledHandler);
            EventDispatcher.RemoveListener<EquippedAutoBoostAppliedEvent>(AutoBoostEquippedHandler);
            EventDispatcher.RemoveListener<BoardRevealStarted>(BoardRevealStartedHandler);
        }

        void IForceWinLosable.ForceWin()
        {
            GameEventM3ManagerCollection.ForEveryManager(manager => manager.HandleDebugWin());

            if (ProcessNonGameEventManagers)
            {
                RaceEventMatch3Manager.HandleDebugWin();
                RoyaleEventMatch3Manager.HandleDebugWin();
                TeamCoopEventMatch3Manager.HandleDebugWin();
            }

            TileController.ForceFinishReveal();
            _episodeTaskManager.HandleDebugWin(_screensManager.GetCurrentScreenType(), _levelHolder.level);

            var levelResultPredicted = new LevelResultPredicted();
            levelResultPredicted.Set(LevelOutcome.Win);
            levelResultPredicted.AllowRecording = DebugMatch3Settings.IsDebugRecording();
            OnLevelResultPredicted(levelResultPredicted);
            OnLevelEnded(new LevelEndedEvent()
            {
                LevelOutcome = LevelOutcome.Win
            });
        }

        void IForceWinLosable.ForceLose()
        {
            TileController.ForceFinishReveal();
            var levelResultPredicted = new LevelResultPredicted();
            levelResultPredicted.Set(LevelOutcome.Lose);
            levelResultPredicted.AllowRecording = false;
            OnLevelResultPredicted(levelResultPredicted);
            OnLevelEnded(new LevelEndedEvent()
            {
                LevelOutcome = LevelOutcome.Lose,
                AllowShowOutOfMovesModal = true
            });
        }

        public void CollectGoals()
        {
            GoalPanel.SetAllGoalsFinished();
            GameController.DebugCompleteGoals();
        }

        void IForceWinLosable.GetReplayData()
        {
            GUIUtility.systemCopyBuffer = _levelAnalyticsReporter.GetReplayData();
        }


        public void SetOneMove()
        {
            GameController.DebugSetOneMoveLeft();
        }

        public void SetCustomMoves(int moves)
        {
            GameController.DebugSetMovesLeft(moves);
        }

        protected void ProcessOnLevelWinGameEvents()
        {
            var level = _levelHolder.level;

            foreach (var manager in GameEventM3ManagerCollection)
            {
                manager.FinalizeScore();
                var collectedGameEventScore = manager.CollectedScores;
                var gameEventUid = manager.ActiveGameEventUid;
                manager.ProcessOnLevelWin();
                if (collectedGameEventScore > 0 && !gameEventUid.IsNullOrEmpty())
                {
                    var gamePlayType = manager.GetGameplayType();
                    var curUid = InventoryItems.GetGameEventScoreUid(gamePlayType);
                    var gameEventScoreTransaction = new Transaction()
                        .AddTag(TransactionTag.LevelReward)
                        .SetAnalyticsData(CurrencyFlow.Level.Name, level.LevelUid, level.Stage.ToString())
                        .SetExtraData(gameEventUid)
                        .Earn(curUid, collectedGameEventScore);
                    WalletTransactionController.MakeOnlyVisualTransaction(gameEventScoreTransaction);
                }
            }
        }

        protected override void OnLevelResultPredicted(LevelResultPredicted ev)
        {
            if (ResultReceived)
                return;

            ResultReceived = true;

            var levelWon = ev.Arg0 == LevelOutcome.Win;

            if (levelWon)
            {
                // enforcing modal preloading to avoid empty frames on board disappearing
                GetLevelSuccessController();
            }

            var level = _levelHolder.level;
            var stagePlayed = level.Stage;
            var levelActualStage = LocationManager.GetLevelState(level.LevelUid).Stage;
            if (levelActualStage > stagePlayed)
            {
                level.IsReplay = true;
            }

            if (ProcessNonGameEventManagers)
            {
                RoyaleEventMatch3Manager.RestorePenalizableScore();
            }


            if (levelWon)
            {
                TriggerLevelWonEvent(level);

                // TODO: Migrate all these calls to managers to an event listener
                level.WinLevel(_playerManager);
                _playerManager.CurrentLocation.IncWins();
                _playerManager.Player.IncCurrentWinStreak();
                NextStageReached = levelActualStage < level.Stage;

                if (ProcessNonGameEventManagers)
                {
                    RaceEventMatch3Manager.ProcessOnLevelWin();
                    RoyaleEventMatch3Manager.ProcessOnLevelWin();
                    TeamCoopEventMatch3Manager.ProcessOnLevelWin();
                }

                ButlerGiftManager.IncrementStreak();
                CanSendChallenge = ChallengeTriviaManager.ProcessOnLevelWin(level, level.Stage);
                SdbManager.ProcessOnLevelWin(level, level.Stage);

                var poiUid = level.ViewConfig?.PoiEntityUid;
                if (!poiUid.IsNullOrEmpty())
                {
                    UpdateLevelPOIState();
                    var poiEntity = POIManager.GetEntity(poiUid);
                    if (poiEntity != null)
                    {
                        poiEntity.Unlock();
                    }
                    else
                    {
                        BDebug.LogError(LogCat.General, $"Couldn't find poi entity {poiUid}");
                    }
                }


                LocationManager.OnLevelPassed(level.Config.Uid, NextStageReached, level.GrindReplays, IsStartedLevelPlay);

                var transaction = new Transaction()
                    .Earn(level.RewardsDictionary)
                    .SetAnalyticsData(CurrencyFlow.Level.Name, level.LevelUid, level.Stage.ToString())
                    .AddTag(TransactionTag.LevelReward);

                WalletTransactionController.MakeTransaction(transaction);
                ProcessOnLevelWinGameEvents();

                if (IsStartedLevelPlay)
                {
                    // Restore life, which was silently subtracted after first turn.
                    _livesManager.AddLife(new LivesData
                    {
                        Hidden = true,
                        IsIgnoreCap = IsStartedLevelWithAdditionalLives
                    });
                }

                _playerManager.Player.TryIncrementFirstTryWinsCount();
                _livesManager.ResetLossStreak();
            }
            else
            {
                if (IsStartedLevelPlay)
                {
                    if (ev.Arg0 == LevelOutcome.ShuffleFailed)
                    {
                        // If level lost due shuffle failed, then restore live anyway (as if player win).
                        // this can only happen if level design is incorrect, so we don't need to take penalty from player. -VK
                        _livesManager.AddLife(new LivesData
                        {
                            Hidden = true,
                            IsIgnoreCap = IsStartedLevelWithAdditionalLives
                        });

                        // Shuffle Failed is not counted as a loss in the streak
                        GameEventM3ManagerCollection.ForEveryManager(manager => { manager.ProcessOnShuffleFailed(); });

                        if (ProcessNonGameEventManagers)
                        {
                            RaceEventMatch3Manager.ProcessOnShuffleFailed();
                        }

                        ButlerGiftManager.ProcessOnShuffleFailed();
                        level.DecrementNumPlayed();
                        SdbManager.ProcessOnShuffleFailed();
                    }
                    else
                    {
                        GameEventM3ManagerCollection.ForEveryManager(manager => { manager.ProcessOnLevelLose(); });

                        if (ProcessNonGameEventManagers)
                        {
                            RaceEventMatch3Manager.ProcessOnLevelLose();
                            RoyaleEventMatch3Manager.ProcessOnLevelLose();
                            TeamCoopEventMatch3Manager.ProcessOnLevelLose();
                        }

                        _playerManager.CurrentLocation.IncLoses();
                        _playerManager.Player.ResetWinStreak();
                        ButlerGiftManager.ResetStreak();
                        _livesManager.AddLossToStreak();
                        SdbManager.ProcessOnLevelLose(IsStartedLevelPlay);
                        level.LoseLevel(Config);
                    }
                }
                else
                {
                    GameEventM3ManagerCollection.ForEveryManager(manager => { manager.ProcessOnExit(); });

                    if (ProcessNonGameEventManagers)
                    {
                        RaceEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                        RoyaleEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                        TeamCoopEventMatch3Manager.ProcessOnExit(IsStartedLevelPlay);
                    }

                    ButlerGiftManager.ResetStreak();
                    SdbManager.ProcessOnLevelLose(IsStartedLevelPlay);
                    level.ExitWithoutPlaying();
                }
            }

            var levelFinishedEvent = EventDispatcher.GetMessage<LevelFinishedEvent>();
            levelFinishedEvent.Set(level.Config.Uid);
            levelFinishedEvent.isWin = ev.Arg0 == LevelOutcome.Win;
            levelFinishedEvent.outcome = ev.Arg0;
            EventDispatcher.TriggerEvent(levelFinishedEvent);
            EventDispatcher.TriggerEvent(EventDispatcher.GetMessage<LevelPlayStateChanged>());

            SaveLevelData(ev.Arg0, level);
        }

        protected void TriggerLevelWonEvent(ILevel level)
        {
            var levelWonEvent = EventDispatcher.GetMessage<LevelWonEvent>();
            levelWonEvent.Set(level.GetNumPlayed());
            _eventDispatcher.TriggerEvent(levelWonEvent);
        }

        private void SaveLevelData(LevelOutcome levelOutcome, ILevel level)
        {
            var currentLevel = _levelHolder.level;
            if (level == null)
            {
                Debug.LogError("Level is null!"); //@NOCHECKIN
            }

            if (currentLevel == null)
            {
                Debug.LogError("Curr level is null!"); //@NOCHECKIN
            }

            var lastLevelPlayedData = new LastLevelPlayedData
            {
                LastPlayedMarkerUid = currentLevel.Config.Uid,
                AnimationsBaseLevelUid = currentLevel.Config.Uid,
                LevelStage = currentLevel.Stage,
                ProgressUpdate = NextStageReached,
                LevelOutcome = levelOutcome,
                Location = currentLevel.Config.LocationUid,
                LevelUid = currentLevel.Config.Uid,
                LevelNum = level.GetNum(Config),
                LastScenePlayed = _playerManager.Player.CurrentEpisodeScene
            };

            lastLevelPlayedData.Save();
        }

        private int CompetitionEventStreakBeLost()
        {
            var firstCompetitionEvent = GameEventM3ManagerCollection.GetFirstActiveGameEvent(manager => manager.ActiveGameEvent is { GameplayType: GameEventGameplayType.Competition }
                                                                                                        && manager.ActiveGameEvent.IsMultiplierScoreStreakActive());

            return firstCompetitionEvent?.GetScoreMultiplier(true) ?? 0;
        }

        private int NonCompetitionEventScoreBeLost()
        {
            var manager = GameEventM3ManagerCollection.GetFirstManager(
                manager => manager.ActiveGameEvent is not { GameplayType: GameEventGameplayType.Competition or GameEventGameplayType.Completion or GameEventGameplayType.Sweepstakes });

            return manager?.ScoresThatWouldBeLost ?? 0;
        }

        private int SweepstakesEventScoreBeLost()
        {
            var manager = GameEventM3ManagerCollection.GetFirstManager(
                manager => manager.ActiveGameEvent is { GameplayType: GameEventGameplayType.Sweepstakes });

            return manager?.ScoresThatWouldBeLost ?? 0;
        }

        private int CompletionEventScoreBeLost()
        {
            // Fallback to Completion event
            var manager = GameEventM3ManagerCollection.GetFirstManager(
                manager => manager.ActiveGameEvent is { GameplayType: GameEventGameplayType.Completion });

            return manager?.ScoresThatWouldBeLost ?? 0;
        }

        private Tuple<float, float> GetGridPositions()
        {
            var corners = new Vector3[4];
            (GameController.GridController as GridController).RectTransform().GetWorldCorners(corners);
            return new Tuple<float, float>(corners[1].y, corners[0].y);
        }

        private bool IsDoubleScoreActive(Action<int> multiplierCallback = null)
        {
            if ((_screensBuilder.CurrentScreenType & ScreenType.SideMap) == 0)
            {
                if (RaceEventMatch3Manager?.IsAnyEventDoubleScoreActive() ?? false)
                {
                    var multiplier = RaceEventMatch3Manager.GetHighestScoreMultiplier(true);
                    if (multiplier > 1)
                    {
                        multiplierCallback?.Invoke(multiplier);
                        return true;
                    }
                }

                if (WeeklyLeaderboardManager.IsMultiplierScoreStreakActive())
                {
                    var multiplier = WeeklyLeaderboardManager.GetScoreMultiplier(true);
                    if (multiplier > 1)
                    {
                        multiplierCallback?.Invoke(multiplier);
                        return true;
                    }
                }
            }

            foreach (int i in Enum.GetValues(typeof(GameEventGameplayType)))
            {
                if (i < 0)
                    continue;

                var gameEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                    ev.Status == GameEventStatus.Active && ev.CanShowInScreen(_screensBuilder.CurrentScreenType) &&
                    (int)ev.GameplayType == i);

                if (gameEvent != null && gameEvent.IsMultiplierScoreStreakActive())
                {
                    var multiplier = gameEvent.GetScoreMultiplier(true);
                    if (multiplier > 1)
                    {
                        multiplierCallback?.Invoke(multiplier);
                        return true;
                    }
                }
            }

            return false;
        }

        protected override void OnLevelEnded(LevelEndedEvent ev)
        {
            _levelStarter?.ResetLevel();
            var levelOutcome = ev.LevelOutcome;
            var level = _levelHolder.level;
            var levelUid = level.Config.Uid;

            if (levelOutcome is LevelOutcome.Exit or LevelOutcome.ExitWithoutPlaying)
            {
                EventDispatcher.TriggerEvent((LevelEndedUnfinishedEvent)EventDispatcher.GetMessage<LevelEndedUnfinishedEvent>().Set(levelUid, levelOutcome));
            }

            SaveLevelData(levelOutcome, level);

            if (ev.AllowShowOutOfMovesModal && levelOutcome
                    is LevelOutcome.OutOfMoves
                    or LevelOutcome.ShuffleFailed)
            {
                BDebug.LogWarning(LogCat.Match3, "Out of moves opened by level ended!");
                var isRaceEventStreakBroken =
                    RaceEventMatch3Manager?.IsAnyEventStreakBroken() ?? false;
                var discoRushLostScore = RaceEventMatch3Manager?.GetCollectEventScore() ?? 0;
                var isButlerStreakBroken = ButlerGiftManager.WillStreakBreak;
                var willLoseSdb = SdbManager.SdbActiveWhenLevelStart;

                var royaleEventScore = RoyaleEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;
                var teamEventScore = TeamCoopEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;

                var outOfMovesContentData = new OutOfMovesContentData
                {
                    CurrentOutcome = levelOutcome,
                    SweepstakesEventScoreLost = SweepstakesEventScoreBeLost(),
                    GameEventScoreLost = NonCompetitionEventScoreBeLost(),
                    CompletionGameEventScoreLost = CompletionEventScoreBeLost(),
                    CompetitionStreakLost = CompetitionEventStreakBeLost(),
                    IsRaceEventStreakBroken = isRaceEventStreakBroken,
                    PurchasePlus5Counter = _purchasePlus5Counter,
                    DiscoRushLostScore = discoRushLostScore,
                    IsButlerStreakBroken = isButlerStreakBroken,
                    SdbLost = willLoseSdb,
                    RoyaleEventStreakScore = royaleEventScore,
                    TeamEventLostScore = teamEventScore,
                    SkipBanner = false,
                };


                var ctrl = ModalsBuilder.CreateModalView<OutOfMovesController>(ModalsType.LevelOutOfMoves);
                ctrl.Setup(outOfMovesContentData);
                ctrl.SetupLevel(level, this);
                ctrl.SetupGridPosition(GetGridPositions(), LevelCamera, GoalPanel);
                ctrl.ShowModal();
                return;
            }

            if (GameController.IsLevelEnded) return;
            GameController.IsLevelEnded = true;

            var boostersToReport = new List<string>();
            foreach (var boosterUid in _spentAutoBoostersAtStart)
            {
                var reportBooster = boosterUid;
                if (_boosterManager.IsInfiniteBoosterActive(boosterUid))
                    reportBooster += "_inf";

                boostersToReport.Add(reportBooster);
            }

            switch (levelOutcome)
            {
                case LevelOutcome.Win:
                    OnSuccess();
                    break;
                case LevelOutcome.Lose:
                case LevelOutcome.ShuffleFailed:
                case LevelOutcome.OutOfMoves:
                    var levelResultPredicted = new LevelResultPredicted();
                    levelResultPredicted.Set(levelOutcome);
                    levelResultPredicted.AllowRecording = false;
                    OnLevelResultPredicted(levelResultPredicted);
                    EventDispatcher.TriggerEvent(EventDispatcher.GetMessage<LevelExitFromOutOfMovesEvent>());

                    Rx.Invoke(Mathf.Clamp(_gridFallNextScreenTransitionDelay, 0f, 10f), LoadNextScreenAfterOutOfMovesAction);

                    break;

                    void LoadNextScreenAfterOutOfMovesAction(long _)
                    {
                        LoadNextScreenAfterOutOfMoves();
                    }
                case LevelOutcome.Exit:
                    levelResultPredicted = new LevelResultPredicted();
                    levelResultPredicted.Set(levelOutcome);
                    levelResultPredicted.AllowRecording = false;
                    OnLevelResultPredicted(levelResultPredicted);
                    ShowFallbackScreen();
                    break;
                case LevelOutcome.ExitWithoutPlaying:
                    SdbManager.ProcessOnLevelLose(false);
                    level.DecrementNumPlayed();
                    ShowFallbackScreen();
                    break;
            }

            var movesLeft = GameController.RemainingMoves;
            var plus5MovesUsed = _purchasePlus5Counter;
            var originalAssistState = new AssistState(GoalsSystem.OriginalGoals, GameController.OriginalGrid);
            var progressAchievedAssistState = originalAssistState - new AssistState(GoalsSystem.GoalProgressLeft, GameController.Grid);
            var assistParams = _assistSystem.GetAssistParams(movesLeft, level.TurnsLimit, progressAchievedAssistState, originalAssistState);
            _playerManager.OnLevelEnded(level, levelOutcome, movesLeft, plus5MovesUsed, assistParams);
            _levelAnalyticsReporter.ReportLevelEnded(levelOutcome, level, ev.ShuffleCount, boostersToReport, originalAssistState, progressAchievedAssistState);

            HandleInventoryOnLevelEnded(levelOutcome);
        }

        protected void ShowFallbackScreen()
        {
            LoadingProcessTracker.LogShowScreen(FallbackScreen.ToString(), _screensManager.GetTrackingPreviousScreenType(), "LevelFallbackScreen");
            _screensBuilder.ShowScreen(FallbackScreen);
        }

        private void LoadNextScreenAfterOutOfMoves()
        {
            HandlePostLevelLose();
        }

        protected virtual void HandlePostLevelLose()
        {
            if (!CanRetryLevelAfterLose)
            {
                ShowFallbackScreen();
                return;
            }

            if (!_royaleEventManager.TryAutoShowEvent(EventAutoshowCondition.Match3, ShowRetryModal))
            {
                ShowRetryModal();
            }
        }

        private void ShowRetryModal()
        {
            _startLevelController = ModalsBuilder.CreateModalView<StartLevelController>(ModalsType.LevelStart);
            _startLevelController.SetupAsRetryLevel(Context.Resolve<GoalViewHelper>(), GoalsSystem, _levelHolder.level, OnConfirmedLevelRetry, OnConfirmedLevelExitWithoutRetry);
            _startLevelController.ShowModal(ShowMode.Delayed);
        }

        protected void OnConfirmedLevelRetry()
        {
            RetryLevelAfterLose();
        }

        protected virtual void OnConfirmedLevelExitWithoutRetry()
        {
            var lastLevelPlayedData = PlayerProfileLocal.GetLastLevelPlayedData();
            lastLevelPlayedData.LevelOutcome = LevelOutcome.Exit;
            ShowFallbackScreen();
        }

        [ContextMenu("Play FallGrid Anim")]
        private void DebugPlayFallGridAnimation()
        {
            var levelExitFromOutOfMovesEvent = EventDispatcher.GetMessage<LevelExitFromOutOfMovesEvent>();
            levelExitFromOutOfMovesEvent.IsDebug = true;
            EventDispatcher.TriggerEvent(levelExitFromOutOfMovesEvent);
        }

        private void HandleInventoryOnLevelEnded(LevelOutcome outcome)
        {
            if (outcome is LevelOutcome.ExitWithoutPlaying or LevelOutcome.ShuffleFailed)
            {
                foreach (var appliedAutoBooster in _spentAutoBoostersAtStart)
                {
                    if (ButlerGiftManager.IsButlerGift(appliedAutoBooster))
                    {
                        continue;
                    }

                    _boosterManager.RestoreEquippedAutoBooster(appliedAutoBooster);
                }
            }
            else
            {
                _playerManager.PlayerInventory.UnequipAllAutoBoosters();
            }

            _boosterManager.OnLevelEndCleanup();
            _spentAutoBoostersAtStart.Clear();
        }

        protected LevelSuccessController GetLevelSuccessController()
        {
            return ModalsBuilder.CreateModalView<LevelSuccessController>(IsLevelLeaderboardEnabled()
                ? ModalsType.LevelSuccess
                : ModalsType.LevelSuccessNoLB);
        }

        private bool IsLevelLeaderboardEnabled()
        {
            const string levelLeaderboardLockUid = "level_leaderboard";
            var levelLeaderboardEnabled = !_lockManager.IsLocked(levelLeaderboardLockUid, LockItemType.Other, out var doesExistConfig);
            return levelLeaderboardEnabled && doesExistConfig;
        }


        protected virtual void OnSuccess()
        {
            var popupRootTransform = _popupManager.GetRootTransform();
            var levelScreenRootTransform = transform.parent;

            if (!SkipLevelSuccessController || CanSendChallenge)
            {
                BackgroundImageController.MoveUpInHierarchy();
                gameObject.SetActive(false);
                
                var levelSuccessController = GetLevelSuccessController();
                var carrotsData = _carrotsManager.GetCarrotsDataForLevel(_levelHolder.level.LevelUid);
                levelSuccessController.Setup(GameEventM3ManagerCollection, RaceEventMatch3Manager, _resourceProvider,
                    _currencyIconsLoader, _levelHolder.level, GoalsSystem.AccumulativeNonLimitedScore, _stageOnStart, CanSendChallenge, carrotsData,
                    _levelSuccessAdManager.GetLevelSuccessAdData(), OnShowLocal, OnHideLocal);
                levelSuccessController.ShowModal();
            }
            else
            {
                var sideMapEventActive =
                    GameEventM3ManagerCollection.GetFirstManager(manager => manager.ActiveGameEvent is SideMapGameEvent
                    {
                        Status: GameEventStatus.Active
                    }) != null;

                switch (_screensBuilder.CurrentScreenType)
                {
                    case ScreenType.SideMapLevelScreen when sideMapEventActive:
                        LoadingProcessTracker.LogShowScreen(ScreenType.SideMapScreen.ToString(), _screensManager.GetTrackingPreviousScreenType(), "LevelSuccessPlay");
                        _screensBuilder.ShowScreen(ScreenType.SideMapScreen);
                        break;
                    default:
                        LoadingProcessTracker.LogShowScreen(ScreenType.EpisodeScreen.ToString(), _screensManager.GetTrackingPreviousScreenType(), "LevelSuccessPlay");
                        _screensBuilder.ShowScreen(ScreenType.EpisodeScreen);
                        break;
                }
            }

            return;

            void OnHideLocal(GameObject modalViewGo)
            {
                // BfgFunnelEvent.LevelWinNextButtonTap(_levelHolder.level.LevelUid);
                modalViewGo.transform.SetParent(popupRootTransform, false);
            }

            void OnShowLocal(GameObject modalViewGo)
            {
                //BfgFunnelEvent.LevelWinScreen(_levelHolder.level.LevelUid);

                var popupManager = Context.Resolve<PopupManager>();
                var popupCanvasScalerResolution = popupManager.GetCanvasScalerResolution();

                _canvasScaler.referenceResolution = popupCanvasScalerResolution;
                modalViewGo.transform.localRotation = Quaternion.Euler(Vector3.zero);
                modalViewGo.transform.localScale = Vector3.one;
                modalViewGo.transform.SetParent(levelScreenRootTransform, false);
                //this hack is important to make sure
                //this modal will be handled by the same camera as match3 temporarily,
                //because we can only record replay from a single camera
            }
        }

        private void BoardRevealStartedHandler(BoardRevealStarted ev)
        {
            if ((_screensBuilder.CurrentScreenType & ScreenType.SideMap) == 0 && WonderTitle.CheckVisibility())
            {
                WonderTitle.enabled = true;
            }
        }

        /// <summary>
        /// Mark auto-booster apply.
        /// </summary>
        /// <remarks>
        /// Called when specific auto-booster is applied to the grid.
        /// </remarks>
        private void AutoBoostEquippedHandler(EquippedAutoBoostAppliedEvent ev)
        {
            if (GameController.IsLevelEnded)
                return;

            var autoBoostUid = ev.Arg0;
            if (autoBoostUid.IsNullOrEmpty()) return;

            _spentAutoBoostersAtStart.Add(autoBoostUid);
            _levelAnalyticsReporter.RegisterAppliedAutoBoosters(new List<AutoBoostInstance> { new(autoBoostUid, ev.Arg1, ev.Arg2) });

            if (ButlerGiftManager.IsButlerGift(autoBoostUid)) return;

            if (ev.Arg3)
            {
                _boosterManager.SpendEquippedAutoBoosters(autoBoostUid);
            }

            var boosterSpentEvent = EventDispatcher.GetMessage<BoosterSpentEvent>();
            var currencyNameToReport = autoBoostUid;

            if (_boosterManager.IsInfiniteBoosterActive(autoBoostUid))
            {
                currencyNameToReport += "_inf";
            }

            boosterSpentEvent.Set(currencyNameToReport, GameController.Level.LevelUid, "prelevel");
            EventDispatcher.TriggerEvent(boosterSpentEvent);
        }

        private void MoreMovesAddedHandler(MoreMovesAddedEvent ev)
        {
            var quantity = ev.Arg0;
            var curMoves = GameController.RemainingMoves;
            var paid = ev.Arg3;

            if (paid)
            {
                IncrementPurchasePlus5Counter();
            }

            AudioProxy.PlaySound(GenericSoundIds.Trade);
            if (GameController.AddExtraMoves(quantity))
            {
                TurnsPanel.AnimateNewMoves(quantity, curMoves, shouldPlayHaptics: true);
            }

            AssistParams.AimAtWinningLevel = true;

            _progressAchievedWhenMovesBought ??= GoalsSystem.ProgressAchieved;

            if (paid)
            {
                _levelAnalyticsReporter.MovesBought(ev.Arg2);
            }

            if (!ev.Arg1.IsNullOrEmpty())
            {
                var boosters = new List<string>();
                var separators = new[] { ',', ';', '+' };
                var parts = ev.Arg1.Trim().Split(separators, StringSplitOptions.RemoveEmptyEntries);

                foreach (var part in parts)
                {
                    boosters.Add(part);
                }

                var autoBoostInstances = new List<AutoBoostInstance>();
                foreach (var booster in boosters)
                {
                    if (booster.IsNullOrEmpty()) continue;

                    autoBoostInstances.Add(new AutoBoostInstance(booster));
                }

                if (autoBoostInstances.Count > 0)
                {
                    Rx.Invoke(M3Settings.InitialBoosterSpawnDelay, _ => { StartCoroutine(TileRevealer.SettleExtraBoosters(GameController.Grid, autoBoostInstances, false)); });
                }
            }

            var randomState = RandomSystem.GetRandomGenerationValues();
            _levelAnalyticsReporter.RegisterMovesAdded(randomState, quantity);
        }

        private static bool IsLevelOutcomeAllowDisplayBoosterOffer(LevelOutcome outcome)
        {
            return outcome is LevelOutcome.Lose or LevelOutcome.OutOfMoves;
        }

        private bool ShouldShowLevelLoseWarning(LevelOutcome outcome)
        {
            var scoreLostIsGreaterThanTheMilestone = false;
            foreach (var manager in GameEventM3ManagerCollection)
            {
                //if for at least one game event (manager) this is true, then it is true for all
                scoreLostIsGreaterThanTheMilestone |= manager.ScoresThatWouldBeLost > manager?.ActiveGameEvent?.GetGoalToNextMilestone();
            }

            var isRaceEventStreakBroken =
                RaceEventMatch3Manager?.IsAnyEventStreakBroken() ?? false;
            var discoRushScoreLost = RaceEventMatch3Manager?.GetCollectEventScore() ?? 0;
            var isLoseLevelCompletely = outcome == LevelOutcome.ShuffleFailed;

            var isDoubleScoreStreakBroken = IsDoubleScoreActive();
            var isRoyaleEventScore = RoyaleEventMatch3Manager?.EventScoreThatWillBeLost() > 0;
            var isButlerStreakBroken = ButlerGiftManager.WillStreakBreak;
            return !isLoseLevelCompletely &&
                   (scoreLostIsGreaterThanTheMilestone ||
                    isRaceEventStreakBroken || discoRushScoreLost > 0 || isDoubleScoreStreakBroken ||
                    isButlerStreakBroken || isRoyaleEventScore);
        }

        private void MoreMovesCancelledHandler(MoreMovesCancelledEvent ev)
        {
            var outcome = ev.Outcome;
            var isRaceEventStreakBroken = RaceEventMatch3Manager?.IsAnyEventStreakBroken() ?? false;
            var discoRushLostScore = RaceEventMatch3Manager?.GetCollectEventScore() ?? 0;
            var isButlerStreakBroken = ButlerGiftManager.WillStreakBreak;
            var willLoseSdb = SdbManager.SdbActiveWhenLevelStart;
            var royaleEventScore = RoyaleEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;
            var teamEventScore = TeamCoopEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;

            if (ShouldShowLevelLoseWarning(outcome))
            {
                void OnExitClick() => ShowBoosterOfferModalOrExit(outcome);

                void OnBackClick()
                {
                    var ctrl = ModalsBuilder.CreateModalView<OutOfMovesController>(ModalsType.LevelOutOfMoves);

                    var outOfMovesContentData = new OutOfMovesContentData
                    {
                        CurrentOutcome = outcome,
                        SweepstakesEventScoreLost = SweepstakesEventScoreBeLost(),
                        GameEventScoreLost = NonCompetitionEventScoreBeLost(),
                        CompletionGameEventScoreLost = CompletionEventScoreBeLost(),
                        CompetitionStreakLost = CompetitionEventStreakBeLost(),
                        IsRaceEventStreakBroken = isRaceEventStreakBroken,
                        PurchasePlus5Counter = _purchasePlus5Counter,
                        DiscoRushLostScore = discoRushLostScore,
                        IsButlerStreakBroken = isButlerStreakBroken,
                        SdbLost = willLoseSdb,
                        RoyaleEventStreakScore = royaleEventScore,
                        TeamEventLostScore = teamEventScore,
                        SkipBanner = true,
                    };
                    ctrl.Setup(outOfMovesContentData);
                    ctrl.SetupLevel(_levelHolder.level, this);
                    ctrl.SetupGridPosition(GetGridPositions(), LevelCamera, GoalPanel);
                    ctrl.ShowModal();
                }

                var warningModal = ModalsBuilder.CreateModalView<GameEventLoseLevelWarningController>(ModalsType.GameEventLoseLevelWarning);

                var getFirstActiveManager = GameEventM3ManagerCollection.GetFirstManager(manager => manager.ActiveGameEvent != null);

                warningModal.Setup(
                    scoreWillBeLost: getFirstActiveManager?.ScoresThatWouldBeLost ?? 0,
                    competitionProgressLost: getFirstActiveManager?.WillCompetitionProgressBeLost ?? false,
                    isOutOfMoves: true,
                    onExitClick: OnExitClick,
                    onBackClick: OnBackClick);

                warningModal.ShowModal();
            }
            else
            {
                ShowBoosterOfferModalOrExit(outcome);
            }
        }

        public void ShowBoosterOfferModalOrExit(LevelOutcome outcome)
        {
            if (IsLevelOutcomeAllowDisplayBoosterOffer(outcome) && _livesManager.CanShowBoosterOffer())
            {
                var outOfMovesBoosterOfferController = ModalsBuilder.CreateModalView<OutOfMovesBoosterOfferController>(ModalsType.OutOfMovesBoosterOfferModal);
                outOfMovesBoosterOfferController.SetupBooster(ItemUid, BoosterAndMovesBoughtHandler, () => CancelledBoosterAndMovesPurchaseHandler(outcome), _levelHolder.level.GetPaletteStage(),
                    MovesAddCount, PurchaseStep.LevelHud);
                outOfMovesBoosterOfferController.ShowModal();

                AudioProxy.PlaySound(GenericSoundIds.LevelFailPopupAppearing);
            }
            else
            {
                OnLevelEnded(new LevelEndedEvent()
                {
                    LevelOutcome = outcome
                });
            }
        }

        private void BoosterAndMovesBoughtHandler()
        {
            var curMoves = GameController.RemainingMoves;
            AudioProxy.PlaySound(GenericSoundIds.Trade);
            if (GameController.AddExtraMoves(MovesAddCount))
            {
                TurnsPanel.AnimateNewMoves(MovesAddCount, curMoves, shouldPlayHaptics: true);
            }

            AssistParams.AimAtWinningLevel = true;

            _progressAchievedWhenMovesBought ??= GoalsSystem.ProgressAchieved;

            _boostButtons.Refresh();
        }

        private void CancelledBoosterAndMovesPurchaseHandler(LevelOutcome levelOutcome)
        {
            OnLevelEnded(new LevelEndedEvent()
            {
                LevelOutcome = levelOutcome
            });
        }

        protected void UpdateLevelPOIState()
        {
            if (_levelHolder.level.POIMarkerState == POIMarkerStateType.Lock)
            {
                _levelHolder.level.POIMarkerState = POIMarkerStateType.Unlock;
            }
        }

        public void SetMenuButtonInteractable(bool interactable)
        {
            if (MenuButton != null)
            {
                MenuButton.interactable = interactable;
            }
        }

        public void OnControlMenuClicked()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);

            if (GameController.GameEnded)
                return;

            var isRaceEventStreakBroken = RaceEventMatch3Manager?.IsAnyEventStreakBroken() ?? false;
            var discoRushLostScore = RaceEventMatch3Manager?.GetCollectEventScore() ?? 0;
            var isButlerStreakBroken = ButlerGiftManager.WillStreakBreak;
            var highestMultiplierStreakBroken = 0;
            var hadDoubleScoreStreakActive = IsDoubleScoreActive(x => { highestMultiplierStreakBroken = x; });
            if (ShouldShowQuitWarning)
            {
                // TODO: Init should only be called once when we initialize the controller
                void OnLeave()
                {
                    GameEventM3ManagerCollection.ForEveryManager(manager => manager.ProcessOnExit());

                    if (ProcessNonGameEventManagers)
                    {
                        RaceEventMatch3Manager.ProcessOnLevelLose();
                        RoyaleEventMatch3Manager.ProcessOnLevelLose();
                        TeamCoopEventMatch3Manager.ProcessOnLevelLose();
                    }

                    OnLevelEnded(new LevelEndedEvent()
                    {
                        LevelOutcome = LevelOutcome.Exit
                    });
                }

                _levelExitMenuController.Init(
                    Context,
                    true,
                    _livesManager.IsInfiniteLivesActive,
                    isRaceEventStreakBroken,
                    hadDoubleScoreStreakActive,
                    discoRushLostScore,
                    isButlerStreakBroken,
                    highestMultiplierStreakBroken,
                    _levelHolder.level,
                    OnLeave);
                _levelExitMenuController.ShowExitMenu();
            }
            else
            {
                void OnLeave()
                {
                    OnLevelEnded(new LevelEndedEvent()
                    {
                        LevelOutcome = LevelOutcome.ExitWithoutPlaying
                    });
                }

                _levelExitMenuController.Init(
                    Context,
                    false,
                    _livesManager.IsInfiniteLivesActive,
                    isRaceEventStreakBroken,
                    hadDoubleScoreStreakActive,
                    discoRushLostScore,
                    isButlerStreakBroken,
                    highestMultiplierStreakBroken,
                    _levelHolder.level,
                    OnLeave);
                _levelExitMenuController.ShowExitMenu();
            }
        }

        void IForceWinLosable.ShowOutOfMoves()
        {
            var isRaceEventStreakBroken =
                RaceEventMatch3Manager?.IsAnyEventStreakBroken() ?? false;
            var discoRushLostScore = RaceEventMatch3Manager?.GetCollectEventScore() ?? 0;
            var isButlerStreakBroken = ButlerGiftManager.WillStreakBreak;
            var willLoseSdb = SdbManager.SdbActiveWhenLevelStart;
            var royaleEventScore = RoyaleEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;
            var teamEventScore = TeamCoopEventMatch3Manager?.EventScoreThatWillBeLost() ?? -1;
            var ctrl = ModalsBuilder.CreateModalView<OutOfMovesController>(ModalsType.LevelOutOfMoves);

            var outOfMovesContentData = new OutOfMovesContentData
            {
                CurrentOutcome = LevelOutcome.OutOfMoves,
                SweepstakesEventScoreLost = SweepstakesEventScoreBeLost(),
                GameEventScoreLost = NonCompetitionEventScoreBeLost(),
                CompletionGameEventScoreLost = CompletionEventScoreBeLost(),
                CompetitionStreakLost = CompetitionEventStreakBeLost(),
                IsRaceEventStreakBroken = isRaceEventStreakBroken,
                PurchasePlus5Counter = _purchasePlus5Counter,
                DiscoRushLostScore = discoRushLostScore,
                IsButlerStreakBroken = isButlerStreakBroken,
                SdbLost = willLoseSdb,
                RoyaleEventStreakScore = royaleEventScore,
                TeamEventLostScore = teamEventScore,
                SkipBanner = false,
            };
            ctrl.Setup(outOfMovesContentData);
            ctrl.SetupLevel(_levelHolder.level, this);
            ctrl.SetupGridPosition(GetGridPositions(), LevelCamera, GoalPanel);
            ctrl.ShowModal();
        }

        List<string> IForceWinLosable.GetEligibleBoosters()
        {
            return _playerManager.CurrentLevel.Config.TrueEligibleBoosts();
        }

        public void SetCustomBoosterAmount(string boosterUid, int boosterCount)
        {
            _playerManager.PlayerInventory.AddBooster(boosterUid, boosterCount);
            var boosterBoughtEvent = EventDispatcher.GetMessage<BoosterBoughtEvent>();
            boosterBoughtEvent.Set(boosterUid);
            boosterBoughtEvent.isEnalbedAutoActivateBoosterMode = true;
            EventDispatcher.TriggerEvent(boosterBoughtEvent);
        }

        public override void SetSuperDiscoBallValue(float updatedValue)
        {
            base.SetSuperDiscoBallValue(updatedValue);
            var randomState = RandomSystem.GetRandomGenerationValues();
            _levelAnalyticsReporter.RegisterSuperDiscoBallValueUpdate(randomState, updatedValue);
        }

        protected override void SetupRendererContainers(ILevel level)
        {
            _rendererContainers.SetupContainers(level, this);
        }
    }
}