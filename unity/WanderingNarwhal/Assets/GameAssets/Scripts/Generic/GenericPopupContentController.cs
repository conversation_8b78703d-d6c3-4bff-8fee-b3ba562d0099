using UnityEngine;

namespace BBB {
	public class GenericPopupContentController : BbbMonoBehaviour {
		[SerializeField] LocalizedText contentText;
        public Transform buttonsAnchor;

		public static GenericPopupContentController Create()
		{
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[GenericPopupContent_P]");
			GameObject contentHolder = GameObject.Instantiate(
				Resources.Load<GameObject>("GenericPopupContent_P")
			);
            UnityEngine.Profiling.Profiler.EndSample();

			return contentHolder.GetComponent<GenericPopupContentController>();
		}

        public void setContentText(string text, params object[] args)
		{
            if(contentText == null)
            {
                Debug.LogError(string.Format("refference didn't set: [LocalizedText.contentText] in {0}", GetType().Name));
                return;
            }

            contentText.SetTextId(text, args);
		}
	}
}