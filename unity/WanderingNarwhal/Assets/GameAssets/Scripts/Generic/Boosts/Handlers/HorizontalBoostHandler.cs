using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Input;
using Cysharp.Threading.Tasks;

namespace BBB.UI.Level.Scripts.Boosts.Handlers
{
    public sealed class HorizontalBoostHandler : BoostHandlerBase
    {
        public HorizontalBoostHandler() : base(BoosterItem.Horizontal)
        {
        }

        protected override async UniTask<bool> OnStart(Coords inputCoords, InputState state)
        {
            if (CheckApplicability(inputCoords, BoosterItem.Horizontal))
            {
                var playerInput = new PlayerInputItemHorizontalBooster(inputCoords);
                await GameController.PlayerInputAsync(playerInput);
                return true;
            }
            return false;
        }

        protected override UniTask<bool> OnDown(Coords inputCoords, InputState state)
        {
            return UniTask.FromResult(false);
        }

        protected override UniTask<bool> OnUp(Coords inputCoords, InputState state)
        {
            return UniTask.FromResult(false);
        }
    }
}