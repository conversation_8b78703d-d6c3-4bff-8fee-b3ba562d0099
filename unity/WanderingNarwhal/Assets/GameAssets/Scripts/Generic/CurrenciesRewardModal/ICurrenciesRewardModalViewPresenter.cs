using System;
using BBB.UI.Core;

namespace GameAssets.Scripts.CurrenciesRewardModalUI
{
    public interface ICurrenciesRewardModalViewPresenter : IViewPresenter
    {
        event Action AdRequested;
        event Action OnCloseButtonClicked;
        void SetupInitialParams(CurrenciesRewardViewModel viewModel, Action onHide);
        void SetupReward();
        void RefreshAd(bool? available = null);
        void Clear();
        void FadeOut();
        void FadeIn();
        void StartOutroAnimation();
    }
}