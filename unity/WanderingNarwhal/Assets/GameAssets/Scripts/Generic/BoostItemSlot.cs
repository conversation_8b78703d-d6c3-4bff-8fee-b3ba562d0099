using System;
using BBB.Core.ResourcesManager;
using BBB.Core.ResourcesManager.Asset;
using UnityEngine;
using UnityEngine.UI;
using PBConfig;
using TMPro;
using BebopBee.Core.Audio;
using BBB.Audio;
using BBB.Core;

namespace BBB.UI
{
    public sealed class BoostItemSlot : BbbMonoBehaviour
    {
        public enum State
        {
            HaveNo = 0,
            Equipped = 1,
            NotEquipped = 2,
            Locked = 3
        }

        [SerializeField] private AsyncLoadableImage _icon;
        [SerializeField] private Image _lockImage;
        [SerializeField] private Image _lockBGImage;
        [SerializeField] private TextMeshProUGUI _quantity;
        [SerializeField] private Button _addMoreButton;
        [SerializeField] private Transform _bgTransform;
        [SerializeField] private Animator _animator;

        private Button _equipUnequipButton;

        private Action<BoosterConfig> _onAddMoreCallback = delegate { };
        private BoosterConfig _config;
        private IInventory _inventory;
        private ILockManager _lockManager;

        private State _currentState;
        private IAssetLoaded<Sprite> _assetLoaded;

        private string _boosterUid;

        public string BoosterUid
        {
            get { return _boosterUid; }
        }

        public void Init(BoosterConfig config, IInventory inventory, ILockManager lockManager, IAssetsManager assetsManager,
            Action<BoosterConfig> callback = null)
        {
            if (callback != null) _onAddMoreCallback = callback;

            _config = config;

            _inventory = inventory;
            _lockManager = lockManager;
            _icon.Show(_config.Thumbnail);             

            _addMoreButton.ReplaceOnClick(OnAddMoreClicked);
            _equipUnequipButton = GetComponent<Button>();
            _equipUnequipButton.ReplaceOnClick(OnEquipUnequipClicked);

            _boosterUid = _config.Uid;

            Refresh();
        }

        public void OnBoosterBought()
        {
            _animator.SetTrigger("BoosterBought");
        }        

        private void ReleaseAsset()
        {
            if (_assetLoaded != null)
            {
                _assetLoaded.Dispose();
                _assetLoaded = null;
            }
        }

        public void Refresh()
        {
            var hasBooster = _inventory.HasBooster(_config.Uid);
            var isEquipped = _inventory.IsEquippedAutoBooster(_config.Uid);
            var isLocked = _lockManager.IsLocked(_config.Uid, LockItemType.Booster);

            _currentState = GetState(hasBooster, isEquipped, isLocked);
            UpdateForCurrentState();
        }

        public static State GetState(bool hasBooster, bool isEquipped, bool isLocked)
        {
            if (hasBooster)
            {
                return isEquipped ? State.Equipped : State.NotEquipped;
            }
            return isLocked ? State.Locked : State.HaveNo;
        }
        
        
        private void UpdateForCurrentState()
        {
            switch (_currentState)
            {
                case State.HaveNo:
//                    _lockImage.enabled = false;
//                    _lockBGImage.enabled = false;
//                    _addMoreButton.gameObject.SetActive(true);
//                    _quantity.gameObject.SetActive(false);
//                    _quantity.text = _inventory.GetBoosterAmount(_config.Uid).ToString();
                    _bgTransform.localScale = new Vector3(1f, 1f, 1f);
                    _equipUnequipButton.enabled = false;
                    break;
                case State.Equipped:
//                    _lockImage.enabled = false;
//                    _lockBGImage.enabled = false;
//                    _addMoreButton.gameObject.SetActive(false);
//                    _quantity.gameObject.SetActive(true);
//                    _quantity.text = _inventory.GetBoosterAmount(_config.Uid).ToString();
                    _bgTransform.localScale = new Vector3(1.3f, 1.3f, 1.3f);
                    _equipUnequipButton.enabled = true;
                    break;
                case State.NotEquipped:
//                    _lockImage.enabled = false;
//                    _lockBGImage.enabled = false;
//                    _addMoreButton.gameObject.SetActive(false);
//                    _quantity.gameObject.SetActive(true);
//                    _quantity.text = (_inventory.GetBoosterAmount(_config.Uid)).ToString();
                    _bgTransform.localScale = new Vector3(1f, 1f, 1f);
                    _equipUnequipButton.enabled = true;
                    break;
                case State.Locked:
//                    _lockImage.enabled = true;
//                    _lockBGImage.enabled = true;
//                    _addMoreButton.gameObject.SetActive(false);
//                    _quantity.gameObject.SetActive(false);
                    _bgTransform.localScale = new Vector3(1f, 1f, 1f);
                    _equipUnequipButton.enabled = false;
                    break;
            }
        }

        private void OnAddMoreClicked()
        {
            _onAddMoreCallback(_config);
        }

        private void OnEquipUnequipClicked()
        {
            switch (_currentState)
            {
                case State.HaveNo:
                   BDebug.LogErrorFormat(LogCat.Match3, "Something went wrong - no boosters {0} to equip", _config.Uid);
                    break;
                case State.Equipped:
                    _inventory.UnequipAutoBooster(_config.Uid);
                    Refresh();
                    break;
                case State.NotEquipped:
                    _inventory.EquipAutoBooster(_config.Uid);
                    AudioProxy.PlaySound(GenericSoundIds.EnablingPowerup);
                    Refresh();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            ReleaseAsset();
        }
    }
}