using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using BBB.Core.Analytics;
using BBB.Core.UI;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.RaceEvents;
using BBB.Screens;
using BBB.Wallet;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.DailyEvents.Popup;
using GameAssets.Scripts.Generic;
using GameAssets.Scripts.Promotions.Banners;
using JetBrains.Annotations;
using PBConfig;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Gradient = UnityEngine.Gradient;

namespace BBB.UI.Level.Views
{
    public class OutOfMovesViewPresenter : ModalsViewPresenter, IOutOfMovesViewPresenter
    {
        private const string ButlerGiftWarningTextId = "FENNECS_PERKS_OOM_WARNING_TEXT";
        private const string RoyaleEventWarningTextId = "PNP_OOM_BALLOON_ROYALE_WARNING_TEXT";
        private const string CollectionEventWarningTextId = "OOM_LOSE_ALL_EVENT_REWARDS";
        private const string CompetitionEventWarningTextId = "PNP_COMPETITION_EVENT_OOM_WARNING_TEXT";
        private const string DiscoRushWarningTextId = "PNP_DISCO_RUSH_OOM_WARNING_TEXT";
        private const string TeamCoopWarningTextId = "PNP_TEAMCOOP_OOM_WARNING_TEXT";
        private const string SweepstakesEventWarningTextId = "SWEEPSTAKES_OOM_WARNING_TEXT";
        private const string DefaultEventWarningTextId = "OOM_LOSE_ALL_EVENT_REWARDS";
        private const string OomSpecialReshuffleStateHeader = "OOM_SPECIAL_RESHUFFLE_STATE_HEADER";
        private const string SdbWarningTextId = "OOM_LOSE_SDB";
        private const string OutOfMovesTitle = "OUT_OF_MOVES_TITLE";
        private const int MaxEventCount = 2;
        
        /// <summary>
        /// WARNING - PZZLS-4141
        /// </summary>
        /// <remarks>
        /// DO NOT REMOVE OR CHANGE THE ORDER OF ENUMS.
        /// IF ABSOLUTELY NECESSARY, UPDATE REFERENCES IN THE PREFAB (OutOfMovesPanel.prefab) ACCORDINGLY.
        /// </remarks>
        public enum OutOfMovesContentTypes
        {
            //Second Modal Content
            FennecPerks,
            RoyaleEvent,
            CollectionEvent,

            //Third Modal Content
            CompetitionEvent,
            RaceEvent,
            DiscoRush,
            CompletionEvent,
            TeamCoopEvent,
            SweepstakesEvent,
        }

        [Serializable]
        public struct RewardIconPair
        {
            public string Name;
            public Sprite Sprite;
        }

        [Serializable]
        public struct LevelStageRibbonPair
        {
            public Stage Stage;
            public Sprite RibbonSprite;
            public Gradient TopBarGradient;
            public Color TopBarShadowColor;
        }
        
        [Serializable]
        public struct OomWarningIconPrefabPair
        {
            public OutOfMovesContentTypes OutOfMovesContentType;
            public OOMGameEventIcon Icon;
            public Transform IconTransform;
        }

        public event Action<string> TriedToUseInventory = delegate { };
        public event Action TriedToPurchase = delegate { };
        public event Action OpenedGacha = delegate { };
        public event Action ShowAnimCompleted = delegate { };
        public event Action ShowMatch3Board = delegate { };
        public event Action HideMatch3Board = delegate { };

        [SerializeField] private LocalizedTextPro _titleLocalization;
        [SerializeField] private Animator _animator;
        [SerializeField] private GameObject[] _notFreeSpinDescription;
        [SerializeField] private GameObject[] _arrowFreeSpinGo;
        [SerializeField] private GameObject[] _topFennecParts;
        [SerializeField] private GameObject[] _topSdbParts;
        [SerializeField] private Shadow _topSdbOutline;
        [SerializeField] private GameObject _match3ModeContent;
        [SerializeField] private GameObject _gainsContent;
        [SerializeField] private TextMeshProUGUI _shuffleText;

        [Space] [Header("Extra boosters")]
        [SerializeField] private List<RewardIconPair> _rewardSprites;
        [SerializeField] private GameObject _extraBoosters;
        [SerializeField] private Image _extraBoosterIcon;
        [SerializeField] private GameObject _plusExtraBoostersText;
        [SerializeField] private TextMeshProUGUI _movesDescription;
        [SerializeField] private TextMeshProUGUI _movesCount;

        [Space] [Header("Speech Bubble")] 
        [SerializeField] private GenericInfoPanel _speechBubble;

        [Space] [Header("Main Action Button")] 
        [SerializeField] private Button _actionButton;
        [SerializeField] private GameObject _actionButtonTitleForPurchasing;
        [SerializeField] private GameObject _actionButtonTitleForInvItems;
        [SerializeField] private GameObject _numberOfInvItems;
        [SerializeField] private TextMeshProUGUI _numberOfInvItemsText;
        [SerializeField] private UIPriceComponent _priceComponent;

        [Space] [Header("M3 Board mode")] 
        [SerializeField] private Button _showMatch3BoardButton;
        [SerializeField] private OutOfMovesMatch3BoardViewer _match3Viewer;
        
        [Space] 
        [SerializeField] private Button _m3ActionButton;
        [SerializeField] private GameObject _m3ActBtnTitleForPurchasing;
        [SerializeField] private GameObject _m3ActBtnTitleForInvItems;
        [SerializeField] private GameObject _m3NumberOfInvItems;
        [SerializeField] private TextMeshProUGUI _m3NumberOfInvItemsText;
        [SerializeField] private UIPriceComponent _m3PriceComponent;
        [SerializeField] private Button _closeMatch3Button;
        [SerializeField] private BannerContainer _bannerContainer;

        [Space] [Header("StagePaletteReference")] 
        [SerializeField] private Image _ribbonImage;
        [SerializeField] private List<LevelStageRibbonPair> _levelStageRibbonPairs;
        [SerializeField] private PopupStagePaletteApplier _popupStagePaletteApplier;
        
        [Space] [Header("Escalating Moves")] 
        [SerializeField] private EscalatingMoves _escalatingMoves;

        [Space] [Header("Loss Modal")] 
        [SerializeField] private GameObject _lossModalContent;
        [SerializeField] private LocalizedTextPro _lossMessageText;
        [SerializeField] private List<OomWarningIconPrefabPair> _oomWarningIconPrefabPairs;
        [SerializeField] private GameObject _regularFennecHolder;
        [SerializeField] private GameEventReplaceableGo _alternateMapFennecHolder;
        [SerializeField]private Transform _activeIconsHolder;
        [SerializeField]private Transform _inactiveItemsHolder;
        
        private List<Transform> _activeOomGameEventIcons;
        private IPlayerManager _playerManager;
        private IGameEventManager _gameEventManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private IScreensBuilder _screensBuilder;
        private OfferConfig _offerConfig;
        private BannerManager _bannerManager;
        private ILocalizationManager _localizationManager;
        private IButlerGiftManager _butlerGiftManager;
        private GenericResourceProvider _genericResourceProvider;
        private IInventory Inventory => _playerManager.PlayerInventory;

        private int _defaultMovesOffered;
        private int _movesOffered;
        private bool _showing;
        private bool _firstAppearance;
        private OutOfMovesViewContentMode _contentMode;
        private GoalResourcesPanel _goalResourcesPanel;
        private OutOfMovesContentData _contentData;
        private Tuple<float, float> _gridVerticalPositions;
        private Camera _levelCamera;
        private bool _isAnimatingContentChange;
        private bool _isAnimatingContentDescriptionChange;
        private List<OutOfMovesContentTypes> _allActiveEvents;

        private static readonly int SkipBanner = Animator.StringToHash("SkipBanner");
        private static readonly int ShowingBoard = Animator.StringToHash("ShowingBoard");
        private static readonly int StartContentIntro = Animator.StringToHash("StartContentIntro");
        private static readonly int StartContentOutro = Animator.StringToHash("StartContentOutro");
        private static readonly int ChangeContentOutro = Animator.StringToHash("ChangeContentOutro");
        private static readonly int ChangeContentDescription = Animator.StringToHash("UpdateContentIntro"); 
        private static readonly int HideAnimParam = Animator.StringToHash("Hide");
        private static readonly int HideShowGameBoard = Animator.StringToHash("HideShowGameBoard");

        public float DelayBeforeWalletHide => 3f;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _playerManager = context.Resolve<IPlayerManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _bannerManager = context.Resolve<BannerManager>();
            _butlerGiftManager = context.Resolve<IButlerGiftManager>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            
            _priceComponent.Init(context);
            _m3PriceComponent.Init(context);
            _alternateMapFennecHolder.Init(_gameEventResourceManager);
            _activeOomGameEventIcons = new List<Transform>();
            _allActiveEvents = new List<OutOfMovesContentTypes>();
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            _escalatingMoves.EscalatingMovesAnimationEnded += EscalatingMovesAnimationEndHandler;
        }

        private void Unsubscribe()
        {
            _escalatingMoves.EscalatingMovesAnimationEnded -= EscalatingMovesAnimationEndHandler;
        }

        public void SetupOfferConfig(OfferConfig offerConfig, int defaultMovesOffered, int movesOffered)
        {
            _offerConfig = offerConfig;
            _defaultMovesOffered = defaultMovesOffered;
            _movesOffered = movesOffered;

            var hasExtraBoosters = !_offerConfig.ExtraReward.IsNullOrEmpty();
            _extraBoosters.SetActive(hasExtraBoosters);
            _plusExtraBoostersText.SetActive(hasExtraBoosters);
            _movesDescription.text = _localizationManager.getLocalizedTextWithArgs("OOM_PLUS5_ESCALATING_AND_KEEP_PLAYING", _movesOffered);

            //We will update moves count after animation
            _movesCount.text = _movesOffered == _defaultMovesOffered ? $"+{_movesOffered}" : $"+{_defaultMovesOffered}";

            if (!hasExtraBoosters) return;

            var extraBoosters = new List<string>();
            var separators = new[] { ',', ';', '+' };
            var parts = _offerConfig.ExtraReward.Trim().Split(separators, StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in parts)
            {
                extraBoosters.Add(part);
            }

            if (extraBoosters.Count == 0)
            {
                _extraBoosters.SetActive(false);
                _plusExtraBoostersText.SetActive(false);
                _gainsContent.SetActive(false);
            }
            else
            {
                var firstBooster = extraBoosters[0];
                Sprite sprite = null;
                foreach (var pair in _rewardSprites)
                {
                    if (!pair.Name.Equals(firstBooster, StringComparison.OrdinalIgnoreCase)) continue;
                    
                    sprite = pair.Sprite;
                    break;
                }

                if (sprite == null)
                {
                    _extraBoosters.SetActive(false);
                    _plusExtraBoostersText.SetActive(false);
                    Debug.LogWarning($"Cannot find sprite for extra booster '{firstBooster}'");
                }

                _extraBoosterIcon.sprite = sprite;
            }
        }

        private void ResetModal()
        {
            _match3ModeContent.SetActive(false);
            _animator.SetBool(ShowingBoard, false);

            _gainsContent.SetActive(false);
            SetTopCharacterState(_topFennecParts ,false);
            SetTopCharacterState(_topSdbParts ,false);

            _shuffleText.gameObject.SetActive(false);
            _actionButton.gameObject.SetActive(false);
            _bannerContainer.gameObject.SetActive(false);

            _lossModalContent.SetActive(false);
        }

        private void SetTopCharacterState(GameObject[] characterObjects, bool state)
        {
            foreach (var go in characterObjects)
            {
                if (go.activeSelf != state)
                {
                    go.SetActive(state);
                }
            }
        }

        private void SetModalMode(bool isShuffle)
        {
            _shuffleText.gameObject.SetActive(isShuffle);
            _actionButton.gameObject.SetActive(!isShuffle);
            _m3ActionButton.gameObject.SetActive(!isShuffle);
            _bannerContainer.gameObject.SetActive(!isShuffle);

            _titleLocalization.SetTextId(isShuffle ? OomSpecialReshuffleStateHeader : OutOfMovesTitle);
        }

        private bool CheckShowM3BoardView()
        {
            if (_contentMode != OutOfMovesViewContentMode.ShowMatch3Board) return false;
            
            _match3ModeContent.SetActive(true);
            _animator.SetBool(ShowingBoard, true);
            return true;
        }

        private void RefreshContentByCurrentEvent()
        {
            OnContentOutroAnimStarted();
            ResetModal();

            if (CheckShowM3BoardView())
                return;

            SetModalMode(_contentMode == OutOfMovesViewContentMode.ShuffleLost);
            CheckForInventoryItems();
            
            switch (_contentMode)
            {
                case OutOfMovesViewContentMode.Gains:
                    _gainsContent.SetActive(true);
                    CheckForSdbWarning();
                    _escalatingMoves.EnableBadge();
                    break;

                case OutOfMovesViewContentMode.SecondModal:
                    ResetWarningIcons();
                    CheckForSdbWarning(forceDisableFennec: true);
                    SetupSecondModalContent();
                    break;

                case OutOfMovesViewContentMode.ThirdModal:
                    ResetWarningIcons();
                    CheckForSdbWarning();
                    CheckForSdbWarning(forceDisableFennec: true);
                    SetupThirdModalContent();
                    break;
            }

            if (!_firstAppearance && !_isAnimatingContentChange)
            {
                _animator.SetTrigger(StartContentIntro);
            }
        }
        
        private void UpdateWarningIcons(OutOfMovesContentTypes outOfMovesContentType)
        {
            var oomGameEventIcon = GetIconPrefab(outOfMovesContentType);
            
            switch (outOfMovesContentType)
            {
                case OutOfMovesContentTypes.CompetitionEvent:
                {
                    var currentEvent = GetCurrentGameEvent(ev => ev.GameplayType is GameEventGameplayType.Competition);

                    if (currentEvent is not { Status: GameEventStatus.Active } ||
                        !currentEvent.IsMultiplierScoreStreakActive())
                        return;
                    
                    oomGameEventIcon.Icon.SetupIcon(currentEvent.Uid, true);
                    break;
                }
                case OutOfMovesContentTypes.SweepstakesEvent:
                {
                    var currentEvent = GetCurrentGameEvent(ev => ev.GameplayType is GameEventGameplayType.Sweepstakes);

                    if (currentEvent is not { Status: GameEventStatus.Active })
                        return;
                    oomGameEventIcon.Icon.SetupIcon(GetActiveGameEventSprite(currentEvent),
                        _contentData.SweepstakesEventScoreLost.ToString());
                    break;
                }
                case OutOfMovesContentTypes.CollectionEvent:
                case OutOfMovesContentTypes.CompletionEvent:
                {
                    var currentEvent = GetCurrentGameEvent(ev =>
                        ev.GameplayType is GameEventGameplayType.Sweepstakes or GameEventGameplayType.Collection
                            or GameEventGameplayType.Completion
                    );

                    if (currentEvent is not { Status: GameEventStatus.Active })
                        return;
                    oomGameEventIcon.Icon.SetupIcon(GetActiveGameEventSprite(currentEvent),
                        _contentData.GameEventScoreLost.ToString());
                    break;
                }
                case OutOfMovesContentTypes.FennecPerks:
                {
                    var butlerSprite = GetActiveButlerGiftSprite();
                    oomGameEventIcon.Icon.SetupIcon(butlerSprite, GetSubTextId(outOfMovesContentType));
                    break;
                }
                default:
                    oomGameEventIcon.Icon.SetupIcon(GetSubTextId(outOfMovesContentType));
                    break;
            }
            
            var iconTransform = oomGameEventIcon.IconTransform;
            iconTransform.SetParent(_activeIconsHolder);
            _activeOomGameEventIcons.Add(iconTransform);
        }

        private void ResetWarningIcons()
        {
            _allActiveEvents.Clear();
            foreach (var activeIcon in _activeOomGameEventIcons)
            {
                activeIcon.SetParent(_inactiveItemsHolder);
            }
            
            _activeOomGameEventIcons.Clear();
        }

        private void SetupModalContent(OrderedDictionary allEvents)
        {
            ResetWarningIcons();
            
            _allActiveEvents = new List<OutOfMovesContentTypes>();

            foreach (var eventToBeShown in allEvents)
            {
                if (eventToBeShown is DictionaryEntry { Value: true } eventKvp)
                {
                    _allActiveEvents.Add((OutOfMovesContentTypes)eventKvp.Key);
                }
            }

            var modalWarningText = string.Empty;
            
            switch (_allActiveEvents.Count)
            {
                case > 1:
                {
                    _allActiveEvents.RemoveRange(MaxEventCount, _allActiveEvents.Count - MaxEventCount);
                    var (firstEventType, secondEventType) = (_allActiveEvents[0], _allActiveEvents[1]);

                    UpdateWarningIcons(firstEventType);
                    UpdateWarningIcons(secondEventType);
                
                    modalWarningText = GetMergedTextId(firstEventType, secondEventType);
                    break;
                }
                case 1:
                {
                    var firstEventType = _allActiveEvents[0];
                    UpdateWarningIcons(firstEventType);
                    modalWarningText = GetWarningTextId(firstEventType);
                    break;
                }
            }

            _lossModalContent.SetActive(true);
            _lossMessageText.SetTextId(modalWarningText);
        }

        /// <summary>
        /// Show content for SDB, Butler Streak, Daily Collect, Royale Event, Weekend Tour, Alternate City Score
        /// </summary>
        private void SetupSecondModalContent()
        {
            var allEvents = new OrderedDictionary();

            // Prioritize Sweepstakes event if present
            TryAddOomEvent(allEvents, OutOfMovesContentTypes.SweepstakesEvent, _contentData.SweepstakesEventLoss);
            TryAddOomEvent(allEvents, OutOfMovesContentTypes.RoyaleEvent, _contentData.RoyaleEventStreakLoss);
            TryAddOomEvent(allEvents, OutOfMovesContentTypes.CollectionEvent, _contentData.GameEventLoss);

            // If no other event, show Fennec Perks if SDB is lost
            if (allEvents.Count == 0)
            {
                TryAddOomEvent(allEvents, OutOfMovesContentTypes.FennecPerks, _contentData.IsButlerStreakBroken, 0);
            }

            SetupModalContent(allEvents);
        }

        private void CheckForSdbWarning(bool forceDisableFennec = false)
        {
            var sdbLost = _contentData.SdbLost;
           SetTopCharacterState(_topFennecParts, !sdbLost && !forceDisableFennec);
           SetTopCharacterState(_topSdbParts, sdbLost);
        }
        
        private void TryAddOomEvent(OrderedDictionary events, OutOfMovesContentTypes eventType, bool isActive, int atIndex = -1)
        {
            if (events == null || !isActive) return;

            if (atIndex < 0 || atIndex >= events.Count)
            {
                events.Add(eventType, true);
                return;
            }
            
            events.Insert(atIndex, eventType, true);
        }

        /// <summary>   
        /// Show content for Competition, DiscoRush, Completion Event
        /// </summary>
        private void SetupThirdModalContent()
        {
            var allEvents = new OrderedDictionary
            {
                { OutOfMovesContentTypes.CompetitionEvent, _contentData.CompetitionEventStreakLoss },
                { OutOfMovesContentTypes.DiscoRush, _contentData.DiscoRushEventLoss },
                { OutOfMovesContentTypes.CompletionEvent, _contentData.CompletionGameEventLoss},
            };
            SetupModalContent(allEvents);
        }

        private OomWarningIconPrefabPair GetIconPrefab(OutOfMovesContentTypes eventType)
        {
            foreach (var pair in _oomWarningIconPrefabPairs)
            {
                if (pair.OutOfMovesContentType == eventType)
                {
                    return pair;
                }
            }

            return default;
        }
        
        private string GetSubTextId(OutOfMovesContentTypes eventType)
        {
            return eventType switch
            {
                OutOfMovesContentTypes.RoyaleEvent => $"{_contentData.RoyaleEventStreakScore}/{RoyaleEvent.TotalSteps - 1}",
                OutOfMovesContentTypes.CollectionEvent => _contentData.GameEventScoreLost.ToString(),
                OutOfMovesContentTypes.CompletionEvent => _contentData.GameEventScoreLost.ToString(),
                OutOfMovesContentTypes.DiscoRush => _contentData.DiscoRushLostScore.ToString(),
                OutOfMovesContentTypes.TeamCoopEvent => _contentData.TeamEventLostScore.ToString(),
                _ => ""
            };
        }
        private static string GetMergedTextId(OutOfMovesContentTypes firstEventType, OutOfMovesContentTypes secondEventType)
        {
            return $"PNP_OOM_{firstEventType.ToString().ToUpper(CultureInfo.InvariantCulture)}_{secondEventType.ToString().ToUpper(CultureInfo.InvariantCulture)}_MERGED_TEXT";
        }

        private static string GetWarningTextId(OutOfMovesContentTypes eventType)
        {
            return eventType switch
            {
                OutOfMovesContentTypes.SweepstakesEvent => SweepstakesEventWarningTextId,
                OutOfMovesContentTypes.FennecPerks => ButlerGiftWarningTextId,
                OutOfMovesContentTypes.RoyaleEvent => RoyaleEventWarningTextId,
                OutOfMovesContentTypes.CollectionEvent => CollectionEventWarningTextId,
                OutOfMovesContentTypes.CompetitionEvent => CompetitionEventWarningTextId,
                OutOfMovesContentTypes.RaceEvent => DefaultEventWarningTextId,
                OutOfMovesContentTypes.DiscoRush => DiscoRushWarningTextId,
                OutOfMovesContentTypes.TeamCoopEvent => TeamCoopWarningTextId,
                OutOfMovesContentTypes.CompletionEvent => DefaultEventWarningTextId,
                _ => string.Empty,
            };
        }
        
        private Sprite GetActiveButlerGiftSprite()
        {
            var genericAtlas = _genericResourceProvider.GetGenericAtlas();
            return genericAtlas.GetSprite($"FennecPerks_{_butlerGiftManager.CurrentStreak}_OOM_Icon");
        }
        
        private Sprite GetActiveGameEventSprite(GameEventBase currentEvent)
        {
            if (currentEvent == null) return null;
            
            var scoreSpriteName = currentEvent.GetScoreIconSpriteNameForCurrentMilestone();
            return _gameEventResourceManager.GetSprite(currentEvent.Uid, scoreSpriteName);
        }

        private GameEventBase GetCurrentGameEvent(Predicate<GameEventBase> filterPredicate)
        {
            var currentEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                filterPredicate(ev) && ev.Status == GameEventStatus.Active && ev.CanShowInScreen(_screensBuilder.CurrentScreenType)) ??
                               _gameEventManager.GetHighestPriorityEvent(
                ev => ev.Status == GameEventStatus.None || ev.Status == GameEventStatus.Missing &&
                    filterPredicate(ev) &&
                    ev.CanShowInScreen(_screensBuilder.CurrentScreenType));

            return currentEvent;
        }
        
        [UsedImplicitly]
        private void OnContentOutroAnimStarted()
        {
            _animator.SetBool(ChangeContentDescription, _isAnimatingContentDescriptionChange);
        }
        
        [UsedImplicitly]
        private void OnContentOutroAnimEnded()
        {
            RefreshContentByCurrentEvent();
        }

        public void UpdateContent(OutOfMovesViewContentMode contentMode, OutOfMovesContentData contentData,
            bool skipOutro = false)
        {
            var showOutro = _contentMode != contentMode;
            var previousContentMode = _contentMode;
            _contentMode = contentMode;
            _contentData = contentData;
            _isAnimatingContentChange = false;
            _isAnimatingContentDescriptionChange = false;
            var isAnimatingOutro = false;
            if (showOutro && !skipOutro)
            {
                if (contentMode == OutOfMovesViewContentMode.ShowMatch3Board)
                {
                    _animator.SetTrigger(StartContentOutro);
                }
                else if (previousContentMode == OutOfMovesViewContentMode.ShowMatch3Board)
                {
                    _animator.SetTrigger(HideShowGameBoard);
                    _isAnimatingContentChange = true;
                }
                else
                {
                    _isAnimatingContentDescriptionChange = previousContentMode == OutOfMovesViewContentMode.SecondModal;
                    _animator.SetTrigger(ChangeContentOutro);
                    _isAnimatingContentChange = true;
                    isAnimatingOutro = true;
                }
            }

            if (!isAnimatingOutro)
            {
                RefreshContentByCurrentEvent();
            }
        }

        public void SetupGridVerticalPositions(Tuple<float, float> positions, Camera levelCamera, GoalResourcesPanel goalResourcesPanel)
        {
            _gridVerticalPositions = positions;
            _levelCamera = levelCamera;
            _goalResourcesPanel = goalResourcesPanel;
        }


        private void EscalatingMovesAnimationEndHandler()
        {
            _movesCount.text = "+" + _movesOffered;
        }

        [UsedImplicitly]
        public void OnShowAnimCompleted()
        {
            if (_firstAppearance)
            {
                if (_contentMode == OutOfMovesViewContentMode.Gains && _movesOffered != _defaultMovesOffered)
                {
                    if (_offerConfig.ExtraReward.IsNullOrEmpty())
                    {
                        _escalatingMoves.PlayJustMoves(_movesOffered - _defaultMovesOffered);
                    }
                    else
                    {
                        _escalatingMoves.PlayMovesPlusBoosters(_movesOffered - _defaultMovesOffered);
                    }
                }
            }

            if (_contentMode == OutOfMovesViewContentMode.Gains && _movesOffered != _defaultMovesOffered)
            {
                _speechBubble.Hide();
            }
            else
            {
                _speechBubble.Show();
            }

            _firstAppearance = false;
            if (_showing)
            {
                ShowAnimCompleted.SafeInvoke();
            }
        }


        [UsedImplicitly]
        public void OnHideAnimStarted()
        {
            // This animation event is called BEFORE OnHide
            _showing = false;
        }

        private void CheckForInventoryItems()
        {
            if (Inventory.HasItem(InventoryItems.ExtraMoves))
            {
                DisplayMovesInInventory();
            }
            else
            {
                SuggestMovesPurchasing();
            }

            _showMatch3BoardButton.ReplaceOnClick(ShowMatch3BoardButtonHandler);
            _closeMatch3Button.ReplaceOnClick(OnHideMatch3BoardButtonClicked);
        }

        private void SuggestMovesPurchasing()
        {
            _actionButtonTitleForPurchasing.SetActive(true);
            _actionButtonTitleForInvItems.SetActive(false);
            _numberOfInvItems.SetActive(false);
            _actionButton.ReplaceOnClick(OnTryToPurchase);

            _m3ActBtnTitleForPurchasing.SetActive(true);
            _m3ActBtnTitleForInvItems.SetActive(false);
            _m3NumberOfInvItems.SetActive(false);
            _m3ActionButton.ReplaceOnClick(OnTryToPurchase);
            UpdatePrice();
        }

        private void UpdatePrice()
        {
            if (_offerConfig == null) return;
            _priceComponent.SetupPrice(_offerConfig.Price.Currencies);
            _m3PriceComponent.SetupPrice(_offerConfig.Price.Currencies);
        }

        private string ConvertToAnalyticsString()
        {
            var eventArray = new List<string>();
            foreach (var activeEvent in _allActiveEvents)
            {
                switch (activeEvent)
                {
                    case OutOfMovesContentTypes.CompetitionEvent:
                    case OutOfMovesContentTypes.CompletionEvent:
                    case OutOfMovesContentTypes.CollectionEvent:
                        var gameEvent = GetCurrentGameEvent(ev =>
                            ev is CompetitionGameEvent or CompletionGameEvent or CollectionGameEvent);
                        if(gameEvent != null)
                            eventArray.Add(gameEvent.Uid);
                        break;
                    default: eventArray.Add(activeEvent.ToString());
                        break;
                }
            }

            return $"[{string.Join(",", eventArray)}]";;
        }

        private void DisplayMovesInInventory()
        {
            var itemCountText = Inventory.GetItemCount(InventoryItems.ExtraMoves).ToString();
            _actionButtonTitleForPurchasing.SetActive(false);
            _actionButtonTitleForInvItems.SetActive(true);
            _numberOfInvItems.SetActive(true);
            _numberOfInvItemsText.SetText(itemCountText);
            _actionButton.ReplaceOnClick(BtnAction);
            _priceComponent.Clean();

            _m3ActBtnTitleForPurchasing.SetActive(false);
            _m3ActBtnTitleForInvItems.SetActive(true);
            _m3NumberOfInvItems.SetActive(true);
            _m3NumberOfInvItemsText.SetText(itemCountText);
            _m3ActionButton.ReplaceOnClick(BtnAction);
            _m3PriceComponent.Clean();
            return;

            void BtnAction()
            {
                if (_contentMode == OutOfMovesViewContentMode.ShowMatch3Board)
                {
                    HideScrims(null);
                }
                
                var currentScreen = GetCurrentScreenInfoForDauAnalytics();
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.OutOfMoves.CategoryName, 
                    currentScreen, DauInteractions.OutOfMoves.ExtraMovesInventory));
                
                TriedToUseInventory.SafeInvoke(InventoryItems.ExtraMoves);
            }
        }

        protected override void OnShow()
        {
            _showing = true;
            _firstAppearance = true;
            base.OnShow();
            SetButtonsInteractable(true);

            _bannerManager.FillContainerWithBanners(_bannerContainer);
            _bannerManager.SetupOutOfMovesCallbacks(OpenGachaButtonHandler, ShowMatch3BoardButtonHandler);

            UpdatePrice();
        }

        protected override void OnHide()
        {
            base.OnHide();
            _speechBubble.Hide();
            _bannerContainer.Clear();
        }

        private void OnTryToPurchase()
        {
            if (_contentMode == OutOfMovesViewContentMode.ShowMatch3Board)
            {
                HideScrims(null);
            }
            
            PurchasePath.Append(GetCurrentScreenInfoForPurchasePath());
            TriedToPurchase.SafeInvoke();
        }

        private void SetButtonsInteractable(bool value)
        {
            _actionButton.interactable = value;
            _showMatch3BoardButton.interactable = value;
            CloseButton.interactable = value;
        }

        private void OpenGachaButtonHandler()
        {
            OpenedGacha.SafeInvoke();
        }

        private void ShowMatch3BoardButtonHandler()
        {
            SetButtonsInteractable(false);
            ShowScrim();
            ShowMatch3Board.SafeInvoke();
        }

        private void OnHideMatch3BoardButtonClicked()
        {
            PlayCloseFX();
            HideScrims(() => HideMatch3Board.SafeInvoke());
        }

        private void ShowScrim()
        {
            _match3ModeContent.SetActive(true);
            _match3Viewer.ShowScrimAtPositions(_levelCamera, _gridVerticalPositions.Item1, _gridVerticalPositions.Item2);
            MatchGoalMaskScaleAndPosition(_goalResourcesPanel.InverseMaskTarget);
        }

        private void MatchGoalMaskScaleAndPosition(RectTransform targetElement)
        {
            var overlayElement = _match3Viewer.InverseMaskTransform;
            Vector3 screenPos = RectTransformUtility.WorldToScreenPoint(_levelCamera, targetElement.position);
            var worldElementSize = targetElement.rect.size;
            worldElementSize = Vector2.Scale(worldElementSize, targetElement.lossyScale);
            overlayElement.sizeDelta = new Vector2(worldElementSize.x,worldElementSize.y);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                overlayElement.parent as RectTransform, 
                screenPos, 
                null,
                out var overlayPosition
            );
            overlayElement.anchoredPosition = overlayPosition;
        }
        
        private void HideScrims(Action onDone)
        {
            _match3Viewer.HideScrims(() =>
            {
                _match3ModeContent.SetActive(false);
                SetButtonsInteractable(true);
                onDone.SafeInvoke();
            });
        }

        public void SetupFreeMove(bool haveFreeMove)
        {
            _arrowFreeSpinGo.Enable(haveFreeMove);
            _notFreeSpinDescription.Enable(!haveFreeMove);
        }

        public void SetupSkipBanner(bool skipBanner, ILevel level)
        {
            if (_screensBuilder.CurrentScreenType is ScreenType.SideMapLevelScreen)
            {
                var currentEvent = GetCurrentGameEvent(ev => ev is SideMapGameEvent);
                var eventPalette = _gameEventResourceManager.GetSettings(currentEvent?.Uid)?.Palette;
                if (eventPalette != null)
                    ApplyPalette(eventPalette);
                else
                    UpdateForStage(Stage.Good);
            }
            else
            {
                UpdateForStage(level.Config.GetPaletteStage());
            }

            _animator.SetBool(SkipBanner, skipBanner);
            _animator.ResetTrigger(HideAnimParam);
        }

        private void UpdateForStage(Stage stage)
        {
            if (_popupStagePaletteApplier != null)
                _popupStagePaletteApplier.Apply(stage);
            var stageSettings = _levelStageRibbonPairs.Find(levelStage => levelStage.Stage.Equals(stage));
            _ribbonImage.sprite = stageSettings.RibbonSprite;
            _topSdbOutline.effectColor = stageSettings.TopBarShadowColor;
        }

        public string GetCurrentScreenInfoForDauAnalytics()
        {
            var activeGameEventsList = ConvertToAnalyticsString();
            return _contentMode switch
            {
                OutOfMovesViewContentMode.SecondModal => $"{DauInteractions.OutOfMoves.Screen2} {activeGameEventsList}",
                OutOfMovesViewContentMode.ThirdModal => $"{DauInteractions.OutOfMoves.Screen3} {activeGameEventsList}",
                _ => DauInteractions.OutOfMoves.Screen1 + "_extra_moves"
            };
        }  
        
        private string GetCurrentScreenInfoForPurchasePath()
        {
            var activeGameEventsList = ConvertToAnalyticsString();
            return _contentMode switch
            {
                OutOfMovesViewContentMode.SecondModal => $"{PurchaseStep.OutOfMovesScreen2} {activeGameEventsList}",
                OutOfMovesViewContentMode.ThirdModal => $"{PurchaseStep.OutOfMovesScreen3} {activeGameEventsList}",
                _ => string.Empty
            };
        }

        protected override void OnCloseButton()
        {
            var currentScreen = GetCurrentScreenInfoForDauAnalytics();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.OutOfMoves.CategoryName, currentScreen, 
                DauInteractions.OutOfMoves.Close));
            base.OnCloseButton();
        }

        protected override void OnCloseButtonClick()
        {
            TriggerOnCloseClicked();
            PlayCloseFX();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
        }
    }
}