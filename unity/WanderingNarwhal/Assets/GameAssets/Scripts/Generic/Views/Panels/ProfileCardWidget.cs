using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using BBB.UI.Level;
using BebopBee.Core;
using BebopBee.Core.Audio;
using BebopBee.Social;
using BebopBee.UnityEngineExtensions;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams;
using RSG;
using SoftMasking;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class ProfileCardWidget : BbbMonoBehaviour
    {
        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private TextMeshProUGUI _placeText;
        [SerializeField] private TextMeshProUGUI _nameText;
        [SerializeField] private TextMeshProUGUI _scoreText;
        [SerializeField] private Animator _animator;
        [SerializeField] private Transform _holderTransform;

        private Match3ResourceProvider _match3ResourceProvider;

        private bool _isOwn;

        private GameObject _crownGo;
        private Tween _spawnCrownTween;

        public void Setup(Match3ResourceProvider resourceProvider, LeaderboardItem leaderboardItem, bool isOwn, int ownScore = -1)
        {
            _isOwn = isOwn;
            _match3ResourceProvider = resourceProvider;

            if (leaderboardItem == null)
            {
                BDebug.LogError(LogCat.Match3, "LeaderboardItem is null");
                return;
            }

            if (!_asyncAvatar)
            {
                BDebug.LogError(LogCat.Match3, "_avatarImage is not alive");
                return;
            }

            _asyncAvatar.Setup(new AvatarInfo(leaderboardItem));

            _placeText.text = leaderboardItem.Rank.ToString();
            _nameText.text = leaderboardItem.Name;
            _scoreText.text = (isOwn && ownScore > 0) ? ownScore.ToCurrency() : leaderboardItem.Score.ToCurrency();

            if (IsCrownable(leaderboardItem.Rank))
            {
                SpawnCrown(leaderboardItem.Rank);
            }
            else if (!ReferenceEquals(_crownGo, null))
            {
                _match3ResourceProvider.ReleaseCached(Match3ResKeys.ProfileCrown);
                Destroy(_crownGo);
                _crownGo = null;
            }
        }

        private static bool IsCrownable(int rank)
        {
            return rank >= 1 && rank <= 3;
        }

        private void SpawnCrown(int rank)
        {
            var promise = _match3ResourceProvider.CacheAndLoadAsync<GameObject>(this, Match3ResKeys.ProfileCrown);
            promise.Then(asset =>
            {
                UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{asset.name}]");
                _crownGo = Instantiate(asset, _holderTransform, false);
                UnityEngine.Profiling.Profiler.EndSample();
                var crownController = _crownGo.GetComponent<CrownUIController>();
                crownController.Setup(rank);

                if (_isOwn)
                {
                    _spawnCrownTween = Rx.Invoke(2f, _ =>
                    {
                        AudioProxy.PlaySound(Match3SoundIds.CrownDropLevelSuccess);
                        _spawnCrownTween = null;
                    });
                }
            }).Done();
        }

        public void Clear()
        {
            if (!ReferenceEquals(_crownGo, null))
            {
                Destroy(_crownGo);
                _crownGo = null;
            }

            if (_spawnCrownTween != null)
            {
                _spawnCrownTween.Kill();
                _spawnCrownTween = null;
            }

            _match3ResourceProvider?.ReleaseCached(Match3ResKeys.ProfileCrown);
            _match3ResourceProvider = null;

            _animator.Rebind();
        }

        public void SetAnimatorTrigger(int outroTriggerHash)
        {
            _animator.SetTrigger(outroTriggerHash);
        }
    }
}