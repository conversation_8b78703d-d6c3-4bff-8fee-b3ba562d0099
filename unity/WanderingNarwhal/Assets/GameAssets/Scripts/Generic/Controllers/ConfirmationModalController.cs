using System;
using BBB.Core;
using GameAssets.Scripts.Generic.Views;

namespace GameAssets.Scripts.Generic.Controllers
{
    public class ConfirmationModalController : BaseModalsController<IConfirmationModalViewPresenter>
    {
        public event Action OnConfirmed;
        public event Action OnRejected;

        public void Setup(string titleId, string messageId, string confirmTextId = "CONFIRMATION_CONFIRM", string rejectTextId = "REJECT", Action onConfirmed = null, Action onRejected = null, object[] args = null)
        {
            DoWhenReady(() =>
            {
                View.Setup(titleId, messageId, confirmTextId, rejectTextId, args);
                OnConfirmed += onConfirmed;
                OnRejected += onRejected;
                Subscribe();
            });
        }

        private void InvokeConfirm()
        {
            OnConfirmed.SafeInvoke();
            base.OnCloseClicked();
        }

        private void InvokeReject()
        {
            OnRejected.SafeInvoke();
            base.OnCloseClicked();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnConfirmed += InvokeConfirm;
            View.OnRejected += InvokeReject;
        }

        private void Unsubscribe()
        {
            View.OnConfirmed -= InvokeConfirm;
            View.OnRejected -= InvokeReject;
        }

        public override void OnHide()
        {
            base.OnHide();
            Clear();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Clear();
        }

        private void Clear()
        {
            Unsubscribe();
            OnConfirmed = null;
            OnRejected = null;
        }
    }
}
