#if BBB_DEBUG

using System.Collections.Generic;
using System.Reflection;
using BBB.Actions;
using BBB.Core;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Generic;
using BBB.Map.Location;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.Quests;
using BBB.UI;
using BBB.UI.Core;
using BBB.UI.Level.HelpingHands;
using BBB.UI.Level.Input;
using BBB.UI.Level.Views;
using BBB.UI.Map.Views;
using BBB.Wallet;
using GameAssets.Scripts.GlobeModal;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Tutorial.Core;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public enum CrawlerMode
    {
        Walkthrough = 0,
        SingleLevel = 1
    }

    /// <summary>
    /// Crawler bot controller that handles fully autonomous game play.
    /// </summary>
    /// <remarks>
    /// Implementation of this bot is very specific and non-generic,
    /// it heavily relies on objects names and hierarchy (transform.Find() usage),
    /// so it may break in future (and it's kind of problem that usually easy to fix).
    /// This is tradeoff for simplicity.
    /// </remarks>
    public sealed class CrawlerController : ContextedUiBehaviour
    {
        private IWalletManager _walletManager;
        private IWalletTransactionController _transactionController => _walletManager.TransactionController;
        private IScreensManager _screensManager;

        private ScreenType _currentScreenType = ScreenType.None;

        private float _tickTimer = 0f;
        [SerializeField] private float _tickDuration = 3f;
        [SerializeField] private bool _enableLogs = true;
        [SerializeField] private CrawlerMode _crawlerMode = CrawlerMode.Walkthrough;
        [SerializeField] private string _levelToReplay = "level1";
        [SerializeField] private int _replayCounter = 0;

        private bool _startCalled = false;

        #region Match3Screen
        private Transform _levelScreen;
        private GameController _gameController;
        private LevelStarter _levelStarter;
        private GoalsSystem _goalsSystem;
        private ILevel _level;
        private M3SpawnSystem _spawnSystem;
        private TileResourceSelector _tileResources;
        private IInputController _inputController;
        private IPlayerManager _playerManager;
        private WondersManager _placeableManager;
        private int _outOfMovesCountOnCurrentLevel;
        private ILocationManager _locationManager;
        private IEventDispatcher _eventDispatcher;
        private IScreensBuilder _screensBuilder;
        private GenericHudManager _genericHudManager;
        private SuperBoostSystem _superBoostSystem;
        private TutorialPlaybackController _tutorialController;
        private IDictionary<string, FBConfig.LocationConfig> _locationConfig;
        private const int OUT_OF_MOVES_MAX_LIMIT = 100;
        #endregion

        #region MapScreen

        private Transform _mapScreen;

        #endregion

        private Transform _popupsHolder;

        [SerializeField] private float _timeScale = 12f;
        [SerializeField] private bool _isInManualMode = true;
        [SerializeField] private bool _passAllStages = false;


        /// <summary>
        /// Set value to false to start autoplay mode.
        /// </summary>
        public bool IsInManualMode
        {
            set => _isInManualMode = value;
        }

        public float TimeScale
        {
            set => _timeScale = value;
        }

        public string LevelToReplay
        {
            set
            {
                _levelToReplay = value;
                if (string.IsNullOrEmpty(_levelToReplay))
                {
                    _crawlerMode = CrawlerMode.Walkthrough;
                }
                else
                {
                    _crawlerMode = CrawlerMode.SingleLevel;
                }
            }
        }

        private bool _isCrawlerStartedDuringSession = false;
        private bool _isStartCrawlerMouseButtonDown = false;
        private float _startCrawlerMouseButtonDownTime;
        private readonly float _startCrawlerMouseButtonDownDuration = 6f;

        /// <summary>
        /// After completing m3 level
        /// this flag is used to prevent go to next level immediately
        /// without pressing HUD Play button.
        /// </summary>
        private bool _isJustReturnedFromMatch3;

        /// <summary>
        /// Helper flag that is active when currently waiting for Wonder to finish.
        /// During this period HUD Play button offers to replace old levels (DailyEvent, GameEvent or random for resources).
        /// </summary>
        private bool _isWaitingForWonder;


        private void Start()
        {
            ContextProvided -= LazyInit;
            ContextProvided += LazyInit;
        }

        protected override void OnDestroy()
        {
            ContextProvided -= LazyInit;
            _screensManager.OnScreenChanged -= OnScreenChanged;
            base.OnDestroy();
        }

        /// <summary>
        /// Pre init controller when Context is not initialized yet.
        /// Used only when component is auto-created on app start.
        /// </summary>
        /// <remarks>
        /// At app awake most of required controllers are not initialized yet,
        /// and they will be initialized only after login.
        /// But this controller requires at least screen manager to be able to track login completion.
        /// </remarks>
        public void PreInitOnAppStart(IScreensManager screensManager)
        {
            _screensManager = screensManager;
            _currentScreenType = _screensManager.GetCurrentScreenType();
            screensManager.OnScreenChanged -= OnScreenChanged;
            screensManager.OnScreenChanged += OnScreenChanged;
        }

        protected override void InitWithContextInternal(IContext context)
        {
            // If component is created on app Awake, then context will be null at this stage. This will be called again after login.
            if (context == null) return;
            _screensManager = context.Resolve<IScreensManager>();
            _currentScreenType = _screensManager.GetCurrentScreenType();
            _screensManager.OnScreenChanged -= OnScreenChanged;
            _screensManager.OnScreenChanged += OnScreenChanged;
            _currentScreenType = _screensManager.GetCurrentScreenType();
            _playerManager = context.Resolve<IPlayerManager>();
            _placeableManager = context.Resolve<WondersManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _genericHudManager = context.Resolve<GenericHudManager>();
            _levelStarter = context.Resolve<LevelStarter>();
            _tutorialController = FindObjectOfType<TutorialPlaybackController>();

            var config = context.Resolve<IConfig>();
            _locationConfig = config.Get<FBConfig.LocationConfig>();
        }

        private void OnScreenChanged(ScreenType arg1, IScreensController arg2, IViewPresenter arg3)
        {
            _isJustReturnedFromMatch3 = _currentScreenType == ScreenType.LevelScreen;

            _currentScreenType = arg1;
            ClearScreenRelatedReferences();
        }

        private void PrintError(string str, GameObject obj = null)
        {
            if (_enableLogs)
            {
                Debug.LogError("#CRAWLER0# " + str, obj);
            }
        }

        private void PrintLog(string str, GameObject obj = null)
        {
            if (_enableLogs)
            {
                Debug.Log("#CRAWLER0# " + str, obj);
            }
        }

        private void Update()
        {
            if (_isInManualMode)
            {
                if (Input.GetKeyDown(KeyCode.F8))
                {
                    _isInManualMode = false;
                    var altPressed = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
                    _passAllStages = altPressed;
                    if (_tutorialController != null)
                    {
                        _tutorialController.StopTutorial();
                    }
                    OpenExternal._debugIsExtrnalUrlOpenDisabled = true;
                }
                else if (Input.GetMouseButtonDown(0))
                {
                    if (_isCrawlerStartedDuringSession)
                    {
                        _isStartCrawlerMouseButtonDown = true;
                        _startCrawlerMouseButtonDownTime = Time.realtimeSinceStartup;
                    }
                }
                else if (Input.GetMouseButtonUp(0))
                {
                    // Start auto mode after mouse hold for 6 sec but only if bot already started at least once previously.
                    if (_isStartCrawlerMouseButtonDown)
                    {
                        if (_isCrawlerStartedDuringSession)
                        {
                            var downDuration = Time.realtimeSinceStartup - _startCrawlerMouseButtonDownTime;
                            if (downDuration > _startCrawlerMouseButtonDownDuration)
                            {
                                _isInManualMode = false;
                                var altPressed = Input.GetKeyDown(KeyCode.LeftAlt) || Input.GetKeyDown(KeyCode.RightAlt);
                                _passAllStages = altPressed;
                                OpenExternal._debugIsExtrnalUrlOpenDisabled = true;
                            }
                        }
                        _isStartCrawlerMouseButtonDown = false;
                    }
                }
                else
                {
                    ManualInput();
                }
            }
            else
            {
                if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(KeyCode.F9))
                {
                    _isInManualMode = true;
                    OpenExternal._debugIsExtrnalUrlOpenDisabled = false;
                    Time.timeScale = 1f;
                    _replayCounter = 0;
                    return;
                }

                Time.timeScale = _timeScale;
                TimerStep();
            }
        }

        private void ManualInput()
        {
            if (Input.GetKeyDown(KeyCode.F3))
            {
                InvokeHandler();
            }
        }

        private void TimerStep()
        {
            if (_tickTimer >= 0)
            {
                _tickTimer -= Time.deltaTime;
            }

            if (_tickTimer < 0)
            {
                _tickTimer = _tickDuration;
            }
            else
            {
                return;
            }

            _isCrawlerStartedDuringSession = true;
            InvokeHandler();
        }

        private void InvokeHandler()
        {
            if (_screensManager.IsTransitionInProgress)
            {
                PrintLog("Transition still in progress");
                return;
            }
            switch (_currentScreenType)
            {
                case ScreenType.EpisodeScreen:
                    switch (_crawlerMode)
                    {
                        case CrawlerMode.Walkthrough:
                            ScreenUpdateHandlerMapScreen();
                            break;
                        case CrawlerMode.SingleLevel:
                        {
                            if (_levelToReplay != null && !_startCalled)
                            {
                                _levelStarter.StartLevel(_levelToReplay);
                                _replayCounter++;
                                BDebug.LogFormat(LogCat.General, $"Crawler bot replays {_levelToReplay} {_replayCounter} time");
                                _startCalled = true;
                            }
                            break;
                        }
                    }
                    break;
                case ScreenType.LevelScreen:
                case ScreenType.HelpingHands:
                case ScreenType.SideMapLevelScreen:
                    ScreenUpdateHandlerMatch3();
                    break;
            }
        }

        private void ScreenUpdateHandlerLandingScreen()
        {
            var playBtn = GameObject.Find("Play_Button");
            if (playBtn == null)
            {
                PrintError("Landing screen: cannot find Play button");
                return;
            }

            var btn = playBtn.GetComponent<Button>();
            btn.onClick.Invoke();
            PrintLog("Landing screen: press Play button");
        }

        private void InvokeTutorialClickHandler()
        {
            var steps = GameObject.FindObjectsOfType<BaseStep>();
            foreach (var s in steps)
            {
                if (!s.gameObject.activeInHierarchy) continue;

                var type = s.GetType();
                var m = type.GetMethod("GenericSubscriber", BindingFlags.Instance | BindingFlags.NonPublic);
                if (m != null)
                {
                    m.Invoke(s, null);
                }

                s.Tap();
            }
        }

        private void ScreenUpdateHandlerMapScreen()
        {
            InvokeTutorialClickHandler();
            if (TryCloseAnyOpenPopup()) return;
            _genericHudManager.ForceShowBottomBar(true);
            TryPressMainHUDPlayButton();
        }

        private void ScreenUpdateHandlerPOIScreen()
        {
            PrintLog(TryTriggerEscButton() ? "Esc button pressed" : "No esc buttons found");
        }

        private bool TryCloseAnyOpenPopup()
        {
            if (_popupsHolder == null)
            {
                _popupsHolder = GameObject.Find("PopupCanvas").transform;
            }

            bool isAnyPopupActive = false;
            Transform popup;
            for (int i = 0; i < _popupsHolder.childCount; i++)
            {
                popup = _popupsHolder.GetChild(i);
                if (popup.gameObject.activeSelf)
                {
                    isAnyPopupActive = true;
                    break;
                }
            }

            if (!isAnyPopupActive) return false;
            if (TryPressGoInLevelStartModal()) return true;
            if (TryBuyLivesIfNeeded()) return true;
            if (TryTriggerEscButton()) return true;

            for (int i = 0; i < _popupsHolder.childCount; i++)
            {
                popup = _popupsHolder.GetChild(i);
                if (popup.gameObject.activeSelf)
                {
                    var buttons = popup.GetComponentsInChildren<Button>();
                    foreach (var btn in buttons)
                    {
                        if (btn.gameObject.activeInHierarchy && btn.interactable && btn.enabled && btn.name.ToLower().Contains("close"))
                        {
                            btn.onClick.Invoke();
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        private bool TryBuyLivesIfNeeded()
        {
            var outOfLivesPopup = FindPopup<NoMoreLivesViewPresenter>();
            if (outOfLivesPopup == null || !outOfLivesPopup.gameObject.activeInHierarchy)
                return false;

            // Press Buy button in the main popup
            var type = outOfLivesPopup.GetType();
            var buyBtnField = type.GetField("_buyButton", BindingFlags.Instance | BindingFlags.NonPublic);
            var buyButton = (Button)(buyBtnField?.GetValue(outOfLivesPopup));
            if (buyButton != null && buyButton.gameObject.activeInHierarchy)
            {
                buyButton.onClick.Invoke();
                return true;
            }

            // Press Buy button in the second part of the popup
            buyBtnField = type.GetField("_buyRefillButton", BindingFlags.Instance | BindingFlags.NonPublic);
            buyButton = (Button)(buyBtnField?.GetValue(outOfLivesPopup));
            if (buyButton != null && buyButton.gameObject.activeInHierarchy)
            {
                // Add money enough to buy lives
                var priceDict = new Dictionary<string, int>() { { WalletCurrencies.RegularCurrency, 900 } };
                var add5MovesPriceTransaction = new Transaction()
                    .Earn(priceDict)
                    .SetDebug(true)
                    .SetIsHidden(true);
                _transactionController.MakeTransaction(add5MovesPriceTransaction);

                buyButton.onClick.Invoke();
                return true;
            }

            return false;
        }
        
        private bool TryPressGoInLevelStartModal()
        {
            if ((_currentScreenType & ScreenType.EpisodeScreen) == 0) return false;
            if (_isJustReturnedFromMatch3 && _isWaitingForWonder) return false;
            for (int i = 0; i < _popupsHolder.childCount; i++)
            {
                var popup = _popupsHolder.GetChild(i);

                if (!popup.gameObject.activeSelf) continue;

                var levelStartModal = popup.GetComponent<StartLevelViewPresenter>();
                if (levelStartModal != null)
                {
                    var goBtn = popup.Find("InverseCanvasScaler/VisualRoot/GenericButtonRoot/GenericButton");
                    if (goBtn == null) continue;

                    var btn = goBtn.GetComponent<Button>();
                    btn.onClick.Invoke();
                    return true;
                }

                var eventModal = popup.GetComponent<CompetitionEventGenericViewPresenter>();
                if (eventModal != null)
                {
                    var goBtn = popup.Find("Modal/GoButton");
                    if (goBtn != null && goBtn.gameObject.activeInHierarchy)
                    {
                        goBtn.GetComponent<Button>().onClick.Invoke();
                        return true;
                    }
                }
            }
            return false;
        }

        private bool TryPressMainHUDPlayButton()
        {
            var holder = GameObject.Find("FlowButtonsHolder");
            if (holder == null) return false;

            var hudButton = holder.transform.Find("MapScreenContainer/LevelButton");
            if (hudButton == null) return false;

            var btn = hudButton.GetComponent<Button>();

            btn.onClick.Invoke();
            _isJustReturnedFromMatch3 = false;
            return true;
        }

        private bool IsCityCompletedAtGoodStage()
        {
            return _playerManager.CurrentLocation.BronzeCompleted;
        }

        private bool IsCityCompletedAtBestStage()
        {
            return _playerManager.CurrentLocation.IsCompleted;
        }

        private void ScreenUpdateHandlerMatch3()
        {
            InvokeTutorialClickHandler();

            FindAllMatch3RelatedReferences();
            
            var outOfMovesPopup = FindPopup<OutOfMovesViewPresenter>();
            if (outOfMovesPopup != null && outOfMovesPopup.gameObject.activeInHierarchy)
            {
                if (_outOfMovesCountOnCurrentLevel >= OUT_OF_MOVES_MAX_LIMIT)
                {
                    PrintError("Couldn't finish level. Check level: " + _level.LevelName + " (" + _level.Config.Uid + ")");
                    if (InvokeButtonClickOnBackButtonPress.TryDebugInvokeEscButtonForCurrentListeners())
                    {
                        PrintLog("Exit level that can't be passed");
                        return;
                    }
                }
                else
                {
                    _outOfMovesCountOnCurrentLevel++;
                    _tickTimer *= 4;
                    var type = outOfMovesPopup.GetType();
                    var confirmMethod = type.GetMethod("OnTryToPurchase", BindingFlags.Instance | BindingFlags.NonPublic);
                    if (confirmMethod != null)
                    {
                        var priceDict = new Dictionary<string, int>() { { WalletCurrencies.RegularCurrency, 5000 } };
                        var add5MovesPriceTransaction = new Transaction()
                            .SetDebug(true)
                            .Earn(priceDict)
                            .SetIsHidden(true);
                        _transactionController.MakeTransaction(add5MovesPriceTransaction);
                        confirmMethod.Invoke(outOfMovesPopup, parameters: null);
                    }

                    return;
                }
            } 
            
            var globePopup = FindPopup<GlobeViewPresenter>();
            if (globePopup != null && globePopup.gameObject.activeInHierarchy)
            {
                var btn = globePopup.transform.Find("InverseCanvasScaler/SendChallengeButton");
                if (btn != null && btn.gameObject.activeInHierarchy)
                {
                    btn.GetComponent<Button>().onClick.Invoke();
                    return;
                }
            }

            if (!IsAllMatch3ReferencesValid()) return;

            if (_gameController.IsInputLocked)
            {
                if (_gameController.GameEnded)
                {
                    //TODO: Handle side map levels?
                    if (_currentScreenType == ScreenType.HelpingHands)
                    {
                        if (_gameController.HasWon)
                        {
                            var helpingHands = _levelScreen.GetComponentInChildren<HelpingHandsContentController>();
                            if (helpingHands != null)
                            {
                                var keepPlayingButton = helpingHands.transform.Find("Container/InfoPanel/Button_1/KeepPlaying");
                                if (keepPlayingButton != null)
                                {
                                    var btn = keepPlayingButton.GetComponent<Button>();
                                    btn.onClick.Invoke();
                                    return;
                                }
                            }
                        }
                    }
                    else
                    {
                        var levelSuccess = _levelScreen.GetComponentInChildren<UI.Level.LevelSuccessBaseViewPresenter>();
                        if (levelSuccess != null && levelSuccess.gameObject.activeInHierarchy)
                        {
                            var noThanksButton = levelSuccess.transform.Find("NoThanksButton");
                            if (noThanksButton != null && noThanksButton.gameObject.activeInHierarchy)
                            {
                                var b = noThanksButton.GetComponent<Button>();
                                b.onClick.Invoke();
                                return;
                            }

                            if (TryTriggerEscButton())
                            {
                                PrintLog("Click Esc on level win");
                                return;
                            }
                        }
                    }

                    if (!TryCloseAnyOpenPopup())
                    {
                        _tickTimer *= 0.5f;
                    }
                }
                else
                {
                    _tickTimer *= 0.5f;
                }
            }
            else
            {
                if (_superBoostSystem.AllowedToUse)
                {
                    _superBoostSystem.UseSuperBoost();
                    _superBoostSystem.ResetProgress();
                    return;
                }

                var bestMove = OptimalMovePicker.GetBestPossibleMove(_level, _level.Grid, _goalsSystem, _spawnSystem, _gameController, _tileResources);
                if (bestMove.Type == PossibleMoveType.DoubleTap)
                {
                    _inputController.AutoDoubleTap(bestMove.FirstCell.Coords);
                }
                else
                {
                    _inputController.AutoSwap(bestMove.FirstCell.Coords, bestMove.SecondCell.Coords);
                }

                _tickTimer *= 0.5f;
            }
        }

        private bool IsAnyPopupActive()
        {
            for (int i = 0; i < _popupsHolder.childCount; i++)
            {
                var popup = _popupsHolder.GetChild(i);
                if (popup.gameObject.activeSelf)
                {
                    return true;
                }
            }

            return false;
        }

        private T FindPopup<T>() where T : BbbMonoBehaviour
        {
            if (_popupsHolder == null)
            {
                _popupsHolder = GameObject.Find("PopupCanvas").transform;
            }

            return _popupsHolder.GetComponentInChildren<T>();
        }

        private bool TryTriggerEscButton()
        {
            return InvokeButtonClickOnBackButtonPress.TryDebugInvokeEscButtonForCurrentListeners();
        }

        private void FindAllMatch3RelatedReferences()
        {
            if (_levelScreen == null)
            {
                _levelScreen = GameObject.Find("LevelCanvas").transform;
            }

            if (_gameController == null)
            {
                _gameController = FindObjectOfType<GameController>();
            }

            if (_gameController == null)
            {
                // Game is already finished or not started yet.
                return;
            }

            if (_goalsSystem == null)
            {
                if (_gameController != null)
                {
                    _goalsSystem = _gameController.GoalsSystem;
                }
            }

            if (_level == null)
            {
                if (_gameController != null)
                {
                    _level = _gameController.Level;
                }
            }

            if (_spawnSystem == null)
            {
                var type = typeof(LevelControllerBase);
                var spawnSystemField = type.GetField("_spawnSystem", BindingFlags.Static | BindingFlags.NonPublic);
                _spawnSystem = (M3SpawnSystem)spawnSystemField.GetValue(null);
            }

            if (_tileResources == null)
            {
                var proxy = FindObjectOfType<LevelControllerReferenceProxy>();
                _tileResources = proxy.TilesResourcesExtra;
            }

            if (_inputController == null)
            {
                _inputController = FindObjectOfType<EasyTouchInputController>();
            }

            if (_superBoostSystem == null)
            {
                var type = typeof(LevelControllerBase);
                var superBoostSystemField = type.GetField("_superBoostSystem", BindingFlags.Static | BindingFlags.NonPublic);
                _superBoostSystem = (SuperBoostSystem)superBoostSystemField?.GetValue(null);
            }

        }

        private bool IsAllMatch3ReferencesValid()
        {
            return
                _levelScreen != null
                && _gameController != null
                && _goalsSystem != null
                && _level != null
                && _spawnSystem != null
                && _tileResources != null
                && _inputController != null
                && _superBoostSystem != null;
        }

        private void ClearScreenRelatedReferences()
        {
            _levelScreen = null;
            _gameController = null;
            _goalsSystem = null;
            _level = null;
            _spawnSystem = null;
            _tileResources = null;
            _inputController = null;
            _outOfMovesCountOnCurrentLevel = 0;

            _mapScreen = null;
        }
    }
}
#endif