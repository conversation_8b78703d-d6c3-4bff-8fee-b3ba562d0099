using BBB;
using UnityEngine;
using UnityEngine.UI;

public class DebugComponentAddSub : BbbMonoBehaviour {
    [SerializeField] Button subButton;
    [SerializeField] Button addButton;
    [SerializeField] Text descriptionText;
    [SerializeField] InputField valueInput;

    public void Init(System.Func<int> getter, System.Action<int> setter, int step = 1)
    {
        addButton.onClick.RemoveAllListeners();
        subButton.onClick.RemoveAllListeners();

        addButton.onClick.AddListener(() =>
        {
            var value = getter() + step;
            valueInput.text = value.ToString();
        });

        subButton.onClick.AddListener(() =>
        {
            var value = getter() - step;
            valueInput.text = value.ToString();
        });

        valueInput.onValueChanged.AddListener(newValue =>
        {
            setter(System.Convert.ToInt32(newValue));
        });

        valueInput.text = getter().ToString();
    }
}
