using BBB;
using BebopBee.UnityEngineExtensions;
using FBConfig;
using UnityEngine;

public class DebugQuestObjectivesPanelController : DebugPanelController
{
    [SerializeField] private GameObject _prefabOfItem;
    [SerializeField] private Transform _container;

    private QuestConfig _questConfigFetch;
    
    protected override void OnInit()
    {
        SetupScrollview();
    }    
    
    private void SetupScrollview()
    {
        _questConfigFetch = gameObject.GetComponentInParent<DebugScreenController>().LastQuestConfig;
        _container.DestroyChildren();
        for (int i = 0; i < _questConfigFetch.ObjectivesFbLength; i++)
        {
            AddQuestObjectiveItem(_questConfigFetch.ObjectivesFb(i).Value);
        }
    }

    private void AddQuestObjectiveItem(QuestObjectiveConfig objective)
    {
        GameObject questObjectGo = Instantiate(_prefabOfItem, _container);
        var questSetupComponent = questObjectGo.GetComponent<DebugQuestEvaluationObject>();
        questSetupComponent.Setup(DebugScreenController.QuestManager, objective);
    }
}
