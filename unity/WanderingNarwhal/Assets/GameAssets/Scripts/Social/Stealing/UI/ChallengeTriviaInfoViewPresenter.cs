using BBB.Screens;
using TMPro;
using UnityEngine;

namespace BBB.UI
{
    public class ChallengeTriviaInfoViewPresenter : ModalsViewPresenter, IChallengeTriviaInfoViewPresenter
    {
        private const float DefaultFillAmount = 0.7f;
        
        [SerializeField] private LocalizedTextPro _firstStepText;
        [SerializeField] private LocalizedTextPro _descriptionText;
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField][Range(0, 1)] private float _fillAmount = DefaultFillAmount;

        public void Setup(int totalWinCount)
        {
            totalWinCount = Mathf.Max(1, totalWinCount);
            _firstStepText.FormatSetArgs(totalWinCount);
            _descriptionText.FormatSetArgs(totalWinCount);
            var filledValue = Mathf.RoundToInt(totalWinCount * _fillAmount);
            _progressBar.SetProgress(filledValue / (float)totalWinCount);
            _progressText.text = $"{filledValue}/{totalWinCount}";
        }
    }
}
