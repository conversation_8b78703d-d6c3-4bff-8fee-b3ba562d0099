using System;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using BBB;
using BBB.Core;
using BBB.Core.AssetBundles.Loaders;
using BBB.Core.ResourcesManager;
using BBB.DI;
using Core.Configs;
using TMPro;

public class LocalizationManager : ILocalizationManager, IContextInitializable
{
    private static readonly string[] IsoCodesCjk = { "ZHS", "JA", "KO"};
    
    private const string DifferentSymbolLanguagesFileName = "DifferentSymbolLanguages";
    private const string CjkStaticInitialFontFileName = "CJKFont_Static_Initial";
    private const string CjkStaticFontPath = "Fonts & Materials/CJKFont_Static";
    
    private static LocalizationManager _instance;
    
    private static TMP_FontAsset _differentSymbolLanguages;
    private static TMP_FontAsset _cjkStaticInitialFont;
    private static TMP_FontAsset _cjkStaticFont;
    private Action _initialFontsLoadedCallback;
    private static bool _initialFontsLoaded;
    
    private IAssetsManager _assetsManager;
    private IConfig _config;
    private IDictionary<string, FBConfig.TextConfig> _textConfig;
    private readonly ICoroutineExecutor _coroutineExecutor;

    public LocalizationManager(ICoroutineExecutor coroutineExecutor)
    {
        _instance = this;
        
        //UpdateTextConfig is being called before context initialization, so getting coroutineExecutor via ctr to start loading earlier if needed
        _coroutineExecutor = coroutineExecutor;
    }

    public void InitializeByContext(IContext context)
    {
        _assetsManager = context.Resolve<IAssetsManager>();
        LoadInitialFallbackFonts();
    }

    public static string GetLocalizedText(string textId, bool noError = false)
    {
        return _instance?.getLocalizedText(textId, noError);
    }

    public static string GetLocalizedTextWithArgs(string textId, params object[] args)
    {
        return _instance?.getLocalizedTextWithArgs(textId, args);
    }
    
    private void UpdateTextConfig()
    {
        LanguageHelper.CheckLanguagesDictionary();
        var isoCode = LanguageHelper.GetISOCodeFromSystemLanguageEx();
        var currentLanguageTextConfigName = LanguageHelper.GetTextConfigName(isoCode);
        _textConfig = _config.Get<FBConfig.TextConfig>(currentLanguageTextConfigName) ?? new Dictionary<string, FBConfig.TextConfig>();

        SetCjkFallback(isoCode);
    }

    private void LoadInitialFallbackFonts()
    {
        if (_differentSymbolLanguages != null) return;

        LoadInitialFont(CjkStaticInitialFontFileName, font => _cjkStaticInitialFont = font);
        LoadInitialFont(DifferentSymbolLanguagesFileName, font =>
        {
            _differentSymbolLanguages = font;
            _initialFontsLoaded = true;
            _initialFontsLoadedCallback?.Invoke();
        });
    }

    private void LoadInitialFont(string fontName, Action<TMP_FontAsset> onComplete)
    {
#if UNITY_EDITOR
        if (SimulatedLoader.SimulateAssetBundleInEditor)
        {
            TMP_FontAsset font = null;
            var guids = UnityEditor.AssetDatabase.FindAssets(fontName);
            if (guids.Length > 0)
            {
                font = UnityEditor.AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]));
            }
            onComplete?.Invoke(font);
        }
        else
#endif
            _assetsManager.LoadAsync<TMP_FontAsset>(fontName, AssetLoadPriority.Immediately).Then(fontAsset => onComplete?.Invoke(fontAsset?.Get()));
    }

    private void SetCjkFallback(string isoCode)
    {
        if (_initialFontsLoaded && _differentSymbolLanguages == null) return;
        
        foreach (var cjkCode in IsoCodesCjk)
        {
            if (isoCode != cjkCode) continue;

            if (_cjkStaticFont != null) return;
            LoadFontAsset(CjkStaticFontPath, font =>
            {
                _cjkStaticFont = font;
                if (_initialFontsLoaded)
                {
                    SetCjkStaticFont();
                }
                else
                {
                    _initialFontsLoadedCallback = SetCjkStaticFont;
                }
            });
            return;
        }

        ResetCjkStaticFont();
    }

    private void SetCjkStaticFont()
    {
        if (_differentSymbolLanguages != null)
        {
            SwitchFallbackFonts(_cjkStaticInitialFont, _cjkStaticFont);
        }
        else
        {
            ResetCjkStaticFont();
        }
    }

    private void LoadFontAsset(string path, Action<TMP_FontAsset> complete)
    {
        _coroutineExecutor?.StartCoroutine(LoadFontCoroutine(path, complete));
    }

    private IEnumerator LoadFontCoroutine(string path, Action<TMP_FontAsset> complete)
    {
        var loadRequest = Resources.LoadAsync<TMP_FontAsset>(path);
        yield return loadRequest;
        
        complete?.Invoke(loadRequest.asset as TMP_FontAsset);
    }

    private void ResetCjkStaticFont()
    {
        if (_cjkStaticFont == null) return;
        
        SwitchFallbackFonts(_cjkStaticFont, _cjkStaticInitialFont, true);
        
        Resources.UnloadAsset(_cjkStaticFont);
        _cjkStaticFont = null;
        Resources.UnloadUnusedAssets();
    }

    private void SwitchFallbackFonts(TMP_FontAsset from, TMP_FontAsset to, bool forceRemove = false)
    {
        if (_differentSymbolLanguages == null) return;
        
        var fallbackList = _differentSymbolLanguages.fallbackFontAssetTable;
        if (to == null || fallbackList.Contains(to))
        {
            if (forceRemove)
            {
                fallbackList.Remove(from);
            }
            return;
        }
        
        var index = from == null ? -1 : fallbackList.IndexOf(from);
        if (index >= 0)
        {
            fallbackList[index] = to;
            return;
        }

        fallbackList.Insert(0, to);
    }

    public void SetConfig(IConfig config, HashSet<Type> updatedConfigs = null)
    {
        _config = config;

        if (!PlayerPrefs.HasKey("CurrentLanguage"))
        {
            PlayerPrefs.SetInt("CurrentLanguage", (int) Application.systemLanguage);
        }

        UpdateTextConfig();

        LocalizedImage.Init(this);
        LocalizedText.Init(this);
        LocalizedTextPro.Init(this);
    }

    public bool ContainsLocalizableText(string textId)
    {
        return _textConfig.ContainsKey(textId);
    }

    public string getLocalizedText(string textId, bool noError = false)
    {
        var ret = textId;

        if (!string.IsNullOrEmpty(textId) && !_textConfig.ContainsKey(textId))
        {
            if (!noError)
            {
                Debug.LogError("LocalizationManager: TextId \"" + textId + "\" not found. Configuration missing.");
            }
        }
        else if (!textId.IsNullOrEmpty() && textId?.Trim().Length > 0)
        {
            ret = _textConfig[textId].Text;
        }

        return ret;
    }

    public string getLocalizedTextWithArgs(string textId, params object[] args)
    {
        var ret = textId;

        if (string.IsNullOrEmpty(textId) || !_textConfig.ContainsKey(textId))
        {
            Debug.LogError("LocalizationManager: TextId \"" + textId + "\" not found. Configuration missing.");
        }
        else
        {
            // TODO: make check for existence of formatting literals in string to avoid exceptions.
            // If text doesn't allow enough placing spots for all arguments, then just ignore them.
            // this will allow to have optional amount of string parameters. -VK
            try
            {
                ret = string.Format(_textConfig[textId].Text, args);
            }
            catch (FormatException ex)
            {
                Debug.LogError($"LocalizationManager: Format exception for '{textId}': " + ex.Message);
                ret = _textConfig[textId].Text;
            }
            catch (System.Exception ex0)
            {
                Debug.LogError($"LocalizationManager: Get Localized Text Excpetion: " + ex0.Message);
            }
        }

        return ret;
    }

    public SystemLanguage getCurrentLanguage()
    {
        return (SystemLanguage) PlayerPrefs.GetInt("CurrentLanguage",(int)SystemLanguage.English);
    }

    public void setCurrentLanguage(SystemLanguage langId)
    {
        PlayerPrefs.SetInt("CurrentLanguage", (int) langId);
        UpdateTextConfig();
    }

    public string GetLocalizedFilename(string filename)
    {
        return LocalizedFilename(filename);
    }

    private static string LocalizedFilename(string filename)
    {
        var currentLanguage = Application.systemLanguage;
        if (currentLanguage == SystemLanguage.English) return filename;

        var languageCode = LanguageHelper.GetISOCodeFromSystemLanguage(currentLanguage);
        if (string.IsNullOrEmpty(languageCode)) return filename;

        var fileNoExtension = Path.GetFileNameWithoutExtension(filename);
        var extension = Path.GetExtension(filename);

        return !string.IsNullOrEmpty(extension) ? $"{fileNoExtension}_{languageCode}{extension}" : $"{fileNoExtension}_{languageCode}";
    }

#if UNITY_EDITOR
    public void DebugPrintDistictCharactersSetForCurrentConfig()
    {
        var allChars = new List<string>(1000);
        foreach (var item in _textConfig)
        {
            var enumerator = System.Globalization.StringInfo.GetTextElementEnumerator(item.Value.Text);
            while(enumerator.MoveNext())
            { 
                var element = enumerator.GetTextElement();
                if (!allChars.Contains(element))
                {
                    allChars.Add(element);
                }
            }
        }

        var sb = new StringBuilder(allChars.Count);
        BDebug.Log(LogCat.General,$"Printing characters set for the language: {((SystemLanguage)PlayerPrefs.GetInt("CurrentLanguage", 0)).ToString()}, distinct characters count: {allChars.Count}");
        allChars.Sort();
        foreach (var ch in allChars)
        {
            sb.Append(ch);
        }

        Debug.Log(sb.ToString());
    }
#endif

    public void SubscribeToConfigUpdates()
    {
        Unsubscribe();
        Config.OnConfigUpdated += SetConfig;
    }

    private void Unsubscribe()
    {
        Config.OnConfigUpdated -= SetConfig;
    }

    public void Restart()
    {
        Unsubscribe();
        _initialFontsLoadedCallback = null;
        _instance = null;
        _assetsManager?.UnloadAsset(_differentSymbolLanguages);
        _differentSymbolLanguages = null;
    
        _assetsManager?.UnloadAsset(_cjkStaticInitialFont);
        _cjkStaticInitialFont = null;
    
        _assetsManager?.UnloadAsset(_cjkStaticFont);
        _cjkStaticFont = null;
    }
}