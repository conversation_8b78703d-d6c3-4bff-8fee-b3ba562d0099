using System;
using System.Collections.Generic;
using BBB.DI;
using BebopBee.Social;
using Cysharp.Threading.Tasks;

namespace BBB.Social.SignIn
{
    public interface ISignInManager
    {
        void InitContext(IContext context);

        bool IsLoggedIn();
        
        string CurrentUserId();

        string CurrentDisplayName();
        string CurrentAvatar();
        string CurrentEmail();
        string GetLinkedUserId();

        UniTask Initialize();
    
        UniTask<LoginResultData> LogInAsync(AccountType accountType);
        UniTask<bool> TrySignInAnonymouslyAsync();
        string LogOut();
        
        void LogOutPlatform(AccountType accountType);
        List<AccountType> GetAuthSources();
        
        void LogFirebaseProfile();
        void Reset();
        void Restart();
    }

    public class LoginResultData
    {
        public bool Cancelled;
        public bool Success;
        public string Error;
    }
}