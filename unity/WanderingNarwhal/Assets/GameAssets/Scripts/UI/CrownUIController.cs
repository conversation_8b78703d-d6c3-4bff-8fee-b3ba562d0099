using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class CrownUIController : BbbMonoBehaviour
    {
        [SerializeField] private List<Sprite> _crownSprites;
        [SerializeField] private List<Image> _crownImages;

        public void Setup(int rank)
        {
            if (rank is < 1 or > 3)
            {
                gameObject.SetActive(false);
            }
            else
            {
                gameObject.SetActive(true);
                _crownImages.ForEach(image => { image.sprite = _crownSprites[rank - 1]; });   
            }
        }
    }
}