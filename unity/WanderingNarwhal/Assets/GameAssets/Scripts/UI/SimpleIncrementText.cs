using System.Collections;
using BBB;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.UI
{
    public class SimpleIncrementText : BbbMonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI _text;
        [SerializeField] private AnimationCurve _fadeAnimCurve;
        [SerializeField] private AnimationCurve _scaleUpCurve;
        [SerializeField] private float _duration = 1f;

        private Coroutine _routine;
        private Transform _transform;
        private float _timer = 0f;

        public void Show(int incrementNumber, string mask = "+{0}")
        {
            _text.text = string.Format(mask, incrementNumber);

            _timer = 0f;
            _text.SetAlpha(0f);
            if (_transform == null)
            {
                _transform = _text.transform;
            }

            _transform.localScale = Vector3.zero;

            if (_routine != null)
            {
                StopCoroutine(_routine);
                _routine = null;
            }

            if (isActiveAndEnabled)
            {
                _routine = StartCoroutine(Routine());
            }
        }

        protected override void OnDisable()
        {
            if (_routine == null)
                return;
            StopCoroutine(_routine);
            _routine = null;
        }

        private IEnumerator Routine()
        {
            while (_timer < _duration)
            {
                var x = _timer / _duration;
                _text.SetAlpha(_fadeAnimCurve.Evaluate(x));
                var scale = _scaleUpCurve.Evaluate(x);
                _transform.localScale = new Vector3(scale,scale, scale); 
                _timer += Time.deltaTime;
                yield return null;
            }
        }
    }
}