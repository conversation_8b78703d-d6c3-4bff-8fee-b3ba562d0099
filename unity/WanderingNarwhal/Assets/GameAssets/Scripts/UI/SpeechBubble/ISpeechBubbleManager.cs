using System;
using System.Collections.Generic;
using BBB;
using UnityEngine;

namespace GameAssets.Scripts.UI.SpeechBubble
{
    public interface ISpeechBubbleManager
    {
        float AutoHideTime { get; }

        public Dictionary<Direction, (RectTransform, RectTransform)> RectTransformData { get; }

        // Unified text display methods
        public void ShowTextDisplay(TextDisplayConfig config, IDictionary<string, int> rewards = null);
        public void ToggleTextDisplay(TextDisplayConfig config, IDictionary<string, int> rewards = null);
        public void HideTextDisplay(Transform targetTransform, Action hideCallback = null);
        public void HideAllTextDisplays();

        // Backward compatibility methods
        public RewardInfoController GetSpeechBubble(Transform targetTransform);

        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        public void ToggleSpeechBubble(SpeechBubbleConfig speechBubbleConfig, IDictionary<string, int> reward = null,
            string rewardText = null, params object[] textArgs);

        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        public void ShowSpeechBubble(SpeechBubbleConfig speechBubbleConfig, IDictionary<string, int> reward = null,
            string rewardText = null, params object[] textArgs);

        [System.Obsolete("Use HideTextDisplay instead", false)]
        public void HideSpeechBubble(Transform targetTransform, Action hideCallback = null);

        [System.Obsolete("Use HideAllTextDisplays instead", false)]
        public void HideAllSpeechBubbles();

        // Floating text backward compatibility
        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        FloatingTextEffectBase ShowFloatingText(string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);

        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        FloatingTextEffectBase ShowFloatingText(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);

        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        FloatingTextEffectBase ShowFloatingTextNoBg(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);

        [System.Obsolete("Use ShowTextDisplay with TextDisplayConfig instead", false)]
        FloatingTextEffectBase ShowFloatingTextWithCurrency(string currency, bool withBg, Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);
    }
}