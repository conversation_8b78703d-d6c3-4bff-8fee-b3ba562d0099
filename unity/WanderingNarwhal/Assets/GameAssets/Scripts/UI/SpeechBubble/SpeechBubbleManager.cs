using System;
using System.Collections.Generic;
using BBB;
using BBB.Controller;
using BBB.Core;
using BBB.DI;
using BBB.Modals;
using BebopBee;
using UnityEngine;

namespace GameAssets.Scripts.UI.SpeechBubble
{
    public enum Direction
    {
        Top,
        Bottom,
        Left,
        Right
    }

    public enum TextDisplayType
    {
        SpeechBubble,
        FloatingText
    }

    [Serializable]
    public class TextDisplayConfig
    {
        [Header("Common Settings")]
        [SerializeField] public Transform TargetTransform;
        [SerializeField] public TextDisplayType DisplayType = TextDisplayType.SpeechBubble;
        [SerializeField] public bool AutoHide = true;

        [Header("Speech Bubble Settings")]
        [SerializeField] public Direction Direction = Direction.Top;

        [Header("Floating Text Settings")]
        [SerializeField] public FloatingTextType FloatingTextType = FloatingTextType.Default;
        [SerializeField] public bool WithBackground = true;
        [SerializeField] public string Currency = "";

        [Header("Text Content")]
        [SerializeField] public string TextId = "";
        [SerializeField] public object[] TextArgs = null;

        [Header("Rewards (Speech Bubble Only)")]
        [SerializeField] public bool HasRewards = false;
    }

    // Keep old config for backward compatibility
    [Serializable]
    [System.Obsolete("Use TextDisplayConfig instead", false)]
    public class SpeechBubbleConfig
    {
        [SerializeField] public Transform TargetTransform;
        [SerializeField] public Direction Direction;
        [SerializeField] public bool AutoHide;

        // Implicit conversion to new config
        public static implicit operator TextDisplayConfig(SpeechBubbleConfig oldConfig)
        {
            return new TextDisplayConfig
            {
                TargetTransform = oldConfig.TargetTransform,
                DisplayType = TextDisplayType.SpeechBubble,
                Direction = oldConfig.Direction,
                AutoHide = oldConfig.AutoHide
            };
        }
    }

    [Serializable]
    public struct FloatingTextPrefab
    {
        public FloatingTextType Type;
        public GameObject Prefab;
    }

    public class SpeechBubbleManager : BbbMonoBehaviour, ISpeechBubbleManager
    {
        [SerializeField] private int _initialPoolSize;
        [SerializeField] private float _speechBubbleAutoHideTime;

        [SerializeField] private RectTransform _topBubble;
        [SerializeField] private RectTransform _bottomBubble;
        [SerializeField] private RectTransform _leftBubble;
        [SerializeField] private RectTransform _rightBubble;
        [SerializeField] private RectTransform _topTip;
        [SerializeField] private RectTransform _bottomTip;
        [SerializeField] private RectTransform _leftTip;
        [SerializeField] private RectTransform _rightTip;

        // Floating text fields
        [SerializeField] private RectTransform _rootRectTransform;
        [SerializeField] private List<FloatingTextPrefab> _floatingTextPrefabs;
        [SerializeField] private Transform _floatingTextDefaultAnchor;

        private IScreensManager _screensManager;
        private IModalsManager _modalsManager;
        private GoPool _speechBubblePool;
        private readonly Dictionary<Transform, RewardInfoController> _shownBubbles = new();

        // Floating text fields
        private Dictionary<int, GoPool> _floatingTextsPool;
        private CurrencyIconsLoader _currencyIconsLoader;
        private Vector3 _leftBottomCorner;
        private Vector3 _rightTopCorner;
        private readonly Dictionary<FloatingTextEffectBase, Transform> _shownTextTargets = new();

        public float AutoHideTime => _speechBubbleAutoHideTime;
        public Dictionary<Direction, (RectTransform, RectTransform)> RectTransformData { get; private set; }

        private void Awake()
        {
            RectTransformData = new Dictionary<Direction, (RectTransform, RectTransform)>
            {
                { Direction.Top, (_topBubble, _topTip) },
                { Direction.Bottom, (_bottomBubble, _bottomTip) },
                { Direction.Left, (_leftBubble, _leftTip) },
                { Direction.Right, (_rightBubble, _rightTip) }
            };
        }

        private void Start()
        {
            //calling from Start to be sure InverseCanvasScaler's OnEnable was called
            UpdateRootCorners();
        }

        private void UpdateRootCorners()
        {
            if (_rootRectTransform == null) return;

            var rootCorners = new Vector3[4];
            _rootRectTransform.GetWorldCorners(rootCorners);

            _leftBottomCorner = rootCorners[0];
            _rightTopCorner = rootCorners[2];
        }

        public void Init(IContext context)
        {
            var genericResourceProvider = context.Resolve<GenericResourceProvider>();
            if (genericResourceProvider == null)
            {
                BDebug.LogError(LogCat.General, $"SpeechBubbleManager: GenericResourceProvider not found in context.");
                return;
            }

            var speechBubblePrefab = genericResourceProvider.GetPreloaded<GameObject>(GenericResKeys.SpeechBubblePrefab);

            if (speechBubblePrefab == null)
            {
                BDebug.LogError(LogCat.General, $"SpeechBubbleManager: SpeechBubblePrefab not found in context.");
                return;
            }

            _speechBubblePool = new GoPool(speechBubblePrefab, transform, _initialPoolSize, instance =>
            {
                var rewardInfoController = instance.GetComponent<RewardInfoController>();
                if (rewardInfoController == null)
                {
                    BDebug.LogError(LogCat.General, $"SpeechBubbleManager: RewardInfoController not found on prefab.");
                }
                else
                {
                    rewardInfoController.Init(context);
                }
            });

            // Initialize floating text pools
            InitFloatingTextPools(context);

            _screensManager = context.Resolve<IScreensManager>();
            _modalsManager = context.Resolve<IModalsManager>();

            Subscribe();
        }

        private void InitFloatingTextPools(IContext context)
        {
            if (_floatingTextsPool != null) return;

            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();

            if (_floatingTextPrefabs != null && _floatingTextDefaultAnchor != null)
            {
                foreach (var floatingTextPrefab in _floatingTextPrefabs)
                {
                    _floatingTextsPool ??= new Dictionary<int, GoPool>();

                    var prefab = floatingTextPrefab.Prefab;
                    if (prefab == null) continue;

                    var pool = new GoPool(prefab, _floatingTextDefaultAnchor, _initialPoolSize, go =>
                    {
                        var floatingTextEffect = go.GetComponent<DynamicFloatingTextEffect>();
                        if (floatingTextEffect != null)
                        {
                            floatingTextEffect.ProvideCurrencyIconLoader(_currencyIconsLoader);
                        }
                        go.SetActive(false);
                    });

                    _floatingTextsPool[(int)floatingTextPrefab.Type] = pool;
                }
            }
        }

        private void Subscribe()
        {
            Unsubscribe();

            _screensManager.OnScreenChangingStarted += HideAllSpeechBubbles;
            _modalsManager.ModalShown += HideAllSpeechBubbles;
            _modalsManager.ModalHidden += HideAllSpeechBubbles;
        }

        private void HideAllSpeechBubbles(IController obj)
        {
            HideAllSpeechBubblesFast();
        }

        private void HideAllSpeechBubbles(ScreenType arg1, IScreensController arg2)
        {
            HideAllSpeechBubblesFast();
        }

        private void Unsubscribe()
        {
            if (_screensManager != null)
            {
                _screensManager.OnScreenChangingStarted -= HideAllSpeechBubbles;
            }

            if (_modalsManager != null)
            {
                _modalsManager.ModalShown -= HideAllSpeechBubbles;
                _modalsManager.ModalHidden -= HideAllSpeechBubbles;
            }
        }

        // Unified text display methods
        public void ShowTextDisplay(TextDisplayConfig config, IDictionary<string, int> rewards = null)
        {
            if (config.TargetTransform == null) return;

            switch (config.DisplayType)
            {
                case TextDisplayType.SpeechBubble:
                    ShowSpeechBubbleInternal(config, rewards);
                    break;
                case TextDisplayType.FloatingText:
                    ShowFloatingTextInternal(config);
                    break;
            }
        }

        public void ToggleTextDisplay(TextDisplayConfig config, IDictionary<string, int> rewards = null)
        {
            if (config.TargetTransform == null) return;

            switch (config.DisplayType)
            {
                case TextDisplayType.SpeechBubble:
                    ToggleSpeechBubbleInternal(config, rewards);
                    break;
                case TextDisplayType.FloatingText:
                    // Floating text doesn't have toggle behavior, just show
                    ShowFloatingTextInternal(config);
                    break;
            }
        }

        public void HideTextDisplay(Transform targetTransform, Action hideCallback = null)
        {
            HideSpeechBubble(targetTransform, hideCallback);
        }

        public void HideAllTextDisplays()
        {
            HideAllSpeechBubbles();
        }

        // Backward compatibility methods
        public RewardInfoController GetSpeechBubble(Transform targetTransform)
        {
            return _shownBubbles.GetValueOrDefault(targetTransform);
        }

        private RewardInfoController GetOrCreateSpeechBubble(Transform targetTransform)
        {
            HideAllSpeechBubblesExcept(targetTransform);

            if (!_shownBubbles.TryGetValue(targetTransform, out var speechBubble))
            {
                speechBubble = _speechBubblePool.Spawn<RewardInfoController>();
                _shownBubbles[targetTransform] = speechBubble;
            }

            return speechBubble;
        }

        public void ToggleSpeechBubble(SpeechBubbleConfig speechBubbleConfig, IDictionary<string, int> reward = null,
            string rewardText = null, params object[] textArgs)
        {
            var config = (TextDisplayConfig)speechBubbleConfig;
            config.TextId = rewardText ?? "";
            config.TextArgs = textArgs;
            config.HasRewards = reward != null && reward.Count > 0;
            ToggleTextDisplay(config, reward);
        }

        public void ShowSpeechBubble(SpeechBubbleConfig speechBubbleConfig, IDictionary<string, int> reward = null,
            string rewardText = null, params object[] textArgs)
        {
            var config = (TextDisplayConfig)speechBubbleConfig;
            config.TextId = rewardText ?? "";
            config.TextArgs = textArgs;
            config.HasRewards = reward != null && reward.Count > 0;
            ShowTextDisplay(config, reward);
        }

        private void ToggleSpeechBubbleInternal(TextDisplayConfig config, IDictionary<string, int> rewards)
        {
            if (config.TargetTransform == null) return;

            if (_shownBubbles.ContainsKey(config.TargetTransform))
            {
                HideSpeechBubble(config.TargetTransform);
            }
            else
            {
                ShowSpeechBubbleInternal(config, rewards);
            }
        }

        private void ShowSpeechBubbleInternal(TextDisplayConfig config, IDictionary<string, int> rewards)
        {
            var targetTransform = config.TargetTransform;
            if (targetTransform == null) return;

            var speechBubble = GetOrCreateSpeechBubble(targetTransform);

            // Convert unified config back to old config for RewardInfoController
            var oldConfig = new SpeechBubbleConfig
            {
                TargetTransform = config.TargetTransform,
                Direction = config.Direction,
                AutoHide = config.AutoHide
            };

            speechBubble.ShowSpeechBubble(oldConfig, OnHideComplete, rewards, config.TextId, config.TextArgs);
        }

        public void HideSpeechBubble(Transform targetTransform, Action callback = null)
        {
            if (_shownBubbles.TryGetValue(targetTransform, out var speechBubble))
            {
                speechBubble.HideRewardInfo((_, _) =>
                {
                    OnHideComplete(speechBubble, targetTransform);
                    callback?.Invoke();
                });
            }
        }

        public void HideAllSpeechBubbles()
        {
            foreach (var speechBubble in _shownBubbles.Values)
            {
                speechBubble.HideRewardInfo(OnHideComplete);
            }
        }

        private void HideAllSpeechBubblesFast()
        {
            foreach (var speechBubble in _shownBubbles.Values)
            {
                speechBubble.HideRewardInfo(OnHideComplete, true);
            }
        }

        private void HideAllSpeechBubblesExcept(Transform targetTransform)
        {
            foreach (var (target, speechBubble) in _shownBubbles)
            {
                if (target != targetTransform)
                {
                    speechBubble.HideRewardInfo(OnHideComplete);
                }
            }
        }

        private void OnHideComplete(RewardInfoController speechBubble, Transform targetTransform)
        {
            _shownBubbles.Remove(targetTransform);
            _speechBubblePool.Release(speechBubble.gameObject);
        }

        // Floating text methods
        public FloatingTextEffectBase ShowFloatingText(string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingText(null, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingText(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingTextWithCurrency(string.Empty, true, target, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingTextNoBg(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingTextWithCurrency(string.Empty, false, target, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingTextWithCurrency(string currency, bool withBg, Transform target,
            string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            if (_floatingTextsPool == null || !_floatingTextsPool.ContainsKey((int)floatingTextType))
            {
                BDebug.LogWarning(LogCat.General, $"SpeechBubbleManager: No floating text pool found for type {floatingTextType}");
                return null;
            }

            if (target != null && _shownTextTargets.ContainsValue(target))
                return GetFloatingTextForTarget(target);

            if (AppDefinesConverter.UnityEditor)
            {
                UpdateRootCorners();
            }

            var floatingText = _floatingTextsPool[(int)floatingTextType].Spawn<DynamicFloatingTextEffect>();

            floatingText.gameObject.SetActive(true);
            _shownTextTargets[floatingText] = target;
            floatingText.FloatingTextType = floatingTextType;

            floatingText.InitScreenCorners(_leftBottomCorner, _rightTopCorner);
            floatingText.SetView(withBg, textId, args);
            floatingText.PlayFloatingAnimation(target, currency, ShowFloatingTextComplete);

            var targetCanvas = target != null ? target.GetComponentInParent<Canvas>() : null;
            if (targetCanvas != null)
            {
                floatingText.SetSortOrder(targetCanvas.sortingOrder + 1);
            }
            else
            {
                floatingText.ResetSortOrder();
            }

            return floatingText;
        }

        private void ShowFloatingTextComplete(FloatingTextEffectBase floatingText)
        {
            floatingText.gameObject.SetActive(false);
            if (_floatingTextsPool != null &&
                _floatingTextsPool.TryGetValue((int)floatingText.FloatingTextType, out var pool))
            {
                pool.Release(floatingText.gameObject);
            }
            _shownTextTargets.Remove(floatingText);
        }

        private FloatingTextEffectBase GetFloatingTextForTarget(Transform target)
        {
            foreach (var kvPair in _shownTextTargets)
            {
                if (kvPair.Value == target) return kvPair.Key;
            }
            return null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
            _speechBubblePool?.Cleanup();
            _shownBubbles.Clear();

            // Cleanup floating text pools
            if (_floatingTextsPool != null)
            {
                foreach (var pool in _floatingTextsPool.Values)
                {
                    pool?.Cleanup();
                }
                _floatingTextsPool = null;
            }
            _shownTextTargets.Clear();
        }
    }
}