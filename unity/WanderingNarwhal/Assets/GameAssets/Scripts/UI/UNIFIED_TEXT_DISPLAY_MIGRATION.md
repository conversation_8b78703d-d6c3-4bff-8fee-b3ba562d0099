# Unified Text Display Manager Migration Guide

## Overview

The `SpeechBubbleManager` and `DynamicFloatingTextManager` have been combined into a single unified manager to improve code maintainability, reduce duplication, and provide a more cohesive text display system.

## What Changed

### Combined Functionality
- `SpeechBubbleManager` now includes all floating text functionality from `DynamicFloatingTextManager`
- The `ISpeechBubbleManager` interface has been extended to include floating text methods
- Object pooling is now shared and more efficient

### Backward Compatibility
- `DynamicFloatingTextManager` is marked as deprecated but still functional
- `FloatingTextManagerAdapter` provides compatibility for existing `IFloatingTextManager` usage
- All existing code should continue to work without immediate changes

## Migration Path

### For New Code
Use `ISpeechBubbleManager` for both speech bubbles and floating text:

```csharp
// Inject the unified manager
private ISpeechBubbleManager _textDisplayManager;

// Show speech bubble (existing functionality)
_textDisplayManager.ShowSpeechBubble(speechBubbleConfig, rewards, "REWARD_TEXT");

// Show floating text (new functionality)
_textDisplayManager.ShowFloatingText(targetTransform, "FLOATING_TEXT");
_textDisplayManager.ShowFloatingTextWithCurrency("coins", true, targetTransform, "CURRENCY_TEXT");
```

### For Existing Code Using IFloatingTextManager
1. **Immediate**: No changes required - compatibility layer handles delegation
2. **Recommended**: Gradually migrate to use `ISpeechBubbleManager` directly
3. **Future**: Remove `IFloatingTextManager` dependencies when convenient

### Configuration Changes
The `SpeechBubbleManager` now requires additional serialized fields for floating text:
- `_rootRectTransform`: For screen boundary calculations
- `_floatingTextPrefabs`: List of floating text prefab configurations
- `_floatingTextDefaultAnchor`: Default parent for floating text

## Benefits

1. **Unified Management**: Single point of control for all text display
2. **Shared Resources**: More efficient object pooling and resource usage
3. **Consistent Behavior**: Screen/modal change handling applies to both systems
4. **Reduced Complexity**: Fewer managers to configure and maintain
5. **Better Performance**: Shared initialization and cleanup logic

## Technical Details

### New Fields in SpeechBubbleManager
```csharp
[SerializeField] private RectTransform _rootRectTransform;
[SerializeField] private List<FloatingTextPrefab> _floatingTextPrefabs;
[SerializeField] private Transform _floatingTextDefaultAnchor;
```

### Extended Interface Methods
```csharp
FloatingTextEffectBase ShowFloatingText(string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);
FloatingTextEffectBase ShowFloatingText(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);
FloatingTextEffectBase ShowFloatingTextNoBg(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);
FloatingTextEffectBase ShowFloatingTextWithCurrency(string currency, bool withBg, Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args);
```

## Testing Recommendations

1. Test existing speech bubble functionality to ensure no regressions
2. Test existing floating text functionality through the compatibility layer
3. Test new unified functionality in development scenarios
4. Verify proper cleanup and pooling behavior
5. Check performance impact of the unified system

## Future Cleanup

Once all code has been migrated:
1. Remove `DynamicFloatingTextManager` class
2. Remove `FloatingTextManagerAdapter` class
3. Remove `IFloatingTextManager` interface (if no longer needed elsewhere)
4. Update dependency injection configurations

## Notes

- The unified manager maintains all existing functionality
- Screen and modal change events now affect both speech bubbles and floating text
- Object pooling is more efficient with shared initialization logic
- Error handling and logging have been improved
