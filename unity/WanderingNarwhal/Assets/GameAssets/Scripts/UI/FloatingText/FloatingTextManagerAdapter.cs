using System;
using BBB.Core;
using BBB.DI;
using GameAssets.Scripts.UI.SpeechBubble;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Adapter class to maintain backward compatibility for IFloatingTextManager
    /// while delegating to the combined SpeechBubbleManager
    /// </summary>
    public class FloatingTextManagerAdapter : BbbMonoBehaviour, IFloatingTextManager
    {
        private ISpeechBubbleManager _speechBubbleManager;

        public void Init(IContext context)
        {
            _speechBubbleManager = context.Resolve<ISpeechBubbleManager>();
            if (_speechBubbleManager == null)
            {
                BDebug.LogError(LogCat.General, "FloatingTextManagerAdapter: ISpeechBubbleManager not found in context.");
            }
        }

        public FloatingTextEffectBase ShowFloatingText(string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return _speechBubbleManager?.ShowFloatingText(textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingText(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return _speechBubbleManager?.ShowFloatingText(target, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingTextNoBg(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return _speechBubbleManager?.ShowFloatingTextNoBg(target, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingTextWithCurrency(string currency, bool withBg, Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return _speechBubbleManager?.ShowFloatingTextWithCurrency(currency, withBg, target, textId, floatingTextType, args);
        }
    }
}
