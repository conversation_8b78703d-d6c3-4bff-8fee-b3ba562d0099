using System;
using System.Collections.Generic;
using BBB.DI;
using BebopBee;
using UnityEngine;

namespace BBB
{
    public enum FloatingTextType
    {
        Default,
        EpisodeSpeechBubble,
    }

    /// <summary>
    /// DEPRECATED: This class has been completely replaced by the unified SpeechBubbleManager with TextDisplayConfig.
    ///
    /// MIGRATION GUIDE:
    /// - Replace IFloatingTextManager with ISpeechBubbleManager
    /// - Use TextDisplayConfig with DisplayType = TextDisplayType.FloatingText
    /// - Call ShowTextDisplay(config) instead of individual floating text methods
    ///
    /// This class now acts as a simple adapter to the unified system and will be removed in future versions.
    /// </summary>
    [System.Obsolete("DynamicFloatingTextManager is deprecated. Use SpeechBubbleManager with TextDisplayConfig for unified text display management.", true)]
    public class DynamicFloatingTextManager : BbbMonoBehaviour, IFloatingTextManager
    {
        [Serializable]
        public struct FloatingTextPrefab
        {
            public FloatingTextType Type;
            public GameObject Prefab;
        }

        [SerializeField] private RectTransform _rootRectTransform;
        [SerializeField] private List<FloatingTextPrefab> _floatingTextPrefabs;
        [SerializeField] private Transform _floatingTextDefaultAnchor;
        [SerializeField] private int _initialPoolSize;

        private Dictionary<int, GoPool> _floatingTextsPool;
        private CurrencyIconsLoader _currencyIconsLoader;
        private Vector3 _leftBottomCorner;
        private Vector3 _rightTopCorner;

        private readonly Dictionary<FloatingTextEffectBase, Transform> _shownTextTargets = new();

        private void Start()
        {
            //calling from Start to be sure InverseCanvasScaler's OnEnable was called
            UpdateRootCorners();
        }

        private void UpdateRootCorners()
        {
            var rootCorners = new Vector3[4];
            _rootRectTransform.GetWorldCorners(rootCorners);

            _leftBottomCorner = rootCorners[0];
            _rightTopCorner = rootCorners[2];
        }

        public void Init(IContext context)
        {
            if (_floatingTextsPool != null) return;
            
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
            foreach (var floatingTextPrefab in _floatingTextPrefabs)
            {
                _floatingTextsPool ??= new Dictionary<int, GoPool>();
                
                var prefab = floatingTextPrefab.Prefab;
                if (prefab == null) continue;

                var pool = new GoPool(prefab, _floatingTextDefaultAnchor, _initialPoolSize, go =>
                {
                    go.GetComponent<DynamicFloatingTextEffect>().ProvideCurrencyIconLoader(_currencyIconsLoader);
                    go.SetActive(false);
                });

                _floatingTextsPool[(int)floatingTextPrefab.Type] = pool;
            }
        }

        public FloatingTextEffectBase ShowFloatingText(string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingText(null, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingText(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingTextWithCurrency(string.Empty, true, target, textId, floatingTextType, args);
        }

        public FloatingTextEffectBase ShowFloatingTextNoBg(Transform target, string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            return ShowFloatingTextWithCurrency(string.Empty, false, target, textId, floatingTextType, args);
        }
       
        public FloatingTextEffectBase ShowFloatingTextWithCurrency(string currency, bool withBg, Transform target,
            string textId, FloatingTextType floatingTextType = FloatingTextType.Default, params object[] args)
        {
            if (target != null && _shownTextTargets.ContainsValue(target)) return GetFloatingTextForTarget(target);

            if (AppDefinesConverter.UnityEditor)
            {
                UpdateRootCorners();
            }
            
            var floatingText = _floatingTextsPool[(int)floatingTextType].Spawn<DynamicFloatingTextEffect>();

            floatingText.gameObject.SetActive(true);
            _shownTextTargets[floatingText] = target;
            floatingText.FloatingTextType = floatingTextType;

            floatingText.InitScreenCorners(_leftBottomCorner, _rightTopCorner);
            floatingText.SetView(withBg, textId, args);
            floatingText.PlayFloatingAnimation(target, currency, ShowFloatingTextComplete);

            var targetCanvas = target != null ? target.GetComponentInParent<Canvas>() : null;
            if (targetCanvas != null)
            {
                floatingText.SetSortOrder(targetCanvas.sortingOrder + 1);
            }
            else
            {
                floatingText.ResetSortOrder();
            }

            return floatingText;
        }

        private void ShowFloatingTextComplete(FloatingTextEffectBase floatingText)
        {
            floatingText.gameObject.SetActive(false);
            if (_floatingTextsPool != null &&
                _floatingTextsPool.TryGetValue((int)floatingText.FloatingTextType, out var pool))
            {
                pool.Release(floatingText.gameObject);
            }
            _shownTextTargets.Remove(floatingText);
        }

        private FloatingTextEffectBase GetFloatingTextForTarget(Transform target)
        {
            foreach (var kvPair in _shownTextTargets)
            {
                if (kvPair.Value == target) return kvPair.Key;
            }
            return null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_floatingTextsPool != null)
            {
                foreach (var pool in _floatingTextsPool.Values)
                {
                    pool?.Cleanup();
                }
                _floatingTextsPool = null;
            }
            _shownTextTargets.Clear();
        }
    }
}