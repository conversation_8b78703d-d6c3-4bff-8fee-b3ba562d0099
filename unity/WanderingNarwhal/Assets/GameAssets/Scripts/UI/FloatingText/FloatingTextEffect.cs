using UnityEngine;

namespace BBB
{
    public class FloatingTextEffect : FloatingTextEffectBase
    {
        [Tooltip("Root for text, which is used when no image showed.")] [SerializeField]
        private Transform _textAnchorImageless;

        [Tooltip("Root for text, which is used when currency image is showed.")] [SerializeField]
        private Transform _textAnchorWithCurrencyImage;

        protected override void BeforeRootVisible(bool withCurrency)
        {
            _floatingText.transform.SetParent(withCurrency ? _textAnchorWithCurrencyImage : _textAnchorImageless);
            FloatingTextRectTransform.anchoredPosition = Vector2.zero;
            FloatingTextRectTransform.sizeDelta = Vector2.zero;
        }

        protected override void AfterRootVisible(bool withCurrency)
        {
        }
    }
}