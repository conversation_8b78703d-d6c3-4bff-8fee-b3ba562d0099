using System.Collections.Generic;
using BBB;
using BBB.DI;
using GameAssets.Scripts.UI.SpeechBubble;
using UnityEngine;

namespace GameAssets.Scripts.UI.Tests
{
    /// <summary>
    /// Test script to verify the unified text display functionality
    /// This can be attached to a GameObject in a test scene to verify the integration
    /// </summary>
    public class UnifiedTextDisplayTest : MonoBehaviour
    {
        [SerializeField] private Transform _testTarget;
        [SerializeField] private bool _testSpeechBubbles = true;
        [SerializeField] private bool _testFloatingText = true;
        
        private ISpeechBubbleManager _textDisplayManager;
        private IFloatingTextManager _floatingTextManager; // For compatibility testing

        private void Start()
        {
            // This would normally be injected via DI
            _textDisplayManager = FindObjectOfType<SpeechBubbleManager>();
            _floatingTextManager = FindObjectOfType<FloatingTextManagerAdapter>();
            
            if (_textDisplayManager == null)
            {
                Debug.LogError("UnifiedTextDisplayTest: No SpeechBubbleManager found in scene");
                return;
            }

            if (_testTarget == null)
            {
                _testTarget = transform;
            }
        }

        [ContextMenu("Test Speech Bubble")]
        public void TestSpeechBubble()
        {
            if (_textDisplayManager == null || !_testSpeechBubbles) return;

            var config = new SpeechBubbleConfig
            {
                TargetTransform = _testTarget,
                Direction = Direction.Top,
                AutoHide = true
            };

            var rewards = new Dictionary<string, int>
            {
                { "coins", 100 },
                { "gems", 5 }
            };

            _textDisplayManager.ShowSpeechBubble(config, rewards, "TEST_REWARD_TEXT");
            Debug.Log("Speech bubble test executed");
        }

        [ContextMenu("Test Floating Text")]
        public void TestFloatingText()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            _textDisplayManager.ShowFloatingText(_testTarget, "TEST_FLOATING_TEXT");
            Debug.Log("Floating text test executed");
        }

        [ContextMenu("Test Floating Text with Currency")]
        public void TestFloatingTextWithCurrency()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            _textDisplayManager.ShowFloatingTextWithCurrency("coins", true, _testTarget, "TEST_CURRENCY_TEXT", FloatingTextType.Default, 50);
            Debug.Log("Floating text with currency test executed");
        }

        [ContextMenu("Test Compatibility Layer")]
        public void TestCompatibilityLayer()
        {
            if (_floatingTextManager == null) return;

            _floatingTextManager.ShowFloatingText(_testTarget, "COMPATIBILITY_TEST");
            Debug.Log("Compatibility layer test executed");
        }

        [ContextMenu("Test Hide All Speech Bubbles")]
        public void TestHideAllSpeechBubbles()
        {
            if (_textDisplayManager == null) return;

            _textDisplayManager.HideAllSpeechBubbles();
            Debug.Log("Hide all speech bubbles test executed");
        }

        private void Update()
        {
            // Test keyboard shortcuts for quick testing
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                TestSpeechBubble();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                TestFloatingText();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                TestFloatingTextWithCurrency();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                TestCompatibilityLayer();
            }
            else if (Input.GetKeyDown(KeyCode.Escape))
            {
                TestHideAllSpeechBubbles();
            }
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("Unified Text Display Test");
            GUILayout.Label("1 - Test Speech Bubble");
            GUILayout.Label("2 - Test Floating Text");
            GUILayout.Label("3 - Test Floating Text with Currency");
            GUILayout.Label("4 - Test Compatibility Layer");
            GUILayout.Label("ESC - Hide All Speech Bubbles");
            GUILayout.EndArea();
        }
    }
}
