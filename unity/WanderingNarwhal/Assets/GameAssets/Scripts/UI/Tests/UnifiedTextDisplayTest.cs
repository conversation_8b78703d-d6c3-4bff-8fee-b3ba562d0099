using System.Collections.Generic;
using BBB;
using BBB.DI;
using GameAssets.Scripts.UI.SpeechBubble;
using UnityEngine;

namespace GameAssets.Scripts.UI.Tests
{
    /// <summary>
    /// Test script to verify the unified text display functionality
    /// This can be attached to a GameObject in a test scene to verify the integration
    /// </summary>
    public class UnifiedTextDisplayTest : MonoBehaviour
    {
        [SerializeField] private Transform _testTarget;
        [SerializeField] private bool _testSpeechBubbles = true;
        [SerializeField] private bool _testFloatingText = true;
        
        private ISpeechBubbleManager _textDisplayManager;
        private IFloatingTextManager _floatingTextManager; // For compatibility testing

        private void Start()
        {
            // This would normally be injected via DI
            _textDisplayManager = FindObjectOfType<SpeechBubbleManager>();
            _floatingTextManager = FindObjectOfType<FloatingTextManagerAdapter>();
            
            if (_textDisplayManager == null)
            {
                Debug.LogError("UnifiedTextDisplayTest: No SpeechBubbleManager found in scene");
                return;
            }

            if (_testTarget == null)
            {
                _testTarget = transform;
            }
        }

        [ContextMenu("Test Speech Bubble (New Unified)")]
        public void TestSpeechBubbleUnified()
        {
            if (_textDisplayManager == null || !_testSpeechBubbles) return;

            var config = new TextDisplayConfig
            {
                TargetTransform = _testTarget,
                DisplayType = TextDisplayType.SpeechBubble,
                Direction = Direction.Top,
                AutoHide = true,
                TextId = "TEST_REWARD_TEXT",
                HasRewards = true
            };

            var rewards = new Dictionary<string, int>
            {
                { "coins", 100 },
                { "gems", 5 }
            };

            _textDisplayManager.ShowTextDisplay(config, rewards);
            Debug.Log("Unified speech bubble test executed");
        }

        [ContextMenu("Test Floating Text (New Unified)")]
        public void TestFloatingTextUnified()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            var config = new TextDisplayConfig
            {
                TargetTransform = _testTarget,
                DisplayType = TextDisplayType.FloatingText,
                FloatingTextType = FloatingTextType.Default,
                TextId = "TEST_FLOATING_TEXT",
                WithBackground = true
            };

            _textDisplayManager.ShowTextDisplay(config);
            Debug.Log("Unified floating text test executed");
        }

        [ContextMenu("Test Floating Text with Currency (New Unified)")]
        public void TestFloatingTextWithCurrencyUnified()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            var config = new TextDisplayConfig
            {
                TargetTransform = _testTarget,
                DisplayType = TextDisplayType.FloatingText,
                FloatingTextType = FloatingTextType.Default,
                TextId = "TEST_CURRENCY_TEXT",
                TextArgs = new object[] { 50 },
                WithBackground = true,
                Currency = "coins"
            };

            _textDisplayManager.ShowTextDisplay(config);
            Debug.Log("Unified floating text with currency test executed");
        }

        [ContextMenu("Test Speech Bubble (Legacy)")]
        public void TestSpeechBubble()
        {
            if (_textDisplayManager == null || !_testSpeechBubbles) return;

            var config = new SpeechBubbleConfig
            {
                TargetTransform = _testTarget,
                Direction = Direction.Top,
                AutoHide = true
            };

            var rewards = new Dictionary<string, int>
            {
                { "coins", 100 },
                { "gems", 5 }
            };

            _textDisplayManager.ShowSpeechBubble(config, rewards, "TEST_REWARD_TEXT");
            Debug.Log("Legacy speech bubble test executed");
        }

        [ContextMenu("Test Floating Text (Legacy)")]
        public void TestFloatingText()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            _textDisplayManager.ShowFloatingText(_testTarget, "TEST_FLOATING_TEXT");
            Debug.Log("Legacy floating text test executed");
        }

        [ContextMenu("Test Floating Text with Currency (Legacy)")]
        public void TestFloatingTextWithCurrency()
        {
            if (_textDisplayManager == null || !_testFloatingText) return;

            _textDisplayManager.ShowFloatingTextWithCurrency("coins", true, _testTarget, "TEST_CURRENCY_TEXT", FloatingTextType.Default, 50);
            Debug.Log("Legacy floating text with currency test executed");
        }

        [ContextMenu("Test Compatibility Layer")]
        public void TestCompatibilityLayer()
        {
            if (_floatingTextManager == null) return;

            _floatingTextManager.ShowFloatingText(_testTarget, "COMPATIBILITY_TEST");
            Debug.Log("Compatibility layer test executed");
        }

        [ContextMenu("Test Hide All Text Displays")]
        public void TestHideAllTextDisplays()
        {
            if (_textDisplayManager == null) return;

            _textDisplayManager.HideAllTextDisplays();
            Debug.Log("Hide all text displays test executed");
        }

        private void Update()
        {
            // Test keyboard shortcuts for quick testing - Unified API
            if (Input.GetKeyDown(KeyCode.Q))
            {
                TestSpeechBubbleUnified();
            }
            else if (Input.GetKeyDown(KeyCode.W))
            {
                TestFloatingTextUnified();
            }
            else if (Input.GetKeyDown(KeyCode.E))
            {
                TestFloatingTextWithCurrencyUnified();
            }
            // Legacy API tests
            else if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                TestSpeechBubble();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                TestFloatingText();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                TestFloatingTextWithCurrency();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                TestCompatibilityLayer();
            }
            else if (Input.GetKeyDown(KeyCode.Escape))
            {
                TestHideAllTextDisplays();
            }
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("Unified Text Display Test");
            GUILayout.Space(10);

            GUILayout.Label("=== NEW UNIFIED API ===");
            GUILayout.Label("Q - Test Speech Bubble (Unified)");
            GUILayout.Label("W - Test Floating Text (Unified)");
            GUILayout.Label("E - Test Floating Text with Currency (Unified)");
            GUILayout.Space(10);

            GUILayout.Label("=== LEGACY API (Backward Compatibility) ===");
            GUILayout.Label("1 - Test Speech Bubble (Legacy)");
            GUILayout.Label("2 - Test Floating Text (Legacy)");
            GUILayout.Label("3 - Test Floating Text with Currency (Legacy)");
            GUILayout.Label("4 - Test Compatibility Layer");
            GUILayout.Space(10);

            GUILayout.Label("ESC - Hide All Text Displays");
            GUILayout.EndArea();
        }
    }
}
