using BBB;
using UnityEngine;

namespace CustomLayout
{
    public class CustomLayoutElement : BbbMonoBehaviour
    {
        private RectTransform _cachedTransform;

        public RectTransform SelfTransform
        {
            get
            {
                if (_cachedTransform == null)
                {
                    _cachedTransform = GetComponent<RectTransform>();
                }

                return _cachedTransform;
            }
        }

        public void Setup()
        {
            SelfTransform.anchorMin = new Vector2(0f,1f);
            SelfTransform.anchorMax = new Vector2(0f,1f);
        }

        public void SetPivot(Vector2 pivot)
        {
            SelfTransform.pivot = pivot;
        }

        public void SetHeight(float height)
        {
            var sizeDelta = SelfTransform.sizeDelta;
            sizeDelta.y = height;
            SelfTransform.sizeDelta = sizeDelta;
        }

        public void SetWidth(float width)
        {
            var sizeDelta = SelfTransform.sizeDelta;
            sizeDelta.x = width;
            SelfTransform.sizeDelta = sizeDelta;
        }
    }
}