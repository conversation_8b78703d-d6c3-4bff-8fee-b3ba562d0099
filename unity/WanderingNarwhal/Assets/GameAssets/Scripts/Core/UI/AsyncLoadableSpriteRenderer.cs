using System;
using BBB.DI;
using BBB.UI.Core;
using UnityEngine;

namespace BBB
{
    public class AsyncLoadableSpriteRenderer : ContextedUiBehaviour
    {
        private SpriteRenderer _sprite;
        private GenericResourceProvider _provider;
        private string _lastSpriteLoaded;

        protected override void InitWithContextInternal(IContext context)
        {
            _sprite = GetComponent<SpriteRenderer>();
            _provider = context.Resolve<GenericResourceProvider>();
        }

        public void Show(string spriteName)
        {
            LazyInit();

            if (spriteName == _lastSpriteLoaded)
                return;

            _lastSpriteLoaded = spriteName;

            _sprite.enabled = false;
            _provider.CacheAndLoadAsync<Sprite>(this, spriteName)
                .Then((Action<Sprite>) OnSpriteLoaded)
                .Catch(Debug.LogError).Done();
        }

        private void OnSpriteLoaded(Sprite spriteLoaded)
        {
            if (_sprite != null)
            {
                _sprite.sprite =spriteLoaded;
                _sprite.enabled = true;
            }
        }

        protected override void OnDestroy()
        {
            if(_lastSpriteLoaded != null)
                _provider.ReleaseCached(_lastSpriteLoaded);
        }
    }
}