using System;

namespace BBB
{
    [Flags]
    public enum ScreenType
    {
        None = 0,
        LoadingScreen = 1 << 0,
        // one free place to add new a screen here
        HelpingHands = 1 << 5,
        LevelScreen = 1 << 6,
        DebugScreen = 1 << 7,
        // one free place to add new a screen here
        SideMapLevelScreen = 1 << 13,
        SideMapScreen = 1 << 14,
        EpisodeScreen = 1 << 20,

        SideMap = SideMapLevelScreen | SideMapScreen,
        Map = SideMapScreen | EpisodeScreen,
        Levels = LevelScreen | SideMapLevelScreen | HelpingHands,
        FullHudScreen = SideMapScreen | EpisodeScreen,
        All = ~None // Keep this one at the end
    }

    public class ScreenTypeConstants
    {
        public static readonly ScreenType PrimaryMap = ScreenType.EpisodeScreen;
    }

    public enum ModalsType
    {
        None,
        LevelStart,
        LevelSuccess,
        LevelSuccessVerticalLB,
        LevelSuccessNoLB,
        LevelOutOfMoves,
        MapNoMoreLives,
        Generic,
        Store,
        GachaClaim,
        SettingsModal,
        Social,
        GenericModal,
        NoConnectionModal,
        DebugModal,
        DialogModal,
        OutOfMovesGacha,
        StoreGacha,
        CurrenciesRewardModal,
        WatchVideoAdModal,
        OutOfMovesBoosterOfferModal,
        StartLevelAdReward,
        AdLootbox,
        BuddyGiftCard,
        GameEvent,
        GameEventVictory,
        GameEventLoseLevelWarning,
        Passport,
        CompetitionEventGeneric,
        CompetitionEventInfo,
        EventLeaderboard,
        DailyTrivia,
        LeagueRewards,
        CompetitionEventRules,
        CompetitionPromotionDemotion,
        GenericPromo,
        WeeklyEventCongrats,
        Basket,
        SdbIntro,
        SdbInfo,
        ChallengeTriviaIntro,
        ChallengeTriviaInfo,
        SdbReward,
        VipProductsModal,
        DailyCollectInfo,
        CityLoading,
        ChangeName,
        EndlessTreasure,
        IapPurchase,
        RaceEventInfo,
        RaceEventCompetition,
        SaveProgress,
        ClaimGift,
        FoodEventCompetition,
        FoodEventInfo,
        DiscoEventCompetition,
        DiscoEventInfo,
        RoundStart,
        SideMapLeaderboard,
        LeaderboardModal,
        QrCodeReaderModal,
        TrackPermissionModal,
        EventIntroModal,
        EventCompleteLocationModal,
        EventCompleteModal,
        RoyaleEventIntroModal,
        RoyaleEventInfoModal,
        RoyaleEventStartModal,
        RoyaleEventMainModal,
        RoyaleEventWinModal,
        ButlerGiftInfoModal,
        CollectionIntroModal,
        CardsRewardModal,
        CollectionMainModal,
        CollectionInfoModal,
        TeamCoopEventIntroModal,
        TeamCoopEventMainModal,
        TeamVsTeamEventIntroModal,
        TeamVsTeamEventMainModal,
        CollectionContentModal,
        WildCardTokenModal,
        WildCardModal,
        WildCardRewardModal,
        WildCardUseModal,
        CollectionCompleteModal,
        WildCardTransformModal,
        NotEnoughStarsModal,
        TripstagramModal,
        TripstagramSnapshotModal,
        SnapshotTaskModal,
        SceneCompletionModal,
        AssetsLoadingModal,
        DeepLinkValidation,
        ComingSoon,
        EndOfContentInfo,
        EndOfContentIntroModal,
        EndOfContentLeaderboardModal,
        DailyTasksIntroModal,
        DailyTasksModal,
        ChallengeModal,
        GlobeModal,
        ChallengeTrivia,
        TapToSkipModal,
        SweepstakesEventModal,
        SweepstakesMilestoneModal,
        SweepstakesMilestoneRewardModal,
        DailyLoginModal,
        ConfirmationModal,
        SweepstakesEmailModal,
        SweepstakesSubmitModal,
    }
}