using System;
using UnityEngine;

namespace BBB
{
    public static class FunctionExtensions
    {
        public static void SafeInvoke(this Action callback)
        {
            callback?.Invoke();
        }
        
        public static void SafeRebind(this Animator animator)
        {
            if (animator != null && animator.isActiveAndEnabled)
            {
                animator.Rebind();
            }
        }
        public static void SafeResetTrigger(this Animator animator, int trigger)
        {
            if (animator != null && animator.isActiveAndEnabled)
            {
                animator.ResetTrigger(trigger);
            }
        }

        public static void SafeSetTrigger(this Animator animator, int trigger)
        {
            if (animator != null && animator.isActiveAndEnabled)
            {
                animator.SetTrigger(trigger);
            }
        }
    }
}