using System;
using System.Collections.Generic;

namespace BBB
{
    public interface IPoolAble
    {
        IPoolManager Manager { get; set; }
        void Reset();
        void Free();
    }

    public interface IPoolManager
    {
        void Release(IPoolAble obj);
    }

    public class PoolManager<T> : IPoolManager
        where T : IPoolAble, new()
    {
        private readonly int _max;
        protected readonly List<T> FreeObjects;
        protected readonly int InitialCapacity;

        public int Count => FreeObjects.Count;

        public int Peak { get; private set; }

        public string Name { get; set; }

        public PoolManager(int initialCapacity = 5, int max = int.MaxValue)
        {
            FreeObjects = new List<T>(initialCapacity);
            _max = max;
            InitialCapacity = initialCapacity;
        }

        public virtual void Prewarm()
        {
            for (var i = 0; i < InitialCapacity; i++)
            {
                var item = Obtain();
                FreeObjects.Add(item);
            }
        }

        public T Obtain()
        {
            T ret;
            if (FreeObjects.Count == 0)
            {
                ret = new T();
                Peak++;
                //uncomment to check allocations
                //UnityEngine.Debug.LogWarning($"Peak for {Name} increased to {Peak}");
            }
            else
            {
                ret = FreeObjects[^1];
                FreeObjects.RemoveAt(FreeObjects.Count - 1);
            }

            ret.Manager = this;
            return ret;
        }

        public virtual void Release(IPoolAble obj)
        {
            if (obj == null) throw new ArgumentNullException(nameof(obj), "obj cannot be null");
            if (FreeObjects.Count < _max)
            {
                FreeObjects.Add((T) obj);
            }

            obj.Reset();
        }
    }
}