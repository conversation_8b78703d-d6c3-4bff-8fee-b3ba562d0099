namespace BBB.DI
{
    public interface IContext
    {
        IContextReleaser Releaser { get; }

        T Resolve<T>(string tag = null, bool allowNull = false) where T : class;

        T Resolve<T, TArg1>(TArg1 arg1, string tag = null) where T : class
            where TArg1 : class;

        T Resolve<T, TArg1, TArg2>(TArg1 arg1, TArg2 arg2, string tag = null) where T : class
            where TArg1 : class
            where TArg2 : class;

        T Resolve<T, TArg1, TArg2, TArg3>(TArg1 arg1, TArg2 arg2, TArg3 arg3, string tag = null) where T : class
            where TArg1 : class
            where TArg2 : class
            where TArg3 : class;
    }
}