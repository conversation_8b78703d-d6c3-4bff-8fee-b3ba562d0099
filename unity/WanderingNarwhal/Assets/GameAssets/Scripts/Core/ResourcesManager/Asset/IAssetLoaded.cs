using System;
using Object = UnityEngine.Object;

namespace BBB.Core.ResourcesManager.Asset
{
    public interface IAssetReleaseable : IDisposable
    {
        int GetReferenceCounter();
        void Retain();
        bool CanFree();
        void Free();
        Type GetAssetType();
        IAssetLoaded<T1> ConvertAsset<T1>() where T1 : Object;
    }
    public interface IAssetLoaded<out T> : IAssetReleaseable  where T : Object
    {
        string Name { get; }
        T Get();

#if BBB_DEBUG
        string AssetBundleName { get; }
#endif

    }
}