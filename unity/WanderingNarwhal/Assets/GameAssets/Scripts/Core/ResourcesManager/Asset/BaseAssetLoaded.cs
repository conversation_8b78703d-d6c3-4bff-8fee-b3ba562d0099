using System;
using Object = UnityEngine.Object;

namespace BBB.Core.ResourcesManager.Asset
{
    public abstract class BaseAssetLoaded<T> : IAssetLoaded<T> where T : Object
    {
        protected T Asset;
        protected int ReferenceCounter;
        public string Name { get; }

#if BBB_DEBUG
        public string AssetBundleName { get; set; }
#endif

        protected BaseAssetLoaded(string name, T asset)
        {
            Name = name;
            Asset = asset;
            if (asset == null)
            {
                BDebug.LogError(LogCat.AssetBundle, "[Base] Failed to load " + name);
            }

            ReferenceCounter = 0;
        }

        public T Get()
        {
            return Asset;
        }

        public int GetReferenceCounter()
        {
            return ReferenceCounter;
        }

        public virtual void Retain()
        {
            ReferenceCounter++;
            BDebug.Log(LogCat.Resources, $"Retain {Name} count:{ReferenceCounter}");
        }

        public bool CanFree()
        {
            return ReferenceCounter <= 0;
        }

        public void Free()
        {
            Asset = null;
        }

        public Type GetAssetType()
        {
            return Asset.GetType();
        }

        public abstract IAssetLoaded<T1> ConvertAsset<T1>() where T1 : Object;

        public bool IsLoaded()
        {
            return Asset != null;
        }

        public virtual void Dispose()
        {
            ReferenceCounter--;
            BDebug.Log(LogCat.Resources, $"Release {Name} count:{ReferenceCounter}");
        }
    }
}