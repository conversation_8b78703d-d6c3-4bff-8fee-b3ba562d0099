using System;
using System.Collections.Generic;
using System.Diagnostics;
using BBB.Core.AssetBundles;
using BBB.Core.Crash;
using BBB.Core.ResourcesManager.Asset;
using BBB.Core.ResourcesManager.Loaders;
using Bebopbee.Core.Systems.Ticksystem;
using BebopBee;
using Core.Debug;
using ProtoBuf;
using RSG;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.U2D;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

namespace BBB.Core.ResourcesManager
{
    public class AssetLoadTimeRecord
    {
        [ProtoMember(1)] public string assetName { get; set; }
        [ProtoMember(2)] public long time { get; set; }
        [ProtoMember(3)] public float assetStartLoadingTime { get; set; }
        [ProtoMember(5)] public float assetEndLoadingTime { get; set; }
        [ProtoMember(6)] public long size { get; set; }
        [ProtoMember(7)] public string bundle { get; set; }
    }

    public enum LoadMode
    {
        Speed,
        Smooth,
    }
    
    public class AssetsManager : IAssetsManager, ITickable
    {
#if BBB_DEBUG
        public static readonly List<AssetLoadTimeRecord> AssetLoadTimeDict = new ();
#endif

        private static List<IAssetLoader> _assetTypeLoaders;
        private static readonly Dictionary<string, object> PromiseByAssetPath = new ();
        private static readonly Dictionary<string, object> AssetLoadedCache = new ();
        public static readonly LoadMode LoadMode = LoadMode.Smooth;

#if BBB_DEBUG
        private readonly Dictionary<string, LogStopwatch> _assetLoadWatchDict = new ();
#endif
        private readonly Queue<IAssetElement> _assetsInQueue = new ();
        private readonly List<string> _assetsToRelease = new ();

        private IBundleManager _bundleManager;


        public AssetsManager()
        {
        }

        public void Init(IBundleManager bundleManager, IBundleInfoProvider bundleInfoProvider, ISpriteAtlasInfoProvider spriteAtlasInfoProvider)
        {
            _bundleManager = bundleManager;
            _assetTypeLoaders = new List<IAssetLoader>()
            {
                new SpriteAtlasLoader(bundleManager, bundleInfoProvider),
                new SpriteInAtlasLoader(spriteAtlasInfoProvider, this),
                new Match3LevelsLoader(bundleManager, bundleInfoProvider),
                new DefaultAssetLoader(bundleManager, bundleInfoProvider)
            };
        }

        public IPromise<IAssetLoaded<T>> LoadAsync<T>(string assetName, AssetLoadPriority priority = AssetLoadPriority.InQueue, bool forceEnqueue = false) where T : Object
        {
            if ((AppDefinesConverter.UnityEditor || LoadMode == LoadMode.Speed) && !forceEnqueue)
                priority = AssetLoadPriority.Immediately;

            try
            {
                BDebug.LogFormat(LogCat.Resources, "load asset {0} with {1}", assetName, priority);
                if (assetName.IsNullOrEmpty())
                {
                    BDebug.LogError(LogCat.Resources, "Trying to load asset with empty filename");
                    Profiler.BeginSample($"AssetBundle:LoadAsync[{assetName}] Resolve");
                    var ret = Promise<IAssetLoaded<T>>.Resolved(null);
                    Profiler.EndSample();
                    return ret;
                }

#if BBB_DEBUG
                if (!_assetLoadWatchDict.ContainsKey(assetName))
                {
                    var logStopWatch = new LogStopwatch();
                    logStopWatch.Start();
                    _assetLoadWatchDict.Add(assetName, logStopWatch);
                }
#endif

                if (PromiseByAssetPath.TryGetValue(assetName, out var value))
                {
                    return (IPromise<IAssetLoaded<T>>) value;
                }

                if (AssetLoadedCache.TryGetValue(assetName, out var assetLoaded))
                {
                    if (assetLoaded is IAssetLoaded<T> asset)
                    {
                        asset.Retain();
                        Profiler.BeginSample($"AssetBundle:LoadAsync[{assetName}] Resolve");
                        var ret = Promise<IAssetLoaded<T>>.Resolved(asset);
                        Profiler.EndSample();
                        return ret;
                    }

                    var assetReleasable = (IAssetReleaseable) assetLoaded;
                    assetReleasable.Retain();
                    var assetType = assetReleasable.GetAssetType();
                    var expectedType = typeof(T); 
                    if (assetType.IsAssignableFrom(expectedType))
                    {
                        var convertedAssetLoaded = assetReleasable.ConvertAsset<T>();
                        Profiler.BeginSample($"AssetBundle:LoadAsync[{assetName}] Resolve");
                        var ret= Promise<IAssetLoaded<T>>.Resolved(convertedAssetLoaded);
                        Profiler.EndSample();
                        return ret;
                    }

                    throw new InvalidCastException($"Can not cast from {assetType.Name} to {expectedType.Name} while getting {assetName}");
                }

                var promise = new Promise<IAssetLoaded<T>>();
                PromiseByAssetPath.Add(assetName, promise);

                switch (priority)
                {
                    case AssetLoadPriority.Immediately:
                    {
                        LoadAsset(assetName, promise);
                        break;
                    }
                    case AssetLoadPriority.InQueue:
                    {
                        var element = new AssetElement<T>(assetName, promise);
                        _assetsInQueue.Enqueue(element);
                        break;
                    }
                }

                return promise;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            Profiler.BeginSample($"AssetBundle:LoadAsync[{assetName}] Resolve");
            var ret2 = Promise<IAssetLoaded<T>>.Resolved(null);
            Profiler.EndSample();
            return ret2;
        }

        public void LoadAsset<T>(string assetName, Promise<IAssetLoaded<T>> promise) where T : Object
        {
            foreach (var loader in _assetTypeLoaders)
            {
                if (!loader.CanLoad<T>(assetName)) continue;
#if BBB_DEBUG
                var sw = new Stopwatch();
                sw.Start();
#endif
                loader.Load<T>(assetName).Then(loaded =>
                {
#if BBB_DEBUG
                    if (_assetLoadWatchDict.ContainsKey(assetName))
                    {
                        _assetLoadWatchDict[assetName].StopLog($"\tasset {assetName} load finished");
                        _assetLoadWatchDict.Remove(assetName);
                    }

                    Profiler.BeginSample($"AssetBundle:LoadAsync[{assetName}] Resolve");
                    try
                    {
#endif
                        if (loaded != null)
                        {
                            loaded.Retain();
                            AssetLoadedCache[assetName] = loaded;
                        }
                        else
                        {
                            BDebug.LogError(LogCat.Resources, $"LoadAsync: Loaded asset with name {assetName} is null.");
                        }
                        
                        promise.ReportProgress(1f);
                        promise.Resolve(loaded);
#if BBB_DEBUG
                    }
                    finally
                    {
                        sw.Stop();
                        Profiler.EndSample();
                        
                        var size = GetEstimatedAssetSize(loaded?.Get());
                        RecordAssetLoadTime(assetName, sw.ElapsedMilliseconds, size, bundle: loaded?.AssetBundleName);
                    }
                    
#endif
                    PromiseByAssetPath.Remove(assetName);
                }).Catch(exception =>
                {
                    promise.Reject(exception);
                    BDebug.LogError(LogCat.Resources, $"LoadAsync {assetName} load error\n{exception}");
                    PromiseByAssetPath.Remove(assetName);
                });
                break;
            }
        }

        static void RecordAssetLoadTime(string assetName, long loadTime, long size, string bundle)
        {
#if BBB_DEBUG
            const int maxCount = 200;
            AssetLoadTimeDict.Add(new AssetLoadTimeRecord()
            {
                assetName = assetName,
                time = loadTime,
                size = size,
                assetStartLoadingTime = Time.realtimeSinceStartup - loadTime / 1000f,
                assetEndLoadingTime = Time.realtimeSinceStartup,
                bundle = bundle ?? "null",
            });

            AssetLoadTimeDict.Sort((a, b) => -a.time.CompareTo(b.time));

            BDebug.LogFormat(LogCat.Resources, "load {0} needs {1}", assetName, loadTime);

            while (_assetTypeLoaders.Count > maxCount)
            {
                _assetTypeLoaders.RemoveAt(_assetTypeLoaders.Count - 1);
            }
#endif
        }

        #region load asset in queue

        private interface IAssetElement
        {
            void LoadAsset(AssetsManager assetsManager);
        }

        private class AssetElement<T> : IAssetElement
            where T : Object
        {
            private readonly string _loadingAssetName;
            private readonly Promise<IAssetLoaded<T>> _promise;

            public AssetElement(string loadingAssetName, Promise<IAssetLoaded<T>> promise)
            {
                _loadingAssetName = loadingAssetName;
                _promise = promise;
            }

            public void LoadAsset(AssetsManager assetsManager)
            {
                assetsManager.LoadAsset(_loadingAssetName, _promise);
            }
        }

        private static long GetEstimatedAssetSize(Object obj)
        {
            if (obj == null) return 0;

            var spr = obj as Sprite;

            // Roughly size of texture in bytes.
            if (spr != null && spr.texture != null) return spr.texture.width * 4L * spr.texture.height;

            var tex = obj as Texture2D;
            if (tex != null) return tex.width * 4L * tex.height;

            var audio = obj as AudioClip;

            if (audio != null) return audio.samples * 8L;

            return 0;
        }

        public void Tick()
        {
            while (_assetsInQueue?.Count > 0)
            {
                var asset = _assetsInQueue.Dequeue();
                asset?.LoadAsset(this);
            }
        }

        public void ReleaseAssets()
        {
            _assetsToRelease.Clear();
            foreach (var assetName in AssetLoadedCache.Keys)
            {
                var asset = (IAssetReleaseable) AssetLoadedCache[assetName];
                BDebug.Log(LogCat.Resources, $"Reference : {assetName} count: {asset.GetReferenceCounter()}");
                
                if (!asset.CanFree()) continue;
                
                _assetsToRelease.Add(assetName);
                asset.Free();
            }
        
            foreach (var assetName in _assetsToRelease)
            {
                AssetLoadedCache.Remove(assetName);
            }

            _bundleManager.ReleaseUnusedBundles();
        }
        
        public void UnloadAsset(Object asset)
        {
            if (asset == null) return;
            
            UnloadAsset(asset.name);

            Resources.UnloadAsset(asset);
        }

        public void UnloadAsset(string assetName)
        {
            if (AssetLoadedCache == null || !AssetLoadedCache.TryGetValue(assetName, out var value)) return;

            if (value != null)
            {
                var assetToRelease = (IAssetReleaseable)value;
                assetToRelease.Free();
                assetToRelease.Dispose();
            }
            AssetLoadedCache.Remove(assetName);
        }
        
        #endregion

        public void Restart()
        {
#if BBB_DEBUG
            AssetLoadTimeDict.Clear();
#endif
            //reject all pending promises
            var promises = new Dictionary<string, object>(PromiseByAssetPath);

            foreach (var (assetName , promise) in promises)
            {
                var stateProperty = promise.GetType().GetProperty("CurState");
                
                if (stateProperty == null) continue;
                
                var propertyValue = (PromiseState)stateProperty.GetValue(promise);
                if (propertyValue == PromiseState.Pending)
                {
                    ((IRejectable)promise).Reject(new RejectAssetByRestartException(assetName));
                }
            }
            ReleaseAssets();
            PromiseByAssetPath.Clear();
            AssetLoadedCache.Clear();
            _assetsInQueue.Clear();
        }
        
        public class RejectAssetByRestartException : Exception
        {
            public RejectAssetByRestartException() : base("Asset has been rejected by Restart!") { }

            public RejectAssetByRestartException(string assetName) : base($"{assetName} has been rejected by Restart!") { }

            public RejectAssetByRestartException(string message, Exception innerException) : base(message, innerException) { }
        }
    }
}