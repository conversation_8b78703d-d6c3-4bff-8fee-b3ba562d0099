using System;
using BBB;
using UnityEngine;

public class FpsStats
{
    public int AverageFps;
    public int MinFps;
    public int MaxFps;
    public int HitchOccurrences;
}

public class FPSTracker : BbbMonoBehaviour
{
    private static FPSTracker _instance;
    private const int MaxFpsRecords = 120; // 

    private const int SampleRate = 1; // In Seconds
    private int _hitchFpsThreshold = 15; // Hitch Threshold

    private readonly float[] _fpsRecords = new float[MaxFpsRecords]; // Record last sample Per SampleRate (last MaxFpsRecords seconds) 
    private int _hitchCounter = 0;
    private int _frames;
    private int _samples;
    private float _duration;
    private bool _startRecording;
    private bool _enabled;
    private readonly FpsStats _lastStat = new();

    private void Awake()
    {
        _instance = this;
    }

    private void Start()
    {
#if BBB_DEBUG
        _guiStyle = new GUIStyle { fontSize = 16, richText = true, normal = new GUIStyleState { textColor = Color.white } };
        GUI.depth = 2;
#endif
    }

    public void StartRecording()
    {
        _startRecording = true;
    }
    private void Update()
    {
        if (!_startRecording || !_enabled)
            return;
        var frameDuration = Time.unscaledDeltaTime;
        _frames++;
        _duration += frameDuration;
        if (_duration > SampleRate)
        {
            var fps = _frames / _duration;
            _fpsRecords[_samples % MaxFpsRecords] = fps;
            _frames = 0;
            _duration = 0;
            _samples++;
            if (fps < _hitchFpsThreshold)
                _hitchCounter += 1;
            if (_samples % MaxFpsRecords == 0)
                _samples = 0;
        }
    }

    private void CalculateStats()
    {
        var fpsSum = 0.0f;
        var fpsCount = 0;
        var fpsMin = _fpsRecords[0];
        var fpsMax = _fpsRecords[0];
        for (var i = _fpsRecords.Length - 1; i > 0; i--)
        {
            if (_fpsRecords[i] > 0)
            {
                fpsSum += _fpsRecords[i];
                fpsCount++;
                
                if (_fpsRecords[i] < fpsMin)
                    fpsMin = _fpsRecords[i];
                if (_fpsRecords[i] > fpsMax)
                    fpsMax = _fpsRecords[i];
            }
        }

        if (fpsCount > 0)
            _lastStat.AverageFps = (int)(fpsSum / fpsCount);
        _lastStat.MinFps = (int)fpsMin;
        _lastStat.MaxFps = (int)fpsMax;
        _lastStat.HitchOccurrences = _hitchCounter;
    }

    public static void Restart()
    {
        if (_instance != null)
            _instance.ResetStats();
    }

    private void ResetStats()
    {
        _hitchCounter = 0;
        for (var i = 0; i < _fpsRecords.Length; i++)
            _fpsRecords[i] = 0.0f;
        _samples = 0;
    }

    public static FpsStats GetStatistics()
    {
        if (_instance == null)
            return null;
        return _instance.GetStats();
    }

    private FpsStats GetStats()
    {
        CalculateStats();
        return _lastStat;
    }
    public static void Setup(bool fpsTrackingEnabled, int hitchThreshold)
    {
        if (_instance == null)
            return;
        _instance.SetConfigValues(fpsTrackingEnabled, hitchThreshold);
    }
    private void SetConfigValues(bool fpsTrackingEnabled, int hitchThreshold)
    {
        _enabled = fpsTrackingEnabled;
        _hitchFpsThreshold = hitchThreshold;
    }


#if BBB_DEBUG
    private bool _fpsDisplayEnabled = true;
    private GUIStyle _guiStyle;
    private readonly Rect _textRect = new(5, 50, 130, 30);
    private void OnGUI()
    {
        var labelToDisplay = "";
        var styleToDisplay = _guiStyle;
        
        CalculateStats();
        
        if (!_fpsDisplayEnabled || _lastStat.AverageFps < 1)
        {
            styleToDisplay = GUIStyle.none;
        }
        else
        {
            labelToDisplay = $"FPS: {_lastStat.AverageFps:F2} -[{_lastStat.MinFps:F2},{_lastStat.MaxFps:F2},{_lastStat.HitchOccurrences}]";
        }
        
        if (GUI.Button(_textRect, labelToDisplay, styleToDisplay))
        {
            _fpsDisplayEnabled = !_fpsDisplayEnabled;
        }
    }
#endif
}