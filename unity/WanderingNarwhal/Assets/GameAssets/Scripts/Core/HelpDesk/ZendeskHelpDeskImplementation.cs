using System;
using System.Collections.Generic;
using System.Threading;
using BBB;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BebopBee;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Database;
using GameAssets.Scripts.Database.Model;
using GameAssets.Scripts.Utils;
using UnityEngine;
using Zendesk.Runtime.Logging;
using Zendesk.Runtime.Messaging.Results.Errors;
using Zendesk.Runtime.SDK;
using Zendesk.Runtime.SDK.Initialisation;
using UnityEngine.Networking;

namespace GameAssets.Scripts.SocialNetworks
{
    public class ZendeskHelpDeskImplementation : IHelpDeskImpl
    {
        #region Fields
        private static bool _initialized;
        private static ZendeskAuthenticationDataModel _zendeskAuthenticationDataModel;
        private readonly List<Action<int>> _onCountChangedList = new();
        private event Func<UniTask> OnInitialized;
        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private BrainCloudManager _brainCloudManager;
        private static readonly Dictionary<string, object> UserCustomFields = new()
        {
            { GameConstants.ZendeskKeyPayerStatusId, string.Empty },
            { GameConstants.ZendeskKeyLevelId, string.Empty },
            { GameConstants.ZendeskKeyGameId,  GameConstants.GameName },
            { GameConstants.ZendeskKeyUserId, string.Empty },
            { GameConstants.ZendeskKeyAppVersion, Application.version },
            { GameConstants.ZendeskKeyPlatform, Application.platform.ToString() }
        };

        private static readonly string[] ZendeskSuspendedRegions = { "RU", "BY" };
        private static bool _loggedIn;
        private readonly SemaphoreSlim _loginSemaphore = new(1, 1);
        #endregion

        #region Helpers
        private string GetPlayerType()
        {
            var player = _playerManager.Player;
            if (player.IsSuperWhale)
                return "Super Fan";
            if (player.IsWhale)
                return "Fan";
            if (player.IsPayer)
                return "Payer";
            return "Non Payer";
        }

        private async UniTask<string> GetOrGenerateJwtTokenAsync()
        {
            _zendeskAuthenticationDataModel ??= DbManager.LoadData<ZendeskAuthenticationDataModel>();
            
            if (_zendeskAuthenticationDataModel != null && !_zendeskAuthenticationDataModel.JwtToken.IsNullOrEmpty() && _zendeskAuthenticationDataModel.JwtTokenExpirationDateUtcSeconds >= DateTimeOffset.UtcNow.ToUnixTimeSeconds())
                return _zendeskAuthenticationDataModel.JwtToken;

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                return string.Empty;
            }

            var tcs = new UniTaskCompletionSource<bool>();

            _brainCloudManager.RequestJwtForZendesk(_accountManager.Profile, response =>
            {
                var jwtToken = response.Data.Response.Token;
                var expirationDate = DateTimeOffset.UtcNow.AddSeconds(response.Data.Response.TokenExpiresAfter).ToUnixTimeSeconds();
                
                if (_zendeskAuthenticationDataModel == null)
                {
                    _zendeskAuthenticationDataModel = new ZendeskAuthenticationDataModel()
                    {
                        JwtToken = jwtToken,
                        JwtTokenExpirationDateUtcSeconds = expirationDate
                    };
                    DbManager.SaveData(_zendeskAuthenticationDataModel);
                }
                else
                {
                    UpdateJwtToken(jwtToken, expirationDate);
                }

                BDebug.Log(LogCat.HelpDesk, $"Zendesk Successfully Got JWT Token: {jwtToken}");
                tcs.TrySetResult(true);
            }, () =>
            {
                BDebug.LogError(LogCat.HelpDesk, "Failed to Generate JWT Token");
                tcs.TrySetResult(false);
            });

            var success = await tcs.Task;
            
            if (!success)
                throw new Exception("JWT token generation failed.");

            return _zendeskAuthenticationDataModel?.JwtToken;
        }

        private async UniTask SetCustomPropertiesAsync()
        {
            UserCustomFields[GameConstants.ZendeskKeyPayerStatusId] = GetPlayerType();
            UserCustomFields[GameConstants.ZendeskKeyLevelId] = _accountManager.Profile.HighestPassedLevelId;
            UserCustomFields[GameConstants.ZendeskKeyUserId] = _accountManager.Profile.Uid;
            
            var result = await ZendeskSdk.Instance.Messaging.SetConversationFieldsAsync(UserCustomFields);
            if (result.IsSuccess)
            {
                BDebug.Log(LogCat.HelpDesk, "Zendesk Successfully Set Conversation Fields");
            }
            else
            {
                BDebug.LogError(LogCat.HelpDesk, $"Zendesk Failed to Set Conversation Fields! Error Message: {result.ErrorMessage}");
            }
        }

        private static bool EnsureInitialized()
        {
            if (_initialized)
                return true;
            
            BDebug.LogError(LogCat.HelpDesk, "Zendesk not initialized.");
            return false;
        }
        
        private bool CanExecuteOperation()
        {
            return EnsureInitialized() && ConnectivityStatusManager.ConnectivityReachable;
        }
        #endregion

        #region IHelpDeskImpl Implementation
        public async void Initialize(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
           
            Subscribe();
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                return;
            }
            
            await Initialize();
        }

        private async UniTask Initialize()
        {
            if (_initialized)
            {
                await TryLogin();
                return;
            }

#if BBB_DEBUG
            ZendeskLogger.SetEnabled(true);
#endif
            var status = await ZendeskSdk.InitializeAsync(config => config.ChannelId = GameConstants.ZendeskChannelId);
            var failedReason = status.Status switch
            {
                InitialisationStatus.Success => string.Empty,
                InitialisationStatus.InvalidChannelId => $"Invalid Channel Id {GameConstants.ZendeskChannelId}",
                InitialisationStatus.FailedToRetrieveSdkSettings => "Failed to Retrieve Sdk Settings",
                InitialisationStatus.Failure => "Failure",
                InitialisationStatus.MessagingNotEnabled => "Messaging not Enabled",
                _ => "Unknown Initialization Status"
            };

            switch (status.Status)
            {
                case InitialisationStatus.FailedToRetrieveSdkSettings when !ConnectivityStatusManager.ConnectivityReachable:
                    BDebug.LogWarning(LogCat.HelpDesk, $"Zendesk Initialization Failed! Reason: {failedReason}, Connectivity not reachable!");
                    break;
                case InitialisationStatus.FailedToRetrieveSdkSettings:
                {
                    var regionCode = await GetRegionCodeAsync();
            
                    BDebug.Log(LogCat.HelpDesk,$"Region Code is {regionCode}");

                    if (!regionCode.IsNullOrEmpty())
                    {
                        foreach (var suspendedRegion in ZendeskSuspendedRegions)
                        {
                            if (regionCode != suspendedRegion)
                                continue;
                
                            BDebug.LogWarning(LogCat.HelpDesk,$"Skipping Zendesk init because we know Zendesk is suspended in {regionCode}. Failure Reason: {failedReason}");
                            return;
                        }
                    }

                    break;
                }
                case InitialisationStatus.Success:
                    await HandleInitializationSuccessAsync();
                    break;
                default:
                    BDebug.LogError(LogCat.HelpDesk, $"Zendesk Initialization Failed! Reason: {failedReason}");
                    break;
            }
        }

        private static async UniTask<string> GetRegionCodeAsync()
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                BDebug.LogWarning(LogCat.HelpDesk, "Cannot check region: no network.");
                return string.Empty;
            }
            
            try
            {
                var req = UnityWebRequest.Get(GameConstants.RegionCheckUrl);
                
                await req.SendWebRequest().ToUniTask();

                if (req.result == UnityWebRequest.Result.Success)
                    return req.downloadHandler.text.Trim();
                
                BDebug.LogWarning(LogCat.HelpDesk,$"Region check failed: {req.error}");
                return string.Empty;
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.HelpDesk, $"Error while checking for Region: {e}");
                return string.Empty;
            }
        }

        private void Subscribe()
        {
           Unsubscribe(); 
           ConnectivityStatusManager.ConnectivityChanged += OnConnectivityChanged;
        }

        private void Unsubscribe()
        {
            ConnectivityStatusManager.ConnectivityChanged -= OnConnectivityChanged;
        }
        
        private async void OnConnectivityChanged(bool isConnected)
        {
            if (!isConnected)
                return;
            
            BDebug.Log(LogCat.HelpDesk, "Connectivity regained - reattempting Zendesk initialization/login...");

            if (!_initialized)
            {
                await Initialize();
            }
            else
            {
                await TryLogin();
            }
        }

        private async UniTask HandleInitializationSuccessAsync()
        {
            BDebug.Log(LogCat.HelpDesk, "Zendesk Initialization Success");
            _initialized = true;
            await TryLogin();

            foreach (var action in _onCountChangedList)
            {
                _ = GetUnreadCountAsync(action);
            }
            
            _onCountChangedList.Clear();
            if (OnInitialized != null)
            {
                await OnInitialized.Invoke();
                OnInitialized = null;
            }
        }

        public bool IsInitialized() => _initialized;

        private async UniTask<bool> TryLogin() 
        {
            await _loginSemaphore.WaitAsync();
            
            if (_loggedIn)
                return true;
            
            try
            {
                var expiredJwtRetryCount = 0;
                const int expiredJwtMaxRetryCount = 3;

                while (true)
                {
                    if (!_initialized)
                    {
                        OnInitialized += async () => await TryLogin();
                        return false;
                    }

                    if (!ConnectivityStatusManager.ConnectivityReachable)
                    {
                        return false;
                    }

                    string token;
                    try
                    {
                        token = await GetOrGenerateJwtTokenAsync();
                        if (token.IsNullOrEmpty())
                        {
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        BDebug.LogError(LogCat.HelpDesk, $"JWT generation error: {ex.Message}");
                        return false;
                    }

                    var loginResult = await ZendeskSdk.Instance.Messaging.LoginAsync(token);
                    if (loginResult.IsSuccess)
                    {
                        _loggedIn = true;
                        BDebug.Log(LogCat.HelpDesk, "Zendesk Login Success");
                        return true;
                    }

                    var failureReason = loginResult.Error switch
                    {
                        LoginError.UnknownFailure => "Unknown",
                        LoginError.InvalidJwt => "Invalid Jwt",
                        LoginError.DifferentUserAlreadyLoggedIn => "Different User Already Logged In",
                        LoginError.RequestedUserAlreadyLoggedIn => "Requested User Already Logged In",
                        LoginError.UserNotFound => "User Not Found",
                        LoginError.ExpiredJwt => "Expired Jwt",
                        null => "Null",
                        _ => "Unrecognized Error"
                    };

                    if (loginResult.Error == LoginError.ExpiredJwt)
                    {
                        ResetJwtToken();
                        expiredJwtRetryCount++;
                        BDebug.Log(LogCat.HelpDesk, $"Zendesk Login Failed due to expired JWT, retrying ({expiredJwtRetryCount}/{expiredJwtMaxRetryCount}). Reason: {failureReason}, Error Message: {loginResult.ErrorMessage}");

                        if (expiredJwtRetryCount >= expiredJwtMaxRetryCount)
                        {
                            BDebug.LogError(LogCat.HelpDesk, "Exceeded maximum login attempts due to expired JWT. Exiting TryLogin safely.");
                            return false;
                        }

                        await UniTask.Delay(1000);
                        continue;
                    }

                    BDebug.LogError(LogCat.HelpDesk, $"Zendesk Login Failed! Failure Reason: {failureReason}, Error Message: {loginResult.ErrorMessage}");
                    await UniTask.Delay(1000);
                    return false;
                }
            }
            finally
            {
                _loginSemaphore.Release();
            }
        }

        public async UniTask ShowHelpCenter()
        {
            if (!CanExecuteOperation())
                return;
            
            await SetCustomPropertiesAsync();
            await ZendeskSdk.Instance.Home.ShowHomeAsync();
        }

        public async UniTask ShowRequestList()
        {
            if (!CanExecuteOperation())
                return;
            
            await SetCustomPropertiesAsync();
            await ZendeskSdk.Instance.Messaging.ShowMessagingAsync();
        }

        public void AddTags(List<string> tags)
        {
            if (!CanExecuteOperation())
                return;
            
            var result = ZendeskSdk.Instance.Messaging.SetConversationTags(tags);
            
            if (result.IsSuccess)
            {
                BDebug.Log(LogCat.HelpDesk, $"Zendesk Successfully set Conversation Tags: {string.Join(", ", tags)}");
            }
            else
            {
                BDebug.LogError(LogCat.HelpDesk, $"Zendesk Failed to Set Conversation Tags: {string.Join(", ", tags)}! Error Message: {result.ErrorMessage}");
            }
        }

        public void ClearTags()
        {
            if (!CanExecuteOperation())
                return;
            
            ZendeskSdk.Instance.Messaging.ClearConversationTags();
        }

        public async UniTask<bool> HasUnreadMessage(Action<int> onUnreadCountChanged = null)
        {
            return await GetUnreadCountAsync(onUnreadCountChanged) > 0;
        }

        public async UniTask<int> GetUnreadCountAsync(Action<int> onUnreadCountChanged)
        {
            while (true)
            {
                if (!_initialized)
                {
                    _onCountChangedList.Add(onUnreadCountChanged);
                    return 0;
                }

                if (!ConnectivityStatusManager.ConnectivityReachable)
                {
                    return 0;
                }

                var unreadResult = await ZendeskSdk.Instance.Messaging.GetUnreadMessageCountAsync(onUnreadCountChanged);
                
                if (unreadResult.IsSuccess)
                    return unreadResult.UnreadCount;

                switch (unreadResult.Error)
                {
                    case UnreadCountError.NoUser:
                        BDebug.Log(LogCat.HelpDesk, $"Zendesk: No user found while fetching unread messages. Error Message: {unreadResult.ErrorMessage}");
                        if (await TryLogin())
                        {
                            continue;
                        }

                        return 0;
                    case UnreadCountError.FailedToFetch:
                        BDebug.LogError(LogCat.HelpDesk, $"Zendesk: Failed to fetch unread count. Error Message: {unreadResult.ErrorMessage}");
                        break;
                    case UnreadCountError.NoConversation:
                        BDebug.Log(LogCat.HelpDesk, $"Zendesk: No conversation found. Error Message: {unreadResult.ErrorMessage}");
                        break;
                    case null:
                        break;
                }

                return 0;
            }
        }

        private static void ResetJwtToken()
        {
            UpdateJwtToken(string.Empty, 0);
        }

        private static void UpdateJwtToken(string jwtToken, long jwtTokenExpirationDateInSeconds)
        {
            DbManager.UpdateData<ZendeskAuthenticationDataModel>((zendeskAuthenticationData) =>
            {
                zendeskAuthenticationData.JwtToken = jwtToken;
                zendeskAuthenticationData.JwtTokenExpirationDateUtcSeconds = jwtTokenExpirationDateInSeconds;
            });
        }

        public void Restart(bool logout)
        {
            Unsubscribe();
            if (!_initialized)
            {
                return;
            }

            if (logout)
            {
                var logoutResult = ZendeskSdk.Instance.Messaging.Logout();
                if (logoutResult.IsSuccess)
                {
                    ResetJwtToken();
                    _loggedIn = false;
                    BDebug.Log(LogCat.HelpDesk, "Zendesk Logout Success");
                }
                else
                {
                    BDebug.LogError(LogCat.HelpDesk, $"Zendesk Logout Failed! Error Message: {logoutResult.ErrorMessage}");
                }
            }
        }
        #endregion
    }
}