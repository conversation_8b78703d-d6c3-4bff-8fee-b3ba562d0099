using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using BBB;
using BBB.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace GameAssets.Scripts.Core.NewtonSoftJsonExtensions
{
    public static class JsonConvertExtensions
    {
        private const string TypeString = "string";
        private const string TypeInt = "int";
        private const string TypeFloat = "float";
        private const string TypeBool = "bool";

        public static object TryDeserializeObject(Type type, string jsonResponse, string tag = null)
        {
            if (jsonResponse == null)
                return default;
            
            try
            {
                return JsonConvert.DeserializeObject(jsonResponse, type);
            }
            catch (Exception e)
            {
                var defaultDeepObjectJson = JsonConvert.SerializeObject(CreateDefaultDeepObject(type));
                return HandleDeserializationException(e, defaultDeepObjectJson, jsonResponse, tag, type.FullName);
            }
        }
        
        public static T TryDeserializeObject<T>(string jsonResponse, string tag = null)
        {
            if (jsonResponse == null)
                return default;
            
            try
            {
                return JsonConvert.DeserializeObject<T>(jsonResponse);
            }
            catch (Exception e)
            {
                var defaultDeepObjectJson = JsonConvert.SerializeObject(CreateDefaultDeepObject<T>());
                return (T)HandleDeserializationException(e, defaultDeepObjectJson, jsonResponse, tag, typeof(T).FullName);
            }
        }

        private static object HandleDeserializationException(Exception e, string defaultDeepObjectJson, string jsonResponse, string tag, string fullTypeName)
        {
            var errorLog =
                $"Json deserialization exception: {e.Message} ; RESPONSE: {jsonResponse} DTO STRUCTURE: {defaultDeepObjectJson}";
            if (tag != null)
            {
                errorLog = "For " + tag + " : " + errorLog;
            }
            BDebug.LogError(LogCat.General, errorLog);
            var defaultDeepDictObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(defaultDeepObjectJson);
            var responseDictObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonResponse);
            var responseDictObjTypeNames = ConvertDictionaryToTypeNames(DeserializeDictionaryRecursively(responseDictObj));
            var defaultDictObjTypeNames = ConvertDictionaryToTypeNames(DeserializeDictionaryRecursively(defaultDeepDictObj));

            var issuesStringBuilder = new StringBuilder();
            if (CompareJsonTypes(responseDictObjTypeNames, defaultDictObjTypeNames, issuesStringBuilder))
                return default;
            
            var issuesString = issuesStringBuilder.ToString();
            BDebug.LogError(LogCat.General, $"Json type comparison issues found: {issuesString} while deserializing type {fullTypeName}");
            return default;
        }

        private static T CreateDefaultDeepObject<T>()
        {
            return (T)CreateDefaultDeepObject(typeof(T));
        }

        public static string CreateDefaultDeepObjectJson(Type type)
        {
            var defaultDeepObjectJson = JsonConvert.SerializeObject(CreateDefaultDeepObject(type));
            return defaultDeepObjectJson;
        }

        private static object CreateDefaultDeepObject(Type type)
        {
            if (type.IsValueType)
            {
                return Activator.CreateInstance(type);
            }

            if (type.GetConstructor(Type.EmptyTypes) == null) return null;
            
            var obj = Activator.CreateInstance(type);
            foreach (var prop in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                if (prop.PropertyType == type)
                {
                    continue; // Avoid self-referencing loop
                }
                if (prop.CanWrite)
                {
                    prop.SetValue(obj, CreateDefaultDeepObject(prop.PropertyType), null);
                }
            }
            return obj;
        }

        private static List<object> DeserializeListRecursively(List<object> list)
        {
            var newList = new List<object>();
            list.ForEach(e => newList.Add(null));
            for (var i = 0; i < list.Count; i++)
            {
                newList[i] = list[i] switch
                {
                    JObject valueObj => DeserializeDictionaryRecursively(valueObj.ToObject<Dictionary<string, object>>()),
                    JArray valueArray => DeserializeListRecursively(valueArray.ToObject<List<object>>()),
                    JValue value => value.Value,
                    _ => list[i]
                };
            }

            return newList;
        }
        
        private static Dictionary<string, object> DeserializeDictionaryRecursively(Dictionary<string, object> dictionary)
        {
            var newDictionary = new Dictionary<string, object>();
            var keys = new string[dictionary.Count];
            dictionary.Keys.CopyTo(keys, 0);

            foreach (var key in keys)
            {
                newDictionary[key] = dictionary[key] switch
                {
                    JObject valueObj => DeserializeDictionaryRecursively(valueObj.ToObject<Dictionary<string, object>>()),
                    JArray valueArray => DeserializeListRecursively(valueArray.ToObject<List<object>>()),
                    JValue value => value.Value,
                    _ => dictionary[key]
                };
            }

            return newDictionary;
        }

        private static Dictionary<string, object> ConvertDictionaryToTypeNames(Dictionary<string, object> dictionary)
        {
            var newDictionary = new Dictionary<string, object>();
            var keys = new string[dictionary.Count];
            dictionary.Keys.CopyTo(keys, 0);

            foreach (var key in keys)
            {
                newDictionary[key] = dictionary[key] switch
                {
                    Dictionary<string, object> nestedDict => ConvertDictionaryToTypeNames(nestedDict),
                    List<object> list => ConvertListToTypeNames(list),
                    _ => GetTypeName(dictionary[key])
                };
            }
    
            return newDictionary;
        }

        private static object ConvertListToTypeNames(List<object> list)
        {
            if (list.Count == 0)
            {
                return "list";
            }

            return list[0] switch
            {
                Dictionary<string, object> => "list_of_objects",
                List<object> => "list_of_lists",
                _ => $"list_of_{GetTypeName(list[0])}"
            };
        }

    private static string GetTypeName(object obj)
    {
        return obj switch
        {
            null => TypeString,
            long or int => TypeInt,
            double or float or decimal => TypeFloat,
            bool => TypeBool,
            _ => TypeString
        };
    }

    private static bool CompareJsonTypes(Dictionary<string, object> first, Dictionary<string, object> second, StringBuilder issuesStringBuilder)
    {
        foreach (var item in first)
        {
            if (!second.ContainsKey(item.Key))
                continue;
            
            switch (item.Value)
            {
                case Dictionary<string, object> nestedFirst when second[item.Key] is Dictionary<string, object> nestedSecond && !CompareJsonTypes(nestedFirst, nestedSecond, issuesStringBuilder):
                    return false;
                case string firstStrValue when second[item.Key] is string secondValue:
                {
                    if (!firstStrValue.StartsWith(secondValue))
                    {
                        issuesStringBuilder.AppendLine($"Type mismatch for key '{item.Key}'. Expected: {item.Value}, Found: {second[item.Key]}");
                    }

                    break;
                }
                default:
                {
                    if (!item.Value.Equals(second[item.Key]))
                    {
                        issuesStringBuilder.AppendLine($"Type mismatch for key '{item.Key}'. Expected: {item.Value}, Found: {second[item.Key]}");
                        return false;
                    }

                    break;
                }
            }
        }
        return true;
    }
    }
}