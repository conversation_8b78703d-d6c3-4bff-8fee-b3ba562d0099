using System.Collections.Generic;
using System.Text;
using BBB.Wallet;
using UnityEngine.Profiling;

namespace BBB.Core
{
    public static class RewardsUtility
    {
        public static bool IsRewardValid(Dictionary<string, int> reward)
        {
            if (reward == null || reward.Count == 0)
            {
                return false;
            }

            foreach (var kvp in reward)
            {
                if (kvp.Value > 0)
                {
                    return true;
                }
            }

            return false;
        }
        
        public static Dictionary<string, int> RewardStringToDict(string rewardStr)
        {
            return RewardStringToDict(rewardStr, out var _);
        }

        public static Dictionary<string, int> RewardStringsToDict(IEnumerable<string> rewardStrs)
        {
            var result = new Dictionary<string, int>();
            foreach (var str in rewardStrs)
            {
                var dict = RewardStringToDict(str, out var _);
                foreach (var kvp in dict)
                {
                    if (result.ContainsKey(kvp.Key))
                    {
                        result[kvp.Key] += kvp.Value;
                    }
                    else
                    {
                        result.Add(kvp.Key, kvp.Value);
                    }
                }
            }

            return result;
        }

        public static string RewardDictToString(Dictionary<string, int> reward)
        {
            var sb = new StringBuilder();
            foreach (var kvp in reward)
            {
                const string comma = ",";
                const string equals = "=";
                if (sb.Length > 0)
                {
                    sb.Append(comma);
                }
                sb.Append(kvp.Key).Append(equals).Append(kvp.Value);
            }
            return sb.ToString();
        }
        
        public static Dictionary<string, int> RewardStringToDict(string rewardStr, out List<string> ordering)
        {
            Profiler.BeginSample("RewardStringToDict");
            var result = new Dictionary<string, int>();
            ordering = new List<string>();
            
            if (string.IsNullOrEmpty(rewardStr))
            {
                BDebug.LogWarning("Empty reward string");
                Profiler.EndSample();
                return result;
            }

            var keyBuilder = new StringBuilder();
            var valueBuilder = new StringBuilder();
            var readingKey = true;

            foreach (var c in rewardStr)
            {
                if (char.IsWhiteSpace(c) || c == '\"' || c == '\\' || c == '{' || c == '}')
                    continue;

                switch (c)
                {
                    case '=' or ':':
                        readingKey = false;
                        continue;
                    case ',' when !TryAddToResult(result, ordering, keyBuilder, valueBuilder, rewardStr):
                        Profiler.EndSample();
                        return null;
                    case ',':
                        keyBuilder.Clear();
                        valueBuilder.Clear();
                        readingKey = true;
                        continue;
                }

                if (readingKey)
                {
                    keyBuilder.Append(c);
                }
                else
                {
                    valueBuilder.Append(c);
                }
            }

            if (!TryAddToResult(result, ordering, keyBuilder, valueBuilder, rewardStr))
            {
                Profiler.EndSample();
                return null;
            }

            Profiler.EndSample();
            return result;
        }
        
        public static Dictionary<string, int> FilterRewards(this Dictionary<string, int> rewards)
        {
            if (!rewards.HasInvalidReward())
                return rewards;
            
            var filtered = new Dictionary<string, int>();
            foreach (var kvp in rewards)
            {
                var key = kvp.Key;
                if (!InventoryItems.IsGameEventScore(key) || GameEventManager.IsValidScore(key, out _))
                {
                    filtered[key] = kvp.Value;
                }
            }
            return filtered;
        }

        private static bool HasInvalidReward(this Dictionary<string, int> rewards)
        {
            if (rewards ==  null || rewards.Count == 0)
                return false;
            
            foreach (var kvp in rewards)
            {
                var key = kvp.Key;
                if (InventoryItems.IsGameEventScore(key) && !GameEventManager.IsValidScore(key, out _))
                    return true;
            }

            return false;
        }
        
        public static Dictionary<string, int> FilterRewards(this Dictionary<string, int> rewards, out List<string> ordering)
        {
            var filtered = new Dictionary<string, int>();
            ordering = new List<string>();
            foreach (var kvp in rewards)
            {
                var key = kvp.Key;
                if (!InventoryItems.IsGameEventScore(key) || GameEventManager.IsValidScore(key, out _))
                {
                    filtered[key] = kvp.Value;
                    ordering.Add(key);
                }
            }
            return filtered;
        }
        
        private static bool TryAddToResult(Dictionary<string, int> result, List<string> ordering, StringBuilder keyBuilder, StringBuilder valueBuilder, string rewardStr)
        {
            if (keyBuilder.Length <= 0 || valueBuilder.Length <= 0)
            {
                BDebug.LogErrorFormat(LogCat.General, "Wrong format for reward string {0}", rewardStr);
                return false;
            }
            
            var key = keyBuilder.ToString();
            if (int.TryParse(valueBuilder.ToString(), out var number))
            {
                result.Add(key, number);
                ordering.Add(key);
                return true;
            }

            BDebug.LogError(LogCat.General, $"Failed to parse {valueBuilder} to integer");
            return false;
        }

        public static bool IsSpecialReward(string rewardName)
        {
            return rewardName switch
            {
                InventoryItems.Portrait or InventoryItems.Mug or InventoryItems.Shirt or InventoryItems.Hat => true,
                _ => false
            };
        }
    }
}