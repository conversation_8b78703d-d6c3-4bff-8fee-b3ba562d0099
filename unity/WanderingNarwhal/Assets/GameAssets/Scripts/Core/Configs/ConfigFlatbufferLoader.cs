using System;
using System.Collections.Generic;
using System.IO;
using BBB;
using BBB.Core.Analytics.TechAnalytics.EventConstants;
using BBB.Core.Analytics.TechAnalytics.Managers;
using Cysharp.Threading.Tasks;
using RPC.Core;
using UnityEngine.Profiling;

namespace Core.Configs
{
    public class ConfigFlatbufferLoader
    {
        private readonly ConfigManifest _configManifest;
        private readonly FlatbufferSerializer _flatbufferSerializer;
        private IFileStorage _fileStorage;

        public ConfigFlatbufferLoader(ConfigManifest configManifest, IFileStorage fileStorage)
        {
            _configManifest = configManifest;
            _flatbufferSerializer = new FlatbufferSerializer();
            _fileStorage = fileStorage;
        }
        
        private TProto? ReadConfig<TProto>(string configName, bool fromServer)
            where TProto : struct
        {
            if (!_configManifest.HasConfig(configName))
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.NotInManifest, MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause, fromServer);
                return null;
            }

            Profiler.BeginSample($"Config.ReadConfig [{configName}]");

            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var hash = _configManifest.GetConfigInfo(configName).Hash;
            var bundleFileName = $"{ConfigUtils.BundleDataPath}/{configName}-{hash}.bin";
            var fileName = $"{ConfigUtils.PersistentDataPath}/{configName}-{hash}.bin";

            //Read 1st from persistent path
            //If not found read from bundle data and save it in persistent path
            Profiler.BeginSample($"Deserialize [{configName}]");
            var data = _flatbufferSerializer.DeserializeFile<TProto>(fileName);
            Profiler.EndSample();
            if (data == null)
            {
                Profiler.BeginSample($"ReadBytes [{configName}]");
                var bytes = _flatbufferSerializer.ReadBytesFromFile(bundleFileName);
                Profiler.EndSample();
                if (bytes != null)
                {
                    Profiler.BeginSample($"DeserializeBytes [{configName}]");
                    data = _flatbufferSerializer.DeserializeBytes<TProto>(bytes);
                    Profiler.EndSample();
                    if (data != null)
                    {
                        Profiler.BeginSample($"SaveConfigBytes [{configName}]");
                        LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);
                        _fileStorage.SaveConfigBytes(Path.GetFileName(fileName), bytes);
                        Profiler.EndSample();
                    }
                    else
                    {
                        LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.CorruptedFile, startDateTime, fromServer);
                    }
                }
                else
                {
                    LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.NotExist, startDateTime, fromServer);
                }
            }

            Profiler.EndSample();

            return data;
        }

        private async UniTask<TProto?> ReadConfigAsync<TProto>(string configName, bool fromServer)
            where TProto : struct
        {
            if (!_configManifest.HasConfig(configName))
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.NotInManifest, MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause, fromServer);
                return null;
            }

            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var hash = _configManifest.GetConfigInfo(configName).Hash;
            var bundleFileName = $"{ConfigUtils.BundleDataPath}/{configName}-{hash}.bin";
            var fileName = $"{ConfigUtils.PersistentDataPath}/{configName}-{hash}.bin";

            //Read 1st from persistent path
            //If not found read from bundle data and save it in persistent path
            var data = await _flatbufferSerializer.DeserializeFileAsync<TProto>(fileName);
            if (data != null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);
                return data;
            }
            
            var bytes = await _flatbufferSerializer.ReadBytesFromFileAsync(bundleFileName);
            if (bytes == null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.NotExist, startDateTime, fromServer);
                return null;
            }

            data = _flatbufferSerializer.DeserializeBytes<TProto>(bytes);
            if (data == null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.CorruptedFile, startDateTime, fromServer);

                return null;
            }
            _fileStorage.SaveConfigBytesAsync(Path.GetFileName(fileName), bytes);
            LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);
            return data;
        }

        public TProto? ParseConfig<TProto>(Dictionary<string, ConfigVersionInfo> configs, string configName, bool fromServer)
            where TProto : struct
        {
            if (configName == null)
                configName = ConfigUtils.GetKey<TProto>();

            // Process result from the server
            if (!configs.ContainsKey(configName)) return ReadConfig<TProto>(configName, fromServer);

            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;

            var configInfo = configs[configName];
            var filename = configInfo.Filename;
            var hash = configInfo.Hash;
            var data = configInfo.Data;

            Profiler.BeginSample($"Config.ParseConfig Deserialize [{configName}]");
            var pbConfigDict = _flatbufferSerializer.DeserializeBytes<TProto>(data);
            Profiler.EndSample();
            if (pbConfigDict == null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.CorruptedData, startDateTime, fromServer);
                return null;
            }
            Profiler.BeginSample($"Config.ParseConfig SaveConfigBytes [{configName}]");
            LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);

            _fileStorage.SaveConfigBytes(filename, data);
            Profiler.EndSample();

            //Update metadata
            _configManifest.UpdateHash(configName, hash);
            return pbConfigDict;
        }

        public async UniTask<TProto?> ParseConfigAsync<TProto>(Dictionary<string, ConfigVersionInfo> configs, string configName, bool fromServer)
            where TProto : struct
        {
            configName ??= ConfigUtils.GetKey<TProto>();

            // Process result from the server
            if (configs == null || !configs.ContainsKey(configName)) return await ReadConfigAsync<TProto>(configName, fromServer);

            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var configInfo = configs[configName];
            var filename = configInfo.Filename;
            var hash = configInfo.Hash;
            var data = configInfo.Data;

            var pbConfigDict = _flatbufferSerializer.DeserializeBytes<TProto>(data);
            if (pbConfigDict == null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.CorruptedData, startDateTime, fromServer);
                return null;
            }
            
            LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);
            _fileStorage.SaveConfigBytesAsync(filename, data);

            //Update metadata
            _configManifest.UpdateHash(configName, hash);
            return pbConfigDict;
        }

        public TProto? ParseConfig<TProto>(IDictionary<string, object> result, string configName, bool fromServer)
            where TProto : struct
        {
            if (configName == null)
                configName = ConfigUtils.GetKey<TProto>();
        
            // Process result from the server
            if (!result.ContainsKey(configName)) return ReadConfig<TProto>(configName, fromServer);
        
            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var configDict = (IDictionary<string, object>)result[configName];
            var filename = (string)configDict["filename"];
            var hash = (string)configDict["hash"];
            var data = (string)configDict["data"];
            
        
            Profiler.BeginSample($"Config.ParseConfig DeserializeBase64String [{configName}]");
            var pbConfigDict = _flatbufferSerializer.DeserializeBase64String<TProto>(data, out var bytes);
            Profiler.EndSample();
            if (pbConfigDict == null)
            {
                LoadingTimeMetricsManager.ReportConfigLoadFail(configName,  EventParameters.CorruptedData, startDateTime, fromServer);
                return null;
            }
            Profiler.BeginSample($"Config.ParseConfig SaveConfigBytes [{configName}]");
            LoadingTimeMetricsManager.ReportConfigLoadSuccess(configName, startDateTime, fromServer);
            _fileStorage.SaveConfigBytes(filename, bytes);
            Profiler.EndSample();
        
            //Update metadata
            _configManifest.UpdateHash(configName, hash);
            return pbConfigDict;
        }
        public void ChangeFileStorage(IFileStorage fileStorage)
        {
            _fileStorage = fileStorage;
        }
    }
}