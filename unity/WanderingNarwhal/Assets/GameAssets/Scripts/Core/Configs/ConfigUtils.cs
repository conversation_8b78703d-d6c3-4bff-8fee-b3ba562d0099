using System;
using System.Collections.Generic;
using System.IO;
using BBB;
using BBB.Core;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Map;
using UnityEngine;
using UnityEngine.Networking;

namespace Core.Configs
{
    public static class ConfigUtils
    {
        public static readonly string PersistentDataPath = $"{Application.persistentDataPath}/Configs";
        public static readonly string BundleDataPath = $"{Application.streamingAssetsPath}/Configs";

        public static string GetKey<T>()
        {
            return GetKey(typeof(T));
        }

        public static string GetKey(Type type)
        {
            if (type.Namespace == null || !type.Namespace.StartsWith("FB")) return type.Name;
            
            if (type.IsClass && type.Name.EndsWith("T"))
                return $"FB{type.Name.Substring(0, type.Name.Length - 1)}";

            return $"FB{type.Name}";
        }

        public static bool VerifyFile(string configName, string storedHash, string filePath)
        {
            if (filePath.Contains("://"))
            {
                using var wwwFile = new WWW(filePath);
                while (!wwwFile.isDone) { }

                if (string.IsNullOrEmpty(wwwFile.error) && wwwFile.bytes != null)
                    return true;
                
                BDebug.LogFormat(LogCat.Config, "Configuration file: {0}-{1}.bin not found in {2}.", configName, storedHash, filePath);
                return false;
            }

            if (File.Exists(filePath))
                return true;
            
            BDebug.LogFormat(LogCat.Config, "Configuration file: {0}-{1}.bin not found in {2}.", configName, storedHash, filePath);
            return false;
        }
        
        public static async UniTask<bool> VerifyFileAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                BDebug.LogError(LogCat.Config, "VerifyFileAsync: filePath is null or empty.");
                return false;
            }

            try
            {
                if (!filePath.Contains("://"))
                    return File.Exists(filePath);

                await UniTask.SwitchToMainThread();

                using var www = UnityWebRequest.Get(filePath);
                var response = await www.SendWebRequest();
                var fileExists = string.IsNullOrEmpty(response.error);
            
                await UniTask.SwitchToThreadPool();

                return fileExists;
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.Config, $"VerifyFileAsync: Error verifying file at path '{filePath}': {ex}");
                return false;
            }
        }

        public static void SaveConfigBytes(string filename, byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0) return;

            var filepath = $"{PersistentDataPath}/{filename}";
            File.WriteAllBytes(filepath, bytes);
        }

        public static void SaveConfigBytesAsync(string filename, byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0) return;

            var filepath = $"{PersistentDataPath}/{filename}";
            File.WriteAllBytesAsync(filepath, bytes);
        }
        
        public static void SaveConfigBase64(string filename, string configBase64)
        {
            var filepath = $"{PersistentDataPath}/{filename}";
            using var file = new StreamWriter(filepath);
            file.Write(configBase64);
        }

        public static List<string> GetUnlockedTasksFromDependencyGroups(HashSet<DependencyGroup<string>> tasks,
            List<string> completedTasks, List<string> unlockedTasks = null)
        {
            unlockedTasks ??= new List<string>();

            foreach (var group in tasks)
            {
                var unlocked = true;
                var groupDependencies = group.Dependencies;
                foreach (var dependency in groupDependencies)
                {
                    if (completedTasks.Contains(dependency)) continue;
                    unlocked = false;
                    break;
                }

                if (!unlocked) continue;
                
                var taskGroup = group.TaskGroup;
                foreach (var task in completedTasks)
                {
                    taskGroup.Remove(task);
                }
                unlockedTasks.AddRange(taskGroup);
            }
            
            return unlockedTasks;
        }
        
        public static HashSet<DependencyGroup<string>> GroupTasksByDependencies(List<SceneTaskConfig> tasks)
        {
            var groups = new HashSet<DependencyGroup<string>>();
            foreach (var task in tasks)
            {
                var dependencies = FlatBufferHelper.ToList(task.Dependencies, task.DependenciesLength);
                GetDependencyGroup(task.Uid, ToListOfList(dependencies), groups);
            }
            return groups;
        }
        
        public static HashSet<DependencyGroup<string>> GroupTasksByDependencies(List<SceneTaskConfigT> tasks)
        {
            var groups = new HashSet<DependencyGroup<string>>();
            foreach (var task in tasks)
            {
                GetDependencyGroup(task.Uid, ToListOfList(task.Dependencies), groups);
            }
            return groups;
        }

        public static List<List<string>> ToListOfList(List<ListString?> listStrings)
        {
            var list = new List<List<string>>();
            foreach (var listString in listStrings)
            {
                if (listString == null) continue;
                list.Add(FlatBufferHelper.ToList(listString.Value.Group, listString.Value.GroupLength));
            }
            return list;
        }

        private static List<List<string>> ToListOfList(List<ListStringT> listStrings)
        {
            var list = new List<List<string>>();
            foreach (var listString in listStrings)
            {
                if (listString == null) continue;
                list.Add(listString.Group);
            }
            return list;
        }

        private static void GetDependencyGroup(string taskUid, List<List<string>> dependencies, HashSet<DependencyGroup<string>> groups)
        {
            if (dependencies.IsNullOrEmpty())
            {
                FindAndAddToGroup(taskUid, DependencyGroup<string>.EmptyDependencies, groups);
            }
            
            foreach (var tasks in dependencies)
            {
                FindAndAddToGroup(taskUid, tasks, groups);
            }
        }

        private static void FindAndAddToGroup(string taskUid, List<string> tasks, HashSet<DependencyGroup<string>> groups)
        {
            foreach (var group in groups)
            {
                if (!group.HasSameDependencies(tasks)) continue;
                
                group.AddToTasks(taskUid);
                return;
            }
                    
            var newGroup = new DependencyGroup<string>();
            newGroup.SetDependencies(tasks);
            newGroup.AddToTasks(taskUid);
            groups.Add(newGroup);
        } 
    }
}