using System;
using System.Collections.Generic;
using BBB.Core.AssetBundles;
using BBB.Core.ResourcesManager;
using BBB.DI;
using FBConfig;
using UnityEngine.Profiling;

namespace Core.Configs
{
    public class BundlesConfigUpdater
    {
        private static readonly Type[] RequiredConfigs = {
            typeof(BundleIndexConfigT),
            typeof(BundleInfoConfigT),
            typeof(SpriteAtlasIndexConfigT)
        };

        private IBundleInfoProvider _bundleInfoProvider;
        private ISpriteAtlasInfoProvider _spriteAtlasInfoProvider;
        private AssetsManager _assetsManager;
        
        public void Initialize(IContext context)
        {
            _bundleInfoProvider = context.Resolve<IBundleInfoProvider>();
            _spriteAtlasInfoProvider = context.Resolve<ISpriteAtlasInfoProvider>();
            
            var config = context.Resolve<IConfig>();
            Init(config);
            Config.OnConfigUpdated -= Init;
            Config.OnConfigUpdated += Init;
        }

        private void Init(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;
            
            Profiler.BeginSample("OnConfigLoaded: Getting bundle configs");
            var bundleIndex = config.Get<BundleIndexConfigT>();
            var bundleInfo = config.Get<BundleInfoConfigT>();
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: IBundleInfoProvider MergeConfigs");
            _bundleInfoProvider.MergeConfigs(bundleIndex, bundleInfo);
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: Getting sprite config");
            var spriteAtlasConfig = config.Get<SpriteAtlasIndexConfigT>();
            Profiler.EndSample();

            Profiler.BeginSample("OnConfigLoaded: ISpriteAtlasInfoProvider MergeConfig");
            _spriteAtlasInfoProvider.MergeConfig(spriteAtlasConfig);
            Profiler.EndSample();
            
            _assetsManager?.Restart();
        }

        public void SetAssetManager(AssetsManager assetsManager)
        {
            _assetsManager = assetsManager;
        }

        public void Restart()
        {
            Config.OnConfigUpdated -= Init;
        }
    }
}