using System;
using System.Collections;
using System.Collections.Generic;
using BBB;
using Cysharp.Threading.Tasks;
using RPC.Core;

namespace Core.Configs
{
    public interface IConfigResolver
    {
        string GetName();
        void Init(ConfigManifest manifest);
        UniTask InitAsync(ConfigManifest manifest, bool fromServer, Dictionary<string, ConfigVersionInfo> configs = null);
        void Parse(IDictionary<string, object> result, bool fromServer);
        void Parse(Dictionary<string, ConfigVersionInfo> configs, bool fromServer);
        UniTask ParseAsync(Dictionary<string, ConfigVersionInfo> configs, bool fromServer);
        IDictionary<string, T> GetConfig<T>();
        IDictionary GetConfig();
        Type GetConfigType();
        TDict1 GetCollection<TDict1>() where TDict1 : struct;
        void ChangeFileStorage(IFileStorage fileStorage);
    }
}