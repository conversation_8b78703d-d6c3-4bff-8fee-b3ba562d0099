using System;
using System.Collections.Generic;

namespace BBB.Core
{
    public class SimplePool<T> where T : class, new()
    {
        private readonly Stack<T> _stack = new();
        private Action<T> _resetAction;

        public void SetupResetAction(Action<T> resetAction)
        {
            _resetAction = resetAction;
        }

        public void Prewarm(int count)
        {
            for (int i = 0; i < count; i++)
            {
                var obj = new T();
                _resetAction.SafeInvoke(obj);
                _stack.Push(obj);
            }
        }

        public T Spawn()
        {
            if (_stack.Count > 0)
                return _stack.Pop();

            var obj = new T();
            _resetAction.SafeInvoke(obj);
            return obj;
        }

        public void Reset()
        {
            foreach (var t in _stack)
            {
                _resetAction.SafeInvoke(t);
            }
            _stack.Clear();
            _resetAction = null;
        }

        public void Release(T obj)
        {
            _resetAction.SafeInvoke(obj);
            _stack.Push(obj);
        }
    }
}