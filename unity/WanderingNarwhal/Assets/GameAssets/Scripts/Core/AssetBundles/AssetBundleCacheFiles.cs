using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.Core.AssetBundles
{
    public class AssetBundleCacheFiles
    {
        private HashSet<string> _persistentFiles;
        private HashSet<string> _remoteFilesInConfig;

        public async UniTask Init(PersistentBundleManifestProvider manifest, IBundleInfoProvider bundleInfoProvider, BundleLoadingSettings settings)
        {
            const string namePrefix = "downloadable/";
            var allBundleFilenames = await manifest.InitAllBundleFilenames();
            _persistentFiles = new HashSet<string>();
            foreach (var filename in allBundleFilenames)
            {
                if (filename.Contains(namePrefix))
                {
                    _persistentFiles.Add(settings.GetPersistentDataUrl(filename));
                }
            }

            var allBundles = bundleInfoProvider.GetAllBundles();
            _remoteFilesInConfig = new HashSet<string>();
            foreach (var bundle in allBundles)
            {
                if (bundle.Contains(namePrefix))
                {
                    _remoteFilesInConfig.Add(settings.GetPersistentDataUrl(bundle));
                }
            }
        }

        
        public void CleanupUnused()
        {
            _persistentFiles.ExceptWith(_remoteFilesInConfig);
            BDebug.Log(LogCat.AssetBundle, $"[AssetBundleCacheFiles] Cleanup Unused: {_persistentFiles.Count}");
            UniTask.Run(() => RemoveFiles(_persistentFiles));
        }

        public void CleanupAll()
        {
            BDebug.Log(LogCat.AssetBundle, $"[AssetBundleCacheFiles] Cleanup All: {_persistentFiles.Count}");
            UniTask.Run(() => RemoveFiles(_persistentFiles));
        }

        private void RemoveFiles(HashSet<string> filesInConfig)
        {
            foreach (var filename in filesInConfig)
            {
                BDebug.Log(LogCat.AssetBundle, $"Trying to Delete {filename}");
                try
                {
                    if (File.Exists(filename))
                        File.Delete(filename);
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                }
            }
        }
    }
}