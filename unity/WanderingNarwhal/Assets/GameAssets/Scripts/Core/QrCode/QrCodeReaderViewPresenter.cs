using System;
using System.Collections;
using System.Runtime.InteropServices;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.UI;
using ZXing;

namespace Core.QrCode
{
    public class QrCodeReaderViewPresenter : ModalsViewPresenter, IQrCodeReaderViewPresenter
    {
        [SerializeField] private RawImage _camTextureOutput;
        [SerializeField] private RectTransform _camTextureRect;
        [SerializeField] private RectTransform _frameOverlayHolder;

        [SerializeField] private string NEED_PERMISSION_TITLE_LOCKEY = "MODAL_NEED_PERMISSION_TITLE";
        [SerializeField] private string NEED_PERMISSION_MSG_LOCKEY = "MODAL_NEED_PERMISSION_MSG";

        [SerializeField] private Button _closeWebChatButton;
        [SerializeField] private Button _switchCameraButton;

        public event Action<string> OnQrCodeRead;
        public event Action OnPermissionDeniedEvent;

        private ILocalizationManager _localizationManager;
        private GenericModalFactory _genericModalFactory;

        private WebCamTexture _currentActiveCamera;

        private int _currentCameraIndex = -1;
        private int _currentDevicesCount;
        private bool _isFrontCamera;
        private bool _scanningQrCode;
        
        private int AvailableCamerasCount { get; set; }

        public override void Init(IContext previousContext)
        {
            _genericModalFactory = previousContext.Resolve<GenericModalFactory>();
            _localizationManager = previousContext.Resolve<ILocalizationManager>();
            
            _closeWebChatButton.ReplaceOnClick(CloseWebChatButtonHandler);
            _switchCameraButton.ReplaceOnClick(SwitchCameraButtonHandler);
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            StartCoroutine(GetQRCode());
        }
        
        private void CloseWebChatButtonHandler()
        {
            _scanningQrCode = false;
            TriggerOnCloseClicked();
        }

        private void SwitchCameraButtonHandler()
        {
            SwitchCamera();
        }

        private IEnumerator GetQRCode()
        {
            yield return ActivateCameraRoutine();
            
            IBarcodeReader barCodeReader = new BarcodeReader();
            _scanningQrCode = true;

            var color32 = new Color32[_currentActiveCamera.width * _currentActiveCamera.height];
            while (_scanningQrCode)
            {
                try
                {
                    if (color32.Length < _currentActiveCamera.width * _currentActiveCamera.height)
                    {
                        color32 = new Color32[_currentActiveCamera.width * _currentActiveCamera.height];
                    }
                    
                    var result = barCodeReader.Decode(_currentActiveCamera.GetPixels32(color32), _currentActiveCamera.width, _currentActiveCamera.height);
                    if (result != null)
                    {
                        _scanningQrCode = false;
                        
                        var qrCode = result.Text;

                        OnQrCodeRead?.Invoke(qrCode);
                    
                        if (!string.IsNullOrEmpty(qrCode))
                        {
                            BDebug.Log(LogCat.General, "DECODED TEXT FROM QR: " + qrCode);
                            break;
                        }
                    }
                }
                catch (Exception ex) { BDebug.LogError(LogCat.General, $"{ex.Message}\n{ex.StackTrace}"); }
                yield return null;
            }
                        
            TriggerOnCloseClicked();
        }

        private void SwitchCamera()
        {
            _currentCameraIndex++;
            StartCoroutine(ActivateCameraRoutine());
        }
        
        private IEnumerator ActivateCameraRoutine()
        {
#if PLATFORM_ANDROID
            if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.Camera))
            {
                UnityEngine.Android.Permission.RequestUserPermission(UnityEngine.Android.Permission.Camera);
                yield return null;
                while (!Application.isFocused)
                {
                    yield return null;
                }
                yield return WaitCache.Seconds(1f);

                if (!UnityEngine.Android.Permission.HasUserAuthorizedPermission(UnityEngine.Android.Permission.Camera))
                {
                    OnPermissionDeniedEvent?.Invoke();
                    var title = _localizationManager.getLocalizedText(NEED_PERMISSION_TITLE_LOCKEY);
                    var msg = _localizationManager.getLocalizedText(NEED_PERMISSION_MSG_LOCKEY);
                    _genericModalFactory.ShowWithOkButton(title, msg, OnOk);

                    void OnOk(int i)
                    {
                    }
                }
            }
#endif

            if (_currentActiveCamera != null)
            {
                StopCamera();
            }

            var devices = WebCamTexture.devices;
            _camTextureOutput.gameObject.SetActive(false);

            while (devices.Length == 0)
            {
                devices = WebCamTexture.devices;
                yield return null;
            }

            _camTextureOutput.gameObject.SetActive(true);

            if (devices.Length > _currentDevicesCount)
            {
                _currentDevicesCount = devices.Length;
            }

            var selectedDevice = default(WebCamDevice);

            if (_currentCameraIndex < 0)
            {
                var index = 0;
                foreach (var d in devices)
                {
                    if (string.IsNullOrEmpty(selectedDevice.name))
                    {
                        _currentCameraIndex = index;
                    }

                    if (!d.isFrontFacing)
                    {
                        _currentCameraIndex = index;
                        break;
                    }

                    index++;
                }
            }
            else if (_currentCameraIndex >= devices.Length)
            {
                _currentCameraIndex = 0;
            }

            selectedDevice = devices[_currentCameraIndex];

            _isFrontCamera = selectedDevice.isFrontFacing;
            AvailableCamerasCount = devices.Length;
            var width = Screen.width / 2;
            var height = Screen.height / 2;
            BDebug.Log(LogCat.General, $"[QrCode] Starting WebCamera w:{width} h:{height}");
            _currentActiveCamera = new WebCamTexture(selectedDevice.name, width, height, 30);
            _camTextureOutput.texture = _currentActiveCamera;
            _currentActiveCamera.Play();
            OrientCameraOutputTexture();
        }

        /// <summary>
        /// Set texture size and position according to current camera feed resolution and rotation.
        /// </summary>
        /// <remarks>
        /// This should be executed in update because camera state data is not synchronious,
        /// and it may change at any time (this depends on device specifics).
        /// </remarks>
        private void OrientCameraOutputTexture()
        {
            if (_currentActiveCamera == null) return;

            var texWidth = _currentActiveCamera.width;
            var texHeight = _currentActiveCamera.height;
            var rotation = _currentActiveCamera.videoRotationAngle;
            _camTextureRect.position = _frameOverlayHolder.position;
            var fitAreaWidth = _camTextureRect.parent.GetComponent<RectTransform>().rect.width;
            var fitAreaHeight = _frameOverlayHolder.rect.height;
            var rotationIndex = ((Mathf.RoundToInt(-rotation / 90f)) % 4 + 4) % 4;
            var visualRotatedTexWidth = rotationIndex == 1 || rotationIndex == 3 ? texHeight : texWidth;
            var visualRotateTexHeight = rotationIndex == 1 || rotationIndex == 3 ? texWidth : texHeight;

            float scaleFactor;
            if (((float)visualRotatedTexWidth / visualRotateTexHeight) >= (float)fitAreaWidth / fitAreaHeight)
            {
                scaleFactor = (float)fitAreaHeight / visualRotateTexHeight;
            }
            else
            {
                scaleFactor = (float)fitAreaWidth / visualRotatedTexWidth;
            }

            var texSizeDelta = new Vector2(texWidth * scaleFactor, texHeight * scaleFactor);
            var localScale = new Vector3(1,1,1);

#if UNITY_IOS
            // On ios for some reason default state is already mirrored. Reverse mirroring logic.
            if (!_isFrontCamera)
#else
            if (_isFrontCamera)
#endif
            {
                // compensate mirrored front camera feed.
                localScale.x *= rotationIndex == 0 || rotationIndex == 2 ? -1 : 1;
                localScale.y *= rotationIndex == 1 || rotationIndex == 3 ? -1 : 1;
            }

            _camTextureRect.sizeDelta = texSizeDelta;
            _camTextureRect.localScale = localScale;
            _camTextureRect.localRotation = Quaternion.Euler(0, 0, rotationIndex * 90);
        }

        private void Update()
        {
            OrientCameraOutputTexture();
        }

        private void StopCamera()
        {
            _camTextureOutput.texture = null;
            if (_currentActiveCamera != null)
            {
                _currentActiveCamera.Stop();
                _currentActiveCamera = null;
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            StopCamera();
        }
    }
}