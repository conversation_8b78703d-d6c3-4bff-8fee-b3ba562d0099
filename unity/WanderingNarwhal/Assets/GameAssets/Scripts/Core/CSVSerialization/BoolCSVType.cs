using System;
using System.Reflection;

namespace BBB.CSVSerialization
{
    public class BoolCSVType : CSVTypeBase
    {
        public override string TypeName
        {
            get { return "Bool"; }
        }

        public override bool IsMatch(PropertyInfo property)
        {
            return property.PropertyType == typeof(bool);
        }

        public override string Serialize(object obj)
        {
            return (bool)obj ? "1" : "0";
        }

        public override object Deserialize(string data, PropertyInfo property)
        {
            switch (data)
            {
                case "1": return true;
                case "0": return false;
            }

            throw new CSVParseException(data, property.PropertyType);
        }

        public override bool IsMatch(FieldInfo field)
        {
            return field.FieldType == typeof(bool);
        }

        public override object Deserialize(string data, FieldInfo field)
        {
            switch (data)
            {
                case "1": return true;
                case "0": return false;
            }

            throw new CSVParseException(data, field.FieldType);
        }
    }
}