using System.Collections.Generic;

namespace BBB.Core.Analytics
{
    public class SaveGameSelectedEvent : BaseEvent
    {
        public SaveGameSelectedEvent(bool forceSyncUp, bool useRemote, 
            PlayerProgress remoteProgress, PlayerProgress localProgress, 
            bool applyChangesImmediately, string localUid, string serverUid)
        {
            var dict = new Dictionary<string, object>
            {
                {CustomEventParameters.ForceSyncUp, forceSyncUp},
                {CustomEventParameters.UseRemote, useRemote},
                
                {CustomEventParameters.RemoteMaxLevel, remoteProgress.MaxLevel},
                {CustomEventParameters.LocalMaxLevel, localProgress.MaxLevel},
                
                {CustomEventParameters.ApplyChangesImmediately, applyChangesImmediately},
                {CustomEventParameters.LocalUid, localUid},
                {CustomEventParameters.ServerUid, serverUid},
            };
            
            Initialize(EventNames.SaveGameSelected, dict);
        }
    }
}