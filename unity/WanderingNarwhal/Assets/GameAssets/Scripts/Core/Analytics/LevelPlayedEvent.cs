using System.Collections.Generic;
using System.Globalization;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Core.Analytics
{
    /// <summary>
    /// Template for played level analytics
    /// </summary>
    public class LevelPlayedEvent : BaseEvent
    {
        public LevelPlayedEvent(int levelResult, int timeSpent,
            int initialMoves, int movesLeft,
            int countBoostersUsed, int countSuperBoosts,
            List<string> inventoryPowerUps,
            AssistState originalAssistState,
            AssistState progressedAssistState,
            int difficulty, string city, double levelNum, string levelUid,
            int numPlayed, int movesBoughtCount,
            string gameEvents,
            Dictionary<string, int> gameEventsMultiplier,
            string lossReason,
            double totalUsdSpent,
            int coinsSpent,
            int livesRemaining, int shuffleCount,
            int adBonus, float skillModifier,
            string replayData,
            string levelVersion,
            int playerStreak,
            int butlerStreak,
            int powerUpsCreated,
            int powerUpsUsed,
            int simpleTilesDestroyed,
            int secondsUntilFirstMove,
            int numberOfWastedMove,
            Dictionary<string, int> boostersUsed,
            int numberOfTimesWheelSpinsBought,
            int movesWonFromWheelSpins,
            int movesAllowed,
            string assetVersion,
            bool sdbActive)
        {
            var dict = new Dictionary<string, object>
            {
                { CustomEventParameters.LevelOutcome, levelResult },
                { CustomEventParameters.Duration, timeSpent },
                { CustomEventParameters.InitialMoves, initialMoves },
                { CustomEventParameters.MovesLeft, movesLeft },
                { CustomEventParameters.MovesAllowed, movesAllowed },

                { CustomEventParameters.CountBoostersUsed, countBoostersUsed },
                { CustomEventParameters.BoostersUsed, boostersUsed },
                { CustomEventParameters.CountSuperBoosts, countSuperBoosts },
                { CustomEventParameters.EnabledPowerups, string.Join(",", inventoryPowerUps) },

                { CustomEventParameters.Difficulty, difficulty },
                { CustomEventParameters.City, city },
                { CustomEventParameters.LevelNum, levelNum },
                { CustomEventParameters.LevelUid, levelUid },

                { CustomEventParameters.NumberTimesPlayed, numPlayed },
                { CustomEventParameters.NumberOfTimes5MovesBought, movesBoughtCount },

                { CustomEventParameters.EventList, gameEvents },
                { CustomEventParameters.EventScoreMultiplier, gameEventsMultiplier },

                { CustomEventParameters.LossReason, lossReason },
                { CustomEventParameters.UsdSpent, totalUsdSpent },
                { CustomEventParameters.CoinsSpent, coinsSpent },

                { CustomEventParameters.LivesRemaining, livesRemaining },
                { CustomEventParameters.ShuffleCount, shuffleCount },
                { CustomEventParameters.AdBonus, adBonus },
                { CustomEventParameters.PlayerMoves, replayData},
                
                { CustomEventParameters.LevelVersion, levelVersion },
                { CustomEventParameters.LayoutVersion, assetVersion },
                { CustomEventParameters.PlayerStreak, playerStreak },
                { CustomEventParameters.FennecsPerks, butlerStreak },
                { CustomEventParameters.SdbActive, sdbActive},
                { CustomEventParameters.PowerUpsCreated, powerUpsCreated },
                { CustomEventParameters.PowerUpsUsed, powerUpsUsed },
                { CustomEventParameters.SimpleTilesDestroyed, simpleTilesDestroyed },
                { CustomEventParameters.SecondsUntilFirstMove, secondsUntilFirstMove },
                { CustomEventParameters.NumberOfWastedMoves, numberOfWastedMove },
                { CustomEventParameters.NumberOfTimesWheelSpinsBought, numberOfTimesWheelSpinsBought},
                { CustomEventParameters.NumberOfMovesFromWheelSpins, movesWonFromWheelSpins},
                
                { CustomEventParameters.AssistAimValue, AssistParams.OriginalAimAtWinningLevel },
                { CustomEventParameters.AssistOnLossStreak, AssistParams.AssistOnLossStreak },
                { CustomEventParameters.SkillModifier, skillModifier.ToString("F2", CultureInfo.InvariantCulture) }
            };

            var totalProgressPercentage = 0;
            var totalGoalCount = 0;

            var sb = new System.Text.StringBuilder();

            foreach (var kvp in originalAssistState)
            {
                totalGoalCount++;
                var originalValue = originalAssistState.GetValue(kvp.Key);
                var updatedValue = progressedAssistState.GetValue(kvp.Key);

                var progressPercentage = (int) (updatedValue / originalValue * 100);

                sb.Append(CustomEventParameters.Goal)
                    .Append('_')
                    .Append((GoalType)kvp.Key);

                dict[sb.ToString()] = (int) originalValue;
                sb.Clear();

                sb.Append(CustomEventParameters.GoalPercentage)
                    .Append('_')
                    .Append((GoalType)kvp.Key);

                dict[sb.ToString()] = progressPercentage;
                sb.Clear();

                totalProgressPercentage += progressPercentage;
            }

            if (totalGoalCount > 0)
            {
                var overallProgress = totalProgressPercentage / totalGoalCount;
                sb.Append(CustomEventParameters.GoalOverallProgress);
                dict[sb.ToString()] = overallProgress;
                sb.Clear();
            }
            
            Initialize(EventNames.Level, dict);
        }
    }
}