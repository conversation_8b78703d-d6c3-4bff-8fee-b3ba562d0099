namespace BBB.InputConsole
{
    public class HelpConsoleHandler : ConsoleHandlerBase
    {
        public override string CommandName => "help";

        public override string[] Aliases => new[]
        {
            "--?",
            "-?",
            "?",
            "--help",
            "-help",
            "help"
        };

        public override string Handle(string argument, InputConsoleView caller)
        {
            return "--help\n" +
                "-help\n" +
                "help\n" +
                "--?\n" +
                "-?\n" +
                "? : print list of commands\n" +
                "actgo [first|all] : activate gameobject\n" +
                "deactgo [first|all] : deactivate gameobject\n"+
                "crawler [optional timescale] : start autoplay crawler bot";
        }
    }
}