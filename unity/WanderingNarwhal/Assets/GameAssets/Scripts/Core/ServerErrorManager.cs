using System;
using BBB;
using BBB.DI;
using BBB.Generic.Modal;
using Bebopbee.Core.Systems.GamemessengerBase;
using Bebopbee.Core.Systems.RpcCommandManager;
using UnityEngine;

namespace BebopBee
{
    public class ServerErrorManager : IGameMessageListener, IServerErrorHandler, IContextInitializable, IContextReleasable
    {
        private IEventDispatcher _dispatcher;
        private Action _callback = delegate { };
        private GenericModalFactory _genericModalFactory;
        private ILocalizationManager _localization;
        private IRestartable _restarter;
        private const string ForceSyncUpTitleLocKey = "FORCE_SYNC_TITLE";
        private const string ForceSyncUpDescriptionLocKey = "FORCE_SYNC_DESC";

        public void InitializeByContext(IContext context)
        {
            _dispatcher = context.Resolve<IEventDispatcher>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _localization = context.Resolve<ILocalizationManager>();
            _restarter = context.Resolve<IRestartable>();
            _dispatcher.RemoveListener<ServerSyncRequiredEvent>(HandleServerSyncRequiredEvent);
            _dispatcher.AddListener<ServerSyncRequiredEvent>(HandleServerSyncRequiredEvent);
        }

        public void ReleaseByContext(IContext context)
        {
            _dispatcher.Unsubscribe(this);
        }
        
        private void HandleServerSyncRequiredEvent(ServerSyncRequiredEvent obj)
        {
            _callback = obj.Arg0;
            HandleServerSyncRequired();
        }

        public void OnMessage(IGameMessage message)
        {
            if (message is ServerErrorMessage result)
            {
                result.Apply(this);
            }
        }

        private void HandleServerSyncRequired()
        {
            var title = _localization.getLocalizedText(ForceSyncUpTitleLocKey);
            var description = _localization.getLocalizedText(ForceSyncUpDescriptionLocKey);

            _genericModalFactory.ShowWithOkButton(title, description, OnModalCallback);
            Time.timeScale = 0;
            return;

            void OnModalCallback(int idx)
            {
                _callback();
                Time.timeScale = 1;
                _restarter.Restart();
            }
        }

        public void HandleForceSyncUp()
        {
            _dispatcher.TriggerEvent(_dispatcher.GetMessage<ForceSyncEvent>());
        }
    }
}