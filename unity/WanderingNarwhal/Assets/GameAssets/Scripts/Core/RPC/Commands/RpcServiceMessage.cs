using Bebopbee.Core.Systems.GamemessengerBase;
using BebopBee;

namespace Core.Rpc.Commands
{
    public abstract class RpcServiceCommandBase
    {
        public abstract void Execute(IRPCService instance);
    }
    
    public class RpcServiceMessage : IGameMessage
    {
        private readonly RpcServiceCommandBase _command;

        public RpcServiceMessage(RpcServiceCommandBase command)
        {
            _command = command;
        }

        public void Apply(IRPCService rpcService)
        {
            _command.Execute(rpcService);
        }
        public bool IsValid()
        {
            return _command != null;
        }
    }
}