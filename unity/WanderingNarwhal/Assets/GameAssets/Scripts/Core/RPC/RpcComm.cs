using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using BBB;
using BBB.Core;
using BBB.DI;
using BebopBee;
using Bebopbee.Core.Systems.RpcCommandManager;
using GameAssets.Scripts.Core;
using ProtoBuf;
using RPC.Batch;
using RPC.Command;
using RSG;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Profiling;

namespace Core.RPC
{
    // public class CustomRpcUploader : Uploader
    public class CustomRpcDownloader : DownloadHandlerScript
    {
        private MemoryStreamPoolAble _stream;
        private int _received;
        private MemoryStreamPool _pool;
        public int ContentLength { get; private set; }

        public MemoryStream MemoryStream
        {
            get { return _stream.MemoryStream; }
        }

        public CustomRpcDownloader(MemoryStreamPool pool)
        {
            _pool = pool;
            _received = 0;
            ContentLength = -1;
        }

        protected override bool ReceiveData(byte[] data, int dataLength)
        {
            if (data == null || data.Length == 0) return false;
            if (_stream == null)
                _stream = _pool.Obtain();
            if (_received + dataLength > _stream.MemoryStream.Capacity)
            {
                var prevSize = _stream.MemoryStream.Capacity;
                _stream.Resize((int)((_received + dataLength) * 1.25f));
                //uncomment to check allocations
                //UnityEngine.Debug.LogWarning($"Resizing RpcComm pool from {prevSize} to {_stream.MemoryStream.Capacity}" );

            }
            _received += dataLength;
            _stream.MemoryStream.Write(data, 0, dataLength);
            return true;
        }

        [Obsolete("Use ReceiveContentLengthHeader")]
        protected override void ReceiveContentLength(int contentLength)
        {
            ContentLength = contentLength;
            _stream ??= _pool.ObtainOfSize(contentLength);
        }

        protected override void CompleteContent()
        {
            ContentLength = _received;
        }

        protected override float GetProgress()
        {
            return ContentLength <= 0 ? 0 : Mathf.Clamp01(_received / (float) ContentLength);
        }
        
        public new void Dispose()
        {
            _stream?.Free();
            base.Dispose();
        }
        
    }

    public interface IRpcComm
    {
        IPromise<RpcCommResponse> ExecuteRequest(IEnumerable<string> commandNames, byte[] bytes);
        Payload GetPayload();
        string GetSessionId();
    }

    public class RpcCommResponse
    {
        public ResultType ResponseStatus;
        public byte[] ResponseContent;
        public MemoryStream ResponseStream;
        public int ResponseSize;
    }

    public class RpcComm : IRpcComm, IContextInitializable
    {
        private const string RpcServiceName = "rpc/v2";

        private static string _serverUrl;
        private readonly Payload _payload;
        private bool _noConnectionStopSending;

        private ICoroutineExecutor _coroutineExecutor;
        private int _commandCounter;
        private string _sessionId;
        private MemoryStreamPool _streamPool;
        private const int Timeout = GameSettings.LongRequestTimeout;
        private static string _userIdOverride;
        private static string _deviceIdOverride;
        
        private static readonly RpcCommResponse NoConnectionResponse = new()
        {
            ResponseStatus = ResultType.NoInternet,
            ResponseContent = null
        };

        public static void SetServerUrl(string url)
        {
            _serverUrl = url;
            BDebug.LogFormat(LogCat.Rpc, "RPC url = {0}", url);
        }

        public RpcComm()
        {
            _payload = new Payload()
            {
                DModel = SystemInfo.deviceModel,
                DName = SystemInfo.deviceName,
                SysMem = SystemInfo.systemMemorySize.ToString(),
                GDevice = SystemInfo.graphicsDeviceID.ToString(),
                GName = SystemInfo.graphicsDeviceName,
                GVendor = SystemInfo.graphicsDeviceVendor,
                GVendorId = SystemInfo.graphicsDeviceVendorID.ToString(),
                GDeviceVersion = SystemInfo.graphicsDeviceVersion,
                GMem = SystemInfo.graphicsMemorySize.ToString(),
                Os = SystemInfo.operatingSystem,
                Cpu = SystemInfo.processorCount.ToString(),
                CpuType = SystemInfo.processorType,
                Platform = PlatformUtil.GetPlatform(),
                Tz = Util.GetTimeZone(),
                BundleIdentifier = PlatformUtil.GetAppIdentifier(),
                BundleVersion = PlatformUtil.GetAppVersion(),
                BundleDisplayName = PlatformUtil.GetBundleDisplayName(),
                BuildNumber = CurrentBundleVersion.GetBuildCode(),
                Language = LanguageHelper.GetISOCodeFromSystemLanguage(),
                Country = PlatformUtil.GetCurrentCountryCode(),
                IsDebug = AppDefinesConverter.BbbDebug,
                TzOffset = Util.GetTimeZoneOffset()
            };
            Application.RequestAdvertisingIdentifierAsync((advertisingId, enable, error) => { _payload.AdvertisingId = advertisingId; });
        }

        public void InitializeByContext(IContext context)
        {
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _streamPool = new MemoryStreamPool(1, 100 * 1024)
            {
                Name = "RpcComm Pool"
            };
            _streamPool.Prewarm();
        }

        public IPromise<RpcCommResponse> ExecuteRequest(IEnumerable<string> commandNames, byte[] bytes)
        {
            var promise = new Promise<RpcCommResponse>();

            if (string.IsNullOrEmpty(_serverUrl))
            {
                promise.Reject(new Exception("Server URL Is Missing"));
            }

            if (!_noConnectionStopSending)
            {
                _coroutineExecutor.StartCoroutine(_DoRequest(commandNames, promise, bytes));
            }

            return promise;
        }

        public Payload GetPayload()
        {
            UpdatePayload();
            return _payload;
        }

        public string GetSessionId()
        {
            return _sessionId;
        }

        //the only difference between sync and not sinc is the line yield www/while(!www.isDone), refactor properly
        //when there's time
        private IEnumerator _DoRequest(IEnumerable<string> commandNames, IPendingPromise<RpcCommResponse> promise, byte[] bytes)
        {
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                BDebug.LogWarning(LogCat.General, "RpcComm No Connection");
                promise.Resolve(NoConnectionResponse);
            }
            else
            {
                var downloadHandler = new CustomRpcDownloader(_streamPool);
                var uploadHandler = new UploadHandlerRaw(bytes);
                var cancelled = false;
                var www = new UnityWebRequest(_serverUrl + RpcServiceName, UnityWebRequest.kHttpVerbPUT, downloadHandler, uploadHandler);
                ConnectivityStatusManager.ConnectivityChanged -= OnConnectivityChanged;
                ConnectivityStatusManager.ConnectivityChanged += OnConnectivityChanged;
                www.timeout = Timeout;
                {
                    www.SendWebRequest();
                    while (!www.isDone)
                    {
                        if (cancelled)
                        {
                            www.Abort();
                            break;
                        }

                        yield return null;
                    }
                    ConnectivityStatusManager.ConnectivityChanged -= OnConnectivityChanged;

                    if (cancelled ||
                        www.result == UnityWebRequest.Result.ConnectionError || 
                        www.result == UnityWebRequest.Result.ProtocolError ||
                        www.result == UnityWebRequest.Result.DataProcessingError)
                    {
                        if (www.responseCode >= 500)
                        {
                            BDebug.LogError(LogCat.Rpc, $"RpcComm error: {www.error}");
                        }
                        else
                        {
                            BDebug.LogWarning(LogCat.Rpc, $"RpcComm error: {(cancelled ? "Cancelled" : www.error)}");
                        }
                    
                        promise.Resolve(NoConnectionResponse);
                    }
                    else
                    {

                        var rpcCommResponse = new RpcCommResponse
                        {
                            ResponseStatus = ResultType.Success,
                            ResponseStream = ((CustomRpcDownloader) www.downloadHandler).MemoryStream,
                            ResponseSize = ((CustomRpcDownloader) www.downloadHandler).ContentLength
                        };

                        Profiler.BeginSample($"RpcComm response to {string.Join(", ", commandNames)}");
                        promise.Resolve(rpcCommResponse);
                        Profiler.EndSample();
                    }
                }
                www.Dispose();
                downloadHandler.Dispose();
                uploadHandler.Dispose();

                void OnConnectivityChanged(bool status)
                {
                    if (status) return;

                    BDebug.LogWarning(LogCat.General, "RpcComm. Connection Changed - aborting request");
                    cancelled = true;
                }
            }
        }

        private void UpdatePayload()
        {
            _payload.Uid = _userIdOverride ?? MultiDevice.GetUserId();
            _payload.DeviceUid = _deviceIdOverride ?? MultiDevice.GetDeviceId();
            _payload.Session = _sessionId;
            _payload.Time = Util.UnixLocalTimestamp().ToString(CultureInfo.InvariantCulture);
            _userIdOverride = null; //override is only used once and then discarded
            _deviceIdOverride = null;
        }

        //FOR DEBUG / LOAD TEST ONLY
        public static void SetUserIdOverride(string userIdOverride)
        {
            _userIdOverride = userIdOverride;
        }

        public static void SetDeviceIdOverride(string deviceIdOverride)
        {
            _deviceIdOverride = deviceIdOverride;
        }
    }
}