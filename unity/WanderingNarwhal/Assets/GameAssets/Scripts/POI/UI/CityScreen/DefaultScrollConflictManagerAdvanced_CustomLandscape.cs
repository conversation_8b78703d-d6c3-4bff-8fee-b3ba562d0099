using BBB.DI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using UnityEngine.UI.Extensions;

namespace BBB.UI.POI
{
    public class DefaultScrollConflictManagerAdvanced_CustomLandscape : <PERSON><PERSON><PERSON><PERSON>Beh<PERSON>our, IBeginDragHandler, IEndDragHandler, IDragHandler
    {
        public ScrollRect ParentScrollRect;
        public ScrollSnapBase SnapScroll;
        private ScrollRect _scrollRect;
        //This tracks if the other one should be scrolling instead of the current one.
        private bool _scrollOther;
        //This tracks wether the other one should scroll horizontally or vertically.
        private bool _scrollOtherHorizontally;
        private IOrientationTracker _orientationTracker;
        
        public void Init(IContext context)
        {
            _orientationTracker = context.Resolve<IOrientationTracker>();
        }
        
        // ReSharper disable once UnusedMember.Local
        protected override void OnEnable()
        {
            //Get the current scroll rect so we can disable it if the other one is scrolling
            _scrollRect = GetComponent<ScrollRect>();

            //If the current scroll Rect has the vertical checked then the other one will be scrolling horizontally.
            _scrollOtherHorizontally = _scrollRect.vertical;
            //Check some attributes to let the user know if this wont work as expected
            if(ParentScrollRect == null)
            {
                Debug.Log("ParentScrollRect == null");
                return;
            }

            if(_scrollOtherHorizontally)
            {
                if(_scrollRect.horizontal)
                    Debug.Log("You have added the SecondScrollRect to a scroll view that already has both directions selected");
                if(!ParentScrollRect.horizontal)
                    Debug.Log("The other scroll rect doesnt support scrolling horizontally");
            }
            else if(!ParentScrollRect.vertical)
            {
                Debug.Log("The other scroll rect doesnt support scrolling vertically");
            }
        }

        //IBeginDragHandler
        public void OnBeginDrag(PointerEventData eventData)
        {
            //Get the absolute values of the x and y differences so we can see which one is bigger and scroll the other scroll rect accordingly
            var difference = eventData.position - eventData.pressPosition;
            difference = _orientationTracker.GetOrientationRotatedVector(difference);
            
            var horizontal = Mathf.Abs(difference.x);
            var vertical = Mathf.Abs(difference.y);
            if(_scrollOtherHorizontally)
            {
                if(horizontal > vertical)
                {
                    // _myScrollRect.movementType = LoopScrollRect.MovementType.Unrestricted;

                    _scrollOther = true;
                    //disable the current scroll rect so it doesnt move.
                    _scrollRect.enabled = false;
                    ParentScrollRect.OnBeginDrag(eventData);
                    if(SnapScroll != null)
                    {
                        SnapScroll.OnBeginDrag(eventData);
                    }
                    else
                    {
                        Debug.Log("SnapScroll == null");
                    }
                }
            }
            else if(vertical > horizontal)
            {
                _scrollOther = true;
                //_myScrollRect.movementType = LoopScrollRect.MovementType.Unrestricted;
                //disable the current scroll rect so it doesnt move.
                _scrollRect.enabled = false;
                ParentScrollRect.OnBeginDrag(eventData);
                if(SnapScroll != null)
                    SnapScroll.OnBeginDrag(eventData);
            }
        }

        //IEndDragHandler
        public void OnEndDrag(PointerEventData eventData)
        {
            if(_scrollOther)
            {
                // _myScrollRect.movementType = LoopScrollRect.MovementType.Elastic;

                _scrollOther = false;
                _scrollRect.enabled = true;
                ParentScrollRect.OnEndDrag(eventData);
                if(SnapScroll != null)
                {
                    var horScroll = SnapScroll as HorizontalScrollSnap;
                    var verScroll = SnapScroll as VerticalScrollSnap;
                    if(verScroll != null)
                        verScroll.OnEndDrag(eventData);
                    if(horScroll != null)
                        horScroll.OnEndDrag(eventData);
                }
            }
        }

        //IDragHandler
        public void OnDrag(PointerEventData eventData)
        {
            if(_scrollOther)
            {
                ParentScrollRect.OnDrag(eventData);
                if(SnapScroll != null)
                    SnapScroll.OnDrag(eventData);
            }
        }
    }
}