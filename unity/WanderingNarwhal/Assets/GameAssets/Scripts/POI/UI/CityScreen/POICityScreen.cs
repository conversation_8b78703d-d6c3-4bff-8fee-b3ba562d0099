using BBB.DI;
using UnityEngine;
using UnityEngine.UI.Extensions;
using System.Collections.Generic;
using BBB.POI;
using UnityEngine.UI;
using DG.Tweening;
using PBConfig;

namespace BBB.UI.POI
{
    public class POICityScreen : BbbMonoBehaviour, IPOIScreen, IContextInitializable, IContextReleasable
    {
        [SerializeField] private HorizontalScrollSnap _horizontalScrollRectSnap;
        [SerializeField] private Transform _contentTr;
        [SerializeField] private Image _locationImage;

        private readonly Vector3 _normalScale = Vector3.one;
        private readonly Vector3 _lesserScale = new(1, 0.8f, 1);

        private List<FBConfig.LocationConfig> _locationConfigs;

        private POIResourceProvider _poiResourceProvider;
        private LevelLocationManager _levelLocationManager;
        private POIGeneralButtonsController _mainGeneralButtonsController;
        private POIScreensSystem _poiScreenSystem;
        private POIScreenController _poiScreenController;
        private POIManager _poiManager;
        private IContext _context;

        private readonly List<string> _tempUidsList = new();

        public void InitializeByContext(IContext context)
        {
            _tempUidsList.Clear();
            _context = context;
            _poiScreenController = context.Resolve<POIScreenController>();
            _poiManager = context.Resolve<POIManager>();
            _poiResourceProvider = context.Resolve<POIResourceProvider>();
            _levelLocationManager = context.Resolve<LevelLocationManager>();
            _mainGeneralButtonsController = context.Resolve<POIGeneralButtonsController>();
            _poiScreenSystem = context.Resolve<POIScreensSystem>();
            _horizontalScrollRectSnap.Awake();
#if BBB_TEST
            AddTest();
#endif
        }

#if BBB_TEST
        private void AddTest()
        {
            Image image = GetComponentInChildren<Image>();
            image.raycastTarget = true;
            Button button = gameObject.AddComponent<Button>();
            button.onClick.AddListener(GotoTripstagram);
        }
#endif

        private void GotoTripstagram()
        {
            _poiScreenSystem.SwitchScreen(POIScreenType.Tripstagram, allowNavigation: true);
        }
        
        public void InitializeByScreenSystem()
        {
            
        }

        public void OnShow(bool allowNavigation = false)
        {
            var i = 0;
            var locationUid = _poiScreenSystem.LocationUid;
            _locationConfigs = _levelLocationManager.GetNeighbourLocations(locationUid);

            _tempUidsList.Clear();
            foreach (var locationConfig in _locationConfigs)
            {
                var location = _poiManager.GetLocation(locationConfig.Uid);
                _tempUidsList.AddRange(location.AllUnlockedContentUids);
            }
            
            _poiScreenController.RefreshLikes(_tempUidsList);
            _tempUidsList.Clear();
            
            foreach(var locationConfig in _locationConfigs)
            {
                if(_locationConfigs.Count > _contentTr.childCount)
                {
                    var prefab = _poiResourceProvider.GetPreloaded<GameObject>(POIResKeys.POICityContent);
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _contentTr);
                    UnityEngine.Profiling.Profiler.EndSample();
                    var controller = go.GetComponent<POICityContentController>();
                    if(i != 0)
                        go.transform.localScale = _lesserScale;
                        
                    controller.Init(_context, locationConfig.Uid, _horizontalScrollRectSnap.ScrollRect);
                }
                else
                {
                    var location = _contentTr.GetChild(i);
                    var poiItem = location.GetComponent<POICityContentController>();
                    if(i != 0)
                    {
                        location.transform.localScale = _lesserScale;
                    }

                    poiItem.Init(_context, locationConfig.Uid, _horizontalScrollRectSnap.ScrollRect);
                }

                i++;
            }
            
            OnShowProcessFinished();
		}

        private void OnShowProcessFinished()
        {
            var locationUid = _poiScreenSystem.LocationUid;
            var index = _locationConfigs.IndexOf(lc => lc.Uid == locationUid);
            if (index < 0) index = 0;
            _horizontalScrollRectSnap.StartingScreen = index;
            _horizontalScrollRectSnap.CurrentPage = index;
            _horizontalScrollRectSnap.UpdateLayout();
            RefreshViewForPage(index);
            
            _horizontalScrollRectSnap.OnSelectionPageChangedEvent.RemoveAllListeners();
            _horizontalScrollRectSnap.OnSelectionPageChangedEvent.AddListener(OnPageChanged);

            _mainGeneralButtonsController.SetBackButtonActive(false);
            _mainGeneralButtonsController.SetCloseButtonActive(false);
        }

        public void OnHide()
        {
            _horizontalScrollRectSnap.OnSelectionPageChangedEvent.RemoveAllListeners();
			_mainGeneralButtonsController.SetBackButtonActive(false);
			_mainGeneralButtonsController.SetCloseButtonActive(true);
        }

        public void OnExit()
        {
            _horizontalScrollRectSnap.OnSelectionPageChangedEvent.RemoveAllListeners();
            _mainGeneralButtonsController.SetBackButtonActive(false);
            _mainGeneralButtonsController.SetCloseButtonActive(false);
        }

        private void RefreshViewForPage(int id)
        {
            foreach(Transform item in _contentTr)
            {
                item.localScale = _lesserScale;
                item.GetComponent<POICityContentController>().DisableScrollers();
            }
            
            _contentTr.GetChild(id).localScale = _normalScale;
            
            var poiGlobeScreen = _context.Resolve<POIGlobeScreen>();
            var go = _contentTr.GetChild(id);
            go.GetComponent<POICityContentController>().EnableScrollers();
            
            
            go.GetComponent<POIEntityPointMachine>().TryPoint();
            
            var currentLocationUid = _locationConfigs[id].Uid; 
            _locationImage.sprite = poiGlobeScreen.GetBgSpriteByUid(currentLocationUid);
        }
        
        private void OnPageChanged(int id)
        {
            foreach(Transform item in _contentTr)
            {
                item.DOScale(_lesserScale, 0.25f);
                var controller = item.GetComponent<POICityContentController>();
                controller.DisableScrollers();
            }

            _contentTr.GetChild(id).DOScale(_normalScale, 0.25f);
            var selectedController =  _contentTr.GetChild(id).GetComponent<POICityContentController>();
            selectedController.EnableScrollers();
            
            var poiGlobeScreen = _context.Resolve<POIGlobeScreen>();
            var currentLocationUid = _locationConfigs[id].Uid; 
            _locationImage.sprite = poiGlobeScreen.GetBgSpriteByUid(currentLocationUid);
            
            var screenSystem = _context.Resolve<POIScreensSystem>();
            screenSystem.LocationUid = currentLocationUid;
            screenSystem.RefreshNavigationBar();
        }

        public void ReleaseByContext(IContext context)
        {
            _context = null;
        }
    }
}