using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.Navigation;
using BBB.POI;
using Bebopbee.Core.UI;
using GameAssets.Scripts.Messages;
using PBConfig;
using UnityEngine;

namespace BBB.UI.POI
{
    public class POIScreenChanged : Message<POIScreenType>
    {
    }

    public enum POIScreenType
    {
        None = -1,
        Globe = 0,
        Location = 1,
        Entity = 2,
        Tripstagram = 3
    }

    public class POIScreensSystem : BbbMonoBehaviour, IContextInitializable, ICoroutineExecutor
    {
        [SerializeField] private UiNavigationBarControllerIconAndText _navigationBar;
        [SerializeField] private Touchable _inputBlocker;

        private IEventDispatcher _eventDispatcher;
        private IScreensBuilder _screensBuilder;
        private ILocalizationManager _localizationManager;
        private IConfig _config;
        private POIScreenType _currentScreenType = (POIScreenType) (-1);

        private string _entityUid;
        private string _locationUid;

        public string LocationUid
        {
            get => _locationUid;
            set
            {
                _locationUid = value;
                _entityUid = null;
            }
        }

        public string EntityUid
        {
            get => _entityUid;
            set
            {
                _entityUid = value;
                var entity = _poiManager.GetEntity(value);

                if (entity != null)
                {
                    _locationUid = entity.LocationUid;
                }
            }
        }

        public POIScreenParameter InitialScreenParameter { get; private set; }

        private Dictionary<POIScreenType, IPOIScreen> _screenControllers;
        private Stack<POIScreenType> _tabStack;
        private POIManager _poiManager;
        private SafeMapOpener _safeMapOpener;
        private IScreensManager _screensManager;

        public void InitializeByContext(IContext context)
        {
            _entityUid = null;
            _locationUid = null;
            _currentScreenType = (POIScreenType) (-1);

            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _poiManager = context.Resolve<POIManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _screensManager = context.Resolve<IScreensManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _config = context.Resolve<IConfig>();

            _safeMapOpener = new SafeMapOpener(context);

            _navigationBar.Init(context);

            _screenControllers ??= new Dictionary<POIScreenType, IPOIScreen>
            {
                { POIScreenType.Globe, context.Resolve<POIGlobeScreen>() },
                { POIScreenType.Location, context.Resolve<POICityScreen>() },
                { POIScreenType.Tripstagram, context.Resolve<POITripstagramScreen>() }
            };

            if (_tabStack == null)
            {
                _tabStack = new Stack<POIScreenType>();
            }
            else
            {
                _tabStack.Clear();
            }

            InitialScreenParameter = context.Resolve<PoiParameterStack>().PopParameter();
        }

        public void InitializeScreens()
        {
            foreach (var screen in _screenControllers.Values)
                screen.InitializeByScreenSystem();
        }

        private NavigationTabParams GetTabParams(POIScreenType screenType)
        {
            string tabName = null;
            string iconImageName = null;

            switch (screenType)
            {
                case POIScreenType.Globe:
                {
                    tabName = _localizationManager.getLocalizedText("EARTH_LOC");
                    break;
                }
                case POIScreenType.Location:
                {
                    var locationConfigs = _config.Get<FBConfig.LocationConfig>();
                    var config = locationConfigs.Get(LocationUid, default);

                    if (!config.IsNull())
                    {
                        tabName = _localizationManager.getLocalizedText(config.LocationName);
                        iconImageName = config.IconName;
                    }

                    break;
                }
                case POIScreenType.Entity:
                {
                    var poiEntity = _poiManager.GetEntity(EntityUid);

                    if (poiEntity == null)
                        throw new InvalidOperationException($"Trying to open tab for entity {EntityUid} but it is not found");

                    tabName = poiEntity.Name;
                    if (!string.IsNullOrWhiteSpace(poiEntity.LocationUid))
                    {
                        iconImageName = poiEntity.IconName;
                    }

                    break;
                }
                case POIScreenType.Tripstagram:
                    return null;
                default:
                    throw new ArgumentOutOfRangeException("screenType", screenType, null);
            }

            var result = new NavigationTabParams
            {
                ScreenType = screenType,
                Name = tabName,
                IconImageName = iconImageName,
                PressAction = () => { SwitchScreen(screenType); }
            };

            return result;
        }

        public POIScreenType GetCurrentTabType()
        {
            var type = POIScreenType.Globe;
            if (_tabStack.Count != 0)
            {
                type = _tabStack.Peek();
            }

            return type;
        }

        private void SetNavigationBar(POIScreenType screen, bool hideEntityTextStatus = false)
        {
            if (_tabStack.Count == 0)
            {
                for (var i = 0; i <= (int) screen; i++)
                {
                    if(i == (int)POIScreenType.Entity && hideEntityTextStatus)
                        continue;
                    _tabStack.Push((POIScreenType) i);
                }
            }
            else if (_tabStack.Peek() < screen)
            {
                _tabStack.Push(screen);
            }
            else
            {
                while (_tabStack.Peek() > screen)
                {
                    _tabStack.Pop();
                }
            }
        }

        public void RefreshNavigationBar()
        {
            var navTabParams = new List<NavigationTabParams>();

            // Fill tabs in order: Globe->Location->Entity->Content.
            var tabArray = _tabStack.ToArray();

            for (var i = tabArray.Length - 1; i >= 0; i--)
            {
                var type = tabArray[i];
                
                var tabParam = GetTabParams(type);
                
                if (tabParam == null) continue;
                
                if (string.IsNullOrWhiteSpace(tabParam.Name))
                {
                    // If tab name is empty, then this most probably is an empty Location.
                    // Location name can be empty if entity is not linked to any location (as in DailyEvent).
                    // When location doesn't exist, we should remove Globe navigation tab too, because this entity is not related to globe. (expected removed tabs in this case: Globe->Location->)
                    // And if for some reason tab is not Location, then we should remove all tabs prior to it anyway, because empty link in chain means connection lost and quick navigation should be disabled.
                    navTabParams.Clear();
                }
                else
                {
                    navTabParams.Add(tabParam);
                }
            }

            if (navTabParams.Count != 0)
            {
                _navigationBar.Setup(navTabParams);
            }
        }

        public void SwitchScreen(POIScreenType type, bool allowNavigation = false, bool hideEntityText = false)
        {
            if (type == POIScreenType.Entity)
            {
                // Force disable Entity screen.
                // Only Tripstagram should be enabled, Entity screen will be removed later.
                type = POIScreenType.Tripstagram;
            }

            if (type == _currentScreenType)
            {
                return;
            }

            _currentScreenType = type;

            SetNavigationBar(type, hideEntityText);
            RefreshNavigationBar();

            foreach (var kvp in _screenControllers)
            {
                bool active = kvp.Key == type;

                if (active)
                    kvp.Value.OnShow(allowNavigation);
                else
                    kvp.Value.OnHide();

                ((MonoBehaviour) kvp.Value).gameObject.SetActive(active);
            }

            var poiScreenChanged = _eventDispatcher.GetMessage<POIScreenChanged>();
            poiScreenChanged.Set(type);
            _eventDispatcher.TriggerEvent(poiScreenChanged);
        }

        public void SetInputBlock(bool value)
        {
            _inputBlocker.raycastTarget = value;
        }

        public void GoBack()
        {
            if (_tabStack.Count > 1)
                _tabStack.Pop();
            else
            {
                Exit_Internal();
                return;
            }

            SwitchScreen(_tabStack.Peek());
        }

        public void Exit()
        {
            Exit_Internal();
        }

        private void Exit_Internal()
        {
            _eventDispatcher.TriggerEventNextFrame(_eventDispatcher.GetMessage<POIScreenSystemExitEvent>());
            _screensBuilder.ShowPreviousIfAnyOf(() =>
                {
                    if ((_screensBuilder.PreviousScreenType & ScreenType.SideMap) > 0)
                    {
                        LoadingProcessTracker.LogShowScreen(ScreenType.SideMapScreen.ToString(), _screensManager.GetTrackingPreviousScreenType(), "PoiExit");
                        _screensBuilder.ShowScreen(ScreenType.SideMapScreen);
                    }
                    else
                    {
                        _safeMapOpener.OpenMapScreen(LocationUid);
                    }
                },
                ScreenType.EpisodeScreen);
            _currentScreenType = POIScreenType.None;
            
            foreach (var kvp in _screenControllers)
            {
                kvp.Value.OnExit();
            }
        }
    }

    public class POIScreenSystemExitEvent : IEvent
    {
    }
}