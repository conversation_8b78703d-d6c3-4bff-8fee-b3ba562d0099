using BBB.DI;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public sealed class GameEventQualificationModalPriorityHandler : GameEventModalPriorityHandler
    {
        public GameEventQualificationModalPriorityHandler(IContext context) : base(context)
        {
        }

        protected override bool HasEventWithRequiredStage() => GameEventManager.ShouldShowQualificationAnnouncement();
    }
}