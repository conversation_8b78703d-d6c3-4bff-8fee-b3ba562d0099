using BBB;
using BBB.DI;
using GameAssets.Scripts.DailyTask;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public class DailyTasksIntroModalPriorityHandler : DailyTasksModalPriorityHandler
    {
        private readonly IDailyTasksManager _dailyTasksManager;

        public DailyTasksIntroModalPriorityHandler(IContext context) : base(context)
        {
            _dailyTasksManager = context.Resolve<IDailyTasksManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return _dailyTasksManager.ShouldAutoShowIntro(currentScreen);
        }
    }
}