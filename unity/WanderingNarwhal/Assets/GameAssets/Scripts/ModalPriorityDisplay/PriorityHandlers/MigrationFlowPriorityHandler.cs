using System;
using System.Collections;
using System.Collections.Generic;
using BBB;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using GameAssets.Scripts.CurrenciesRewardModalUI;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public class MigrationFlowPriorityHandler : ModalPriorityHandlerBase
    {
        private const string MigrationRewardTitle = "MIGRATION_REWARD_TITLE";
        private const string MigrationInfoTitle = "MIGRATION_INFO_TITLE";
        private const string MigrationInfoDesc = "MIGRATION_INFO_DESC";

        // as per <PERSON><PERSON> and <PERSON><PERSON><PERSON> - it is fine to keep it hardcoded to not rely on configs during migration
        private static readonly Dictionary<string, int> Reward = new()
        {
            { WalletCurrencies.RegularCurrency, 2000 },
            { InventoryItems.InfRocket, 60 },
            { InventoryItems.InfBomb, 60 },
            { InventoryItems.InfBolt, 60 },
        };

        private readonly IAccountManager _accountManager;
        private readonly IModalsBuilder _modalsBuilder;
        private readonly IWalletManager _walletManager;
        private readonly IUIWalletManager _uiWalletManager;
        private readonly ILocalizationManager _localizationManager;

        public MigrationFlowPriorityHandler(IContext context) : base(context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return _accountManager.JustMigrated;
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            var flowCompleted = false;

            var saveProgressModalController = _modalsBuilder.CreateModalView<SaveProgressModalController>(ModalsType.SaveProgress);
            saveProgressModalController.OnClose -= SaveProgressModalCloseHandler;
            saveProgressModalController.OnClose += SaveProgressModalCloseHandler;

            var rewardModalController = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);

            var transaction = new Transaction()
                .AddTag(TransactionTag.Gift)
                .SetAnalyticsData(CurrencyFlow.DailyTasks.Name, CurrencyFlow.Social.Gifts.Name, CurrencyFlow.Social.Gifts.FreeGift)
                .Earn(Reward);

            rewardModalController.SetupInitialParams(
                new CurrenciesRewardViewModel()
                {
                    TitleText = MigrationRewardTitle,
                    RewardDict = Reward,
                },
                skippedCurrencies =>
                {
                    _walletManager.TransactionController.MakeTransaction(transaction);
                    _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Gift, skippedCurrencies);

                    var genericModalController = _modalsBuilder.CreateModalView<GenericModalController>(ModalsType.GenericModal);
                    genericModalController.SetupWithOkButtonOnly(_localizationManager.getLocalizedText(MigrationInfoTitle), _localizationManager.getLocalizedText(MigrationInfoDesc),
                        _ => { saveProgressModalController.ShowModal(ShowMode.Delayed); });
                    genericModalController.ShowModal(ShowMode.Delayed);
                });

            rewardModalController.ShowModal(ShowMode.Delayed);

            _accountManager.SetJustMigrated(false);

            while (!flowCompleted)
                yield return null;

            yield break;

            void SaveProgressModalCloseHandler()
            {
                saveProgressModalController.OnClose -= SaveProgressModalCloseHandler;
                flowCompleted = true;
            }
        }
    }
}