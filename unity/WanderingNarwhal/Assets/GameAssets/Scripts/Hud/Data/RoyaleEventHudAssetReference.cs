using BBB.DI;
using BBB.RaceEvents;

namespace BBB.UI
{
    public class RoyaleEventHudAssetReference : IGenericHudDrivenAssetReference
    {
        private const string HudNameMask = "{0}_hud";
        public string AssetName { get; set; }

        public bool ShouldBeInstantiated(IContext context)
        {
            var currentScreen = context.Resolve<IScreensManager>().GetCurrentScreenType();
            if ((currentScreen & ScreenType.FullHudScreen) == 0)
                return false;

            var royaleEventManager = context.Resolve<IRoyaleEventManager>();
            var royaleEvent = royaleEventManager.GetHighestPriorityEvent();
            if (royaleEvent == null || !royaleEvent.ShouldShowIcon()) return false;

            AssetName = string.Format(HudNameMask, royaleEvent.Uid);
            return true;
        }
    }
}