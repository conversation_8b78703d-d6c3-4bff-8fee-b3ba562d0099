using BBB.DI;
using GameAssets.Scripts.DailyTask;

namespace BBB.UI
{
    public class DailyTaskHudAssetReference : IGenericHudDrivenAssetReference
    {
        public string AssetName { get; set; }

        public bool ShouldBeInstantiated(IContext context)
        {
            return context.Resolve<IDailyTasksManager>().ShouldShowHud(context.Resolve<IScreensManager>().GetCurrentScreenType());
        }
    }
}
