using System;
using BBB;
using BBB.Audio;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;
using BBB.Wallet;
using BebopBee.Core.Audio;
using BBB.DI;
using Core.Configs;

public class GachaClaimModalViewPresenter : ModalsViewPresenter, IGachaClaimModalViewPresenter
{
    private const string GACHA_CLAIM_MODAL_TITLE = "GACHA_CLAIM_MODAL_TITLE";
    private const string GACHA_CLAIM_MODAL_TITLE_OOM = "GACHA_CLAIM_MODAL_TITLE_OOM";

    public event Action ClaimEvent = delegate { };

    [SerializeField] private Button _claimButton;
    [SerializeField] private PrizesGOFactory _factory;
    [SerializeField] private Transform _holder;

    [SerializeField] private LocalizedTextPro _titleText;
    [SerializeField] private LocalizedTextPro _giftName;

    private IUIWalletManager _uiWalletManager;
    private ILocalizationManager _localizationManager;

    private GameObject _gachaPrizeInstance;
    private bool _isClaimed;

    protected override void OnContextInitialized(IContext context)
    {
        base.OnContextInitialized(context);
        _uiWalletManager = context.Resolve<IUIWalletManager>();
        _localizationManager = context.Resolve<ILocalizationManager>();
        
        _factory.ProvideAdditionalSprites(context.Resolve<IConfig>());
        Config.OnConfigUpdated -= _factory.ProvideAdditionalSprites;
        Config.OnConfigUpdated += _factory.ProvideAdditionalSprites;

        _claimButton.ReplaceOnClick(ClaimButtonClickedHandler);
    }

    private void ClaimButtonClickedHandler()
    {
        if (_isClaimed)
            return;

        _isClaimed = true;
        ClaimEvent.SafeInvoke();
        AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        AudioProxy.PlaySound(GenericSoundIds.CollectReward);
    }

    public void Setup(Prize prize, GachaType gachaType)
    {
        SetupPrizeObjectFor(_factory.Create(prize, false), gachaType, prize);

        _titleText.enabled = true;
        _titleText.SetTextId(gachaType == GachaType.FreeSpin ? GACHA_CLAIM_MODAL_TITLE : GACHA_CLAIM_MODAL_TITLE_OOM);
        SetupGiftName(prize);
    }

    private void SetupPrizeObjectFor(GameObject prizeObject, GachaType gachaType, Prize prize)
    {
        _uiWalletManager.ResetCurrencyInstances();
        
        if (_gachaPrizeInstance != null)
        {
            Destroy(_gachaPrizeInstance);
            _gachaPrizeInstance = null;
        }

        _gachaPrizeInstance = prizeObject;
        prizeObject.transform.SetParent(_holder, false);

        if (gachaType == GachaType.FreeSpin)
        {
            var currencyUid = string.Empty;
            if (prize.Type == GachaPrizeType.Booster)
            {
                currencyUid = prize.TypeData;
            }
            else if (prize.Type == GachaPrizeType.Regular)
            {
                currencyUid = WalletCurrencies.RegularCurrency;
            }
            else if (prize.Type == GachaPrizeType.Premium)
            {
                currencyUid = WalletCurrencies.PremiumCurrency;
            }

            if (!currencyUid.IsNullOrEmpty())
                _uiWalletManager.SetCurrencyInstance(currencyUid, _gachaPrizeInstance);
        }
    }

    private void SetupGiftName(Prize prize)
    {
        var currencyUid = prize.Type switch
        {
            GachaPrizeType.Regular => WalletCurrencies.RegularCurrency,
            GachaPrizeType.Life => WalletCurrencies.LifeCurrency,
            GachaPrizeType.Booster => prize.TypeData,
            _ => string.Empty
        };
        
        _giftName.SetRawText(RewardsAmountStringFormatDefinitions.LocalizeCurrencyCount(currencyUid, prize.Value, _localizationManager, true, true));
    }

    protected override void OnShow()
    {
        base.OnShow();
        _isClaimed = false;
        transform.SetAsLastSibling();
        AudioProxy.PlaySound(GenericSoundIds.ClaimPopupAppearing);
    }

    protected override void OnHide()
    {
        base.OnHide();
        ClaimEvent.SafeInvoke();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        Config.OnConfigUpdated -= _factory.ProvideAdditionalSprites;
    }
}