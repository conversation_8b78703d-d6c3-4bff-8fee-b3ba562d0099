using UnityEngine;
using HedgehogTeam.EasyTouch;
using UnityEngine.UI;
using System.Collections;
using BBB;
using GameAssets.Scripts.Utils;

namespace GameAssets.Scripts.DailyEvents.Popup
{
    public class GenericInfoPanel : BbbMonoBehaviour
    {
        [SerializeField] private Animator _showHideAnimator;
        [SerializeField] private Button _invokeButton;
        private bool _justShown;
        private bool _shown;
        private int _visibleHash;
        private void Awake()
        {
            _visibleHash = Animator.StringToHash("Visible");
            EasyTouch.On_TouchUp += OnTouchUpHandler;
            _invokeButton.ReplaceOnClick(Switch);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            EasyTouch.On_TouchUp -= OnTouchUpHandler;
        }

        public void Switch()
        {
            if (_shown)
                Hide();
            else
                Show();
        }

        public void Show()
        {
            if (!_shown)
                _justShown = true;

            _shown = true;
            _showHideAnimator.SetBool(_visibleHash, true);
        }

        public void Show(float delay)
        {
            if (gameObject.activeInHierarchy)
                StartCoroutine(ShowWithDelayCoroutine(delay));
        }

        public void Hide()
        {
            _shown = false;
            _justShown = false;
            _showHideAnimator.SetBool(_visibleHash, false);
        }

        private IEnumerator ShowWithDelayCoroutine(float delay)
        {
            yield return WaitCache.Seconds(delay);

            if (!_shown || !_showHideAnimator.GetBool(_visibleHash))
                Show();
        }

        private void OnTouchUpHandler(Gesture gesture)
        {
            if (!gameObject.activeInHierarchy)
                return;

            if (_justShown)
                return;

            _shown = false;
            _showHideAnimator.SetBool(_visibleHash, false);
        }

        private void LateUpdate()
        {
            _justShown = false;
        }
    }
}