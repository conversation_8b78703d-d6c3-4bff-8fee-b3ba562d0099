using BebopBee;

namespace BBB
{
    public class GetAllMessagesCommand : CommandBase
    {
        public override void Execute(BBB.DI.IContext context)
        {
            if (CurrentStatus == CommandStatus.Pending)
            {
                CurrentStatus = CommandStatus.Running;
                var playerCom = context.Resolve<BebopBee.Social.PlayerComEventsManager>();

                void Callback(bool success)
                {
                    if (success)
                    {
                        CurrentStatus = CommandStatus.Success;
                    }
                    else
                    {
                        CurrentStatus = CommandStatus.Failure;
                        StatusMessage = "Get All Messages request failed.";
                    }
                }

                playerCom.GetAllMessages(Callback);
            }
            else
            {
                base.Execute(context);
            }
        }
    }
}