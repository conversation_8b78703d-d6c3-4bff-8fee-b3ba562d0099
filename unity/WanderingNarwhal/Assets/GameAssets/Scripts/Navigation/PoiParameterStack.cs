using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.POI;
using BBB.POI.ContentManagement;
using BBB.UI.POI;

namespace BBB.Navigation
{
    public class PoiParameterStack : IContextInitializable
    {
        private POIManager _poiManager;
        private POILikesManager _likesManager;
    
        private readonly Stack<POIScreenParameter> _transitions = new Stack<POIScreenParameter>(); 

        public void InitializeByContext(IContext context)
        {
            _poiManager = context.Resolve<POIManager>();
            _likesManager = context.Resolve<POILikesManager>();
        }

        public void PushTransition(POIScreenParameter parameter)
        {
            _transitions.Push(parameter);

            if (parameter == null || parameter.PoiEntityUid.IsNullOrEmpty()) return;
            
            var poiEntity = _poiManager.GetEntity(parameter.PoiEntityUid);

            if (poiEntity == null)
            {
                BDebug.LogError(LogCat.General, $"POIEntity {parameter.PoiEntityUid} is missing");
            }
            else
            {
                _likesManager.GetAllLikeInfo(poiEntity.AllContentUids);
            }
        }

        public POIScreenParameter PopParameter()
        {
            return _transitions.Count == 0 ? null : _transitions.Pop();
        }
    }
}
