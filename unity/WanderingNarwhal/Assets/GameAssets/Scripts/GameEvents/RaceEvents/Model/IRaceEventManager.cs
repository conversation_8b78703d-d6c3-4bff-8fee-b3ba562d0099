using System;
using System.Collections;
using System.Collections.Generic;
using BBB.RaceEvents.UI;
using BebopBee.Core.UI;
using RPC.Social;

namespace BBB.RaceEvents
{
    public interface IRaceEventManager
    {
        void ProcessEventStates();
        RaceEvent GetRaceEvent(string eventUid);
        RaceEvent GetHighestPriorityEvent();
        IEnumerable<RaceEvent> GetAllEvents();
        bool TryToShowRace(RaceEvent raceEvent, ShowMode showMode = ShowMode.Delayed, RaceInfoOpeningReason openingReason = RaceInfoOpeningReason.Start, bool showHelp = false, int ownPreviousRaceRank = -1);
        void ClaimRewardFor(RaceEvent raceEvent);
        void HandleLoss(RaceEvent raceEvent);
        void IncrementScores(string eventUid, int score);
        void DebugSetScore(string eventUid, int score);
        void DebugReleaseEvent(string eventUid);
        IEnumerable<(string raceEventUid, string stageUid, int score)> GetLastDeltaScoreTuples();
        void ClearLastDeltaScores();
        IEnumerable<(INotifiableEvents gameEvent, DateTime dateTime)> GetFutureNotifiableEvents(
            Func<RaceEvent, DateTime> timeSelector);
        void FetchRemoteData();
        void EnterLevelFlow();
        void CollectEventInitialSetup(bool status, RaceEvent raceEvent, Action close);
    }
}