using System;
using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BebopBee;
using Core.RPC;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Database;
using GameAssets.Scripts.SocialScreens.Teams;

namespace BBB.TeamEvents
{
    public class TeamEventLeaderboard : ITeamEventLeaderboard
    {
        private BrainCloudManager _brainCloudManager;
        private IAccountManager _accountManager;
        private ISocialManager _socialManager;
        private TeamEvent _ownerEvent;
        
        private bool _battleMode;
        private bool _fetchingDataNow;
        private bool _shouldSubmitScore;
        private bool _loaded;
        
        private BCTeamEventData _cachedRemoteData;
        private BCTeamEventData CachedRemoteData
        {
            get
            {
                if (!_loaded)
                {
                    _loaded = true;
                    _cachedRemoteData = DbManager.LoadDataById<BCTeamEventData>(_ownerEvent.Uid);
                }
                
                if (_cachedRemoteData == null || !_cachedRemoteData.IsManaged || !_cachedRemoteData.IsValid)
                {
                    _loaded = false;
                    return null;
                }
                
                return _cachedRemoteData;
            }
        }

        public TeamCoopLeaderboardData LeaderboardData { get; private set; }
        public bool FetchingDataNow => _fetchingDataNow;
        
        private string OwnUserUid => _brainCloudManager.ProfileId;
        private bool ShouldSyncScore => _ownerEvent.Joined && !_ownerEvent.Finished && (_shouldSubmitScore || _ownerEvent.CurrentOwnScore != _ownerEvent.CurrentSubmittedOwnScore);

        public void Init(TeamEvent ownerEvent, IContext context, bool battleMode)
        {
            _ownerEvent = ownerEvent;
            _battleMode = battleMode;
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _socialManager = context.Resolve<ISocialManager>();
            
            ProcessRemoteData(CachedRemoteData);
        }

        public void Release()
        {
            DbManager.DeleteDataById<BCTeamEventData>(_ownerEvent.Uid);
            LeaderboardData = null;
            _cachedRemoteData = null;
            _shouldSubmitScore = false;
        }

        public async UniTask<TeamCoopLeaderboardData> SubmitScore(int score, string eventUid)
        {
            var completionSource = new UniTaskCompletionSource<TeamCoopLeaderboardData>();

            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                try
                {
                    _shouldSubmitScore = true;
                    UpdateCachedScore(score);
                    completionSource.TrySetResult(LeaderboardData);
                }
                catch (Exception e)
                {
                    completionSource.TrySetException(new Exception("Error submitting score to the local storage: " + e.Message + " Stack trace: " + e.StackTrace));
                }

                return await completionSource.Task;
            }

            _fetchingDataNow = true;
            _ownerEvent.SubmitTeamEventScore(eventUid, score, Success, Failure);

            return await completionSource.Task;

            void Success(BCTeamEventResponse response)
            {
                _fetchingDataNow = false;
                if (response.Status == 200)
                {
                    try
                    {
                        var remoteData = response.Response.TeamEventData;
                        ProcessRemoteData(remoteData, true);
                        SaveLeaderboard(remoteData);
                        completionSource.TrySetResult(LeaderboardData);
                    }
                    catch (Exception e)
                    {
                        TrySetException(response, e.Message + " " + e.StackTrace);
                    }
                }
                else
                {
                    _shouldSubmitScore = true;
                    TrySetException(response, string.Empty);
                }
            }

            void Failure()
            {
                _fetchingDataNow = false;
                _shouldSubmitScore = true;
            }

            void TrySetException(BCTeamEventResponse bcTeamEventData, string message)
            {
                completionSource.TrySetException(new Exception("Error submitting score: " +
                                                               Newtonsoft.Json.JsonConvert.SerializeObject(
                                                                   bcTeamEventData) + message));
            }
        }

        private void ProcessRemoteData(BCTeamEventData remoteData, bool fromServer = false)
        {
            if (remoteData == null)
            {
                LeaderboardData = null;
                return;
            }
            
            LeaderboardData = ConvertLeaderboardData(remoteData, fromServer);
            ExtendLeaderboardDataWithMissingTeamMates(LeaderboardData);
            SortItems(LeaderboardData);
            AssignRanks(LeaderboardData);
        }

        private void UpdateCachedScore(int score)
        {
            if (CachedRemoteData == null || LeaderboardData.OwnScore == score) return;

            DbManager.UpdateDataById<BCTeamEventData>(_ownerEvent.Uid, teamEventData =>
            {
                if (teamEventData.Members == null) return;
                            
                foreach (var member in teamEventData.Members)
                {
                    if (member.PlayerId == OwnUserUid)
                    {
                        member.Score = score;
                    }
                }
            });
            _loaded = false;
            ProcessRemoteData(CachedRemoteData);
        }

        public async UniTask<TeamCoopLeaderboardData> FetchData(string eventUid, string localLeaderboardId, int localLeaderboardVersion)
        {
            var completionSource = new UniTaskCompletionSource<TeamCoopLeaderboardData>();
            
            if (!ConnectivityStatusManager.ConnectivityReachable)
            {
                completionSource.TrySetResult(LeaderboardData);
                return await completionSource.Task;
            }
            
            if (ShouldSyncScore)
            {
                await SubmitScore(_ownerEvent.CurrentOwnScore, eventUid);
                completionSource.TrySetResult(LeaderboardData);
                return await completionSource.Task;
            }

            _fetchingDataNow = true;
            _ownerEvent.FetchTeamEvent(eventUid, localLeaderboardId, localLeaderboardVersion, Success, Failure);
            
            return await completionSource.Task;

            void Success(BCTeamEventResponse response)
            {
                _fetchingDataNow = false;
                if (response.Status == 200)
                {
                    try
                    {
                        var remoteData = response.Response.TeamEventData;
                        ProcessRemoteData(remoteData, true);
                        SaveLeaderboard(remoteData);
                        completionSource.TrySetResult(LeaderboardData);
                    }
                    catch (Exception e)
                    {
                        TrySetException(response, e.Message + " " + e.StackTrace);
                    }
                }
                else
                {
                    TrySetException(response, string.Empty);
                }
            }

            void Failure()
            {
                _fetchingDataNow = false;
            }
            
            void TrySetException(BCTeamEventResponse bcTeamEventData, string message)
            {
                completionSource.TrySetException(new Exception("Error fetching score: " +
                                                               Newtonsoft.Json.JsonConvert.SerializeObject(
                                                                   bcTeamEventData) + message));
            }
        }

        private static void SortItems(TeamCoopLeaderboardData leaderboardData)
        {
            SortTeamLeaderboardEntries(leaderboardData);
            SortTeamVsTeamLeaderboardEntries(leaderboardData);
        }

        private static void SortTeamLeaderboardEntries(TeamCoopLeaderboardData leaderboardData)
        {
            leaderboardData.TeamLeaderboardEntries?.Sort((a, b) =>
            {
                if (a.ContributionType != b.ContributionType) 
                    return a.ContributionType.CompareTo(b.ContributionType);
                
                if (a.Score != b.Score) 
                    return -a.Score.CompareTo(b.Score);
                
                return string.Compare(a.PlayerName, b.PlayerName, StringComparison.Ordinal);
            });
        }

        private static void SortTeamVsTeamLeaderboardEntries(TeamCoopLeaderboardData leaderboardData)
        {
            leaderboardData.TeamVsTeamLeaderboardEntries?.Sort((a, b) =>
            {
                if (a.TeamScore != b.TeamScore) 
                    return -a.TeamScore.CompareTo(b.TeamScore);
                
                return string.Compare(a.TeamName, b.TeamName, StringComparison.Ordinal);
            });
        }

        private void AssignRanks(TeamCoopLeaderboardData leaderboardData)
        {
            if (leaderboardData.TeamLeaderboardEntries != null)
            {
                var rankCounter = 1;
                foreach (var entry in leaderboardData.TeamLeaderboardEntries)
                {
                    entry.Rank = rankCounter;
                    entry.RewardDictionary = _ownerEvent.GetContributionRewardBasedOnRankIndex(rankCounter - 1);
                    rankCounter++;
                }
            }

            if (leaderboardData.TeamVsTeamLeaderboardEntries != null)
            {
                var rankCounter = 1;
                foreach (var entry in leaderboardData.TeamVsTeamLeaderboardEntries)
                {
                    entry.TeamRank = rankCounter;
                    var teamRewardDict = _ownerEvent.GetTeamRewardBasedOnRankIndex(rankCounter - 1);
                    if (teamRewardDict is {Count: > 0})
                    {
                        foreach (var teamReward in teamRewardDict)
                        {
                            entry.RewardDictionary = new Dictionary<string, int> {{teamReward.Key, teamReward.Value}};
                        }
                    }
                    
                    rankCounter++;
                }
            }
        }

        private TeamCoopLeaderboardData ConvertLeaderboardData(BCTeamEventData bcTeamEventData, bool fromServer = false)
        {
            var result = new TeamCoopLeaderboardData();
            result.TeamTotalScore = bcTeamEventData.TeamScore ?? 0;

            var ownScore = 0;
            if (bcTeamEventData.Members != null)
            {
                result.TeamLeaderboardEntries = new List<TeamCoopLeaderboardEntryData>();
                foreach (var member in bcTeamEventData.Members)
                {
                    result.TeamLeaderboardEntries.Add(new TeamCoopLeaderboardEntryData
                    {
                        PlayerId = member.PlayerId,
                        PlayerName = member.PlayerName,
                        PlayerAvatar = member.PictureUrl,
                        PlayerCountry = member.SummaryFriendData?.Country ?? "US",
                        Score = member.Score,
                        LastNudgedTimestamp = member.EventExtraData?.LastNudgedTimestamp ?? 0,
                        ContributionType = member.Score > 0 ? ContributionType.Contributed : ContributionType.NotContributed
                    });

                    if (member.PlayerId == OwnUserUid)
                    {
                        ownScore = member.Score;
                        result.OwnScore = ownScore;
                    }
                }
            }

            if (fromServer)
            {
                _ownerEvent.SetSubmittedScore(ownScore);
            }
            
            if (bcTeamEventData.Teams != null)
            {
                result.TeamVsTeamLeaderboardEntries = new List<TeamVsTeamLeaderboardEntryData>();
                foreach (var member in bcTeamEventData.Teams)
                {
                    result.TeamVsTeamLeaderboardEntries.Add(new TeamVsTeamLeaderboardEntryData
                    {
                        TeamUid = member.TeamUid,
                        TeamName = member.TeamName,
                        TeamScore = member.TeamScore,
                        TeamIcon = member.GroupSummaryData?.Icon
                    });
                }
            }

            result.LocalLeaderboardVersion = bcTeamEventData.LocalLeaderboardVersion;
            result.LocalLeaderboardId = bcTeamEventData.LocalLeaderboardId;
            
            return result;
        }

        private void ExtendLeaderboardDataWithMissingTeamMates(TeamCoopLeaderboardData leaderboardData)
        {
            var teamMates = _socialManager.TeamMates;
            var foundTeamMatesSet = new HashSet<string>();
            leaderboardData.TeamLeaderboardEntries ??= new List<TeamCoopLeaderboardEntryData>();

            bool ownIsFound = false;
            foreach (var entry in leaderboardData.TeamLeaderboardEntries)
            {
                foundTeamMatesSet.Add(entry.PlayerId);

                if (entry.PlayerId == OwnUserUid)
                {
                    ownIsFound = true;
                }
            }

            foreach (var teamMate in teamMates)
            {
                if (!foundTeamMatesSet.Contains(teamMate.Uid))
                {
                    leaderboardData.TeamLeaderboardEntries.Add(new TeamCoopLeaderboardEntryData
                    {
                        PlayerId = teamMate.Uid,
                        PlayerName = teamMate.Name,
                        PlayerAvatar = teamMate.Avatar,
                        PlayerCountry = teamMate.Country,
                        Score = 0,
                        ContributionType = ContributionType.NotContributed
                    });
                }
            }

            if (!ownIsFound)
            {
                leaderboardData.TeamLeaderboardEntries.Add(GenerateOwnEntry(_ownerEvent.CurrentOwnScore));
            }

            if (_battleMode)
            {
                leaderboardData.TeamVsTeamLeaderboardEntries ??= new List<TeamVsTeamLeaderboardEntryData>();
                
                var ownTeamFound = false;
                foreach (var entry in leaderboardData.TeamVsTeamLeaderboardEntries)
                {
                    if (entry.TeamUid == _accountManager.Profile.CurrentTeam.TeamUid)
                    {
                        ownTeamFound = true;
                        break;
                    }
                }

                if (!ownTeamFound)
                {
                    leaderboardData.TeamVsTeamLeaderboardEntries.Add(GenerateOwnTeamEntry());
                }
            }
        }

        private TeamCoopLeaderboardEntryData GenerateOwnEntry(int ownScore)
        {
            return new TeamCoopLeaderboardEntryData
            {
                Score = ownScore,
                PlayerId = OwnUserUid,
                PlayerName = _accountManager.Profile.Name,
                PlayerAvatar = _accountManager.Profile.Avatar,
                PlayerCountry = _accountManager.Profile.Country,
                ContributionType = ownScore > 0
                    ? ContributionType.Contributed
                    : ContributionType.NotContributed
            };
        }
        
        private TeamVsTeamLeaderboardEntryData GenerateOwnTeamEntry()
        {
            return new TeamVsTeamLeaderboardEntryData
            {
                TeamUid =  _accountManager.Profile.CurrentTeam.TeamUid,
                TeamName = _accountManager.Profile.CurrentTeam.Name,
                TeamIcon = _accountManager.Profile.CurrentTeam.Icon
            };
        }

        private void SaveLeaderboard(BCTeamEventData remoteData)
        {
            if (_cachedRemoteData.SafeEquals(remoteData)) return;
            
            _cachedRemoteData = remoteData;
            _loaded = true;

            try
            {
                remoteData.EventUid = _ownerEvent.Uid;
                DbManager.SaveData(remoteData);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.General, $"Error saving TeamCoopLeaderboardData: {e.Message}");
            }
        }
    }
}