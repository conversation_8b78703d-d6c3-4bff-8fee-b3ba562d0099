using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.AssetBundles;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Modals;
using BBB.UI;
using BBB.UI.Level;
using BBB.UI.Level.Controllers;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using Core.Configs;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.CurrenciesRewardModalUI;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.GameEvents.RoyaleEvents.UI;
using GameAssets.Scripts.IAP.EndlessTreasure;
using PBConfig;

namespace BBB.RaceEvents
{
    public class RoyaleEventManager : IContextInitializable, IContextReleasable, IRoyaleEventManager, ISchedulableDataProvider, IGameEventProvider, IBundlePredownloadProvider
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(RoyaleGameEventConfig),
            typeof(GameEventMetaConfig)
        };

        private readonly List<RoyaleEvent> _eventsList = new();
        private readonly List<RoyaleEvent> _tempEventList = new();
        private readonly Dictionary<string, RoyaleEvent> _eventsDict = new();

        private IConfig _config;
        private GameEventMetaConfig _metaConfig;
        private IGameEventResourceManager _gameEventResourceManager;
        private IScreensManager _screenManager;
        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private TimeManager _timeManager;
        private IModalsBuilder _modalsBuilder;
        private ILockManager _lockManager;
        private GameNotificationManager _notificationManager;
        private ILocalizationManager _localizationManager;
        private IEventDispatcher _eventDispatcher;
        private IWalletManager _walletManager;
        private IModalsManager _modalsManager;
        private IUIWalletManager _uiWalletManager;
        private ILocationManager _locationManager;
        private BundlesBackgroundDownloaderManager _bundlesBackgroundDownloaderManager;
        private ICoroutineExecutor _coroutineExecutor;

        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;

        private readonly HashSet<string> _notJoinedEventsAutoshownThisSession = new();
        private bool _suppressPenalty;

        void IContextInitializable.InitializeByContext(IContext context)
        {
            _config = context.Resolve<IConfig>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _screenManager = context.Resolve<IScreensManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _timeManager = context.Resolve<TimeManager>();
            _lockManager = context.Resolve<ILockManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _walletManager = context.Resolve<IWalletManager>();
            _modalsManager = context.Resolve<IModalsManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _bundlesBackgroundDownloaderManager = context.Resolve<BundlesBackgroundDownloaderManager>();
        }

        void IContextReleasable.ReleaseByContext(IContext context)
        {
            ClearEvents();
            Unsubscribe();
        }

        public void Setup()
        {
            SetupGameEventMetaConfig(_config);
            Subscribe();
            ProcessEventStates();
        }

        private void SetupGameEventMetaConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _metaConfig = config.TryGetDefaultFromDictionary<GameEventMetaConfig>();

            OnPlayerProfileUpdated(config);
        }

        private void OnPlayerProfileUpdated(IConfig config)
        {
            ClearEvents();

            var eventConfigDict = config.Get<RoyaleGameEventConfig>();
            if (eventConfigDict == null)
            {
                BDebug.LogError(LogCat.General, "RaceGameEventConfig is missing");
                return;
            }

            foreach (var royaleEventConfig in eventConfigDict.Values)
            {
                var royaleEvent = new RoyaleEvent(royaleEventConfig, _notificationManager, _playerManager,
                    _config, _localizationManager, this, _coroutineExecutor, _suppressPenalty);

                _eventsDict.Add(royaleEvent.Uid, royaleEvent);
                _eventsList.Add(royaleEvent);
            }

            _gameEventResourceManager.SetupRoyaleEvents(this, _eventsList);
        }

        private void ClearEvents()
        {
            foreach (var (_, royaleEvent) in _eventsDict)
            {
                if (royaleEvent == null)
                    continue;

                royaleEvent.OnAfterViewingStateUpdate = null;
                royaleEvent.StopCoroutine();
            }

            _eventsDict.Clear();
            _eventsList.Clear();
        }

        private void Subscribe()
        {
            Unsubscribe();

            Config.OnConfigUpdated += SetupGameEventMetaConfig;
            _accountManager.ProfileUpdated += OnProfileUpdated;
            _screenManager.OnScreenChangingStarted += OnScreenChangingStartedHandler;
            _eventDispatcher.AddListener<InventoryBoosterSpentEvent>(OnBoosterSpentEvent);
            _eventDispatcher.AddListener<MoreMovesAddedEvent>(OnMoreMovesAddedEvent);
            _eventDispatcher.AddListener<GachaSpinPurchasedEvent>(OnGachaSpinPurchasedEvent);
            _bundlesBackgroundDownloaderManager.RegisterProvider(this);
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupGameEventMetaConfig;
            _accountManager.ProfileUpdated -= OnProfileUpdated;
            _screenManager.OnScreenChangingStarted -= OnScreenChangingStartedHandler;
            _eventDispatcher.RemoveListener<InventoryBoosterSpentEvent>(OnBoosterSpentEvent);
            _eventDispatcher.RemoveListener<MoreMovesAddedEvent>(OnMoreMovesAddedEvent);
            _eventDispatcher.RemoveListener<GachaSpinPurchasedEvent>(OnGachaSpinPurchasedEvent);
            _bundlesBackgroundDownloaderManager.UnregisterProvider(this);
        }

        private void OnScreenChangingStartedHandler(ScreenType type, IScreensController controller)
        {
            ProcessEventStates();
        }

        private void OnBoosterSpentEvent(InventoryBoosterSpentEvent boosterSpentEvent)
        {
            var boosterId = boosterSpentEvent.Arg0;
            foreach (var royaleEvent in _eventsList)
            {
                if (!royaleEvent.Joined || !royaleEvent.IsLaunched()) continue;
                RegisterItemBought(boosterId);
            }
        }

        private void OnMoreMovesAddedEvent(MoreMovesAddedEvent moreMovesAddedEvent)
        {
            var paid = moreMovesAddedEvent.Arg3;
            var price = moreMovesAddedEvent.Arg2;
            if (!paid || price == null) return;

            RegisterItemBought("plus_moves", price);
        }

        private void OnGachaSpinPurchasedEvent(GachaSpinPurchasedEvent gachaSpinPurchasedEvent)
        {
            var paid = gachaSpinPurchasedEvent.Arg0;
            var price = gachaSpinPurchasedEvent.Arg1;
            if (!paid || price == null) return;

            RegisterItemBought("gacha_spin", price);
        }

        private void RegisterItemBought(string itemId, Price price = null)
        {
            var regularSpent = -1L;
            if (price != null)
            {
                const string regularKey = "regular";
                if (!price.Currencies.TryGetValue(regularKey, out regularSpent)) return;
            }

            foreach (var royaleEvent in _eventsList)
            {
                if (!royaleEvent.Joined || !royaleEvent.IsLaunched()) continue;
                royaleEvent.RegisterSpent(itemId, regularSpent);
            }
        }

        private bool IsNotReady()
        {
            return _eventsList.Count == 0;
        }

        public void ProcessEventStates()
        {
            if (IsNotReady())
                return;

            var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;

            if (!string.IsNullOrWhiteSpace(highestPassedLevel)
                && !_lockManager.IsLocked(GameEventManager.WeeklyStoriesLockKey, LockItemType.DailyTour))
            {
                var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);

                foreach (var royaleGameEvent in _eventsList)
                {
                    royaleGameEvent.ProcessState(highestPassedSortOrder);
                }
            }
        }

        private void OnProfileUpdated(IPlayer obj)
        {
            OnPlayerProfileUpdated(_config);
        }

        void IRoyaleEventManager.SuppressStatePenalty(bool suppress)
        {
            _suppressPenalty = suppress;
        }

        DateTime ISchedulableDataProvider.GetCurrentUtcDateTime()
        {
            return _timeManager.GetCurrentDateTime().AddSeconds(_metaConfig.DebugLocalTimeOffsetInSeconds);
        }

        int ISchedulableDataProvider.TotalRelativeDuration => 0;

        int ISchedulableDataProvider.GetTotalPrecedingDuration(int relativeSortIndex)
        {
            return 0;
        }

        public DateTime GetRelativeTimelineStart()
        {
            var relativeTimelineStart = _metaConfig.RelativeTimelineStart;
            return new DateTime(relativeTimelineStart.Year, relativeTimelineStart.Month,
                relativeTimelineStart.Day, _metaConfig.UtcHourToStart, 0, 0);
        }

        public (int hour, int minute) GetUtcHourAndMinuteToStart()
        {
            return (_metaConfig.UtcHourToStart, _metaConfig.UtcMinuteToStart);
        }

        public IGameEventWithResources GetGameEvent(string uid)
        {
            return GetRoyaleEvent_Internal(uid);
        }

        public IEnumerable<IGameEventWithResources> GetGameEventsWithResource(string resourceId)
        {
            foreach (var gameEvent in _eventsDict.Values)
            {
                if (gameEvent != null && gameEvent.EventResourceId == resourceId)
                {
                    yield return gameEvent;
                }
            }
        }

        public bool ShowRoyaleEvent(RoyaleEvent royaleEvent, bool shouldProcess = true)
        {
            if (shouldProcess)
            {
                var highestPassedLevel = _accountManager.Profile.HighestPassedLevelId;
                var highestPassedSortOrder = LevelHelper.GetLevelSortOrder(_config, highestPassedLevel);
                royaleEvent.ProcessState(highestPassedSortOrder);
            }

            if (!royaleEvent.Joined)
            {
                var ctrl = _modalsBuilder.CreateModalView<RoyaleEventIntroController>(ModalsType.RoyaleEventIntroModal);
                ctrl.Setup(royaleEvent);
                ctrl.ShowModal(ShowMode.Delayed, ModalsTags.RoyaleEvent);
            }
            else
            {
                if (!ConnectivityStatusManager.ConnectivityReachable && !royaleEvent.RoundWon)
                    return false;

                var ctrl = _modalsBuilder.CreateModalView<RoyaleEventMainModalController>(ModalsType.RoyaleEventMainModal);
                royaleEvent.CheckForTimeExpiration();
                ctrl.Setup(EventAutoshowCondition.Normal, royaleEvent);
                ctrl.ShowModal(ShowMode.Delayed, ModalsTags.RoyaleEvent);
            }

            return true;
        }

        private bool AutoShowEvent(EventAutoshowCondition eventAutoshowCondition, RoyaleEvent royaleEvent,
            Action closeCallback)
        {
            if (royaleEvent.Joined)
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent, royaleEvent.Uid));
                DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);

                var ctrl = _modalsBuilder.CreateModalView<RoyaleEventMainModalController>(ModalsType.RoyaleEventMainModal);
                royaleEvent.CheckForTimeExpiration();
                ctrl.Setup(eventAutoshowCondition, royaleEvent, closeCallback);
                ctrl.ShowModal(tag: ModalsTags.RoyaleEvent);
                return true;
            }

            BDebug.LogError(LogCat.General, "Trying to autoshow the event which is not joined, this is not supposed to happen");
            return false;
        }

        public void ClaimRewardFor(RoyaleEvent royaleEvent)
        {
            var reward = royaleEvent.ClaimReward();

            if (!RewardsUtility.IsRewardValid(reward))
                return;

            var analyticsData = royaleEvent.GetCurrencyFlowAnalyticsData();
            var rewardDictionary = new Dictionary<string, long>();

            foreach (var kv in reward)
            {
                rewardDictionary[kv.Key] = kv.Value;
            }

            var transaction = new Transaction()
                .AddTag(TransactionTag.GameEvent)
                .SetAnalyticsData(analyticsData.Category, analyticsData.Family, analyticsData.Item)
                .Earn(rewardDictionary);

            WalletTransactionController.MakeTransaction(transaction);

            var winModalController = _modalsBuilder.CreateModalView<RoyaleEventWinController>(ModalsType.RoyaleEventWinModal);
            _modalsManager.PrioritizeNextModalWithTag(ModalsTags.RoyaleEvent);

            var viewModel = new RoyaleEventWinViewModel
            {
                Uid = royaleEvent.Uid,
                PrizePoolSize = royaleEvent.PrizePoolSize,
                LastRoundWinnersCount = royaleEvent.LastRoundWinnersCount
            };
            winModalController.Setup(viewModel, reward, () =>
            {
                var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
                _modalsManager.PrioritizeNextModalWithTag(ModalsTags.RoyaleEvent);

                rewardModal.SetupInitialParams(
                    new CurrenciesRewardViewModel
                    {
                        RewardDict = reward,
                        SubtitleText = EndlessTreasure.RewardSubtitle,
                    },
                    skippedCurrencies =>
                    {
                        _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.GameEvent, skippedCurrencies);
                        EnterLevelFlow();
                    });

                rewardModal.ShowModal(ShowMode.Delayed, ModalsTags.RoyaleEvent);
            });
            winModalController.ShowModal(ShowMode.Delayed);
        }

        public void EnterLevelFlow()
        {
            var currentScreen = _screenManager.GetCurrentScreenType();
            if ((currentScreen & ScreenType.FullHudScreen) == 0)
                return;

            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<EventFlowRequestedEvent>());
        }

        public bool TryAutoShowEvent(EventAutoshowCondition condition, Action closeCallback = null)
        {
            var eventToAutoShow = GetEventToAutoShow(condition);
            if (eventToAutoShow == null) return false;

            if (condition == EventAutoshowCondition.NonJoined)
            {
                if (_notJoinedEventsAutoshownThisSession.Contains(eventToAutoShow.Uid)) return false;

                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent, eventToAutoShow.Uid));
                DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
                var ctrl = _modalsBuilder.CreateModalView<RoyaleEventIntroController>(ModalsType.RoyaleEventIntroModal);
                ctrl.Setup(eventToAutoShow);
                ctrl.ShowModal(ShowMode.Delayed, ModalsTags.RoyaleEvent);
                _notJoinedEventsAutoshownThisSession.Add(eventToAutoShow.Uid);
                closeCallback?.Invoke();
                return true;
            }

            return (ConnectivityStatusManager.ConnectivityReachable || eventToAutoShow.RoundWon) && AutoShowEvent(condition, eventToAutoShow, closeCallback);
        }

        private RoyaleEvent GetEventToAutoShow(EventAutoshowCondition condition)
        {
            if (!ShouldAutoShow(condition)) return null;

            var highestPriorityAutoShowEvent = (RoyaleEvent)GameEventUtils.GetHighestPriorityEvent(comparableEvent => comparableEvent is RoyaleEvent raceEvent
                                                                                                                      && EventLookupFilter(raceEvent), _eventsList);

            return highestPriorityAutoShowEvent;
        }

        public RoyaleEvent GetRoyaleEvent(string eventUid)
        {
            var ev = GetRoyaleEvent_Internal(eventUid);
            return ev != null && EventLookupFilter(ev) ? ev : null;
        }

        public RoyaleEvent GetHighestPriorityEvent()
        {
            return (RoyaleEvent)GameEventUtils.GetHighestPriorityEvent(comparableEvent => comparableEvent is RoyaleEvent ev
                                                                                          && EventLookupFilter(ev), _eventsList);
        }

        private bool EventLookupFilter(RoyaleEvent royaleEvent)
        {
            return _gameEventResourceManager.AllAssetsAvailable(royaleEvent.Uid) && royaleEvent.IsLaunched();
        }

        private RoyaleEvent GetRoyaleEvent_Internal(string eventUid)
        {
            if (eventUid.IsNullOrEmpty())
                return null;

            if (_eventsDict.TryGetValue(eventUid, out var ev))
                return ev;

            BDebug.LogError(LogCat.Events, $"Event {eventUid} was not found");

            return null;
        }

        public IEnumerable<RoyaleEvent> GetAllEvents()
        {
            return _eventsList;
        }

        public bool ShouldAutoShow(EventAutoshowCondition condition)
        {
            _tempEventList.Clear();

            foreach (var ev in _eventsList)
            {
                if (ev.ShouldBeAutoShown(condition))
                {
                    if (ConnectivityStatusManager.ConnectivityReachable || ev.RoundWon)
                    {
                        _tempEventList.Add(ev);
                    }
                }
                else if (condition == EventAutoshowCondition.NonJoined && ShouldAutoShowNotJoined(ev)) //special autoshow case, once per session if not joined
                {
                    _tempEventList.Add(ev);
                }
            }

            return _tempEventList.Count != 0;
        }

        private bool ShouldAutoShowNotJoined(RoyaleEvent ev)
        {
            return ev.IsLaunched() && !ev.Joined && !ev.IsInCooldown() && !ev.MaxCapReached() && !_notJoinedEventsAutoshownThisSession.Contains(ev.Uid);
        }

        public List<BackgroundDownloadData> GetBundlesToPredownload()
        {
            List<BackgroundDownloadData> bundles = null;
            foreach (var gameEvent in _eventsList)
            {
                if (gameEvent.BundlesLoadLevelUid.IsNullOrEmpty() || _locationManager.GetLevelStage(gameEvent.BundlesLoadLevelUid) <= 0) continue;

                var eventBundles = _gameEventResourceManager.GetBundleNamesForEvent(gameEvent.EventResourceId);
                if (eventBundles == null || eventBundles.Count == 0) continue;

                bundles ??= new List<BackgroundDownloadData>();
                foreach (var bundle in eventBundles)
                {
                    if (bundle.IsNullOrEmpty()) continue;
                    bundles.Add(new BackgroundDownloadData { Priority = BackgroundDownloadPriority.Normal, Name = bundle });
                }
            }

            return bundles;
        }
    }
}