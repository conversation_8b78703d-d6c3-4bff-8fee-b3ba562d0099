using System;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.EndGameEvents;
using BBB.UI;
using BebopBee.Core.UI;

namespace BBB
{
    public partial class GameEventManager
    {
        private void AutoShowCompetitionGameEventModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            if (!gameEvent.IsLaunched())
                return;
            
            var narrativeIds = gameEvent.CurrentNarrativeIds;
            var precedingNarrativeId = narrativeIds.Item1;
            var hasPrecedingNarrative = !precedingNarrativeId.IsNullOrEmpty();
            
            if (gameEvent.IsReadyToQualify() && !gameEvent.IsQualificationRewardCollected)
            {
                gameEvent.TryQualify();
                ClaimRewardsFor(gameEvent);
                return;
            }
            
            if (gameEvent.IsQualifiedToJoin)
            {
                if (!gameEvent.IntroductionAlreadyShown)
                {
                    //intro modal show
                    Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent,gameEvent.Uid));
                    DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
                    if(hasPrecedingNarrative)
                        _modalsManager.PrioritizeNextModalWithTag(ModalsTags.GameEvent);
                            
                    gameEvent.MarkIntroDone();
                }
                ShowLeaderboardModal(gameEvent, showMode);
            }
            else if (gameEvent.GameplayType is not GameEventGameplayType.EndOfContent)
            {
                if (!gameEvent.InfoAlreadyShown)
                {
                    if (hasPrecedingNarrative)
                        _modalsManager.PrioritizeNextModalWithTag(ModalsTags.GameEvent);

                    gameEvent.MarkInfoShown();
                    gameEvent.MarkIntroDone();
                }

                ShowQualificationModal(gameEvent, showMode);
            }
        }

        private void ShowQualificationModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            if (gameEvent.GameplayType.IsSideMap()) //side map event doesn't have qualification
                return;
            
            //qualification modal shown
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.GameEvent,gameEvent.Uid));
            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.GameEventClick, DauInteractions.TapOnAutoPopups.GameEventClose);
            var competitionEventGenericModalController = _modalsBuilder.CreateModalView<CompetitionEventGenericModalController>(ModalsType.CompetitionEventGeneric);
            competitionEventGenericModalController.Setup(CompetitionEventOpeningMode.Qualification, gameEvent, OnClosed);

            void OnClosed(bool okay)
            {
                if (okay)
                    GoToNextGameEventLevel();
            }

            _modalsManager.PrioritizeNextModalWithTag(ModalsTags.GameEvent);
            competitionEventGenericModalController.ShowModal(showMode, tag: ModalsTags.GameEvent);
        }

        public void ShowInformationModal(CompetitionGameEvent gameEvent, Action<bool> closeCallback)
        {
            var genericCtrl = _modalsBuilder.CreateModalView<CompetitionEventGenericModalController>(ModalsType.CompetitionEventGeneric);
            genericCtrl.Setup(CompetitionEventOpeningMode.Information, gameEvent, okay =>
            {
                if (okay)
                    GoToNextGameEventLevel();

                closeCallback?.Invoke(okay);
            });

            genericCtrl.ShowModal(ShowMode.Immediate);
        }

        public void ShowCompletionModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            ShowLeaderboardModal(gameEvent, showMode);
        }

        public void ShowLeaderboardModal(CompetitionGameEvent gameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            if (!gameEvent.IsReadyToQualify())
            {
                BDebug.LogError(LogCat.Leaderboards, $"Trying to show leaderboard for game event {gameEvent.Uid} but it is not ready to qualify");
                return;
            }

            var leaderboardCtrl = gameEvent switch
            {
                EndOfContentGameEvent => _modalsBuilder.CreateModalView<EndOfContentLeaderboardModalController>(ModalsType.EndOfContentLeaderboardModal),
                SideMapGameEvent => _modalsBuilder.CreateModalView<SideMapLeaderboardModalController>(ModalsType.SideMapLeaderboard),
                _ => _modalsBuilder.CreateModalView<EventLeaderboardModalController>(ModalsType.EventLeaderboard)
            };
            leaderboardCtrl.Setup(gameEvent, (callbackCode, okay) =>
            {
                switch (callbackCode)
                {
                    case EventLeaderboardCallbackCode.Ended:

                        //qualification modal shown

                        bool inRestartZone = gameEvent.MeInRestartZone();
                        bool promotionZone = gameEvent.MeInPromotionZone();
                        bool demotionZone = gameEvent.MeInDemotionZone();

                        if (inRestartZone || promotionZone || demotionZone)
                        {
                            LeagueChangeMessageType messageType;
                            if (inRestartZone)
                                messageType = LeagueChangeMessageType.Restart;
                            else if (promotionZone)
                                messageType = LeagueChangeMessageType.Promotion;
                            else
                                messageType = LeagueChangeMessageType.Demotion;


                            var competitionEventPromotionDemotionModalController = _modalsBuilder.CreateModalView<CompetitionEventPromotionDemotionModalController>(ModalsType.CompetitionPromotionDemotion);
                            competitionEventPromotionDemotionModalController.Setup(messageType, gameEvent, OnClosed);

                            void OnClosed()
                            {
                            }

                            _modalsManager.PrioritizeNextModalWithTag(ModalsTags.GameEvent);
                            competitionEventPromotionDemotionModalController.ShowModal(showMode, tag: ModalsTags.GameEvent);
                        }
                        
                        break;
                    case EventLeaderboardCallbackCode.Default:
                    {
                        if (okay)
                        {
                            GoToNextGameEventLevel();
                        }
                           
                        break;
                    }
                }
            });

            _modalsManager.PrioritizeNextModalWithTag(ModalsTags.GameEvent);
            leaderboardCtrl.ShowModal(showMode, tag: ModalsTags.GameEvent);
        }

        //for competitive event manual show is the same as autoshow
        private void ShowCompetitionGameEvent(CompetitionGameEvent competitionGameEvent, ShowMode showMode = ShowMode.Delayed)
        {
            AutoShowCompetitionGameEventModal(competitionGameEvent);
        }

        public int GetLastScoreShown(string eventUid)
        {
            if (_eventsDict.TryGetValue(eventUid, out var instance))
            {
                var competitionEvent = (CompetitionGameEvent) instance;
                return competitionEvent.GetLastScoreShownInCurrentLeague();
            }

            return -1;
        }

        public void SetLastScoreShown(string eventUid, int value)
        {
            if (_eventsDict.TryGetValue(eventUid, out var instance))
            {
                var competitionEvent = (CompetitionGameEvent) instance;
                competitionEvent.SetLastScoreShownInCurrentLeague(value);
            }
        }

        public void SetLastRankShown(string eventUid, int value)
        {
            if (!eventUid.IsNullOrEmpty() && _eventsDict.TryGetValue(eventUid, out var instance))
            {
                var competitionEvent = (CompetitionGameEvent) instance;
                competitionEvent.SetLastRankShownInCurrentLeague(value);
            }
        }

        public int GetLastScoreSeenFor(string eventUid, string playerUid)
        {
            if (!string.IsNullOrEmpty(eventUid) && _eventsDict.TryGetValue(eventUid, out var instance))
            {
                var competitionEvent = (CompetitionGameEvent) instance;
                return competitionEvent.GetLastScoreSeenFor(playerUid);
            }

            return -1;
        }

        public void SetLastScoreSeenFor(string eventUid, string playerUid, int value)
        {
            if (_eventsDict.TryGetValue(eventUid, out var instance))
            {
                var competitionEvent = (CompetitionGameEvent) instance;
                competitionEvent.SetLastScoreSeenFor(playerUid, value);
            }
        }
    }
}