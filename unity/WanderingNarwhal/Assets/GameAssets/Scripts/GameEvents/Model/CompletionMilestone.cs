using System.Collections.Generic;
using BBB.Core;
using BBB.Wallet;
using PBConfig;

namespace BBB
{
    public class CompletionMilestone : IGameEventMilestone
    {
        private const string GiftBoxKey = "giftbox";

        public int StartScore { get; }
        public int Goal { get; }
        public string GoalName { get; }

        public string EventUid { get; }
        public string RewardUid { get; }
        public int RewardNumber { get; }
        public Dictionary<string, int> GiftBoxContent => _giftBoxContent.FilterRewards();
        private readonly Dictionary<string, int> _giftBoxContent;

        public string ScoreSpriteName => string.Empty;

        public string RewardSpriteName
        {
            get
            {
                return RewardUid switch
                {
                    WalletCurrencies.RegularCurrency or WalletCurrencies.PremiumCurrency or InventoryItems.InfLife =>
                        "heap_" + RewardUid,
                    GiftBoxKey => GiftBoxKey,
                    _ => RewardUid
                };
            }
        }

        public CompletionMilestone()
        {
            RewardUid = "lif_inf";
            RewardNumber = 15;
            _giftBoxContent = null;
        }

        public CompletionMilestone(string eventUid, MilestoneConfig milestoneConfig, int startScore , int goal)
        {
            StartScore = startScore;
            Goal = goal;
            EventUid = eventUid;
            
            var rewardParts = RewardsUtility.RewardStringToDict(milestoneConfig.Reward);
            switch (rewardParts.Count)
            {
                case > 1:
                    RewardUid = GiftBoxKey;
                    RewardNumber = 1;
                    _giftBoxContent = rewardParts;
                    break;

                case 1:
                    string firstKey = null;
                    var firstValue = 0;

                    foreach (var kvp in rewardParts)
                    {
                        firstKey = kvp.Key;
                        firstValue = kvp.Value;
                        break;
                    }

                    RewardUid = firstKey;
                    RewardNumber = firstValue;
                    _giftBoxContent = null;
                    break;
            }
        }

        public int GetGoal()
        {
            return Goal;
        }

        public int GetRelativeScore(int totalScore)
        {
            return totalScore - StartScore;
        }

        public bool IsGiftBoxReward()
        {
            return RewardUid == GiftBoxKey;
        }

        public MilestoneTarget GetMilestoneTarget()
        {
            return MilestoneTarget.None;
        }
    }
}