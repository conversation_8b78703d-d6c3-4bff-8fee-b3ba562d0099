namespace BBB
{
    // Attempt to unify milestones visualisation by UBP by wrapping Collection and Completion Event with same interface 
    public interface IGameEventWithMilestones
    {
        int CurrentScore { get; }
        int PreviousTotalScore { get; }

        int GetCurrentMilestoneIndex();
        int GetPreviousMilestoneIndex();
        int GetLastMilestoneIndex();

        IGameEventMilestone GetMilestoneByIndex(int index);
        GameEventCurrencyFlowData? GetCurrencyFlowAnalyticsData(int milestoneIndex);
    }
}