namespace BBB
{
    public class FireLeagueTopBorderItem : EventLeaderboardItemBase 
    {
        public int Score { get; }
        public int NumberOfPlayers { get; }

        public override int PrefabIndex
        {
            get { return 1; }
        }

        public FireLeagueTopBorderItem(int score, int numberOfPlayers)
        {
            Score = score;
            NumberOfPlayers = numberOfPlayers;
        }
    }
}