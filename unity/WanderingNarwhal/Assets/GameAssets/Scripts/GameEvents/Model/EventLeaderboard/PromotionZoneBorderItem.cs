namespace BBB
{
    public class PromotionZoneBorderItem : EventLeaderboardItemBase
    {
        public int Score { get; }
        public int NumberOfPlayers { get; }

        public override int PrefabIndex
        {
            get { return 3; }
        }

        public PromotionZoneBorderItem(int score, int numberOfPlayers)
        {
            Score = score;
            NumberOfPlayers = numberOfPlayers;
        }
    }
}