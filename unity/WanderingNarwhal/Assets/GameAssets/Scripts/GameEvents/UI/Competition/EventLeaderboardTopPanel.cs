using BBB.DI;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using BBB.Core.UI;

namespace BBB
{
    public class EventLeaderboardTopPanel : BbbMonoBehaviour
    {
        [SerializeField] private List<Image> _leagueIconImages;
        [SerializeField] private GameEventReplaceableGo _gameEventReplaceableGo;

        private IGameEventResourceManager _gameEventResourceManager;

        public void Init(IContext context)
        {
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _gameEventReplaceableGo.Init(_gameEventResourceManager);
        }

        public void Refresh(string eventUid, League league)
        {
            var leagueSprite = _gameEventResourceManager.GetSprite(eventUid, league.ToSkinName());
            _leagueIconImages.ForEach(image => image.sprite = leagueSprite);

            _gameEventReplaceableGo.Refresh(eventUid, GameEventResKeys.MainAnimatedComposition);
            _gameEventReplaceableGo.InvokeOnGo(go =>
            {
                var leagueViewApplier = go.GetComponent<ILeagueViewApplier>();
                leagueViewApplier?.Apply(league);
            });
        }

        public void Clear()
        {
            _leagueIconImages.ForEach(image => image.sprite = null);
        }
    }
}