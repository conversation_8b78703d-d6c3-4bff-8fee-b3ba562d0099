using System;
using System.Collections.Generic;
using BBB.UI.Core;

namespace BBB
{
    public interface ICompetitionEventInfoViewPresenter : IViewPresenter
    {
        /// <summary>
        /// Email, Phone Number
        /// </summary>
        event Action<string, string> SubmittedToLottery;
        event Action GoButtonClicked;
        event Action TermsButtonClicked;
        void SetupMain(string gameEventUid, IList<string> avatarUrls);
        void SetupLottery(string gameEventUid, bool isSubmitted, DateTime endTime);
    }
}