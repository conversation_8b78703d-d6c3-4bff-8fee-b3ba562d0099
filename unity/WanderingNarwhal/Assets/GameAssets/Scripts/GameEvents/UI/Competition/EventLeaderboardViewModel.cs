
using System;
using System.Collections.Generic;

namespace BBB
{
    public enum CompetitionEventAvailableAction
    {
        None = 0,
        Submit = 1
    }

    public class EventLeaderboardViewModel
    {
        public string EventUid { get; private set; }
        public string EventName { get; private set; }
        
        public string EventDescription { get; private set; }
        public League League { get; private set; } 
        public int ScoreToSubmit { get; private set; }
        public bool IsEnded { get; private set; }

        public Func<string, TimeSpan> TimeLeftGetter { get; private set; }
        public Func<int> GetSubmittedScore { get; private set; }
        public Func<bool> IsWon { get; private set; }
        public Func<int> GetOwnPlace { get; private set; }
        public Func<League, int, Dictionary<string, int>> GetRewards { get; private set; }

        //leaderboard
        public List<EventLeaderboardItemBase> Items { get; set; }

        public static EventLeaderboardViewModel CreateViewModel(CompetitionGameEvent gameEvent)
        {
            var viewModel = new EventLeaderboardViewModel
            {
                EventUid = gameEvent.Uid,
                EventName = gameEvent.NameText,
                EventDescription = gameEvent.DescriptionText
            };
            var eventLeaderboard = gameEvent.EventLeaderboard;
            var currentLeague = eventLeaderboard.CurrentLeague;
            viewModel.League = currentLeague;

            var status = gameEvent.Status;

            if (status.ShouldRunTimer())
                viewModel.TimeLeftGetter = gameEvent.GetTimeLeft;
            else
                viewModel.TimeLeftGetter = viewModel.GetZeroTime;

            viewModel.ScoreToSubmit = gameEvent.CurrentScore;
            viewModel.GetSubmittedScore = gameEvent.EventLeaderboard.GetSubmittedScore;
            viewModel.IsEnded = gameEvent.IsEnded();
            viewModel.IsWon = gameEvent.IsWon;
            viewModel.GetOwnPlace = gameEvent.EventLeaderboard.GetOwnPlace;
            viewModel.GetRewards = (league, place) => gameEvent.GetOrderedRewardForPlace(league, place, out var _);

            viewModel.Items = gameEvent.CachedOpponents;

            return viewModel;
        }     

        private TimeSpan GetZeroTime(string arg = null)
        {
            return TimeSpan.Zero;
        }

        public void Destroy()
        {
            TimeLeftGetter = null;
            GetSubmittedScore = null;
            IsWon = null;
            GetOwnPlace = null;
            GetRewards = null;
        }
    }
}