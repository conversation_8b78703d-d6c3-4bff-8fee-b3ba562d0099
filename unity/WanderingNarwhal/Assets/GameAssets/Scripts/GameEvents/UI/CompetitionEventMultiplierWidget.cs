using System;
using System.Collections.Generic;
using TMPro;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.UnityEngineExtensions;
using UnityEngine;
using UnityEngine.UI;
using Beebopbee.Core.Extensions;

namespace BBB
{
    public class CompetitionEventMultiplierWidget : ContextedUiBehaviour
    {
        [SerializeField] private Animator _segmentAnimator;
        [SerializeField] private TextMeshProUGUI[] _segmentValues;
        [SerializeField] private TextMeshProUGUI[] _segmentValues2;
        [SerializeField] private Image _leagueIcon;
        [SerializeField] private bool _showIcon;
        [SerializeField] private bool _playAnimation;
        
        /// <summary>
        /// Customizable filter to hide icon for specific game events.
        /// </summary>
        [SerializeField] private string[] _hideForSpecificGameEvents;
        
        private IGameEventManager _gameEventManager;
        private IScreensBuilder _screensBuilder;
        private GenericResourceProvider _genericResourceProvider;
        
        private static string _latestUsedGameEventUid;
        private string _lastLoadedSpriteName;
        private bool _displayOnGameEventEnded;

        protected override void InitWithContextInternal(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            
            _gameEventManager.OnGameEventExpired -= GameEventExpiredHandler;
            _gameEventManager.OnGameEventExpired += GameEventExpiredHandler;
        }
        
        private void GameEventExpiredHandler(GameEventBase obj)
        {
            if (_latestUsedGameEventUid != obj.Uid) return;
            if(_displayOnGameEventEnded) return;
            gameObject.SetActive(false);
            _latestUsedGameEventUid = null;
        }

        private string GetCurrentGameEventUid()
        {
            Predicate<GameEventBase> eventPredicate;
            
            if (_displayOnGameEventEnded)
            {
                eventPredicate = e =>
                            e.GameplayType == GameEventGameplayType.Competition &&
                            e.IsMultiplierScoreStreakActive() &&
                            e.CanShowInScreen(_screensBuilder.CurrentScreenType) &&
                            (e.Status is GameEventStatus.Active or GameEventStatus.Ended);
            }
            else
            {
                eventPredicate = e =>
                            e.GameplayType == GameEventGameplayType.Competition &&
                            e.IsMultiplierScoreStreakActive() &&
                            e.CanShowInScreen(_screensBuilder.CurrentScreenType) &&
                            e.Status is GameEventStatus.Active;
            }
            
            var currentEvent = _gameEventManager.GetHighestPriorityEvent(eventPredicate);
            
            return currentEvent?.Uid;
        }
        
        private bool IsEnabledForGameEvent(string gameEventUid)
        {
            if (string.IsNullOrEmpty(gameEventUid))
            {
                Debug.LogError("Attempt to check GameEventIcon for IsEnabledForGameEvent with empty gameEventUid");
                return false;
            }

            if (_hideForSpecificGameEvents == null)
            {
                return true;
            }

            var strippedEventUid = gameEventUid.RemoveDigits().RemoveVersion();

            foreach (var disabledEventName in _hideForSpecificGameEvents)
            {
                if (disabledEventName == strippedEventUid)
                {
                    return false;
                }
            }

            return true;
        }
        
        public void Refresh(bool displayOnGameEventEnded = false)
        {
            LazyInit();
            _displayOnGameEventEnded = displayOnGameEventEnded;
            var gameEventUid = GetCurrentGameEventUid();
            
            if (!gameEventUid.IsNullOrEmpty() && IsEnabledForGameEvent(gameEventUid))
            {
                _latestUsedGameEventUid = gameEventUid;
                gameObject.SetActive(true);
                RefreshMultiplierStatus(gameEventUid);
            }
            else
            {
                gameObject.SetActive(false);
            }
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            ReleaseLoadedSprite();
            if (_gameEventManager != null)
            {
                _gameEventManager.OnGameEventExpired -= GameEventExpiredHandler;
            }
        }
        
        private void ReleaseLoadedSprite()
        {
            if (_lastLoadedSpriteName == null) return;
            
            _genericResourceProvider?.ReleaseCached(_lastLoadedSpriteName);
            _lastLoadedSpriteName = null;
        }

        private void RefreshMultiplierStatus(string gameEventUid)
        {
            var isLevelScreen = (_screensBuilder.CurrentScreenType & ScreenType.Levels) > 0;
            var gameEvent = _gameEventManager.FindGameEventByUid(gameEventUid);
            
            if (gameEvent is CompetitionGameEvent competitionGameEvent)
            {
                var status = competitionGameEvent.IsMultiplierScoreStreakActive();
                var previousMultiplier = competitionGameEvent.GetPreviousScoreMultiplier();
                var currentScoreMultiplier = competitionGameEvent.GetScoreMultiplier(isLevelScreen);
                var scoreMultipliers = competitionGameEvent.GetScoreMultipliersForEvent()?.Values;
                List<int> segments = null;

                if (scoreMultipliers != null)
                {
                    segments = new List<int>();

                    foreach (var multiplier in scoreMultipliers)
                    {
                        segments.Add(multiplier);
                    }
                }

                if (!status || segments?.Count != _segmentValues.Length)
                {
                    gameObject.SetActive(false);
                    return;
                }

                _leagueIcon.enabled = false;
                
                if (_showIcon)
                {
                    var spriteName = competitionGameEvent.Uid + "_" +
                                     competitionGameEvent.EventLeaderboard.CurrentLeague.ToMultiplierSpriteName();

                    if (spriteName != _lastLoadedSpriteName)
                    {
                        ReleaseLoadedSprite();
                        _lastLoadedSpriteName = spriteName;
                        _genericResourceProvider.CacheAndLoadAsync<Sprite>(this, spriteName)
                            .Then(OnSpriteLoaded)
                            .Catch(Debug.LogError)
                            .Done();
                    }
                    else
                    {
                        OnSpriteLoaded(_leagueIcon?.sprite);
                    }
                }
                
                for (var i = 0; i < segments.Count; i++)
                {
                    _segmentValues[i].text = segments[i] + "x";
                    _segmentValues2[i].text = segments[i] + "x";
                }
                
                _segmentAnimator.PlayMainOrRebind();
                var isOnMaxMultiplier = previousMultiplier == currentScoreMultiplier;
                var currentIndex = segments.FindIndex(a => a == currentScoreMultiplier);
                
                if (_playAnimation && !isOnMaxMultiplier)
                {
                    _segmentAnimator.SetTrigger("Trans" + currentIndex);
                }
                else
                {
                    currentIndex += 1;
                    _segmentAnimator.SetTrigger("Idle" + currentIndex);
                }
            }
            else
            {
                gameObject.SetActive(false);
            }
        }
        
        public void OnHide()
        {
            gameObject.SetActive(false);
        }
        
        private void OnSpriteLoaded(Sprite sprite)
        {
            if (_leagueIcon is null) return;
            _leagueIcon.sprite = sprite;
            _leagueIcon.enabled = true;
        }
    }
}
