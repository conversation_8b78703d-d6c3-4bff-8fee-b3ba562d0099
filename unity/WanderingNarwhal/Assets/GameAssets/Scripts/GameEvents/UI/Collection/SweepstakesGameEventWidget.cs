using System.Collections;
using BBB.Audio;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.MMVibrations;
using BBB.Modals;
using BBB.Wallet;
using BebopBee.Core.Audio;
using BebopBee.Core.UI;
using DG.Tweening;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Hud;
using GameAssets.Scripts.Wallet.Visualizing;
using Spine.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class SweepstakesGameEventWidget : UnifiedGameEventWidget
    {
        private const int ProgressBarPageIndex = 0;

        [SerializeField]
        private HorizontalCarousel _horizontalCarousel;
        [SerializeField]
        private GameObject _progressBarView;

        [SerializeField] private SkeletonGraphic _progressTitleSpine;
        [SerializeField] private ParticleSystem _progressTitleVfx;
        [SerializeField] private CurrencyImpactReceiver _impactReceiver;

        [SerializeField] private Transform _rewardItem;
        [SerializeField] private Button _termsButton;
        [SerializeField] private GameObject _checkmark;

        [SerializeField] private string _titleIntroName;
        [SerializeField] private string _titleIdleName;
        [SerializeField] private string _titleOutroName;

        private ILocalizationManager _localizationManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private string _lastGameEventUid;
        private int _progressBarIndex;
        private bool _titleAnimationInProgress;
        private bool _levelWinFlowInProgress;

        public override void Init(IContext context)
        {
            base.Init(context);

            _localizationManager = context.Resolve<ILocalizationManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _horizontalCarousel.Initialize();

            //do not call ReplaceOnClick
            //to not break the internal flow of terms button
            _termsButton.AddOnClick(OnTermsButtonClicked);
        }

        private void SetupItemView(SweepstakesItemView itemView, GameEventBase gameEvent)
        {
            if (itemView.InfoButton != null)
            {
                itemView.InfoButton.ReplaceOnClick(OnInfoButtonClicked);
            }

            if (itemView.TermsButton != null)
            {
                //do not call ReplaceOnClick
                //to not break the internal flow of terms button
                itemView.TermsButton.AddOnClick(OnTermsButtonClicked);
            }

            if (itemView.ClockCountdownText)
            {
                itemView.ClockCountdownText.Init(_localizationManager, gameEvent.GetTimeLeft);
            }

            if (itemView.Title)
            {
                itemView.Title.SetText(gameEvent.NameText);
            }
        }

        private void OnPageChangedHandler(int page)
        {
            if (page != _progressBarIndex)
            {
                _horizontalCarousel.ResumeAutoScroll();
            }
            else
            {
                PauseAutoScrollAndAnimateTitle();
            }
        }

        private void PauseAutoScrollAndAnimateTitle()
        {
            if (_titleAnimationInProgress)
            {
                return;
            }

            _titleAnimationInProgress = true;
            _horizontalCarousel.PauseAutoScroll();
            _progressTitleVfx.Play();

            var intro = _progressTitleSpine.AnimationState.SetAnimation(0, _titleIntroName, false);
            intro.Complete += _ => PlayIdle();
            return;

            void PlayIdle()
            {
                var idle = _progressTitleSpine.AnimationState.SetAnimation(0, _titleIdleName, false);
                idle.Complete += _ => PlayOutro();
            }

            void PlayOutro()
            {
                var outro = _progressTitleSpine.AnimationState.SetAnimation(0, _titleOutroName, false);
                outro.Complete += _ =>
                {
                    _titleAnimationInProgress = false;
                    _progressTitleVfx.Stop(true);
                    if (!_levelWinFlowInProgress)
                    {
                        _horizontalCarousel.ResumeAutoScroll();
                    }
                    else
                    {
                        PauseAutoScrollAndAnimateTitle();
                    }
                };
            }
        }

        private void PopulateCarousel(SweepstakesGameEvent gameEvent)
        {
            _progressBarView.transform.SetParent(transform);

            _horizontalCarousel.AutoScrollDelayTime =
                _gameEventManager.SweepStakesGameEventConfig.MainBannersStayDuration;
            _horizontalCarousel.OverrideScrollDuration(_gameEventManager.SweepStakesGameEventConfig
                .MainBannersScrollDuration);

            _horizontalCarousel.Clear();
            _horizontalCarousel.AddChild(_progressBarView);
            _progressBarIndex = _horizontalCarousel.ItemsCount - 1;
            var itemView = _progressBarView.GetComponent<SweepstakesItemView>();
            SetupItemView(itemView, gameEvent);

            for (int i = 0; i < gameEvent.TotalMilestones; i++)
            {
                var milestoneBannerPrefab =
                    _gameEventResourceManager.GetGenericAsset<GameObject>(gameEvent.Uid,
                        $"{GameEventResKeys.MilestoneBanner}_{i + 1}");

                if (milestoneBannerPrefab == null)
                    continue;

                var bannerObject = Instantiate(milestoneBannerPrefab, transform);
                _horizontalCarousel.AddChild(bannerObject);
                itemView = bannerObject.GetComponent<SweepstakesItemView>();
                SetupItemView(itemView, gameEvent);
            }

            _horizontalCarousel.OnPageChanged -= OnPageChangedHandler;
            _horizontalCarousel.OnPageChanged += OnPageChangedHandler;
            PauseAutoScrollAndAnimateTitle();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            _horizontalCarousel.Initialize();
            if (!_levelWinFlowInProgress)
            {
                _horizontalCarousel.StartAutoScroll();
            }
        }

        protected override void OnAnimationCompleteHandler()
        {
            if (!_levelWinFlowInProgress)
            {
                _horizontalCarousel.StartAutoScroll();
            }
            else
            {
                PauseAutoScrollAndAnimateTitle();
            }
        }

        protected override void OnAnimationScheduledHandler()
        {
            _horizontalCarousel.SetActivePage(_progressBarIndex);
            _horizontalCarousel.StopAutoScroll();
        }

        protected override bool IsLocked()
        {
            return _genericHudManager.IsBlockedByOther;
        }

        public override void Refresh()
        {
            base.Refresh();

            _currentGameEvent = GetCurrentGameEvent();
            if (_currentGameEvent != null && _lastGameEventUid != _currentGameEvent.Uid)
            {
                PopulateCarousel(_currentGameEvent as SweepstakesGameEvent);
                _lastGameEventUid = _currentGameEvent.Uid;
            }
        }

        protected override void RefreshWithNoAnimation(IGameEventWithMilestones gameEventWithMilestones, out int milestoneIndex,
            out int totalScore)
        {
            base.RefreshWithNoAnimation(gameEventWithMilestones, out milestoneIndex, out totalScore);

            var isLast = milestoneIndex >= gameEventWithMilestones.GetLastMilestoneIndex();
            var goalReached = totalScore >= gameEventWithMilestones.GetMilestoneByIndex(milestoneIndex).Goal;
            _checkmark.SetActive(isLast && goalReached);
        }

        private void OnTermsButtonClicked()
        {
            var productId = _horizontalCarousel.ActivePage == ProgressBarPageIndex ? DauInteractions.Sweepstakes.ProgressBar : DauInteractions.Sweepstakes.Carousel;
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Sweepstakes.Name, DauInteractions.Sweepstakes.Terms, productId));
        }

        private void OnInfoButtonClicked()
        {
            var productId = _horizontalCarousel.ActivePage == ProgressBarPageIndex ? DauInteractions.Sweepstakes.ProgressBar : DauInteractions.Sweepstakes.Carousel;
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Sweepstakes.Name, DauInteractions.Sweepstakes.Info, productId));

            AudioProxy.PlaySound(GenericSoundIds.GameEventHudButton);
            var infoModal = _modalsBuilder.CreateModalView<CompetitionEventInfoModalController>(ModalsType.CompetitionEventInfo);
            infoModal.Setup(_currentGameEvent, CompetitionInfoType.Main);
            infoModal.ShowModal(ShowMode.Delayed, ModalsTags.GameEvent);
        }

        public override bool ShouldBeShown()
        {
            var currentScreen = _screensManager.GetCurrentScreenType();
            return currentScreen is ScreenType.EpisodeScreen or ScreenType.SideMapScreen
                   && _currentGameEvent is { Uid: not null, GameplayType: GameEventGameplayType.Sweepstakes }
                   && _currentGameEvent.ShouldShowHudIcon();
        }

        protected override GameEventBase GetCurrentGameEvent()
        {
            return _gameEventManager.GetHighestPriorityEvent(ge =>
                ge.GameplayType == GameEventGameplayType.Sweepstakes && ge.ShouldShowHudIcon() &&
                ge.CanShowInScreen(_screensManager.GetCurrentScreenType()));
        }

        private IEnumerator OnNewMilestoneOpenHandler()
        {
            var showingReward = true;
            var parent = _rewardItem.parent;
            var position = _rewardItem.position;
            var scale = _rewardItem.localScale;
            var siblingIndex = _rewardItem.GetSiblingIndex();

            _gameEventManager.ShowSweepstakesMilestoneRewardModal(_currentGameEvent as SweepstakesGameEvent,
                _rewardItem,
                () =>
                {
                    _rewardItem.SetParent(parent);
                    _rewardItem.position = position;
                    _rewardItem.localScale = scale;
                    _rewardItem.SetSiblingIndex(siblingIndex);

                    showingReward = false;
                });
            yield return new WaitUntil(() => !showingReward);
        }

        protected override IEnumerator AnimateMilestone(IGameEventWithMilestones gameEventWithMilestones, int milestoneIndex, int lastMilestoneIndex)
        {
            var previousTotalScore = gameEventWithMilestones.PreviousTotalScore;
            var currentScore = gameEventWithMilestones.CurrentScore;

            var milestone = gameEventWithMilestones.GetMilestoneByIndex(milestoneIndex);
            var previousRelativeScore = Mathf.Max(milestone.GetRelativeScore(previousTotalScore), 0);
            var currentRelativeScore = milestone.GetRelativeScore(currentScore);
            var milestoneGoal = milestone.GetGoal();

            var goalReached = currentRelativeScore >= milestoneGoal;
            if (goalReached)
            {
                _modalsManager.HideAllModals();
                _genericModalsBlocker.StartBlocking(GenericModalsBlocker.BlockingReason.SweepstakesMilestone, _progressBarMaxAnimDuration);
                _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<SweepstakesMilestoneReachedEvent>());
            }

            var tween = AnimateProgressBarAndText(previousRelativeScore, currentRelativeScore, milestoneGoal);
            _cancelableTweens.Add(tween);

            while (tween.IsPlaying())
                yield return null;

            if (!goalReached)
                yield break;

            var isLastMilestone = milestoneIndex >= lastMilestoneIndex;
            AudioProxy.PlaySound(GenericSoundIds.GameEventProgressBar);

            _genericModalsBlocker.StopBlocking(GenericModalsBlocker.BlockingReason.SweepstakesMilestone);
            if (previousRelativeScore < milestoneGoal)
            {
                yield return OnNewMilestoneOpenHandler();
            }

            if (isLastMilestone)
            {
                _checkmark.SetActive(true);
                yield break;
            }

            if (_doNotWaitForNextGoalIntro)
            {
                _coroutineExecutor.StartCoroutine(_rewardController.ShowcaseNextGoal(gameEventWithMilestones, milestoneIndex, true));
                yield return new WaitForSeconds(_nextGoalFixedDelay);
            }
            else
            {
                yield return _rewardController.ShowcaseNextGoal(gameEventWithMilestones, milestoneIndex, true);
            }
        }

        protected override void ReportDauInteractionEvent(int notifierStatus)
        {
            var productId = string.Empty;
            var activePage = _horizontalCarousel.ActivePage;
            switch (activePage)
            {
                case ProgressBarPageIndex:
                    if (_currentGameEvent is IGameEventWithMilestones milestoneEvent)
                    {
                        productId = $"{DauInteractions.Sweepstakes.ProgressBarReward}{milestoneEvent.GetCurrentMilestoneIndex() + 1}"; //+1 to report the upcoming milestone number
                    }

                    break;
                default: //carousel
                    productId = $"{DauInteractions.Sweepstakes.CarouselReward}{activePage}";
                    break;
            }

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.Sweepstakes.Name, productId, notifierStatus));
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _horizontalCarousel.OnPageChanged -= OnPageChangedHandler;
        }

        public void SetFlowInProgress(bool inProgress)
        {
            _levelWinFlowInProgress = inProgress;
            if (_levelWinFlowInProgress)
            {
                _horizontalCarousel.StopAutoScroll();
            }
            else
            {
                _horizontalCarousel.StartAutoScroll();
            }
        }
    }
}