using System;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.RaceEvents;
using BBB.UI;
using BBB.UI.Core;
using Beebopbee.Core.Extensions;
using GameAssets.Scripts.UI.SpeechBubble;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    /// <summary>
    /// this is a generic class for game event icon 
    /// </summary>
    public class GameEventIcon : ContextedUiBehaviour
    {
        private enum DisplayIconType
        {
            EventIcon,
            ScoreIcon,
            HudIcon,
            OOMWarningIcon,
        }

        private const string EventIconEnding = "_" + GameEventResKeys.Icon;
        private const string ScoreIconEnding = "_" + GameEventResKeys.ScoreIcon;
        private const string HUDIconEnding = "_" + GameEventResKeys.HudIcon;
        private const string DoubleTextDescription = "DOUBLE_SCORE_DESCRIPTION";
        
        [SerializeField] protected Image _image;
        [SerializeField] private DisplayIconType _iconType;
        [Header("2x")]
        [SerializeField] private GameObject[] _secondBadgeElements;
        [SerializeField] private TextMeshProUGUI _lowestMultiplier;
        [SerializeField] private GameObject _timer;
        [SerializeField] private RectTransform _badgeRoot;
        [SerializeField] private float _badgeRotationOffset;
        [SerializeField] private Button _infoButton;
        [SerializeField] private SpeechBubbleConfig _speechBubbleConfig;
        [SerializeField] private Animator _animator;
        [SerializeField] private Sprite _normalEventBadgeSprite;
        [SerializeField] private Sprite _competitionEventBadgeSprite;
        [SerializeField] private Image[] _badgeImages;

        /// <summary>
        /// Customizeable filter to hide icon for specific game events.
        /// </summary>
        [SerializeField] private string[] _hideForSpecificGameEvents;

        [Tooltip("Children will be refreshed with parent")]
        public GameObject _childIcon;

        [SerializeField]
        private float _scale = 1.0f;

        private Vector2 _initialDeltaSize;
        private Vector2 _originalSize;
        
        private IGameEventManager _gameEventManager;
        private GenericResourceProvider _genericResourceProvider;
        private IScreensBuilder _screensBuilder;
        private IRaceEventManager _raceEventManager;
        private IEventDispatcher _eventDispatcher;
        private ISpeechBubbleManager _speechBubbleManager;
        private static string _latestUsedGameEventUid;
        private string _lastLoadedSpriteName;
        private bool _isRetry;
        private LevelOutcome? _levelOutcome;

        public Image ImageRef => _image;
        private static readonly int Retry = Animator.StringToHash("Retry");
        private static readonly int Normal = Animator.StringToHash("Normal");


        private void InitIconSize()
        {
            if (_image.IsNull()) return;
            RectTransform rT = _image.GetComponent<RectTransform>();
            _initialDeltaSize = rT.sizeDelta;
            _originalSize = new Vector2(rT.rect.width, rT.rect.height);
        }

        protected override void InitWithContextInternal(IContext context)
        {
            InitIconSize();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _raceEventManager = context.Resolve<IRaceEventManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _speechBubbleManager = context.Resolve<ISpeechBubbleManager>();

            _gameEventManager.OnGameEventExpired -= GameEventExpiredHandler;
            _gameEventManager.OnGameEventExpired += GameEventExpiredHandler;

            _eventDispatcher.RemoveListener<LevelEndedEvent>(OnLevelEnded);
            _eventDispatcher.AddListener<LevelEndedEvent>(OnLevelEnded);

            if (_infoButton != null)
                _infoButton.ReplaceOnClick(ToggleMultiplierScoreEventInfo);
        }

        private void OnLevelEnded(LevelEndedEvent levelEndedEvent)
        {
            _levelOutcome = levelEndedEvent.LevelOutcome;
        }

        private void ToggleMultiplierScoreEventInfo()
        {
            if (_speechBubbleConfig.TargetTransform != null)
                _speechBubbleManager.ToggleSpeechBubble(_speechBubbleConfig, null, DoubleTextDescription);
        }

        private void GameEventExpiredHandler(GameEventBase obj)
        {
            if (_latestUsedGameEventUid == obj.Uid)
            {
                gameObject.SetActive(false);
                _latestUsedGameEventUid = null;
            }
        }

        private string GetIconName(string gameEventUid)
        {
            if (gameEventUid.IsNullOrEmpty())
            {
                Debug.LogError($"Attempt to get EventIconName for empty gameEventUid");
                return string.Empty;
            }

            var currentEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                ev.Status == GameEventStatus.Active && ev.CanShowInScreen(_screensBuilder.CurrentScreenType) && ev.Uid == gameEventUid);
            if (currentEvent != null && currentEvent.Uid == gameEventUid)
            {
                switch (currentEvent)
                {
                    case CollectionGameEvent collectionGameEvent:
                    {
                        var iconName = $"{currentEvent.EventResourceId}_{collectionGameEvent.GetScoreIconSpriteNameForCurrentMilestone()}";
                        return iconName;
                    }
                    case CompetitionGameEvent competitionGameEvent when
                        competitionGameEvent.IsMultiplierScoreStreakActive() && _iconType == DisplayIconType.OOMWarningIcon:
                    {
                        var spriteName = competitionGameEvent.Uid + "_" +
                                         competitionGameEvent.EventLeaderboard.CurrentLeague.ToBadgeSpriteName();
                        return spriteName;
                    }
                }
            }

            string GetEnding()
            {
                switch (_iconType)
                {
                    case DisplayIconType.EventIcon:
                        return EventIconEnding;
                    case DisplayIconType.ScoreIcon:
                        return ScoreIconEnding;
                    case DisplayIconType.HudIcon:
                        return HUDIconEnding;
                    default:
                        Debug.LogError($"Unhandled event icon type {_iconType.ToString()}");
                        return string.Empty;
                }
            }

            var result = gameEventUid.RemoveDigits();
            if (UseCommonEventIcons(gameEventUid))
                result = result.RemoveVersion();
            return result + GetEnding();
        }

        private bool UseCommonEventIcons(string gameEventUid)
        {
            var gameEvent = _gameEventManager.FindGameEventByUid(gameEventUid);
            if (gameEvent == null)
                return true;
            return gameEvent.GameplayType != GameEventGameplayType.EndOfContent;
        }

        private string GetCurrentGameEventUid(out GameEventBase gameEvent)
        {
            var currentEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                ev.Status == GameEventStatus.Active &&
                ev.CanShowInScreen(_screensBuilder.CurrentScreenType) && IsEnabledForGameEvent(ev.Uid));
            if (currentEvent == null)
            {
                currentEvent = _gameEventManager.GetHighestPriorityEvent(ev =>
                    (ev.Status is GameEventStatus.Accomplished
                        or GameEventStatus.AccomplishedTripstagramViewed
                        or GameEventStatus.AccomplishmentRewardCollected
                        or GameEventStatus.Failed
                        or GameEventStatus.FailureRewardCollected)
                    && ev.CanShowInScreen(_screensBuilder.CurrentScreenType));
            }

            var gameEventUid = currentEvent != null
                               && currentEvent.GetTimeLeft().TotalSeconds > 0f
                               && !currentEvent.Uid.IsNullOrEmpty()
                ? currentEvent.Uid
                : _latestUsedGameEventUid;

            currentEvent = gameEventUid.IsNullOrEmpty() ? null : _gameEventManager.FindGameEventByUid(gameEventUid);
            if (currentEvent == null || !currentEvent.CanShowInScreen(_screensBuilder.CurrentScreenType))
                gameEventUid = null;

            gameEvent = currentEvent;
            return gameEventUid;
        }

        private bool IsEnabledForGameEvent(string gameEventUid)
        {
            if (gameEventUid.IsNullOrEmpty())
            {
                Debug.LogError($"Attempt to check GameEventIcon for IsEnabledForGameEvent with empty gameEventUid");
                return false;
            }

            if (_hideForSpecificGameEvents == null)
                return true;

            var strippedEvenUid = gameEventUid.RemoveDigits().RemoveVersion();
            foreach (var disabledEventName in _hideForSpecificGameEvents)
            {
                if (disabledEventName == strippedEvenUid || disabledEventName == gameEventUid)
                {
                    return false;
                }
            }

            return true;
        }

        [ContextMenu("RefreshSprite")]
        public void Refresh()
        {
            LazyInit();
            var gameEventUid = GetCurrentGameEventUid(out _);
            TryRefreshForGameEvent(gameEventUid);
        }

        public bool TryRefreshForGameEvent(string gameEventUid)
        {
            var result = false;

            LazyInit();
            if (!gameEventUid.IsNullOrEmpty() && IsEnabledForGameEvent(gameEventUid))
            {
                LoadSprite(gameEventUid);
                RefreshChildren(gameEventUid);
                UpdateBadgeSprite(gameEventUid);
                var multiplierInfo = GetMultiplierInfo(gameEventUid);
                RefreshMultiplierStatus(multiplierInfo);
                result = true;
            }
            else
            {
                gameObject.SetActive(false);
            }

            return result;
        }

        private (bool, int, int) GetMultiplierInfo(string gameEventUid)
        {
            var isLevelScreen = (_screensBuilder.CurrentScreenType & ScreenType.Levels) > 0;

            var gameEvent = _gameEventManager.FindGameEventByUid(gameEventUid);
            if (gameEvent != null)
            {
                return (gameEvent.IsMultiplierScoreStreakActive(), gameEvent.GetLowestMultiplier(), gameEvent.GetScoreMultiplier(isLevelScreen));
            }

            var raceGameEvent = _raceEventManager.GetRaceEvent(gameEventUid);
            return raceGameEvent != null
                ? (raceGameEvent.Joined && raceGameEvent.IsMultiplierScoreStreakActive(), raceGameEvent.GetLowestMultiplier(), raceGameEvent.GetScoreMultiplier(isLevelScreen))
                : (false, 0, 0);
        }

        private void LoadSprite(string gameEventUid)
        {
            gameObject.SetActive(true);

            var iconName = GetIconName(gameEventUid);
            if (_lastLoadedSpriteName == iconName)
                return;

            _latestUsedGameEventUid = gameEventUid;
            ReleaseLoadedSprite();
            _lastLoadedSpriteName = iconName;
            _genericResourceProvider.CacheAndLoadAsync<Sprite>(this, GetIconName(gameEventUid), AssetLoadPriority.InQueue)
                .Then(OnSpriteLoaded)
                .Catch(Debug.LogError)
                .Done();
        }

        public bool TryRefreshByLevel(string levelUid, bool isRetry)
        {
            var result = false;

            LazyInit();
            gameObject.SetActive(false);
            _isRetry = isRetry;
            var gameEventUid = GetCurrentGameEventUid(out _);
            var multiplierInfo = (false, 0, 0);
            if (!gameEventUid.IsNullOrEmpty() && IsEnabledForGameEvent(gameEventUid))
            {
                multiplierInfo = GetMultiplierInfo(gameEventUid);
                var canScoresBeGathered = _gameEventManager.CanLabelsBeGatheredAtLevel(levelUid);
                if (canScoresBeGathered)
                {
                    LoadSprite(gameEventUid);
                    ChangeIconSize(GetGameEvent(gameEventUid) is CollectionGameEvent);
                    result = true;
                    RefreshMultiplierStatus(multiplierInfo);
                    RefreshChildren(gameEventUid);
                    UpdateBadgeSprite(gameEventUid);
                }
            }

            return result;
        }

        private void UpdateBadgeSprite(string gameEventUid)
        {
            var sprite = _normalEventBadgeSprite;

            if (GetGameEvent(gameEventUid) is CompetitionGameEvent competitionGameEvent &&
                competitionGameEvent.SpecialType == SpecialCompetitionEventType.Casual)
            {
                sprite = _competitionEventBadgeSprite;
            }

            if (_badgeImages is not { Length: > 0 }) return;
            foreach (var badgeImage in _badgeImages)
            {
                badgeImage.sprite = sprite;
            }
        }

        private IComparableEvent GetGameEvent(string gameEventUid)
        {
            if (gameEventUid.IsNullOrEmpty())
                return null;

            var gameEvent = _gameEventManager.FindGameEventByUid(gameEventUid);
            if (gameEvent != null)
                return gameEvent;

            return _raceEventManager.GetRaceEvent(gameEventUid);
        }

        private void OnSpriteLoaded(Sprite sprite)
        {
            if (_image is null || sprite is null) return;
            _image.sprite = sprite;
            _image.enabled = true;
        }

        private void RefreshChildren(string optionalExplicitGameEventUid = null)
        {
            if (_childIcon == null || _childIcon == gameObject) return;
            var icon = _childIcon.GetComponent<GameEventIcon>();
            if (icon == null) return;
            if (optionalExplicitGameEventUid.IsNullOrEmpty())
            {
                icon.Refresh();
            }
            else
            {
                icon.TryRefreshForGameEvent(optionalExplicitGameEventUid);
            }
        }

        private void ChangeIconSize(bool scale)
        {
            var rectTransform = _image.GetComponent<RectTransform>();
            if (scale)
            {
                rectTransform.sizeDelta = _initialDeltaSize - (1.0f - _scale) * _originalSize;
            }
            else
            {
                rectTransform.sizeDelta = _initialDeltaSize;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ReleaseLoadedSprite();
            if (_gameEventManager != null)
            {
                _gameEventManager.OnGameEventExpired -= GameEventExpiredHandler;
            }

            _eventDispatcher?.RemoveListener<LevelEndedEvent>(OnLevelEnded);
        }

        private void ReleaseLoadedSprite()
        {
            if (_lastLoadedSpriteName == null) return;
            _genericResourceProvider?.ReleaseCached(_lastLoadedSpriteName);
            _lastLoadedSpriteName = null;
        }

        private void RefreshMultiplierStatus((bool isMultiplierActive, int lowestMultiplier, int currenMultiplier) multiplierInfo)
        {
            if (_infoButton != null)
                _infoButton.interactable = multiplierInfo.isMultiplierActive;

            if (_secondBadgeElements.IsNullOrEmpty())
                return;

            if (_isRetry && _levelOutcome == LevelOutcome.ShuffleFailed)
                return;

            if (multiplierInfo.isMultiplierActive)
            {
                foreach (var badgeElement in _secondBadgeElements)
                {
                    badgeElement.SetActive(true);
                    if (badgeElement.TryGetComponent(out TextMeshProUGUI text))
                    {
                        text.text = multiplierInfo.currenMultiplier + "x";
                    }

                    if (_lowestMultiplier != null)
                    {
                        _lowestMultiplier.text = multiplierInfo.lowestMultiplier + "x";
                    }

                    if (_animator != null)
                    {
                        _animator.SetTrigger(_isRetry ? Retry : Normal);
                    }
                }

                if (_timer != null)
                    _timer.SetActive(false);
                _badgeRoot.localEulerAngles = new Vector3(0, 0, _badgeRotationOffset);
            }
            else
            {
                foreach (var badgeElement in _secondBadgeElements)
                {
                    badgeElement.SetActive(false);
                }

                if (_timer != null)
                {
                    _timer.SetActive(true);
                    GetCurrentGameEventUid(out var gameEvent);
                    _timer.GetComponent<ClockCountdownTextInitializer>().Refresh(gameEvent);
                }

                _badgeRoot.localEulerAngles = Vector3.zero;
            }
        }
    }
}