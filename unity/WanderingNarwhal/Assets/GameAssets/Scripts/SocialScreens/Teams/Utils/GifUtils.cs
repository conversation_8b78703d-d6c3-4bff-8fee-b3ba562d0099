using System.Collections.Generic;
using ThreeDISevenZeroR.UnityGifDecoder;
using UnityEngine;

namespace GameAssets.Scripts.SocialScreens.Teams.Utils
{
    public static class GifUtils
    {
        public static Texture2D GetFirstFrame(string path)
        {
            using var gifStream = new GifStream(path);
            while (gifStream.HasMoreData)
            {
                switch (gifStream.CurrentToken)
                {
                    case GifStream.Token.Image:
                        var image = gifStream.ReadImage();
                        var firstFrame = new Texture2D(
                            gifStream.Header.width,
                            gifStream.Header.height,
                            TextureFormat.ARGB32, false);
                        firstFrame.SetPixels32(image.colors);
                        firstFrame.Apply();
                        return firstFrame;
                    default:
                        gifStream.SkipToken(); // Other tokens
                        break;
                }
            }

            return null;
        }

        public static void DecodeGif(byte[] gifBytes, out List<Texture2D> frames, out List<float> frameDelays)
        {
            frames = new List<Texture2D>();
            frameDelays = new List<float>();

            using var gifStream = new GifStream(gifBytes);
            while (gifStream.HasMoreData)
            {
                while (gifStream.HasMoreData)
                {
                    switch (gifStream.CurrentToken)
                    {
                        case GifStream.Token.Image:
                            var image = gifStream.ReadImage();
                            var frame = new Texture2D(
                                gifStream.Header.width,
                                gifStream.Header.height,
                                TextureFormat.ARGB32, false);

                            frame.SetPixels32(image.colors);
                            frame.Apply();

                            frames.Add(frame);
                            frameDelays.Add(image.SafeDelaySeconds); // More about SafeDelay below
                            break;

                        case GifStream.Token.Comment:
                            var commentText = gifStream.ReadComment();
                            Debug.Log(commentText);
                            break;

                        default:
                            gifStream.SkipToken(); // Other tokens
                            break;
                    }
                }
            }
        }
    }
}