using Bebopbee.Core.Systems.RpcCommandManager;
using RPC.Teams;

namespace GameAssets.Scripts.SocialScreens.Teams.RPCCommands
{
    public class JoinTeamCommand : RpcCommand<JoinTeam>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.TeamUid = (string)args[0];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 1;
        }
    }
}