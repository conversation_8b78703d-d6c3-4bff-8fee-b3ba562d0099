using Bebopbee.Core.Systems.RpcCommandManager;
using RPC.Teams;

namespace GameAssets.Scripts.SocialScreens.Teams.RPCCommands
{
    public class SearchTeamsCommand : RpcCommand<SearchTeams>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.Query = (string)args[0];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 1;
        }
    }
}