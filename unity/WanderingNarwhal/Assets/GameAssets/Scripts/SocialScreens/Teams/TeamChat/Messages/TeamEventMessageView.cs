using System;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.RaceEvents.UI;
using BBB.Social.Chat;
using BBB.TeamEvents;
using BBB.UI.Core;
using Bebopbee.Core.Extensions.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages
{
    public abstract class TeamEventMessageView<T> : ContextedUiBehaviour, IMessageView where T : TeamEvent
    {
        private const string MessageObject = "message_object";

        [SerializeField] private RectTransform _root;
        [SerializeField] private Canvas _rootCanvas;
        [SerializeField] private TextMeshProUGUI _title;
        [SerializeField] private Button _playButton;
        [SerializeField] private Transform _eventViewRoot;

        [SerializeField] private GameObject[] _senderInfoHolder;
        [SerializeField] private AsyncAvatar _asyncAvatar;

        private ITeamEventManager _teamEventManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private Action<Type> _nudgeTeamCoopClickedCallback;

        public ChatMessage Message { get; private set; }
        public RectTransform RectTransform => _root;
        protected ILocalizationManager LocalizationManager { get; private set; }

        protected override void InitWithContextInternal(IContext context)
        {
            LocalizationManager = context.Resolve<ILocalizationManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _playButton.ReplaceOnClick(PlayButtonHandler);
        }

        public void Setup(ChatMessage message, bool isHeadless, Action<Type> nudgeTeamCoopClickedCallback)
        {
            LazyInit();

            Message = message;
            _nudgeTeamCoopClickedCallback = nudgeTeamCoopClickedCallback;
            _title.text = GetMessageText();

            _eventViewRoot.RemoveAllChildren();
            _senderInfoHolder.Enable(!isHeadless);
            if (!isHeadless)
            {
                _asyncAvatar.Setup(new AvatarInfo(message.Sender.Pic));
            }

            if (_teamEventManager.GetHighestPriorityEvent() is T teamEvent)
            {
                var eventVisualConfig = _gameEventResourceManager.GetGenericAsset<TeamCoopEventVisualConfig>(teamEvent.EventResourceId, GameEventResKeys.TeamCoopGameEventSettings);
                if (eventVisualConfig == null)
                {
                    BDebug.LogError(LogCat.General, $"{GameEventResKeys.TeamCoopGameEventSettings} not found for event {teamEvent.EventResourceId}");
                    return;
                }

                var prefab = eventVisualConfig.FindPrefabByName(MessageObject);
                if (prefab == null)
                {
                    BDebug.LogError(LogCat.General, $"{MessageObject} not found in {GameEventResKeys.TeamCoopGameEventSettings} for event {teamEvent.EventResourceId}");
                    return;
                }

                var messageObject = Instantiate(prefab, _eventViewRoot);
                messageObject.SetActive(true);
            }
        }

        protected abstract string GetMessageText();

        public void SetVisible(bool visible)
        {
            _rootCanvas.enabled = visible;
        }

        public void SetConnectionState(bool connected)
        {
        }

        public void Destroy()
        {
        }

        private void PlayButtonHandler()
        {
            _nudgeTeamCoopClickedCallback?.Invoke(typeof(T));
        }
    }
}