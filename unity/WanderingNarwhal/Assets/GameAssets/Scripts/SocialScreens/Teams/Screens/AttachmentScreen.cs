using System;
using BBB;
using BBB.DI;
using BBB.UI.Core;
using BebopBee;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.Social;
using BBB.Chat;
using GameAssets.Scripts.SocialScreens.Teams.Utils;
using RenderHeads.Media.AVProVideo;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using BBB.Social.Chat;

namespace GameAssets.Scripts.SocialScreens.Teams.Screens
{
    public class AttachmentScreen : ContextedUiBehaviour
    {
        [SerializeField] private Button _closeButton;

        [Header("Image attachment")]
        [SerializeField] private GameObject _imageHolder;
        [SerializeField] private AsyncImage _image;
        [SerializeField] private Transform _imageTransformationRoot;
        [SerializeField] private AspectRatioFitter _aspectRatioFitter;

        [Header("Video attachment")]
        [SerializeField] private GameObject[] _videoHolders;
        [SerializeField] private GameObject _videoLoadingHolder;
        [SerializeField] private MediaPlayer _videoPlayer;

        [SerializeField] private Button _playVideoButton;
        [SerializeField] private Button _pauseVideoButton;

        [SerializeField] private TextMeshProUGUI _progressTimeText;
        [SerializeField] private TextMeshProUGUI _durationText;
        [SerializeField] private Slider _timeProgressSlider;

        [Header("Gif attachment")]
        [SerializeField] private GameObject _gifHolder;
        [SerializeField] private AsyncGif _asyncGif;

        private UserSettings _userSettings;
        private RenderTexture _renderTexture;

        private string _attachmentUrl;
        private ChatMessageAttachmentType _attachmentType;
        private float _aspectRatio;
        private bool _inversed;
        private bool _rewinded;
        private int _imageOrientation;

        protected override void Awake()
        {
            base.Awake();
            
            _closeButton.ReplaceOnClick(Hide);
            _playVideoButton.ReplaceOnClick(ResumeVideoButtonHandler);
            _pauseVideoButton.ReplaceOnClick(PauseVideoButtonHandler);

            _videoPlayer.Events.AddListener(VideoPlayerEventsHandler);

            _timeProgressSlider.onValueChanged.AddListener(ValueChangedListener);
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _userSettings = context.Resolve<UserSettings>();
        }

        private void ResumeVideoButtonHandler()
        {
            if (_videoPlayer.Control.IsFinished())
            {
                _rewinded = true;
                _videoPlayer.Rewind(false);
            }
            else
            {
                _videoPlayer.Play();
                ShowPlayButton(false);
            }
        }

        private void PauseVideoButtonHandler()
        {
            _videoPlayer.Pause();
            ShowPauseButton(false);
            ShowPlayButton(true);
        }

        private void ShowPauseButton(bool show)
        {
            _pauseVideoButton.gameObject.SetActive(show);
        }

        private void ShowPlayButton(bool show)
        {
            _playVideoButton.gameObject.SetActive(show);
        }

        private void ShowVideoLoadingHolder(bool show)
        {
            _videoLoadingHolder.SetActive(show);
        }

        private void SetInteractableSlider(bool interactable)
        {
            _timeProgressSlider.interactable = interactable;
        }

        private void VideoPlayerEventsHandler(MediaPlayer mediaPlayer, MediaPlayerEvent.EventType eventType, ErrorCode arg2)
        {
            if (AppDefinesConverter.BbbDebug && Application.platform == RuntimePlatform.IPhonePlayer)
            {
                Debug.LogError(eventType.ToString());
            }

            switch (eventType)
            {
                case MediaPlayerEvent.EventType.MetaDataReady:
                    UpdateTime(0f, (float)_videoPlayer.Info.GetDuration());
                    SetInteractableSlider(true);
                    break;
                case MediaPlayerEvent.EventType.Started:
                    ShowVideoLoadingHolder(false);
                    ShowPlayButton(false);
                    ShowPauseButton(true);
                    break;
                case MediaPlayerEvent.EventType.FinishedPlaying:
                    ShowVideoLoadingHolder(false);
                    ShowPauseButton(false);
                    ShowPlayButton(true);
                    break;
                case MediaPlayerEvent.EventType.Stalled:
                case MediaPlayerEvent.EventType.StartedSeeking:
                    _videoLoadingHolder.SetActive(true);
                    break;
                case MediaPlayerEvent.EventType.Unstalled:
                    _videoLoadingHolder.SetActive(false);
                    break;
                case MediaPlayerEvent.EventType.FinishedSeeking:
                    _videoLoadingHolder.SetActive(false);

                    if (_rewinded)
                    {
                        _videoPlayer.Play();

                        ShowPlayButton(false);
                        ShowPauseButton(true);
                    }
                    else
                    {
                        _videoPlayer.Pause();

                        ShowPlayButton(true);
                        ShowPauseButton(false);
                    }

                    _rewinded = false;

                    break;
                case MediaPlayerEvent.EventType.ResolutionChanged:
                case MediaPlayerEvent.EventType.ReadyToPlay:
                case MediaPlayerEvent.EventType.FirstFrameReady:
                case MediaPlayerEvent.EventType.Closing:
                case MediaPlayerEvent.EventType.Error:
                case MediaPlayerEvent.EventType.SubtitleChange:
                case MediaPlayerEvent.EventType.StartedBuffering:
                case MediaPlayerEvent.EventType.FinishedBuffering:
                case MediaPlayerEvent.EventType.PropertiesChanged:
                case MediaPlayerEvent.EventType.PlaylistItemChanged:
                case MediaPlayerEvent.EventType.PlaylistFinished:
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(eventType), eventType, null);
            }
        }

        private void SpriteUpdatedHandler(Sprite sprite)
        {
            var width = sprite.rect.width;
            var height = sprite.rect.height;
            
            if (_imageOrientation >= (int)NativeGallery.ImageOrientation.Normal)
            {
                var angleOfRotation = 360f - _imageOrientation * 90;
                _imageTransformationRoot.localEulerAngles = new Vector3(0f, 0f, angleOfRotation);

                switch (_imageOrientation)
                {
                    case > (int)NativeGallery.ImageOrientation.Rotate270:
                        Debug.LogError($"Unknown orientation format: {((NativeGallery.ImageOrientation)_imageOrientation).ToString()}");
                        break;
                    case (int)NativeGallery.ImageOrientation.Rotate90 or (int)NativeGallery.ImageOrientation.Rotate270:
                        _imageTransformationRoot.localScale = Vector3.one * (width / height);
                        height = width;
                        width = sprite.rect.height;
                        break;
                }
            }

            SetAspectRation(width / height);
        }

        public void Show()
        {
            gameObject.SetActive(true);
            if (_attachmentType == ChatMessageAttachmentType.Video)
            {
                AudioProxy.MuteMusic(true);
            }
        }

        public void Hide()
        {
            gameObject.SetActive(false);
            _image.Stop();
            _videoPlayer.Stop();
            AudioProxy.MuteMusic(false);
        }

        private void Update()
        {
            if (_attachmentType == ChatMessageAttachmentType.Video && _videoPlayer.Info != null)
            {
                UpdateTime((float)_videoPlayer.Control.GetCurrentTime(), (float)_videoPlayer.Info.GetDuration());
            }
        }

        private void UpdateTime(float currentTime, float totalTime)
        {
            _progressTimeText.text = $"{((int)currentTime).SecondsToMS()}";
            _durationText.text = $"{((int)totalTime).SecondsToMS()}";

            _timeProgressSlider.SetValueWithoutNotify(totalTime < float.Epsilon ? 0f : Mathf.Clamp01(currentTime / totalTime));
        }

        private void ValueChangedListener(float newValue)
        {
            if (_videoPlayer.Info != null)
            {
                ShowPauseButton(false);
                ShowPlayButton(false);

                _videoPlayer.Pause();
                _videoPlayer.Control.Seek(newValue * _videoPlayer.Info.GetDuration());
            }
        }

        public void Setup(ChatMessage message)
        {
            LazyInit();
            _attachmentType = message.GetAttachmentType();
            _imageHolder.SetActive(false);
            _videoHolders.Enable(false);
            _gifHolder.SetActive(false);
            SetInversedAspectRatio(false);
            _imageTransformationRoot.localScale = Vector3.one;
            _imageTransformationRoot.localEulerAngles = Vector3.zero;

            if (_attachmentType == ChatMessageAttachmentType.None)
            {
                Debug.LogError("Attempt to show attachment of message without attachments");
                Hide();
                return;
            }

            Show();

            var attachment = message.Attachments[0];
            if (attachment.OriginalWidth.HasValue && attachment.OriginalHeight.HasValue)
            {
                var width = attachment.OriginalWidth.Value;
                var height = attachment.OriginalHeight.Value;
                SetAspectRation((float)width / height);
            }
            else
            {
                SetAspectRation(1f);
            }

            if (_attachmentType == ChatMessageAttachmentType.Image)
            {
                _imageOrientation = attachment.AdditionalProperties.GetIntProperty(ChatMessageAdditionalProperties.ImageOrientation);

                _image.Load(attachment.AssetUrl, SocialConstants.Attachments, error => { }, SpriteUpdatedHandler);
                _imageHolder.SetActive(true);
            }
            else if (_attachmentType == ChatMessageAttachmentType.Video)
            {
                ShowVideoLoadingHolder(true);
                ShowPauseButton(false);
                ShowPlayButton(false);
                SetInteractableSlider(false);

                UpdateTime(0f, 0f);

                _videoHolders.Enable(true);

                _videoPlayer.AudioVolume = _userSettings.SFXOn ? 1f : 0f;
                _videoPlayer.OpenMedia(new MediaPath(attachment.AssetUrl, MediaPathType.AbsolutePathOrURL), true);
            }
            else if (_attachmentType == ChatMessageAttachmentType.Gif)
            {
                _gifHolder.SetActive(true);
                _asyncGif.Load(attachment.AssetUrl);
            }
        }

        private void SetAspectRation(float aspectRatio)
        {
            _aspectRatio = Mathf.Clamp(aspectRatio, 0.1f, 10f);
            RefreshAspectRatio();
        }

        private void SetInversedAspectRatio(bool inversed)
        {
            _inversed = inversed;
            RefreshAspectRatio();
        }

        private void RefreshAspectRatio()
        {
            _aspectRatioFitter.aspectRatio = _inversed ? 1f / _aspectRatio : _aspectRatio;
        }
    }
}