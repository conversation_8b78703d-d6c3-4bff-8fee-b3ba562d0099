using RPC.Teams;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamInfo
{
    public class TeamMemberInfo
    {
        public string Uid { get; private set; }
        public string Name { get; private set; }
        public string Avatar { get; private set; }
        public int Scores { get; private set; }
        public int Helps { get; private set; }
        public string Country { get; private set; }
        public bool IsLeader { get; private set; }
        public bool IsOwner { get; private set; }
        public int HelpCount { get; private set; }

        public void SetupTeamMemberData(TeamMemberData teamMemberData)
        {
            Uid = teamMemberData.Uid;
            Name = teamMemberData.Name;
            Avatar = teamMemberData.Avatar;
            Scores = teamMemberData.Trophies;
            Country = teamMemberData.Country;
            Helps = teamMemberData.HelpCount;
            IsLeader = teamMemberData.IsAdmin;
            IsOwner = teamMemberData.IsOwner;
        }
    }
}