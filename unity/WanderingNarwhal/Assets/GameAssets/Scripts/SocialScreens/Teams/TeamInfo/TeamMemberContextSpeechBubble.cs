using System;
using BBB;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamInfo
{
    public class TeamMemberContextSpeechBubble : BbbMonoBehaviour
    {
        [SerializeField] private GameObject _holder;
        [SerializeField] private RectTransform _anchorRoot;
        [SerializeField] private Button _promoteButton;
        [SerializeField] private Button _depromoteButton;
        [SerializeField] private Button _kickButton;
        [SerializeField] private Button _closeButton;
        [SerializeField] private Vector2 _offset;

        private Action<TeamMemberInfo> _adminButtonCallback;
        private Action<TeamMemberInfo> _kickButtonCallback;

        private TeamMemberView _teamMemberView;

        public bool IsShown { get; private set; }

        private void Awake()
        {
            _promoteButton.ReplaceOnClick(AdminButtonHandler);
            _depromoteButton.ReplaceOnClick(AdminButtonHandler);
            _kickButton.ReplaceOnClick(KickButtonHandler);
            _closeButton.ReplaceOnClick(Hide);
        }

        public void Setup(Action<TeamMemberInfo> adminButtonCallback, Action<TeamMemberInfo> kickButtonCallback)
        {
            _adminButtonCallback = adminButtonCallback;
            _kickButtonCallback = kickButtonCallback;
        }

        private void KickButtonHandler()
        {
            _kickButtonCallback?.Invoke(_teamMemberView.TeamMemberInfo);
            Hide();
        }

        private void AdminButtonHandler()
        {
            _adminButtonCallback?.Invoke(_teamMemberView.TeamMemberInfo);
            Hide();
        }

        public void ShowFor(TeamMemberView teamMemberView)
        {
            _teamMemberView = teamMemberView;

            _promoteButton.gameObject.SetActive(!teamMemberView.TeamMemberInfo.IsLeader);
            _depromoteButton.gameObject.SetActive(teamMemberView.TeamMemberInfo.IsLeader);
            
            UpdatePosition();
            IsShown = true;
            _holder.SetActive(true);
        }

        public void Hide()
        {
            IsShown = false;
            _holder.SetActive(false);
        }

        private void Update()
        {
            if (_teamMemberView == null)
            {
                Hide();
                return;
            }

            UpdatePosition();
        }

        private void UpdatePosition()
        {
            if (_teamMemberView == null)
                return;
            
            _anchorRoot.position = _teamMemberView.transform.position;
            _anchorRoot.anchoredPosition += _offset;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _kickButtonCallback = null;
            _adminButtonCallback = null;
        }
    }
}