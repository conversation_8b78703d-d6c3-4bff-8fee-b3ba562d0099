using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BebopBee.Core;
using DG.Tweening;
using UnityEngine;
using BBB.Core.Analytics;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Utils;

namespace BBB
{
    public class LeaderboardModalController : EventBaseModalsController<ILeaderboardViewPresenter>
    {
        private const string DefaultCategory = LeaderboardFilterTypes.PlayersCountry;

        private WeeklyLeaderboardManager _weeklyLeaderboardManager;
        private ILeaderboardManager _leaderboardManager;
        private GameNotificationManager _notificationManager;
        private IPlayerManager _playerManager;
        private IFloatingTextManager _floatingTextManager;

        private string _currentCategory = DefaultCategory;
        private bool _weeklyLeaderboardsEnded;
        private Tweener _autoRefreshTweener;
        private RanksDeltas _deltas;
        private Coroutine _ranksDeltaAnimationCoroutine;

        protected override string EventUid => CurrencyFlow.GameEvents.WeeklyLeaderboard;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _weeklyLeaderboardManager = context.Resolve<WeeklyLeaderboardManager>();
            _leaderboardManager = context.Resolve<ILeaderboardManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _floatingTextManager = context.Resolve<IFloatingTextManager>();
        }

        public void SetupCategory(string category)
        {
            _currentCategory = category;
        }

        public override void OnShow()
        {
            base.OnShow();
            Subscribe();

            RefreshLeaderboardEnd();
            ShowCurrentCategory();
        }

        public override void OnHide()
        {
            base.OnHide();

            _currentCategory = DefaultCategory;

            if (!IsReady()) return;

            Unsubscribe();

            if (_weeklyLeaderboardsEnded)
            {
                _weeklyLeaderboardManager.EndCurrentWeeklyLeaderboard();
                _weeklyLeaderboardsEnded = false;
            }

            if (_autoRefreshTweener != null)
            {
                _autoRefreshTweener.Kill();
                _autoRefreshTweener = null;
            }

            if (_ranksDeltaAnimationCoroutine != null)
            {
                _coroutineExecutor.StopCoroutine(_ranksDeltaAnimationCoroutine);
                _ranksDeltaAnimationCoroutine = null;
                View.SetSnappingEnabled(true);
                View.SetInputLocked(false);
            }
        }

        private void RefreshLeaderboardEnd()
        {
            var status = _weeklyLeaderboardManager.Status;
            switch (status)
            {
                case WeeklyLeaderboardStatus.Inactive:
                case WeeklyLeaderboardStatus.OutOfBounds:
                {
                    return;
                }
            }

            _weeklyLeaderboardsEnded = _weeklyLeaderboardManager.Status == WeeklyLeaderboardStatus.Ended;

            if (!_weeklyLeaderboardsEnded)
            {
                _autoRefreshTweener?.Kill();

                var timeTillEnd = (float)_weeklyLeaderboardManager.GetTimeLeft().TotalSeconds;

                if (timeTillEnd > 0)
                {
                    _autoRefreshTweener = Rx.Invoke(timeTillEnd + 1f, x =>
                    {
                        RefreshLeaderboardEnd();
                        _autoRefreshTweener = null;
                    });
                }
            }
            else
            {
                _autoRefreshTweener?.Kill();
                _autoRefreshTweener = null;
            }

            View.SetupTimerView(_weeklyLeaderboardsEnded);
        }

        private void Subscribe()
        {
            Unsubscribe();

            View.SetupDebugAnimationCallback(DebugRestartAnimation);
            View.TabCategorySelected += TabSelectedHandler;
        }

        private void Unsubscribe()
        {
            View.TabCategorySelected -= TabSelectedHandler;
        }

        private void TabSelectedHandler(string category)
        {
            if (_currentCategory != category)
            {
                _currentCategory = category;
                ShowCurrentCategory();
            }
        }

        private void ShowCurrentCategory()
        {
            View.SetInputLocked(false);
            View.SetSnappingEnabled(true);

            var hasData = _leaderboardManager.HasData(_currentCategory);
            var hasConnection = ConnectivityStatusManager.ConnectivityReachable;

            _deltas = _leaderboardManager.GetLastDeltas();
            var delta = _deltas.GetDelta(_currentCategory);
            if (delta > 0 && hasData)
            {
                _ranksDeltaAnimationCoroutine = _coroutineExecutor.StartCoroutine(RanksAnimationRoutine());
            }
            else if (!hasData && !hasConnection)
            {
                View.ShowNoConnection(_currentCategory);
            }
            else
            {
                View.ShowCategory(_currentCategory, _leaderboardManager.GetFilteredScoresData(_currentCategory));
                ResetCurrentTabNotifier();
            }

            if (_currentCategory == LeaderboardFilterTypes.PlayersWeekly && !hasConnection && hasData && _weeklyLeaderboardsEnded)
            {
                _floatingTextManager.ShowFloatingText(LocalizationManagerHelper.OfflineConnectionProblemKey);
            }
        }

        private void DebugRestartAnimation()
        {
            _deltas = _leaderboardManager.GetLastDeltas();
            _deltas.SetDelta(_currentCategory, 5);

            ShowCurrentCategory();
        }

        private IEnumerator RanksAnimationRoutine()
        {
            View.SetInputLocked(true);
            View.SetSnappingEnabled(false);

            var delta = _deltas.GetDelta(_currentCategory);
            _deltas.SetDelta(_currentCategory, 0);

            if (delta < 0)
            {
                delta = 0;
            }

            var animated = false;
            var data = _leaderboardManager.GetFilteredScoresData(_currentCategory);
            if (data != null && data.ScoresCount() > 0)
            {
                var playerIndex = GetPlayerIndex(data.GetScores());
                if (playerIndex >= 0)
                {
                    var endIndex = Mathf.Min(playerIndex + delta, data.ScoresCount() - 1);
                    if (endIndex != playerIndex)
                    {
                        // Clone scores list to prevent corruption of leaderboards in case if this coroutine doesn't end correctly. -VK
                        var clonedData = data.Clone();
                        var playerScore = clonedData[playerIndex];
                        playerScore.Position = clonedData[endIndex].Position;
                        clonedData.Replace(playerScore, playerIndex);

                        clonedData.MoveFromTo(playerIndex, endIndex);
                        for (var i = playerIndex; i < endIndex; i++)
                        {
                            var score = clonedData[i];
                            score.Position--;
                            clonedData.Replace(score, i);
                        }

                        View.ShowCategory(_currentCategory, clonedData);
                        if (endIndex > LeaderboardViewPresenter.GetPlayerIndexOffsetForLeaderboard(_currentCategory))
                        {
                            View.FocusOnListItem(endIndex);
                        }
                        
                        yield return WaitCache.Seconds(1.5f);
                        var isDone = false;
                        View.ShowDeltaAnimated(clonedData, animatedDelta: endIndex - playerIndex, () => { isDone = true; });

                        ResetCurrentTabNotifier();
                        while (!isDone)
                        {
                            yield return null;
                        }

                        View.ShowCategory(_currentCategory, data, false);
                        yield return WaitCache.Seconds(1.5f);

                        animated = true;
                    }
                }
            }

            if (!animated)
            {
                View.ShowCategory(_currentCategory, _leaderboardManager.GetFilteredScoresData(_currentCategory));
            }

            View.SetSnappingEnabled(GetPlayerIndex(data.GetScores()) >= LeaderboardViewPresenter.GetPlayerIndexOffsetForLeaderboard(_currentCategory));

            View.SetInputLocked(false);
            _ranksDeltaAnimationCoroutine = null;
        }

        private void ResetCurrentTabNotifier()
        {
            var notifier = _notificationManager.GetLeaderboardNotifier(LeaderboardFilterTypes.CategoryToNotifierType(_currentCategory));
            notifier?.ResetNotifier();
            _playerManager.Player.SocialInfo.AssignSeenToVisualized(_currentCategory);
        }

        private int GetPlayerIndex(List<LeaderboardManager.Score> scores)
        {
            return scores.IndexOf(x => x.IsOwnPlayer);
        }
    }
}