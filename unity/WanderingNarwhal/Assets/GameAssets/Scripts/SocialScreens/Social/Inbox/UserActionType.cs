using System;

namespace BBB.UI
{
    public enum UserActionType
    {
        [Obsolete]
        SendLife,
        AcceptLife,
        [Obsolete]
        GiftFreeSpin,

        /// <summary>
        /// Another player sent a friend request.
        /// </summary>
        [Obsolete]
        AcceptFriendShip,

        /// <summary>
        /// Another player rejected friendship.
        /// </summary>
        [Obsolete]
        RejectFriendship,

        /// <summary>
        /// Another player accepted friendship.
        /// </summary>
        [Obsolete]
        FriendRequestAccepted,

        /// <summary>
        /// Another player sent a gift.
        /// </summary>
        [Obsolete]
        AcceptGift,

        /// <summary>
        /// Another player asked for a gift.
        /// </summary>
        [Obsolete]
        SendGift,

        /// <summary>
        /// Another player sent a Buddy gift.
        /// </summary>
        [Obsolete]
        AcceptBuddyGift,
    }
}