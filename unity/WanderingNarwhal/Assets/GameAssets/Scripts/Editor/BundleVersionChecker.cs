using System;
using System.IO;
using BebopBee.Core;
using UnityEditor;
using UnityEngine;

namespace BBB
{
    [InitializeOnLoad]
    public static class BundleVersionChecker
    {
        /// <summary>
        /// Class name to use when referencing from code.
        /// </summary>
        const string ClassName = "CurrentBundleVersion";

        const string TargetCodeFile = "Assets/GameAssets/Scripts/Config/" + ClassName + ".Generated.cs";

        static BundleVersionChecker()
        {
            var bundleVersion = PlayerSettings.bundleVersion;
#if UNITY_ANDROID
            var buildCode = PlayerSettings.Android.bundleVersionCode.ToString();
#elif UNITY_IPHONE
            var buildCode = PlayerSettings.iOS.buildNumber.ToString();
#else
            var buildCode = "0";
#endif
            var lastVersion = CurrentBundleVersion.GetVersion();
            var shouldUpdate = lastVersion != bundleVersion || buildCode != CurrentBundleVersion.GetBuildCode();
            var commitHash = "";
#if !UNITY_CLOUD_BUILD && !UNITY_STANDALONE_WIN && !UNITY_EDITOR_WIN
            commitHash = GitTools.RunGitCommand("rev-parse --short HEAD");
            // Clean up whitespace around hash. (seems to just be the way this command returns :/ )
            commitHash = string.Join("", commitHash.Split(default(string[]), StringSplitOptions.RemoveEmptyEntries));
            Debug.Log("Current Commit: " + commitHash);
#endif
            shouldUpdate |= commitHash != CurrentBundleVersion.GetCommitHash();
            
            if (shouldUpdate)
            {
                Debug.Log($"Update version info: {lastVersion} -> {bundleVersion} {buildCode} -> {CurrentBundleVersion.GetBuildCode()}  in file {TargetCodeFile}");
                CreateNewBuildVersionClassFile(bundleVersion, buildCode, commitHash);
            }
        }

#if UNITY_CLOUD_BUILD
        public static void PreExport(UnityEngine.CloudBuild.BuildManifestObject manifest)
        {
                        var bundleVersion = PlayerSettings.bundleVersion;
#if UNITY_ANDROID
            var buildCode = PlayerSettings.Android.bundleVersionCode.ToString();
#elif UNITY_IPHONE
            var buildCode = PlayerSettings.iOS.buildNumber.ToString();
#else
            var buildCode = "0";
#endif
            var lastVersion = CurrentBundleVersion.GetVersion();
            var shouldUpdate = lastVersion != bundleVersion || buildCode != CurrentBundleVersion.GetBuildCode();
            var commitHash = ""; 
            manifest.TryGetValue<string>("scmCommitId", out commitHash);
            shouldUpdate |= commitHash != CurrentBundleVersion.GetCommitHash();

            if (shouldUpdate)
            {
                Debug.Log($"Update version info: {lastVersion} -> {bundleVersion} {buildCode} -> {CurrentBundleVersion.GetBuildCode()}  in file {TargetCodeFile}");
                CreateNewBuildVersionClassFile(bundleVersion, buildCode, commitHash);
            }
        }
#endif
        static void CreateNewBuildVersionClassFile(string bundleVersion, string buildCode, string commitHash)
        {
            using (var writer = new StreamWriter(TargetCodeFile, false))
            {
                try
                {
                    var code = GenerateCode(bundleVersion, buildCode, commitHash);
                    writer.WriteLine("{0}", code);
                }
                catch (Exception ex)
                {
                    var msg = " threw:\n" + ex;
                    Debug.LogError(msg);
                    EditorUtility.DisplayDialog("Error when trying to regenerate class", msg, "OK");
                }
            }
        }

        /// <summary>
        /// Regenerates (and replaces) the code for ClassName with new bundle version id.
        /// </summary>
        /// <returns>
        /// Code to write to file.
        /// </returns>
        /// <param name='bundleVersion'>
        /// New bundle version.
        /// </param>
        static string GenerateCode(string bundleVersion, string buildCode, string commitHash)
        {
            var code = @"
public partial class " + ClassName + @"
{
    partial void GetVersion(ref string version)
    {
        version = " + $" \"{bundleVersion}\";" + @"
    }
    partial void GetBuildCode(ref string buildCode) 
    {
        buildCode = " + $" \"{buildCode}\";" + @"
    }
    partial void GetCommitHash(ref string commitHash) 
    {
        commitHash = " + $" \"{commitHash}\";" + @"
    }
}";
            return code;
        }
    }
}