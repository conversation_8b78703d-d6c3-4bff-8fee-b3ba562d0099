using System.Collections.Generic;
using System.Linq;
using BebopBee.UnityEngineExtensions;
using UnityEngine;
using UnityEditor;

public class FindMissingScripts : EditorWindow
{
    [MenuItem("BebopBee/Find Missing Scripts")]
    public static void ShowWindow()
    {
        EditorWindow.GetWindow(typeof(FindMissingScripts));
    }
 
    public void OnGUI()
    {
        
        if (GUILayout.Button("Find Missing Scripts in all prefabs"))
        {
            FindInAll();
        }
        
        if (GUILayout.But<PERSON>("Find Missing Scripts in selected prefabs"))
        {
            FindInSelected();
        }
    }

    private static void FindInAll()
    {
        var assets = new List<GameObject>();
        var paths = AssetDatabase.GetAllAssetPaths();

        int index = 0;
        foreach (var path in paths)
        {
            if (path.EndsWith(".prefab"))
            {
                var asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                assets.Add(asset);
                
                EditorUtility.DisplayProgressBar("Analyzing assets", "Loading all assets...", index / (float)paths.Length);
                index++;
            }
        }
        
        FindIn(assets);
        
        EditorUtility.ClearProgressBar();
    }
    
    private static void FindInSelected()
    {
        FindIn(Selection.gameObjects);
    }

    private static void FindIn(IEnumerable<GameObject> gameObjects)
    {
        
        int goCount = 0, componentsCount = 0, missingCount = 0;
        var count = gameObjects.Count();
        foreach (GameObject g in gameObjects.SelectMany(g => g.transform.GetChildrenRecursevely().Select(t => t.gameObject)))
        {
            
            EditorUtility.DisplayProgressBar("Analyzing gameobjects", "Looking for missing scripts...", goCount/(float)count);
            goCount++;
            Component[] components = g.GetComponents<Component>();
            for (int i = 0; i < components.Length; i++)
            {
                componentsCount++;
                if (components[i] == null)
                {
                    missingCount++;
                    string s = g.name;
                    Transform t = g.transform;
                    while (t.parent != null) 
                    {
                        s = t.parent.name +"/"+s;
                        t = t.parent;
                    }
                    Debug.Log (s + " has an empty script attached in position: " + i, g);
                }
            }
        }
 
        Debug.Log(string.Format("Searched {0} GameObjects, {1} components, found {2} missing", goCount, componentsCount, missingCount));

    }
}