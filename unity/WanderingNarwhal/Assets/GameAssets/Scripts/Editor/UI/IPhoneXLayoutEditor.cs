using UnityEditor;
using UnityEngine;

namespace GameAssets.Scripts.Editor.UI
{
    [ExecuteInEditMode]
    public class IPhoneXLayoutEditor : MonoBehaviour
    {
        private static string _safeInsetsDefine = "USE_SAFE_INSETS";

        private void OnEnable()
        {
            //Set the initial state on enable
            PlayerPrefs.SetInt(_safeInsetsDefine, IsLayoutPresent() ? 1 : 0);
        }

        [MenuItem("BebopBee/Toggle iPhoneX Layout")]
        private static void ToggleXLayout()
        {
            Debug.Log("Toggle iPhoneX layout");

            if (IsLayoutPresent())
            {
                DestroyImmediate(GameObject.FindGameObjectWithTag("xLayout"));
                PlayerPrefs.SetInt(_safeInsetsDefine, 0);
            }
            else
                InstantiateXLayout();
        }

        private static bool IsLayoutPresent()
        {
            return GameObject.FindGameObjectWithTag("xLayout") != null;
        }

        private static void InstantiateXLayout()
        {
            var prefab = AssetDatabase.LoadAssetAtPath("Assets/GameAssets/EditorAssets/iPhoneXLayout.prefab", typeof(GameObject));
            var go = Instantiate(prefab, Vector3.zero, Quaternion.identity) as GameObject;

            if (go != null)
            {
                go.transform.SetAsLastSibling();
                PlayerPrefs.SetInt(_safeInsetsDefine, 1);
            }
            else
                Debug.LogError("No iPhoneX layout prefab present!");
        }
        
        
    }
}
