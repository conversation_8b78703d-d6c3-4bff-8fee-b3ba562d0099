using UnityEditor;
using System;
using System.Linq;
using System.IO;
using System.Collections.Generic;
#if UNITY_5_5_OR_NEWER
using UnityEngine.Profiling;
#endif

using Model = UnityEngine.AssetGraph.DataModel.Version2;

namespace UnityEngine.AssetGraph
{
    [CustomNode("Configure Bundle/Extract Shared  & Sprite Atlas", 71)]
    public class ExtractSharedAndSpriteAtlas : Node
    {
        enum GroupingType : int
        {
            ByFileSize,
            ByRuntimeMemorySize
        };

        [SerializeField] private string m_bundleNameTemplate;
        [SerializeField] private string m_spriteAtlasNameTemplate;
        [SerializeField] private SerializableMultiTargetInt m_groupExtractedAssets;
        [SerializeField] private SerializableMultiTargetInt m_groupSizeByte;
        [SerializeField] private SerializableMultiTargetInt m_groupingType;

        public override string ActiveStyle
        {
            get { return "node 3 on"; }
        }

        public override string InactiveStyle
        {
            get { return "node 3"; }
        }

        public override string Category
        {
            get { return "Configure"; }
        }

        public override Model.NodeOutputSemantics NodeInputType
        {
            get { return Model.NodeOutputSemantics.AssetBundleConfigurations; }
        }

        public override Model.NodeOutputSemantics NodeOutputType
        {
            get { return Model.NodeOutputSemantics.AssetBundleConfigurations; }
        }

        private Dictionary<string, string> _spritesAtlasMap = new Dictionary<string, string>();
        private Dictionary<string, HashSet<string>> _atlasSpriteMap = new Dictionary<string, HashSet<string>>();

        public override void Initialize(Model.NodeData data)
        {
            m_bundleNameTemplate = "shared_*";
            m_spriteAtlasNameTemplate = "spriteatlas_*";
            m_groupExtractedAssets = new SerializableMultiTargetInt();
            m_groupSizeByte = new SerializableMultiTargetInt();
            m_groupingType = new SerializableMultiTargetInt();
            data.AddDefaultInputPoint();
            data.AddDefaultOutputPoint();
        }

        public override Node Clone(Model.NodeData newData)
        {
            var newNode = new ExtractSharedAndSpriteAtlas();
            newNode.m_groupExtractedAssets = new SerializableMultiTargetInt(m_groupExtractedAssets);
            newNode.m_groupSizeByte = new SerializableMultiTargetInt(m_groupSizeByte);
            newNode.m_groupingType = new SerializableMultiTargetInt(m_groupingType);
            newNode.m_bundleNameTemplate = m_bundleNameTemplate;
            newNode.m_spriteAtlasNameTemplate = m_spriteAtlasNameTemplate;
            newData.AddDefaultInputPoint();
            newData.AddDefaultOutputPoint();
            return newNode;
        }

        public override void OnInspectorGUI(NodeGUI node, AssetReferenceStreamManager streamManager, NodeGUIEditor editor, Action onValueChanged)
        {
            EditorGUILayout.HelpBox("Extract Shared Assets: Extract shared assets between asset bundles and add bundle configurations.", MessageType.Info);
            editor.UpdateNodeName(node);

            GUILayout.Space(10f);

            var newValue = EditorGUILayout.TextField("Bundle Name Template", m_bundleNameTemplate);
            if (newValue != m_bundleNameTemplate)
            {
                using (new RecordUndoScope("Bundle Name Template Change", node, true))
                {
                    m_bundleNameTemplate = newValue;
                    onValueChanged();
                }
            }

            var newValueSpriteAtlas = EditorGUILayout.TextField("SpriteAtlas Name Template", m_spriteAtlasNameTemplate);
            if (newValueSpriteAtlas != m_spriteAtlasNameTemplate)
            {
                using (new RecordUndoScope("SpriteAtlas Name Template Change", node, true))
                {
                    m_spriteAtlasNameTemplate = newValueSpriteAtlas;
                    onValueChanged();
                }
            }

            GUILayout.Space(10f);

            //Show target configuration tab
            editor.DrawPlatformSelector(node);
            using (new EditorGUILayout.VerticalScope(GUI.skin.box))
            {
                var disabledScope = editor.DrawOverrideTargetToggle(node, m_groupSizeByte.ContainsValueOf(editor.CurrentEditingGroup), (bool enabled) =>
                {
                    using (new RecordUndoScope("Remove Target Grouping Size Settings", node, true))
                    {
                        if (enabled)
                        {
                            m_groupExtractedAssets[editor.CurrentEditingGroup] = m_groupExtractedAssets.DefaultValue;
                            m_groupSizeByte[editor.CurrentEditingGroup] = m_groupSizeByte.DefaultValue;
                            m_groupingType[editor.CurrentEditingGroup] = m_groupingType.DefaultValue;
                        }
                        else
                        {
                            m_groupExtractedAssets.Remove(editor.CurrentEditingGroup);
                            m_groupSizeByte.Remove(editor.CurrentEditingGroup);
                            m_groupingType.Remove(editor.CurrentEditingGroup);
                        }

                        onValueChanged();
                    }
                });

                using (disabledScope)
                {
                    var useGroup = EditorGUILayout.ToggleLeft("Subgroup shared assets by size", m_groupExtractedAssets[editor.CurrentEditingGroup] != 0);
                    if (useGroup != (m_groupExtractedAssets[editor.CurrentEditingGroup] != 0))
                    {
                        using (new RecordUndoScope("Change Grouping Type", node, true))
                        {
                            m_groupExtractedAssets[editor.CurrentEditingGroup] = (useGroup) ? 1 : 0;
                            onValueChanged();
                        }
                    }

                    using (new EditorGUI.DisabledScope(!useGroup))
                    {
                        var newType = (GroupingType)EditorGUILayout.EnumPopup("Grouping Type", (GroupingType)m_groupingType[editor.CurrentEditingGroup]);
                        if (newType != (GroupingType)m_groupingType[editor.CurrentEditingGroup])
                        {
                            using (new RecordUndoScope("Change Grouping Type", node, true))
                            {
                                m_groupingType[editor.CurrentEditingGroup] = (int)newType;
                                onValueChanged();
                            }
                        }

                        var newSizeText = EditorGUILayout.TextField("Size(KB)", m_groupSizeByte[editor.CurrentEditingGroup].ToString());
                        int newSize;
                        Int32.TryParse(newSizeText, out newSize);

                        if (newSize != m_groupSizeByte[editor.CurrentEditingGroup])
                        {
                            using (new RecordUndoScope("Change Grouping Size", node, true))
                            {
                                m_groupSizeByte[editor.CurrentEditingGroup] = newSize;
                                onValueChanged();
                            }
                        }
                    }
                }
            }

            EditorGUILayout.HelpBox("Bundle Name Template replaces \'*\' with number.", MessageType.Info);
        }

        /**
         * Prepare is called whenever graph needs update.
         */
        public override void Prepare(BuildTarget target,
            Model.NodeData node,
            IEnumerable<PerformGraph.AssetGroups> incoming,
            IEnumerable<Model.ConnectionData> connectionsToOutput,
            PerformGraph.Output Output)
        {
            if (string.IsNullOrEmpty(m_bundleNameTemplate))
            {
                throw new NodeException("Bundle Name Template is empty.", "Set valid bundle name template.", node);
            }

            if (m_groupExtractedAssets[target] != 0)
            {
                if (m_groupSizeByte[target] < 0)
                {
                    throw new NodeException("Invalid size. Size property must be a positive number.", "Set valid size.", node);
                }
            }

            Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() InitializeSpriteAtlasCache");
            InitializeSpriteAtlasCache();
            Profiler.EndSample();
            // Pass incoming assets straight to Output
            if (Output != null)
            {
                var destination = (connectionsToOutput == null || !connectionsToOutput.Any()) ? null : connectionsToOutput.First();

                if (incoming != null)
                {
                    var buildMap = AssetBundleBuildMap.GetBuildMap();
                    buildMap.ClearFromId(node.Id);

                    var dependencyCollector =
                        new Dictionary<string, (HashSet<string> dependentGroups, string mainGroup)>(); // [asset path:bundles names[], main bundle name (for this specific  asset)]
                    var spriteAtlasCollector = new Dictionary<string, HashSet<string>>(); // [atlas, groups]
                    
                    var assetGroup = new Dictionary<string, string>();
                    var groupAssets = new Dictionary<string, HashSet<string>>();
                    // build dependency map
                    Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() build dependency map");
                    foreach (var ag in incoming)
                    {
                        Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() loop through keys");
                        foreach (var key in ag.assetGroups.Keys)
                        {
                            var assets = ag.assetGroups[key];

                            Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() loop through assets for key " + key);
                            var assetPaths = new string[assets.Count];
                            int i = 0;
                            foreach (var a in assets)
                            {
                                assetPaths[i] = a.importFrom;
                                assetGroup[a.importFrom] = key;
                                i++;
                            }

                            CollectDependencies(key, assetPaths, dependencyCollector, spriteAtlasCollector);
                            if (!groupAssets.ContainsKey(key))
                            {
                                groupAssets[key] = new HashSet<string>();
                            }

                            groupAssets[key].UnionWith(assetPaths);
                            Profiler.EndSample();
                        }

                        Profiler.EndSample();
                    }

                    Profiler.EndSample();
                    
                    // For all SpriteAtlas that belongs to more than 1 group, we remove all those sprites from each group
                    Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() step through atlases dependencies entries");
                    foreach (var kv in spriteAtlasCollector)
                    {
                        var spritesInAtlas = _atlasSpriteMap[kv.Key];
                        if (kv.Value.Count > 1)
                        {
                            var isAllDownloadable = true;
                            foreach (var group in kv.Value)
                            {
                                groupAssets[group].ExceptWith(spritesInAtlas);
                                groupAssets[group].Remove(kv.Key);
                                isAllDownloadable &= group.StartsWith("downloadable");
                            }

                            var replaceWith = Path.GetFileNameWithoutExtension(kv.Key).ToLower();
                            var spriteGroupName = m_spriteAtlasNameTemplate.Replace("*", replaceWith);
                            if (isAllDownloadable)
                            {
                                spriteGroupName = $"downloadable/{spriteGroupName}";
                            }

                            groupAssets[spriteGroupName] = new HashSet<string>();
                            groupAssets[spriteGroupName].Add(kv.Key);
                        }
                        else
                        {
                            var group = kv.Value.First();
                            groupAssets[group].ExceptWith(spritesInAtlas);
                            groupAssets[group].Add(kv.Key);
                        }
                    }

                    Profiler.EndSample();

                    Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() step through dependencies entries");

                    foreach (var entry in dependencyCollector)
                    {
                        if (entry.Value.dependentGroups.Count < 2) continue;
                        // If a file is already included in a group we keep it there
                        if (assetGroup.ContainsKey(entry.Key)) continue;
                        var fileName = entry.Key;
                        var groups = entry.Value.dependentGroups;
                        var isAllDownloadable = true;
                        foreach (var group in groups)
                        {
                            groupAssets[group].Remove(fileName);
                            isAllDownloadable &= group.StartsWith("downloadable");
                        }

                        var newName = m_bundleNameTemplate.Replace("*", "");
                        // if (isAllDownloadable)
                        // {
                        //     newName = $"downloadable/{newName}";
                        // }
                        
                        if (!groupAssets.ContainsKey(newName))
                        {
                            groupAssets[newName] = new HashSet<string> {fileName};
                        }
                        groupAssets[newName].Add(fileName);
                    }
                    Profiler.EndSample();
                    
                    Profiler.BeginSample("ExtractSharedAndSpriteAtlas.Prepare() output");
                    var output = new Dictionary<string, List<AssetReference>>();
                    foreach (var entry in groupAssets)
                    {
                        if (entry.Value.Count == 0) continue;
                        var files = entry.Value.Select((path) =>
                        {
                            var f = AssetReferenceDatabase.GetReference(path);
                            if (f.variantName.IsNullOrEmpty() || string.IsNullOrWhiteSpace(f.variantName))
                                f.variantName = null;
                            return f;
                        });
                        output[entry.Key] = files.ToList();
                        
                        var bundleConfig = buildMap.GetAssetBundleWithNameAndVariant(node.Id, entry.Key, string.Empty);
                        bundleConfig.AddAssets(node.Id, entry.Value);
                    }
                    Output(destination, output);
                    Profiler.EndSample();
                }
                else
                {
                    // Overwrite output with empty Dictionary when there is no incoming asset
                    Output(destination, new Dictionary<string, List<AssetReference>>());
                }
            }
        }

        private void InitializeSpriteAtlasCache()
        {
            var assets = AssetDatabase.FindAssets("t:SpriteAtlas");
            foreach (var atlasName in assets)
            {
                var path = AssetDatabase.GUIDToAssetPath(atlasName);
                if (path.Contains("_sd")) continue;
                var fakeAsset = AssetReferenceDatabase.GetReference(path);
                var hash = AssetDatabase.GetAssetDependencyHash(path);
                if (fakeAsset.dependencyHash != hash)
                {
                    fakeAsset.dependencyHash = hash;
                    fakeAsset.dependencies = AssetDatabase.GetDependencies(path, false);
                }
                var dependencies = fakeAsset.dependencies;
                _atlasSpriteMap[path] = new HashSet<string>();
                if (dependencies.Length > 0)
                {
                    try
                    {
                        foreach (var sprite in dependencies)
                        {
                            if (sprite == null) continue;
                            if (AssetDatabase.GetMainAssetTypeAtPath(sprite) == typeof(Texture2D))
                            {
                                _spritesAtlasMap[sprite] = path;
                                _atlasSpriteMap[path].Add(sprite);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError(e.Message);
                    }
                }
            }
        }

        private void CollectDependencies(string groupKey, string[] assetPaths, Dictionary<string, (HashSet<string> dependentGroups, string partOfGroup)> collector,
            Dictionary<string, HashSet<string>> spriteAtlasCollector)
        {
            Profiler.BeginSample("ExtractSharedAndSpriteAtlas.CollectDependencies() GetDependencies Hash");
            var fakeAsset = AssetReferenceDatabase.GetReferenceWithType(groupKey, typeof(AssetBundleReference));
            fakeAsset.variantName = null;
            var dependenciesHash = fakeAsset.dependenciesHash ?? new HashSet<Hash128>();
            var queryDependencies = false;
            for (var i = 0; i < assetPaths.Length; i++)
            {
                var hash = AssetDatabase.GetAssetDependencyHash(assetPaths[i]);
                if (!dependenciesHash.Contains(hash))
                {
                    queryDependencies = true;
                    break;
                }
            }

            Profiler.EndSample();

            Profiler.BeginSample("ExtractSharedAndSpriteAtlas.CollectDependencies() GetDependencies");
            string[] dependencies;
            if (queryDependencies)
            {
                dependencies = AssetDatabase.GetDependencies(assetPaths, false);
                var hashSet = new HashSet<Hash128>();
                for (var i = 0; i < assetPaths.Length; i++)
                {
                    var hash = AssetDatabase.GetAssetDependencyHash(assetPaths[i]);
                    hashSet.Add(hash);
                }

                fakeAsset.dependenciesHash = hashSet;
                fakeAsset.dependencies = dependencies;
            }
            else
            {
                dependencies = fakeAsset.dependencies;
            }

            Profiler.EndSample();
            // Debug.Log($"----- CollectDependencies group:{groupKey} assetPaths: {string.Join(",", assetPaths)}");
            foreach (var d in dependencies)
            {
                var dependencyName = d;
                // AssetBundle must not include script asset
                Profiler.BeginSample("ExtractSharedAndSpriteAtlas.CollectDependencies() GetMainAssetTypeAtPath");
                var assetType = TypeUtility.GetMainAssetTypeAtPath(d);
                Profiler.EndSample();
                if (assetType == typeof(MonoScript))
                {
                    continue;
                }

                if (assetType == typeof(Texture2D) && _spritesAtlasMap.ContainsKey(d))
                {
                    var atlasPath = _spritesAtlasMap[d];
                    // Debug.Log($"group:{groupKey} path:{d} atlas:{atlasPath}");
                    // Debug.Log($"----- CollectDependencies group:{groupKey} assetPath: {d} atlasPath:{atlasPath} dependencyName:{dependencyName}");
                    if (!spriteAtlasCollector.ContainsKey(atlasPath))
                    {
                        spriteAtlasCollector[atlasPath] = new HashSet<string>();
                    }

                    spriteAtlasCollector[atlasPath].Add(groupKey);
                }
                else
                {
                    // if (d.Contains("/Gizmos/") || d.Contains("/Editor/") || d.Contains("/Resources/") || d.Contains(".dll"))
                    if (d.Contains("/Gizmos/") || d.Contains("/Editor/") || d.Contains(".dll"))
                        continue;

                    HashSet<string> dependentGroupsList;
                    if (!collector.ContainsKey(dependencyName))
                    {
                        dependentGroupsList = new HashSet<string>();
                        collector[dependencyName] = (dependentGroupsList, null);
                    }
                    else
                    {
                        dependentGroupsList = collector[dependencyName].dependentGroups;
                    }

                    if (!dependentGroupsList.Contains(groupKey))
                    {
                        dependentGroupsList.Add(groupKey);
                    }
                }
            }

            // Debug.Log($"-----");
        }

        private long GetSizeOfAsset(AssetReference a, GroupingType t)
        {
            long size = 0;

            // You can not read scene and do estimate
            if (a.isSceneAsset)
            {
                t = GroupingType.ByFileSize;
            }

            if (t == GroupingType.ByRuntimeMemorySize)
            {
                var objects = a.allData;
                foreach (var o in objects)
                {
#if UNITY_5_6_OR_NEWER
                    size += Profiler.GetRuntimeMemorySizeLong(o);
#else
                    size += Profiler.GetRuntimeMemorySize(o);
#endif
                }

                a.ReleaseData();
            }
            else if (t == GroupingType.ByFileSize)
            {
                System.IO.FileInfo fileInfo = new System.IO.FileInfo(a.absolutePath);
                if (fileInfo.Exists)
                {
                    size = fileInfo.Length;
                }
            }

            return size;
        }
    }
}