using System;
using System.Collections.Generic;
using System.Linq;
using CleanupUtility.Editor;
using UnityEditor;
using UnityEngine.AssetGraph;
using UnityEngine.AssetGraph.DataModel.Version2;
using Model = UnityEngine.AssetGraph.DataModel.Version2;

namespace GameAssets.Scripts.Editor.Import.Pipeline
{
    /// <summary>
    /// Custom node for asset graph.
    /// Preferably, this node's input needs to be attached to BuildBundles node's output
    /// to make the validation process happen after bundles build step. 
    /// </summary>
    [CustomNode("Custom/ValidateBundlesDependencies", 1000)]
    public class ValidateBundlesDependencies : Node
    {
        public override string Category => "Custom";
        public override string ActiveStyle => "node 2 on";
        public override string InactiveStyle => "node 2";

        public override void Initialize(NodeData data)
        {
            data.AddDefaultInputPoint();
            data.AddDefaultOutputPoint();
        }

        public override Model.NodeOutputSemantics NodeInputType
        {
            get { return Model.NodeOutputSemantics.Any; }
        }

        public override Model.NodeOutputSemantics NodeOutputType
        {
            get { return Model.NodeOutputSemantics.Any; }
        }

        public override Node Clone(NodeData newData)
        {
            return new ValidateBundlesDependencies();
        }

        public override void OnInspectorGUI(NodeGUI node, AssetReferenceStreamManager streamManager, NodeGUIEditor editor, Action onValueChanged)
        {
        }

        public override void Prepare(BuildTarget target, NodeData nodeData, IEnumerable<PerformGraph.AssetGroups> incoming, IEnumerable<ConnectionData> connectionsToOutput, PerformGraph.Output outputFunc)
        {
            var destination = (connectionsToOutput == null || !connectionsToOutput.Any())? null : connectionsToOutput.First();
            if (outputFunc == null) return;
            if (incoming != null)
            {
                foreach(var ag in incoming) {
                    outputFunc(destination, new Dictionary<string, List<AssetReference>>(ag.assetGroups));
                }
            }
            else
            {
                outputFunc(destination, new Dictionary<string, List<AssetReference>>());
            }
        }

        public override void Build(BuildTarget target, NodeData nodeData, IEnumerable<PerformGraph.AssetGroups> incoming, IEnumerable<ConnectionData> connectionsToOutput,
            PerformGraph.Output outputFunc, Action<NodeData, string, float> progressFunc)
        {
            var destination = (connectionsToOutput == null || !connectionsToOutput.Any())? null : connectionsToOutput.First();
            if (incoming == null) return;

            foreach (var ag in incoming)
            {
                outputFunc?.Invoke(destination, new Dictionary<string, List<AssetReference>>(ag.assetGroups));
            }
#if DISABLE_BUNDLES_DEPS_VALIDATION
            return;
#endif

            var so = AssetDatabase.FindAssets("t: " + nameof(BundlesDependenciesConfigSO));
            BundlesDependenciesConfigSO configSO = null;
            if (so.Length > 0)
            {
                var path = AssetDatabase.GUIDToAssetPath(so[0]);
                configSO = AssetDatabase.LoadAssetAtPath<BundlesDependenciesConfigSO>(path);
            }

            BundlesDependenciesConfigData config = configSO == null ? new BundlesDependenciesConfigData() : configSO.ConfigData;
            var failedBundles = BundlesDependenciesTestWindow.TestBundlesDependencies(config.bundlesCategories);

            if (failedBundles != null && failedBundles.Count > 0)
            {
                var wnd = BundlesDependenciesTestWindow.OpenWindow();
                if (wnd != null)
                {
                    wnd.LastTestResults = failedBundles;
                }

                throw new Exception("Bundles dependencies test failed");
            }

            if (!MainSceneDependenciesTest.ValidateMainSceneDependencies())
            {
                throw new Exception("Main Scene dependencies test failed");
            }
        }
        
    }
}