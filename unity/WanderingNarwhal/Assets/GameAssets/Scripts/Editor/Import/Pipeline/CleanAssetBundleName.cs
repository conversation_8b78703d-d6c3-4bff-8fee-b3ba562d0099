using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.AssetGraph;
using UnityEngine.AssetGraph.DataModel.Version2;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using Model = UnityEngine.AssetGraph.DataModel.Version2;

namespace GameAssets.Scripts.Editor.Import.Pipeline
{
    [CustomNode("Custom/CleanAssetBundleName", 1000)]
    public class CleanAssetBundleName : Node
    {
        public override string Category => "Custom";
        public override string ActiveStyle => "node 2 on";
        public override string InactiveStyle => "node 2";

        public override void Initialize(NodeData data)
        {
            data.AddDefaultInputPoint();
            data.AddDefaultOutputPoint();
        }

        public override Model.NodeOutputSemantics NodeInputType
        {
            get { return Model.NodeOutputSemantics.Any; }
        }

        public override Model.NodeOutputSemantics NodeOutputType
        {
            get { return Model.NodeOutputSemantics.Any; }
        }

        public override Node Clone(NodeData newData)
        {
            return new ValidateBundlesDependencies();
        }

        public override void OnInspectorGUI(NodeGUI node, AssetReferenceStreamManager streamManager, NodeGUIEditor editor, Action onValueChanged)
        {
        }

        public override void Prepare(BuildTarget target, NodeData nodeData, IEnumerable<PerformGraph.AssetGroups> incoming, IEnumerable<ConnectionData> connectionsToOutput,
            PerformGraph.Output outputFunc)
        {
            var destination = (connectionsToOutput == null || !connectionsToOutput.Any()) ? null : connectionsToOutput.First();
            if (outputFunc == null) return;
            if (incoming != null)
            {
                foreach (var ag in incoming)
                {
                    outputFunc(destination, new Dictionary<string, List<AssetReference>>(ag.assetGroups));
                }
            }
            else
            {
                outputFunc(destination, new Dictionary<string, List<AssetReference>>());
            }
        }

        public override void Build(BuildTarget target, NodeData nodeData, IEnumerable<PerformGraph.AssetGroups> incoming, IEnumerable<ConnectionData> connectionsToOutput,
            PerformGraph.Output outputFunc, Action<NodeData, string, float> progressFunc)
        {
            var destination = (connectionsToOutput == null || !connectionsToOutput.Any()) ? null : connectionsToOutput.First();
            if (incoming == null) return;


            foreach (var ag in incoming)
            {
                outputFunc?.Invoke(destination, new Dictionary<string, List<AssetReference>>(ag.assetGroups));
            }

            CleanUpAssetBundleFromImporter(target);
        }

        private void CleanUpAssetBundleFromImporter(BuildTarget target)
        {
            var reports = AssetBundleBuildReport.BuildReports;
            AssetBundleBuildReport validReport = null;
            foreach (var report in reports)
            {
                validReport = report;
            }
            
            if (validReport == null)
                return;
            
            var onlyLevels = false;
            foreach (var bundleFile in validReport.BuiltBundleFiles)
            {
                if (bundleFile.fileName.Contains("LevelsManifest"))
                {
                    onlyLevels = true;
                    break;
                }
            }

            AssetBundle.UnloadAllAssetBundles(true);
            var deserializer = new DeserializerBuilder()
                .WithNamingConvention(new CamelCaseNamingConvention())
                .Build();

            var allAssetInBundles = new HashSet<string>();

            var basePath = Path.Combine(Path.Combine(Application.dataPath, ".."), "AssetBundles", BuildTargetUtility.TargetToAssetBundlePlatformName(target));

            var mainPath = Path.Combine(basePath, onlyLevels ? "LevelsManifest" : "MainManifest");
            var main = AssetBundle.LoadFromFile(mainPath);
            if (main != null)
            {
                var mainManifest = main.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
                var allBundlesNames = mainManifest.GetAllAssetBundles();

                for (int i = 0; i < allBundlesNames.Length; i++)
                {
                    var bundleName = allBundlesNames[i];
                    var bundleWithoutHash = $"{bundleName.Substring(0, bundleName.LastIndexOf('_'))}.unity3d.manifest";
                    var result = deserializer.Deserialize<Dictionary<string, object>>(File.OpenText(Path.Combine(basePath, bundleWithoutHash)));
                    var assets = (List<object>)result.GetSafe("Assets");
                    foreach (var asset in assets)
                    {
                        allAssetInBundles.Add((string)asset);
                    }
                }

                main.Unload(true);
            }

            var allAssetsRegistered = new HashSet<string>();
            var names = AssetDatabase.GetAllAssetBundleNames();
            foreach (var name in names)
            {
                var assets = AssetDatabase.GetAssetPathsFromAssetBundle(name);
                foreach (var assetName in assets)
                {
                    if ((onlyLevels && assetName.Contains(".M3L")) || (!onlyLevels && !assetName.Contains(".M3L")))
                    {
                        allAssetsRegistered.Add(assetName);
                    }
                }
            }

            allAssetsRegistered.ExceptWith(allAssetInBundles);
            foreach (var toRemove in allAssetsRegistered)
            {
                var importer = AssetImporter.GetAtPath(toRemove);
                importer.SetAssetBundleNameAndVariant(string.Empty, string.Empty);
                AssetDatabase.WriteImportSettingsIfDirty(toRemove);
            }
        }
    }
}