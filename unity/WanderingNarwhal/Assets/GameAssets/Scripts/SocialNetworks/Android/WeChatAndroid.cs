#if UNITY_ANDROID
using UnityEngine;
namespace BebopBee.Messaging
{
    public class WeChatAndroid : IWeChat
    {
        private static readonly string androidPluginName = "com.bebopbee.social.WeChat";
        private static AndroidJavaClass pluginClass;

        public void Initialize()
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                pluginClass = new AndroidJavaClass(androidPluginName);
            }
        }

        public bool isAvailable
        {
            get { return false; }
            //get { return pluginClass.CallStatic<bool>("IsAvailable"); } // Disabled for now.
        }

        public void sendMessage(string message)
        {
            pluginClass.CallStatic("SendMessage", message);
        }

        public void sharePicture(string path)
        {
            pluginClass.CallStatic("SharePicture", path);
        }
    }
}
#endif
