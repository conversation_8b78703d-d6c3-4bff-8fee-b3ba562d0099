using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.UI.Core;

namespace BBB
{
    public class POIUnlockedNotifier : BaseNotifier
    {
        private IEventDispatcher _eventDispatcher;
        private IScreensManager _screenManager;
        private IPlayerManager _playerManager;
        
        private ScreenType _lastScreenType;
        private string _notifierUid = "";
        private IDictionary<string, int> _notificationsSaveDictionary => _playerManager.Player.PlayerDO.NotificationsDictionary;

        public void Init(IContext context)
        {
            _playerManager = context.Resolve<IPlayerManager>();
            InitSaves("poi");

            _eventDispatcher = context.Resolve<IEventDispatcher>();

            _eventDispatcher.Unsubscribe(this);
            _eventDispatcher.RemoveListener<LockItemUnlockedEvent>(OnLockItemUnlockedEvent);
            _eventDispatcher.AddListener<LockItemUnlockedEvent>(OnLockItemUnlockedEvent);

            _screenManager = context.Resolve<IScreensManager>();
            _screenManager.OnScreenChanged -= ScreenChangingCatcher;
            _screenManager.OnScreenChanged += ScreenChangingCatcher;
            
            // we have to reset notifier when session starts
            ResetNotifier();
        }

        private void InitSaves(string notifierUid)
        {
            _notifierUid = notifierUid;

            if (notifierUid.IsNullOrEmpty())
                return;

            if (!_notificationsSaveDictionary.TryAdd(_notifierUid, 0))
            {
                InternalStatus = _notificationsSaveDictionary[notifierUid];
            }

            CheckForStatusUpdate ();
        }
        
        private void OnLockItemUnlockedEvent(LockItemUnlockedEvent obj)
        {
            var lockItemIdentity = obj.Arg0;
            if (lockItemIdentity.LockItemType == LockItemType.POI)
                IncrementNotifier();
        }

        private void ScreenChangingCatcher(ScreenType screenType, IScreensController arg2, IViewPresenter arg3)
        {
            _lastScreenType = screenType;
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher?.RemoveListener<LockItemUnlockedEvent>(OnLockItemUnlockedEvent);

            if (_screenManager != null)
            {
                _screenManager.OnScreenChanged -= ScreenChangingCatcher;
            }
        }

        protected override void UpdateStatus(int status)
        {
            if (!_notifierUid.IsNullOrEmpty())
                _notificationsSaveDictionary[_notifierUid] = InternalStatus;
            
            base.UpdateStatus(status);
        }
    }
}