using BBB.DI;
using GameAssets.Scripts.SocialScreens.Teams;
using UnityEngine;

namespace BBB
{
    public class LeaderboardNotifier : BaseNotifier
    {
        public enum NotifierType
        {
            TeamsWorld,
            TeamsCountry,
            PlayersWorld,
            PlayersCountry,
            PlayersWeekly,
        }

        private NotifierType _currentType;
        private IEventDispatcher _eventDispatcher;
        private ISocialManager _socialManager;
        private bool _isSocialLocked = true;

        /// <summary>
        /// Highest timestamp, shared between multiple instances of SocialNotifier.
        /// </summary>
        private static long _highestLastSeenTimestamp;
        private static Coroutine _runningValidationRoutine;

        public LeaderboardNotifier(NotifierType type)
        {
            _currentType = type;
        }

        private bool IsSocialLocked()
        {
            if (!_isSocialLocked)
            {
                return false;
            }

#if DISABLE_SOCIAL
            _isSocialLocked = true;
#else
            _isSocialLocked = !_socialManager.IsSocialUnlocked();
#endif
            return _isSocialLocked;
        }

        public void Init(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _socialManager = context.Resolve<ISocialManager>();
            
            _eventDispatcher.RemoveListener<LeaderboardRankChangedEvent>(OnRanksIncreased);
            _eventDispatcher.AddListener<LeaderboardRankChangedEvent>(OnRanksIncreased);
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher?.RemoveListener<LeaderboardRankChangedEvent>(OnRanksIncreased);
        }
        
        private void OnRanksIncreased(LeaderboardRankChangedEvent ev)
        {
            if (IsSocialLocked()) 
                return;

            if (_currentType != ev.Arg0)
                return;
            
            if (GetStatus() <= 0)
            {
                IncrementNotifier();
            }
        }
    }
}