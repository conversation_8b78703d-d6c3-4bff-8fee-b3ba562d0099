using BBB.Core.UI;
using BBB.DI;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class SideMapViewPresenter : ScreensViewPresenter, ISideMapViewPresenter
    {
        [SerializeField] private Image _background;
        [SerializeField] private GameEventReplaceableGo _backgroundHolder;

        private IGameEventManager _gameEventManager;
        private IGameEventResourceManager _gameEventResourceManager;
        private GameEventBase _currentGameEvent;

        public float CTADelay => 0;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _gameEventManager = previousContext.Resolve<IGameEventManager>();
            _gameEventResourceManager = previousContext.Resolve<IGameEventResourceManager>();
            _backgroundHolder.Init(_gameEventResourceManager);
        }

        protected override void OnShow()
        {
            base.OnShow();

            var currentEvent = _gameEventManager.GetCurrentSideMapEvent();
            if (_currentGameEvent != currentEvent)
            {
                UpdateBackground(currentEvent);
                _currentGameEvent = currentEvent;
            }
        }

        private void UpdateBackground(GameEventBase gameEvent)
        {
            if (gameEvent == null)
                return;
            
            _background.sprite = _gameEventResourceManager.GetSprite(gameEvent.Uid, GameEventResKeys.ScreenBackgroundImage);
            _backgroundHolder.Refresh(gameEvent.Uid, GameEventResKeys.ScreenAnimatedComposition);
        }
    }
}