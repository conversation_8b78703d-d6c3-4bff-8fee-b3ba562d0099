using BBB;
using BBB.DI;
using GameAssets.Scripts.Tutorial.Core;

namespace GameAssets.Scripts.Tutorial.Steps
{
    public class SettingsIntroSetup : BaseTutorialStepSetup
    {
        public override void Init(IContext context)
        {
            base.Init(context);

            EnterCondition = () => (LastScreenType == ScreenType.LevelScreen && PlayerManager.CurrentLevel.Config.Uid == LevelUid);
            ExitCondition = () => (LocationManager.GetLevelStage(LevelUid) > 0);
        }
    }
}