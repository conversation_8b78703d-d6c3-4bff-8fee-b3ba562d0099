using System.Collections;
using BBB;
using BBB.Controller;
using GameAssets.Scripts.Tutorial.Core;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace GameAssets.Scripts.Tutorial.Steps
{
    public class BoostersIntro : BaseStep
    {
        [SerializeField] private GameObject _holder;
        [SerializeField] private RectTransform _speechBubbleHolder;
        [SerializeField] private Vector2 _speechBubbleOffset;

        private bool _anyNewModalPushed;
        public override void Setup()
        {
            base.Setup();

            var waitForStartLevelModal = new Substep(CoroutineExecutor) { };
            waitForStartLevelModal.SetupExecutionCoroutine(WaitForStartLevelModal);

            var highlightBoosters = new Substep(CoroutineExecutor) { };
            highlightBoosters.SetupExecutionCoroutine(HighlightBoosters);

            SubstepsList.Add(waitForStartLevelModal);
            SubstepsList.Add(highlightBoosters);

            _holder.SetActive(false);
        }

        private IEnumerator WaitForStartLevelModal()
        {
            while (!ModalsManager.IsShowingModal(ModalsType.LevelStart))
                yield return null;
        }

        private IEnumerator HighlightBoosters()
        {
            _screenLocker.SetActive(true);
            yield return WaitCache.Seconds(0.3f);
            _screenLocker.SetActive(false);

            TutorialElementsHighlighter.EnableHighlightElementsOfType("hud_boosters", true);
            var hudBoosters = TutorialElementsHighlighter.GetElementOfType("hud_boosters");
            var followingCoroutine = StartCoroutine(FollowTransform(_speechBubbleHolder, hudBoosters.transform, _speechBubbleOffset));
            _holder.SetActive(true);

            ResetTap();
            _anyNewModalPushed = false;

            ModalsManager.ModalPushed += ModalPushedHandler;
            while (ModalsManager.IsShowingModal(ModalsType.LevelStart) && !IsTapped && !_anyNewModalPushed && !IsAnyInput)
                yield return null;

            ModalsManager.ModalPushed -= ModalPushedHandler;

            StopCoroutine(followingCoroutine);
            _holder.SetActive(false);
            TutorialElementsHighlighter.EnableHighlightElementsOfType("hud_boosters", false);
        }
        
        private void ModalPushedHandler(IController controller)
        {
            _anyNewModalPushed = true;
        }

        protected override void OnDestroy()
        {
            ModalsManager.ModalPushed -= ModalPushedHandler;
            base.OnDestroy();
        }
    }
}