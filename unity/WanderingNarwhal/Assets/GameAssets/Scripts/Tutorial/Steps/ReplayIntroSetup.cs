using BBB;
using BBB.DI;
using BBB.Modals;
using BBB.UI.Level;
using GameAssets.Scripts.Tutorial.Core;

namespace GameAssets.Scripts.Tutorial.Steps
{
    public class ReplayIntroSetup : BaseTutorialStepSetup
    {
        public override string DebugNarrativeUid => "replay";
        
        private LevelSuccessViewPresenter _levelSuccessViewPresenter;

        public override void Init(IContext context)
        {
            base.Init(context);
            var modalsManager = context.Resolve<IModalsManager>();

            EnterCondition = () =>
                !ScreensManager.IsTransitionInProgress
                && LastScreenType == ScreenType.LevelScreen
                && modalsManager.IsShowingAModal()
                && modalsManager.IsShowingModal(ModalsType.LevelSuccess, ModalsType.LevelSuccessVerticalLB, ModalsType.LevelSuccessNoLB)
                && IsReplayRequestedState();

            ExitCondition = () => false;
            return;

            bool IsReplayRequestedState()
            {
                if (_levelSuccessViewPresenter == null)
                    _levelSuccessViewPresenter = FindObjectOfType<LevelSuccessViewPresenter>();

                if (_levelSuccessViewPresenter == null)
                    return false;

                return _levelSuccessViewPresenter.ViewStateField == LevelSuccessBaseViewPresenter.ViewState.WasReplayRequested;
            }
        }
    }
}