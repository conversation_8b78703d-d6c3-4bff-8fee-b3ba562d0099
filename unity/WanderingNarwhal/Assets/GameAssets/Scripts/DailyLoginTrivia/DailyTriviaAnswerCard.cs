using BBB.BrainCloud;
using BBB.Core;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.Utils;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.DailyTrivia
{
    public class DailyTriviaAnswerCard : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;
        [SerializeField] private GameObject[] _correctHolders;
        [SerializeField] private GameObject[] _incorrectHolders;

        [SerializeField] private LocalizedTextPro _yourAnswerText;
        [SerializeField] private LocalizedTextPro _explanationText;

        [SerializeField] private Image _image;
        [SerializeField] private AspectRatioFitter _imageAspectRatioFitter;
        [SerializeField] private ImpactPreset _triviaImageSettleImpactPreset;

        private IVibrationsWrapper _vibrationsWrapper;

        public void Setup(TriviaData triviaData, bool correctAnswer, string yourAnswerText,
            TriviaImagesLoader triviaImagesLoader, IVibrationsWrapper vibrationsWrapper)
        {
            _correctHolders.Enable(false);
            _incorrectHolders.Enable(false);
            _explanationText.ClearText();
            _vibrationsWrapper = vibrationsWrapper;

            if (correctAnswer)
            {
                _correctHolders.Enable(true);
            }
            else
            {
                _incorrectHolders.Enable(true);
            }

            _yourAnswerText.FormatSetArgs(yourAnswerText);

            var explanation = correctAnswer ? triviaData.CorrectExplanation : triviaData.IncorrectExplanation;

            if (!explanation.IsNullOrEmpty())
            {
                _explanationText.SetTextId(explanation);
            }

            if (_image == null) return;

            if (!triviaImagesLoader.IsImagesReady)
            {
                Debug.LogError(
                    "Attempt to show card with images when images are not loaded yet, that should not be the case");
                return;
            }

            var answerImage = triviaImagesLoader.AnswerImage;
            if (answerImage == null)
            {
                Debug.LogError("AnswerImage is not ready");
                return;
            }

            _image.sprite = answerImage;
            _imageAspectRatioFitter.aspectRatio = answerImage.rect.width / answerImage.rect.height;
        }

        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            _animator.SetTrigger("Hide");
        }

        [UsedImplicitly]
        [AnimationTriggered("AnswerCardWithImageIntro", "AnswerCardWithImage", "Base Layer")]
        public void PlayHapticFeedback()
        {
            BDebug.Log(LogCat.Vibration,
                $"Playing haptic feedback for answer card -- Vibrations available={_vibrationsWrapper != null} with Impact Type = {_triviaImageSettleImpactPreset}");
            _vibrationsWrapper?.PlayHaptic(_triviaImageSettleImpactPreset);
        }
    }
}