using System;
using BBB.DI;
using BBB.UI.Core;
using BBB.Wallet;
using TMPro;
using UnityEngine;

namespace GameAssets.Scripts.Promotions.Pack
{
    public enum LocalizationType
    {
        Number,
        Time,
        NumberX,
        NumberXEach
    }

    [RequireComponent(typeof(TextMeshProUGUI))]
    public class CurrenciesLocalizer : ContextedUiBehaviour
    {
        private const string EachTextId = "CURRENCY_EACH";
        [SerializeField] private TextMeshProUGUI _text;
        [SerializeField] private LocalizationType _localizationType;
        [SerializeField] private int _number = 3600;

        private ILocalizationManager _localizedManager;

        private void OnValidate()
        {
            _text = GetComponent<TextMeshProUGUI>();
            if (enabled)
                Refresh();
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _localizedManager = context.Resolve<ILocalizationManager>();
        }

        private void Start()
        {
            LazyInit();
            Refresh();
        }

        private void Refresh()
        {
            switch (_localizationType)
            {
                case LocalizationType.Number:
                    _text.text = _number.ToString();
                    break;
                case LocalizationType.Time:
                    _text.text = RewardsAmountStringFormatDefinitions.LocalizeCurrencyCount(InventoryItems.InfLife, _number, _localizedManager, true, true);
                    break;
                case LocalizationType.NumberX:
                    _text.text = RewardsAmountStringFormatDefinitions.LocalizeCurrencyCount(InventoryBoosters.ShovelBooster, _number, _localizedManager, true, true);
                    break;
                case LocalizationType.NumberXEach:
                    var numberX = RewardsAmountStringFormatDefinitions.LocalizeCurrencyCount(InventoryBoosters.ShovelBooster, _number, _localizedManager, true, true);
                    _text.text = _localizedManager != null ? $"{numberX} {_localizedManager.getLocalizedText(EachTextId)}" : $"{numberX} EACH";
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }
}