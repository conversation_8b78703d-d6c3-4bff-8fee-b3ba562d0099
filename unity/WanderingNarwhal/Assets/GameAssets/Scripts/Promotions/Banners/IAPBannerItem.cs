using BBB;
using BBB.Wallet;
using FBConfig;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Banners
{
    public class IAPBannerItem : BbbMonoBehaviour
    {
        [SerializeField] private Image _icon;
        [SerializeField] private TextMeshProUGUI _text;
        [SerializeField] private LocalizedTextPro _subtitleText;

        public void Apply(IAPBannerItemConfig iapBannerItemConfig, IAPStoreMarketItemConfig? storeItemConfig)
        {
            _icon.gameObject.SetActive(false);
            _text.gameObject.SetActive(false);

            if (iapBannerItemConfig.Icon != null)
            {
                _icon.sprite = iapBannerItemConfig.Icon;
                _icon.gameObject.SetActive(true);
            }
            else
            {
                _text.text = iapBannerItemConfig.Text;
                _text.gameObject.SetActive(true);
            }

            if (iapBannerItemConfig.SubtitleSubtitleType == IAPBannerItemConfig.SubtitleType.LocalizedUid)
            {
                _subtitleText.SetTextId(iapBannerItemConfig.SubtitleText);
            }
            else if (iapBannerItemConfig.SubtitleSubtitleType == IAPBannerItemConfig.SubtitleType.RawText)
            {
                _subtitleText.SetRawText(iapBannerItemConfig.SubtitleText);
            }
            else if (storeItemConfig.HasValue)
            {
                _subtitleText.SetRawText(GetRewardsNumber(storeItemConfig.Value.RewardOrdered, iapBannerItemConfig.SubtitleSubtitleType).ToString());
            }
        }

        private int GetRewardsNumber(IAPRewardOrdered? rewardOrdered, IAPBannerItemConfig.SubtitleType subtitleType)
        {
            if (rewardOrdered?.RewardLength == 0)
                return 0;

            var count = 0;
            for (var i = 0; i < rewardOrdered?.RewardLength; i++)
            {
                var reward = rewardOrdered.Value.Reward(i).Value;
                if (subtitleType == IAPBannerItemConfig.SubtitleType.Coins)
                {
                    if (reward.Uid == WalletCurrencies.RegularCurrency)
                    {
                        count = (int)reward.Count;

                        break;
                    }
                }
                else if (subtitleType == IAPBannerItemConfig.SubtitleType.Boosters)
                {
                    if (InventoryItems.BoosterUids.Contains(reward.Uid))
                    {
                        if (InventoryItems.IsInfinite(reward.Uid))
                            count++;
                        else
                            count += (int)reward.Count;
                    }
                }
            }

            return count;
        }
    }
}