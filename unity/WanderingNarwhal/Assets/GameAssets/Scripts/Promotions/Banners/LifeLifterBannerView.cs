using BBB;
using BBB.DI;
using TMPro;
using UnityEngine;
using IapManager = BBB.IAP.IapManager;

namespace GameAssets.Scripts.Promotions.Banners
{
    public class LifeLifterBannerView : BannerView
    {
        [SerializeField] private TextMeshProUGUI _priceText;

        private IapManager _iapManager;
        private BannerManager _bannerManager;

        private string _titleLocalizationUid;
        public override string TitleLocalizationUid => _titleLocalizationUid;

        protected override void InitWithContextInternal(IContext context)
        {
            _iapManager = context.Resolve<IapManager>();
            _bannerManager = context.Resolve<BannerManager>();
        }

        protected override void OnSetup()
        {
            var lifeLifterProductData = _iapManager.GetPurchasableItemByConfigId(LivesManager.LifeLifterIAPUid);
            if (lifeLifterProductData == null)
            {
                Debug.LogError($"Life lifter: {LivesManager.LifeLifterIAPUid} is not initialized");
                gameObject.SetActive(false);
                return;
            }

            _priceText.text = _iapManager.GetPurchasableItemByConfigId(LivesManager.LifeLifterIAPUid).LocalizedPriceString;
            _titleLocalizationUid = _iapManager.GetIapConfig(LivesManager.LifeLifterIAPUid).Name;
        }

        protected override void ActionButtonHandler()
        {
            _bannerManager.LogBannerClickedAnalytics(Banner.Promotion?.Uid);
            _bannerManager.DoBannerPurchase(LivesManager.LifeLifterIAPUid, Banner.Promotion?.Uid, new IapPurchaseParams()
            {
                ShowGenericRewardScreen = false,
                SkipClaim = true,
            });
        }

        public override bool ShouldBeShown()
        {
            // something super weird happens with _initialized state of banners being tru all the time
            // so forcing the init
            InitWithContextInternal(_context);
            
            if (_iapManager is not { Ready: true })
                return false;

            var lifeLifterProductData = _iapManager.GetPurchasableItemByConfigId(LivesManager.LifeLifterIAPUid);
            
            if (lifeLifterProductData != null) return true;
            
            Debug.LogError($"Life lifter: {LivesManager.LifeLifterIAPUid} is not initialized");
            return false;
        }
    }
}