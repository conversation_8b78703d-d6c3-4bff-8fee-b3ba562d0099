using System.Collections.Generic;
using FBConfig;

namespace GameAssets.Scripts.Promotions
{
    public struct PromotionAction
    {
        public readonly string ActionUid;
        public readonly Dictionary<string, string> ActionParams;

        public PromotionAction(string actionUid, Dictionary<string, string> actionsParams = null)
        {
            ActionUid = actionUid;
            ActionParams = actionsParams;
        }

        public PromotionAction(ActionItem actionItem)
        {
            ActionUid = actionItem.Action;
            ActionParams = null;

            if (actionItem.ParamsLength == 0)
                return;

            ActionParams = new Dictionary<string, string>();
            for (var i = 0; i < actionItem.ParamsLength; i++)
            {
                var param = actionItem.Params(i);
                if (param != null)
                    ActionParams[param.Value.Key] = param.Value.Value;
            }
        }
    }
}