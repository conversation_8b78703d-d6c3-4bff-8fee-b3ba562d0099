using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.IAP;
using BBB.Quests;
using FBConfig;
using PBConfig;
using IAPStoreMarketItemConfig = FBConfig.IAPStoreMarketItemConfig;

namespace BBB.Store
{
    public class StoreIapItem : StoreItem
    {
        private readonly ProductData _productData;

        public IAPRewardOrdered? Reward;
        public StoreIapItem(IAPStoreItemConfig itemConfig,  IAPStoreMarketItemConfig iapStoreMarketItemConfig, ProductData productData )
        {
            Uid = itemConfig.ItemUid;
            Name = iapStoreMarketItemConfig.Name;
            Description = iapStoreMarketItemConfig.Description;
            LocalizedPrice = productData.LocalizedPriceString;
            Icon = iapStoreMarketItemConfig.Icon;
            IconPrefab = iapStoreMarketItemConfig.IconPrefab;
            Prefab = itemConfig.Prefab;
            Predicate = itemConfig.Predicate;
            
            if (iapStoreMarketItemConfig.SaleBadgeText.IsNullOrEmpty())
            {
                SaleRibbonTitle = iapStoreMarketItemConfig.DiscountOfId;
            }
            else
            {
                SaleNumber = iapStoreMarketItemConfig.SaleBadgeText;
                SaleTitle = iapStoreMarketItemConfig.DiscountOfId;
            }
            
            _productData = productData;
            Amount = iapStoreMarketItemConfig.Amount;
            Reward = iapStoreMarketItemConfig.RewardOrdered;

            if (!iapStoreMarketItemConfig.Category.IsNullOrEmpty() && iapStoreMarketItemConfig.Amount > 0)
            {
                RewardDictionary = new Dictionary<string, long>()
                {
                    { iapStoreMarketItemConfig.Category, iapStoreMarketItemConfig.Amount }
                };
            }
        }

        public override void Purchase(Action onSuccess, Action onFailed = null)
        {
            BDebug.Log(LogCat.Iap, "Purchase in StoreIapItem - " + _productData.ID);
            BoughtAmount = Amount;

            IapPurchaseProcessor.Purchase(_productData.ID, new IapPurchaseParams()
            {
                PurchaseSource = PurchaseSource.Store,
                FlowCompletedCallback = () => onSuccess?.Invoke(),
                FlowFailedCallback = () => onFailed?.Invoke(),
                ShowGacha = true,
                ShowGenericRewardScreen = true,
                SkipClaim = true,
            });
        }
    }
}