using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using BBB.Controller;
using BBB.Core;
using BBB.Core.Crash;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.UI;
using UnityEngine;
using BBB.Generic.Modal;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Tutorial.Core;
using UnityEngine.UI;

namespace BBB.Modals
{
    public class ModalsManager : BaseViewsManager, IModalsManager
    {
        public event Action<IController> ModalPushed = delegate { };
        public event Action<IController> ModalShown = delegate { };
        public event Action<ModalsType> ModalShownType = delegate { };
        public event Action<IController> ModalHidden = delegate { };

        [SerializeField] private Canvas _modalCanvas;
        [SerializeField] private GameObject _locker;
        [SerializeField] private GameObject _lockerScrim;
        [SerializeField] private Transform _lockerLoadingRoot;

        private IScreensManager _screensManager;
        private ITutorialPlaybackController _tutorialPlaybackController;
        private IEpisodicScenesManager _episodicScenesManager;

        /// <summary>
        /// Modals currently displayed.
        /// </summary>
        private readonly List<Tuple<IController, IViewPresenter>> _activeModalsStack = new();

        /// <summary>
        /// Immediate modals, which currently waiting until modal display block is not lifted. All of modals in this queue will be showed at once.
        /// </summary>
        /// <remarks>
        /// Modals display blocking is happening during tutorial or map camera movement sequence.
        /// </remarks>
        private readonly List<ModalsQueueEntry> _immediateModalsQueue = new();

        private bool _immediateModalsQueueCleaningLock = false;

        /// <summary>
        /// Delayed modals waiting to be displayed. Modals from this queue will be showed one by one (only one at a time) by their priority index (lower value - sooner display).
        /// </summary>
        /// <remarks>
        /// Blocking is happening during tutorial or map camera movement sequence or if other modal is currently active.
        /// </remarks>
        private readonly List<ModalsQueueEntry> _modalsQueue = new();

        /// <summary>
        /// Modals, that wait for hide animation to complete.
        /// </summary>
        private readonly List<IController> _hidingModalsQueue = new();

        private Coroutine _waitTempBlockRoutine = null;
        private Coroutine _waitHideRoutine = null;
        private IGenericModalsBlocker _genericModalsBlocker;
        private GameObject _loadingIndicator;

        private float _currentScrimAlpha;
        private Tween _fadeTween;

        /// <summary>
        /// Tag that had last showed Delayed modal (immediate modals ignored, because they don't affect grouping use-cases).
        /// </summary>
        public string LastShowedModalTag { get; private set; }

        private const float MaxScrimAlpha = 0.9f;
        private const float ScrimChangeDuration = 0.3f;

        protected override void Awake()
        {
            base.Awake();
            EnableLockerScreen(false);
            RootTransform = _modalCanvas.transform;
            RootTransform.hierarchyCapacity = 30;
        }

        public void Init(ILocationManager locationManager, IScreensManager screensManager,
            IGenericModalsBlocker genericModalsBlocker)
        {
            _screensManager = screensManager;
            _genericModalsBlocker = genericModalsBlocker;
        }

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            _screensManager = context.Resolve<IScreensManager>();
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _screensManager.NextScreenPreShown += NextScreenPreShownHandler;
        }

        private void Unsubscribe()
        {
            if (_screensManager != null)
            {
                _screensManager.NextScreenPreShown -= NextScreenPreShownHandler;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
            _screensManager = null;
        }

        private void NextScreenPreShownHandler(ScreenType arg1, IScreensController arg2)
        {
            if (IsShowingAModal())
            {
                HideAllModals();
            }
        }

        private void EnableLockerScreen(bool enable, bool showScrim = false, bool showLoadingIndicator = false)
        {
            _locker.SetActive(enable);
            _lockerScrim.SetActive(enable && showScrim);
            if (_loadingIndicator != null)
            {
                _loadingIndicator.SetActive(enable && showLoadingIndicator);
            }
        }

        // you can consider these scrim related section as first iteration of shared scrim which we would like to implement later
        // At the moment it tries to solve quite complex request on synchronizing alpha of the scrim on bonus time flow and dynamic fdading
        public void SetCurrentScrimAlpha(float scrimAlpha)
        {
            _currentScrimAlpha = scrimAlpha;
        }

        public void ApplyScrimBlendedFadeIn(Image scrim)
        {
            FadeScrim(scrim, _currentScrimAlpha, MaxScrimAlpha);
        }

        public void ApplyScrimBlendedFadeOut(Image scrim)
        {
            FadeScrim(scrim, scrim.color.a, 0f);
        }

        private void FadeScrim(Image scrim, float startAlpha, float endAlpha)
        {
            var currentColor = scrim.color;
            var duration = Mathf.Abs(startAlpha - endAlpha) / MaxScrimAlpha * ScrimChangeDuration;
            _currentScrimAlpha = endAlpha;

            if (_fadeTween != null && _fadeTween.IsPlaying())
            {
                _fadeTween.Kill(true);
            }

            _fadeTween = DOTween.To(() => startAlpha, alpha =>
            {
                currentColor.a = alpha;
                scrim.color = currentColor;
            }, endAlpha, duration).OnComplete(() => { _fadeTween = null; });
        }

        public List<ModalsType> OnLowMemory()
        {
            return RemoveModals(_ => true);
        }

        public List<ModalsType> RemoveRareModals()
        {
            return RemoveModals(controllerView => controllerView.View is IDestroyable);
        }

        public IController CurrentActiveModal => _activeModalsStack.Count > 0 ? _activeModalsStack[^1].Item1 : null;

        private List<ModalsType> RemoveModals(Func<ControllerView, bool> shouldRemovePredicate, bool forceAll = false)
        {
            var viewTypesRemoved = new List<ModalsType>();
            var viewMapCopy = new Dictionary<Type, ControllerView>();

            foreach (var entry in ControllerViewMap)
            {
                viewMapCopy[entry.Key] = entry.Value;
            }

            foreach (var controllerViewPair in viewMapCopy)
            {
                var controllerView = controllerViewPair.Value;
                var controllerType = controllerView.Controller.GetType();

                if (!forceAll && (IsShowingModal(controllerType) || !shouldRemovePredicate(controllerView))) continue;

                viewTypesRemoved.Add((ModalsType)controllerView.ViewType);

                if (controllerView.View is IDestroyable destroyable)
                {
                    destroyable.Destroy();
                }
                else
                {
                    Destroy(controllerView.ViewGameObject);
                }

                ControllerViewMap.Remove(controllerType);
            }

            return viewTypesRemoved;
        }

        public void ProvideOtherManagers(ITutorialPlaybackController tutorialPlaybackController, IEpisodicScenesManager episodicScenesManager)
        {
            _tutorialPlaybackController = tutorialPlaybackController;
            _episodicScenesManager = episodicScenesManager;
        }

        /// <summary>
        /// Show modal or add modal to the queue if it's not proper time to show it immediately.
        /// </summary>
        /// <param name="controller">Modal controller.</param>
        /// <param name="showMode"> Mode of modal show method.
        /// Immediate - should immediately while other modals are already active
        /// AddToTop - add to the top of showing deque to make it shown right after current will be closed
        /// AddToBottom - add to the bottom of showing deque to mak it shown as the last one after every other queued is closed</param>
        /// <remarks>
        /// Modal display may be delayed by these factors:
        /// by the fact that other modal is currently active;
        /// by map location animations (such as coins drop or camera focusing movement). Note: map animations can be only on Map Screen of the game;
        /// or by tutorial active state (this state is active whenever any tutorial step is currently playing);
        /// 
        /// Only first blocking factor can be omitted by boolean option argument `immediate`.
        /// If modal is blocked for display at current time, it will be placed into queue, which will wait for proper time to display in coroutine or for moment of hiding of currently active modals.
        /// 
        /// We have two queues: for immediate modals and for delayed modals.
        /// First queue will wait for location map blocking and other one will wait until all current modals not closed.
        /// Immediate modals, if more than one in queue, will show up all at once when proper time has come.
        ///
        /// When current screen is changing all active modals are forcing to close and all queues are clearing.
        /// Modals never stay active between screen transitions and should never show up after screen transition (if show was triggered before transition). -VK
        /// </remarks>
        public void PushModal(IController controller, ShowMode showMode, string tag, ModalSetupParamsBase setupParams)
        {
            if (controller == null)
            {
                BDebug.LogError(LogCat.General, "Pushed modal controller is null");
                return;
            }

            ModalPushed(controller);

            BDebug.Log(LogCat.Modals, $"{Time.frameCount}: Modal {controller.GetType()} pushed  with mode {showMode.ToString()} tag={tag}");
            if (showMode == ShowMode.Immediate)
            {
                var byPassCondition = ((IModalsController)controller).CanBypassTransition();
                var isTempBlocked = false;

                var view = controller.GetView;
                if ((view != null && view.IsVisible()) || (!byPassCondition && IsBlocked()))
                {
                    _waitTempBlockRoutine ??= StartCoroutine(WaitForTemporaryBlockComplete());
                    isTempBlocked = true;
                }

                if (isTempBlocked)
                {
                    if (!ControllerExistsInQueue(_immediateModalsQueue, controller) && !ControllerExistsInStack(_activeModalsStack, controller))
                    {
                        var entry = new ModalsQueueEntry
                        {
                            Tag = tag,
                            ModalController = controller,
                            SetupParams = setupParams
                        };
                        _immediateModalsQueue.Add(entry);
                    }
                }
                else
                {
                    if (!IsShowingAModal() || !_activeModalsStack.Exists(m => m.Item1 == controller))
                    {
                        LastShowedModalTag = tag;

                        // without this wrapping common scenario will lead to overlap:
                        // 1. Modal with immediate mode pushed
                        // 2. It is loading and it takes some time
                        // 3. Another modal is pushed with Delayed mode
                        // 4. Because of immediate modal isn't registered in any queue or stack it leads to delayed modal being shown as well together with immediate
                        var entry = new ModalsQueueEntry
                        {
                            Tag = tag,
                            ModalController = controller,
                            SetupParams = setupParams
                        };

                        _immediateModalsQueue.Add(entry);
                        StartCoroutine(DisplayModalView(controller, setupParams, displayedCallback: () => { _immediateModalsQueue.Remove(entry); }));
                    }
                }
            }
            else
            {
                var index = _modalsQueue.IndexOf(entry => entry.ModalController == controller);

                //modal can only be added if it does not exist in the queue
                //or has setup params and those params are not the same as existing ones
                var canAddThisModal = index < 0 || (!ReferenceEquals(setupParams, null) && setupParams != _modalsQueue[index].SetupParams);

                if (canAddThisModal)
                {
                    if (!_activeModalsStack.Exists(m => m.Item1 == controller))
                    {
                        var queueEntry = new ModalsQueueEntry
                        {
                            Tag = tag,
                            ModalController = controller,
                            SetupParams = setupParams
                        };
                        _modalsQueue.Add(queueEntry);
                    }
                }
                else
                {
                    if (index >= 0)
                    {
                        _modalsQueue[index].Tag = tag;

                        BDebug.Log(LogCat.Modals, $"Trying to add modal {controller.GetType().Name} while it is already in the queue");
                    }
                }

                if (_waitTempBlockRoutine == null && !IsShowingAModal())
                {
                    _waitTempBlockRoutine = StartCoroutine(WaitForTemporaryBlockComplete());
                }
            }

            return;

            bool ControllerExistsInQueue(IEnumerable<ModalsQueueEntry> queue, IController iController)
            {
                foreach (var entry in queue)
                {
                    if (entry.ModalController == iController)
                    {
                        return true;
                    }
                }

                return false;
            }

            bool ControllerExistsInStack(List<Tuple<IController, IViewPresenter>> stack, IController iController)
            {
                foreach (var entry in stack)
                {
                    if (entry.Item1 == iController)
                    {
                        return true;
                    }
                }

                return false;
            }
        }

        /// <summary>
        /// Wait until current map animation not finished or while tutorial is active, then show all queued immediate modals or one modal from non-immediate queue.
        /// Note: Non-immediate modals queue is also used to show next modal on every close modal action, which doesn't require coroutine to wait. -VK
        /// </summary>
        private IEnumerator WaitForTemporaryBlockComplete()
        {
            while (IsBlocked())
            {
                yield return null;
            }

            if (_immediateModalsQueue.Count > 0)
            {
                while (_immediateModalsQueueCleaningLock)
                    yield return null;

                _immediateModalsQueueCleaningLock = true;
                for (int i = _immediateModalsQueue.Count - 1; i >= 0; i--)
                {
                    var modal = _immediateModalsQueue[i];
                    if (!_hidingModalsQueue.Contains(modal.ModalController))
                    {
                        // same as described above in method PushModal, we do not want to remove modals from queue until they are in active modals stack
                        // otherwise we getting overlap
                        LastShowedModalTag = modal.Tag;
                        yield return StartCoroutine(DisplayModalView(modal.ModalController, modal.SetupParams));
                        _immediateModalsQueue.Remove(modal);
                    }
                }

                _immediateModalsQueueCleaningLock = false;

                // If there are still modals left in queue, then they are currently hiding.
                while (_immediateModalsQueue.Count > 0)
                {
                    // We can't show modal if it's currently hiding,
                    // So we need to wait until modal is hidden before showing it again.
                    // Same logic applies for non-immediate modals. -VK
                    yield return null;

                    if (_immediateModalsQueueCleaningLock)
                        yield return null;

                    _immediateModalsQueueCleaningLock = true;
                    for (int i = _immediateModalsQueue.Count - 1; i >= 0; i--)
                    {
                        if (!_hidingModalsQueue.Contains(_immediateModalsQueue[i].ModalController))
                        {
                            LastShowedModalTag = _immediateModalsQueue[i].Tag;
                            yield return StartCoroutine(DisplayModalView(_immediateModalsQueue[i].ModalController, _immediateModalsQueue[i].SetupParams));
                            _immediateModalsQueue.RemoveAt(i);
                        }
                    }

                    _immediateModalsQueueCleaningLock = false;
                }
            }

            while (IsBlockedByTutorial())
            {
                yield return null;
            }

            while (true)
            {
                // Show one non-immediate modal from queue
                // but only if it's not currently hiding.

                // If there all modals in queue currently hiding,
                // then repeat this logic at next frame -VK 

                if (_modalsQueue.Count > 0 && !IsShowingAModal())
                {
                    var selectedModalIndex = -1;
                    var currentPriority = int.MaxValue;
                    var isExistModalWithSameTag = false;
                    if (!LastShowedModalTag.IsNullOrEmpty())
                    {
                        foreach (var m in _modalsQueue)
                        {
                            if (m.Tag != LastShowedModalTag) continue;

                            isExistModalWithSameTag = true;
                            break;
                        }
                    }

                    for (var i = _modalsQueue.Count - 1; i >= 0; i--)
                    {
                        var nextQueueEntry = _modalsQueue[i];
                        var nextModal = nextQueueEntry.ModalController;

                        if (_hidingModalsQueue.Contains(nextModal))
                            continue;

                        var isAllowedByTag = !isExistModalWithSameTag || nextQueueEntry.Tag == LastShowedModalTag;
                        if (!isAllowedByTag)
                            continue;

                        var priority = nextModal.DisplayPriority;
                        if (priority >= currentPriority)
                            continue;

                        selectedModalIndex = i;
                        currentPriority = priority;
                    }

                    // There are all modals in queue currently hiding, we need to wait
                    if (selectedModalIndex < 0)
                    {
                        yield return null;
                        continue;
                    }

                    var entry = _modalsQueue[selectedModalIndex];
                    BDebug.Log(LogCat.Modals,
                        $"{Time.frameCount}: Showing modal from delayed queue {entry.ModalController.GetType().Name}. priority = {entry.ModalController.DisplayPriority}. tag = {entry.Tag}. lastShowedTag = {LastShowedModalTag}. total queue size = {_modalsQueue.Count}");

                    LastShowedModalTag = entry.Tag;
                    var fastCoroutine = DisplayModalView(entry.ModalController, entry.SetupParams, true, () =>
                    {
                        // if it is not done in callback, then in case of preloading, modals queue will be empty and actives modal will be empty leading to unstable scenario
                        // Where can be invoked one modal over another
                        _modalsQueue.Remove(entry);
                    });

                    while (fastCoroutine.MoveNext())
                    {
                        yield return fastCoroutine.Current;
                    }

                    _waitTempBlockRoutine = null;
                    yield break;
                }

                // Suddenly no modals left to show or already showed other modal.
                // If there is still a modal in queue, then this coroutine should restart next time any modal hide.-VK
                break;
            }

            _waitTempBlockRoutine = null;
        }

        /// <summary>
        /// Is currently blocked modals display check.
        /// </summary>
        private bool IsBlocked()
        {
#if UNITY_EDITOR || BBB_DEBUG
            if (Input.GetKey(KeyCode.B))
            {
                return true;
            }
#endif

            if (_screensManager != null && _screensManager.IsTransitionInProgress) return true;

            //don't show any modal on Construction screen mode
            if (_episodicScenesManager is { ScreenMode: EpisodeScreenMode.Construction } &&
                _screensManager != null && _screensManager.GetCurrentScreenType() == ScreenType.EpisodeScreen)
            {
                return true;
            }

            return _genericModalsBlocker != null && _genericModalsBlocker.IsBlocked();
        }

        private bool IsBlockedByTutorial()
        {
            return _tutorialPlaybackController != null && _tutorialPlaybackController.IsTutorialShouldBlockNonImmediatePopupsDisplay.Value;
        }

        private IEnumerator DisplayModalView(IController controller, ModalSetupParamsBase setupParams, bool nullifyBlockCoroutine = false, Action displayedCallback = null)
        {
            BDebug.Log(LogCat.Modals, $"{Time.frameCount}: DisplayModalView called for {controller.GetType()}");
            UnityEngine.Profiling.Profiler.BeginSample($"DisplayModalView [{controller.GetType()}] Setup");
            if (setupParams != null)
            {
                var modalController = (IModalsController)controller;
                modalController.Setup(setupParams);
            }

            UnityEngine.Profiling.Profiler.EndSample();
            var controllerView = ControllerViewMap.GetSafe(controller.GetType());
            if (controllerView != null && !_activeModalsStack.Exists(_ => _.Item1 == controller))
            {
                CrashLoggerService.Log($"PushModal NotReady {controller.GetType()}");

                var fastCoroutine = WaitForControllerIsReady(controllerView);
                while (fastCoroutine.MoveNext())
                {
                    yield return fastCoroutine.Current;
                }

                if (nullifyBlockCoroutine)
                    _waitTempBlockRoutine = null;
                UnityEngine.Profiling.Profiler.BeginSample($"DisplayModalView [{controller.GetType()}] Show");
                controllerView.Controller.Show();
                UnityEngine.Profiling.Profiler.EndSample();
                BDebug.Log(LogCat.Modals, $"{Time.frameCount}: Modal {controllerView.Controller.GetType()} shown");

                StartCoroutine(OnPostShow(controllerView.Controller));
            }
            else if (controllerView != null)
            {
                CrashLoggerService.Log($"PushModal Already loaded {controller?.GetType()}");
                controllerView.ViewGameObject.transform.SetAsLastSibling();

                if (nullifyBlockCoroutine)
                    _waitTempBlockRoutine = null;
                UnityEngine.Profiling.Profiler.BeginSample($"DisplayModalView [{controller.GetType()}] Show");
                controller.Show();
                UnityEngine.Profiling.Profiler.EndSample();
                BDebug.Log(LogCat.Modals, $"{Time.frameCount}: Modal {controllerView.Controller.GetType()} shown");

                StartCoroutine(OnPostShow(controller));
            }
            else
            {
                BDebug.LogError(LogCat.Resources, "unloadasset ControllerViewMap does contain " + controller.GetType().Name);
            }

            displayedCallback?.Invoke();
            yield break;

            IEnumerator OnPostShow(IController controller)
            {
                yield return null;
                yield return null;
                if (controller.GetView.IsVisible() && !_hidingModalsQueue.Contains(controller))
                {
                    controller.OnPostShow();
                }
            }
        }

        private IEnumerator WaitForControllerIsReady(ControllerView controllerView)
        {
            if (!controllerView.Controller.IsReady())
            {
                // When we do a preload of modal, we show loading bar on top left. The issue though is that when we do a switch between modals,
                // we will run into scenario with quick blink of no scrim which was requested to be resolved
                EnableLockerScreen(true, !_activeModalsStack.IsNullOrEmpty() || !_hidingModalsQueue.IsNullOrEmpty(), true);

                while (!controllerView.Controller.IsReady())
                    yield return null;

                // use this code to emulate loading of modals on device
                // yield return WaitCache.Seconds(1f);
                EnableLockerScreen(false);
            }

            _activeModalsStack.Add(new Tuple<IController, IViewPresenter>(controllerView.Controller, controllerView.View));
            CrashLoggerService.Log($"ShowModal WaitForControllerIsReady {controllerView.Controller?.GetType()}");

            UnityEngine.Profiling.Profiler.BeginSample($"DisplayModalView [{controllerView.Controller.GetType()}] ModalShown");
            ModalShown(controllerView.Controller);
            ModalShownType((ModalsType)controllerView.ViewType);
            UnityEngine.Profiling.Profiler.EndSample();

            controllerView.ViewGameObject.transform.SetAsLastSibling();

            var modalController = controllerView.Controller as IModalsController;

            if (modalController == null)
            {
                Debug.LogError($"ModalsManager has controller of wrong type {controllerView.Controller.GetType().Name}");
            }

            modalController?.TriggerDoWhenReady();
        }

        /// <summary>
        /// Hide modal view and also trigger show of next modal in queue if it exists.
        /// </summary>
        public void HideModal(IController controller)
        {
            BDebug.Log(LogCat.Modals, $"{Time.frameCount}: HideModal {controller.GetType()}");
            CrashLoggerService.Log($"HideModal {controller?.GetType()}");

            if (!_activeModalsStack.Exists(m => m.Item1 == controller))
            {
                _modalsQueue.RemoveAll(entry => entry.ModalController == controller);
                _immediateModalsQueue.RemoveAll(entry => entry.ModalController == controller);
                return;
            }

            var view = controller.GetView;
            if (view != null && view.IsVisible())
            {
                if (!_hidingModalsQueue.Contains(controller))
                {
                    _hidingModalsQueue.Add(controller);
                }

                _waitHideRoutine ??= StartCoroutine(HideModalsRoutine());
            }

            _activeModalsStack.RemoveAll(tuple => tuple.Item1 == controller);
            ModalHidden(controller);

            if (_modalsQueue.Count == 0 || IsShowingAModal()) return;

            // At the moment of hiding of modal check if there is next modal in queue
            // and also try to start waiting coroutine to show this modal on next frame or after blocking factor lifted. -VK
            _waitTempBlockRoutine ??= StartCoroutine(WaitForTemporaryBlockComplete());
        }

        /// <summary>
        /// Waiter coroutine for hiding modals.
        /// </summary>
        /// <remarks>
        /// This coroutine watches queue of hiding modals and updates it when any modal is fully hidden.
        /// Queue can be checked in other parts of system at any moment
        /// to determine if some modal is currently in a middle of hiding process.
        /// Modal can't be showed again if it's not yet fully hidden.
        /// </remarks>
        private IEnumerator HideModalsRoutine()
        {
            do
            {
                yield return null;
                for (int i = _hidingModalsQueue.Count - 1; i >= 0; i--)
                {
                    var view = _hidingModalsQueue[i].GetView;
                    if (view == null || !view.IsVisible())
                    {
                        _hidingModalsQueue.RemoveAt(i);
                    }
                }
            } while (_hidingModalsQueue.Count > 0);

            _waitHideRoutine = null;
        }

        public void HideAllModals()
        {
            BDebug.Log(LogCat.Modals, $"{Time.frameCount}: HideAllModals");
            for (int i = 0; i < _activeModalsStack.Count; i++)
            {
                var kv = _activeModalsStack[i];
                var view = kv.Item1.GetView;
                if (!_hidingModalsQueue.Contains(kv.Item1) && kv.Item1.IsReady() && view != null && view.IsVisible() && !kv.Item1.CanBypassTransition())
                {
                    kv.Item1.Hide();
                }
            }

            _activeModalsStack.Clear();
            _modalsQueue.Clear();
            _immediateModalsQueue.Clear();
            if (_waitTempBlockRoutine != null)
            {
                StopCoroutine(_waitTempBlockRoutine);
                _waitTempBlockRoutine = null;
            }
        }

        /// <summary>
        /// Is currently displaying any modal.
        /// </summary>
        /// <param name="includingQueue">Count also modals that are going to just show up from delayed queue.</param>
        public bool IsShowingAModal(bool includingQueue = false)
        {
            if (includingQueue)
            {
                return _activeModalsStack.Count > 0 || HasModalInQueue();
            }

            return _activeModalsStack.Count > 0;
        }

        public bool ShouldHideHud()
        {
            foreach (var activeModal in _activeModalsStack)
            {
                if (activeModal.Item1.ShouldHideHud) return true;
            }

            return false;
        }

        public bool HasModalInQueue() => _immediateModalsQueue.Count > 0 || _modalsQueue.Count > 0;


        public bool IsShowingModal(ModalsType modalType, bool includingHidingModals = false)
        {
            foreach (var (controller, _) in _activeModalsStack)
            {
                var controllerView = ControllerViewMap.GetSafe(controller.GetType());
                if ((ModalsType)controllerView.ViewType == modalType)
                    return true;
            }

            if (includingHidingModals)
            {
                for (var i = _hidingModalsQueue.Count - 1; i >= 0; i--)
                {
                    var controllerView = ControllerViewMap.GetSafe(_hidingModalsQueue[i].GetType());
                    if ((ModalsType)controllerView.ViewType == modalType)
                        return true;
                }
            }

            return false;
        }

        public bool IsShowingModal(params ModalsType[] modalTypes)
        {
            foreach (var modalType in modalTypes)
            {
                if (IsShowingModal(modalType))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Is showing specific modal.
        /// </summary>
        /// <param name="modalControllerType">Modal controller type.</param>
        /// <param name="includingQueue">Count modals in queue.</param>
        public bool IsShowingModal(Type modalControllerType, bool includingQueue = true)
        {
            for (int i = 0; i < _activeModalsStack.Count; i++)
            {
                if (_activeModalsStack[i].Item1.GetType() == modalControllerType)
                {
                    return true;
                }
            }

            if (includingQueue)
            {
                for (int i = 0; i < _modalsQueue.Count; i++)
                {
                    if (_modalsQueue[i].ModalController.GetType() == modalControllerType)
                    {
                        return true;
                    }
                }

                for (int i = 0; i < _immediateModalsQueue.Count; i++)
                {
                    if (_immediateModalsQueue[i].ModalController.GetType() == modalControllerType)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public void PrioritizeNextModalWithTag(string tag)
        {
            BDebug.Log(LogCat.Modals, $"Prioritizing tag {tag}");
            LastShowedModalTag = tag;
        }

        protected override void PostInstantiatedView(IController ctrl, GameObject rootGo)
        {
            var rectTransform = rootGo.transform as RectTransform;
            if (rectTransform != null)
            {
                rectTransform.offsetMax = Vector2.zero;
                rectTransform.offsetMin = Vector2.zero;
            }
        }

        public string LogQueue()
        {
            var modalsListString = new StringBuilder();

            AppendQueueEntries("_modalsQueue", _modalsQueue, modalsListString);
            modalsListString.Append(", ");
            AppendQueueEntries("_immediateModalsQueue", _immediateModalsQueue, modalsListString);

            return modalsListString.ToString();
        }

        private static void AppendQueueEntries(string queueName, IEnumerable<ModalsQueueEntry> queue, StringBuilder builder)
        {
            builder.Append(queueName).Append(" = [");

            foreach (var entry in queue)
            {
                BDebug.Log(LogCat.Modals, $"{entry.ModalController}, tag: {entry.Tag}");
                builder.Append(entry.ModalController).Append(" - ").Append(entry.Tag).Append(",");
            }

            if (builder[^1] == ',')
            {
                builder.Length--;
            }

            builder.Append("]");
        }

        public void SetLoadingIndicatorPrefab(GameObject loadingIndicatorPrefab)
        {
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{loadingIndicatorPrefab.name}]");
            _loadingIndicator = Instantiate(loadingIndicatorPrefab, _lockerLoadingRoot, false);
            UnityEngine.Profiling.Profiler.EndSample();
        }

        public void Restart()
        {
            foreach (var aTuple in _activeModalsStack)
            {
                aTuple.Item1.Destroy();
                aTuple.Item2.DisposeResources();
                aTuple.Item2.DisposeContext();
            }

            _activeModalsStack.Clear();
            RemoveModals(_ => true, true);
            _tutorialPlaybackController = null;
        }
    }
}