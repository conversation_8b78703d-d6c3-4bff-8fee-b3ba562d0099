using System.Collections.Generic;
using System.Collections.ObjectModel;
using Debug = UnityEngine.Debug;

namespace BBB.Core.Wallet
{
    public enum WalletType
    {
        CurrencyWallet,
        ResourcesWallet,
        LocalResourcesWallet
    }

    public class WalletsAggregator : IWalletsAggregator
    {
        private readonly IReadOnlyDictionary<WalletType, IWallet> _walletByType;
        private readonly IReadOnlyDictionary<string, WalletType> _walletTypeByCurrency;
        private readonly List<IWallet> _allWallets;

        public WalletsAggregator(IWalletPredefiner walletPredefiner)
        {
            _walletByType = walletPredefiner.WalletByType;

            _allWallets = new List<IWallet>();
            foreach (var wallet in _walletByType.Values)
            {
                _allWallets.Add(wallet);
            }

            _walletTypeByCurrency = walletPredefiner.WalletTypeByCurrency;
        }

        public List<IWallet> GetAllWallets()
        {
            return _allWallets;
        }

        public bool HashWallet(WalletType walletType)
        {
            return _walletByType.ContainsKey(walletType);
        }

        public IWallet GetWalletByType(WalletType walletType)
        {
            return _walletByType[walletType];
        }

        public IWallet GetWalletByCurrency(string currencyName)
        {
            if (!_walletTypeByCurrency.ContainsKey(currencyName))
            {
                // Now currency names can be not for wallet, but also for inventory. -VK
                //BDebug.LogErrorFormat(LogCat.Wallet, "Invalid currency uid {0}!", currencyName);
                return null;
            }

            var walletType = _walletTypeByCurrency[currencyName];
            return _walletByType[walletType];
        }

        public WalletType GetWalletTypeByCurrency(string currencyName)
        {
            return _walletTypeByCurrency[currencyName];
        }
    }
}