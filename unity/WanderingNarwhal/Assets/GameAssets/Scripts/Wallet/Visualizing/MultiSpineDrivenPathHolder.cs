using System.Collections.Generic;
using BBB;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace GameAssets.Scripts.Wallet.Visualizing
{
    public class MultiSpineDrivenPathHolder : BbbMonoBehaviour, ITransformDrivenPathHolder
    {
        [SerializeField] private string _pathUid;
        [SerializeField] private Transform _poolRoot;
        [SerializeField] private SpineDrivenPathHolder _spineDrivenPathHolderPrefab;
        [SerializeField] private Transform _sharedEndTarget;

        private readonly List<SpineDrivenPathHolder> _pool = new();
        private SpineDrivenPathHolder _lastSpineDrivenPathHolder;

        public string PathUid => _pathUid;

        private void Awake()
        {
            TransformDrivenPathsRegister.Add(this);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            TransformDrivenPathsRegister.Remove(this);

            foreach (var spineDrivenPathHolder in _pool)
            {
                spineDrivenPathHolder.Finished -= FinishedHandler;
            }

            _pool.Clear();
        }

        private SpineDrivenPathHolder GetSpineDrivenPathHolder()
        {
            if (_pool.IsNullOrEmpty())
            {
                var spineDrivenPathHolder = Instantiate(_spineDrivenPathHolderPrefab, _poolRoot);
                spineDrivenPathHolder.Init();
                _pool.Add(spineDrivenPathHolder);

                spineDrivenPathHolder.Finished += FinishedHandler;
            }

            var result = _pool[0];
            _pool.RemoveAt(0);

            return result;
        }

        private void FinishedHandler(SpineDrivenPathHolder spineDrivenPathHolder)
        {
            _pool.Add(spineDrivenPathHolder);
        }

        public FollowingTransform GetFollowingTransformFromStartPosition(Vector3? startPosition = null)
        {
            _lastSpineDrivenPathHolder = GetSpineDrivenPathHolder();
            _lastSpineDrivenPathHolder.SetPositions(startPosition ?? transform.position, _sharedEndTarget.position);

            // Forced solution in case if as start transform of spine curve the root bone is selected. then refresh of inner hierarchy happens at late update by spine and end position
            // It is not required if start transform is actual spine component is selected, but it may not be always desired as curves can adjust based on start position,
            // but keeps static when you translate the spine component itself
            LateSpineRefresh(_lastSpineDrivenPathHolder);
            return _lastSpineDrivenPathHolder.GetFollowingTransformFromStartPosition();
        }

        public Vector3[] GetWaypointPositions()
        {
            return _lastSpineDrivenPathHolder.GetWaypointPositions();
        }

        private async void LateSpineRefresh(SpineDrivenPathHolder spineDrivenPathHolder)
        {
            await UniTask.NextFrame();
            spineDrivenPathHolder.SetEndPosition(_sharedEndTarget.position);
        }
    }
}