using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BebopBee;
using BebopBee.Core;
using DG.Tweening;
using GameAssets.Scripts.Wallet.Visualizing;
using BBB.MMVibrations.Plugins;
using DG.Tweening.Core;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.Wallet
{
    public class AnimateCurrencyCommand : CommandBase
    {
        // to get rid of million local vars to prevent losing data on releasing pool instance
        private class DelegateDataInstance
        {
            public int CountFinished = 0;

            public string CurrencyUid;
            public int OriginalCount;

            public Action<string, int, float> CurrencyVisualizationStartedCallback; //uid, original count, time before first hit
            public Action<string, int, int, Vector3> CurrencyCoinHitCallback; //uid, index, total count, position

            public GameObject Instance;
            public bool InstanceBased;

            public Tweener PauseTimeTweener;
        }

        private const float FullCircleDegrees = 359.99f;

        private string _currencyUid;
        private ICurrencyObjectProvider _objProvider;
        private CurrencyFlightSettings _selectedSettings;
        private int _originalCount;
        private int _renderCount;
        private Action<string, int, float> _currencyVisualizationStartedCallback; //uid, original count, time before first hit
        private Action<string> _currencyVisualizationEndedCallback; //uid
        private Action<string, int, int, Vector3> _currencyCoinHitCallback; //uid, index, total count, position

        private Action _forceCompleteAction;
        private bool _forceCompleted;
        private OverlayEffectRenderer _effectRenderer;

        private InstanceBasedCurrencyFlightSettings _instanceBasedCurrencyFlightSettings;
        private GameObject _instance;
        private bool _instanceBased;
        private FollowingTransform _followingTransform;
        private GameObject _impactFxInstance;
        private GameObject _appearingFxInstance;
        private readonly List<Tweener> _tweenersToKill = new();
        private string _pathUid;
        private float _durationMultiplier;
        private AnimationCurve _translationTimeCurve = AnimationCurve.Linear(0, 0, 1, 1);
        private bool _replacedInstanceBased;
        private float _groupDelay;

        //becomes true after last coin landed, after the command status changed to Success
        public bool Completed { get; private set; }
        public float GroupDelay => _groupDelay;

        public void Init(string currencyUid, ICurrencyObjectProvider currencyObjProvider,
            CurrencyFlightSettings selectedSettings, string pathUid,
            int count, Action<string, int, float> currencyVisualizationStartedCallback,
            Action<string> currencyVisualizationEndedCallback,
            Action<string, int, int, Vector3> curCoinHitCallback,
            OverlayEffectRenderer effectRenderer, float groupDelay)
        {
            ValidateState();

            _currencyUid = currencyUid;
            _objProvider = currencyObjProvider;
            _selectedSettings = selectedSettings;
            _pathUid = pathUid;
            _originalCount = count;
            _renderCount = selectedSettings.ClampCount(count);
            _currencyVisualizationStartedCallback = currencyVisualizationStartedCallback;
            _currencyVisualizationEndedCallback = currencyVisualizationEndedCallback;
            _currencyCoinHitCallback = curCoinHitCallback;
            _effectRenderer = effectRenderer;
            _groupDelay = groupDelay;
        }

        public void SetInstanceBased(GameObject instance, InstanceBasedCurrencyFlightSettings instanceBasedCurrencyFlightSettings)
        {
            _instanceBasedCurrencyFlightSettings = instanceBasedCurrencyFlightSettings;
            _instanceBased = true;
            _instance = instance;
        }

        private void ValidateState()
        {
            // Not using assert to keep our log system
            if (IsTimeConstraint)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of IsTimeConstraint");
            }

            if (_currencyUid != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _currencyUid");
            }

            if (_objProvider != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _objProvider");
            }

            if (_selectedSettings != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _selectedSettings");
            }

            if (!_pathUid.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_pathUid} incorrect state of _pathUid");
            }

            if (_originalCount != 0)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _originalCount");
            }

            if (_renderCount != 0)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _renderCount");
            }

            if (_currencyVisualizationStartedCallback != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _currencyVisualizationStartedCallback");
            }

            if (_currencyVisualizationEndedCallback != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _currencyVisualizationEndedCallback");
            }

            if (_currencyCoinHitCallback != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _currencyCoinHitCallback");
            }

            if (Completed)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of Completed");
            }

            if (_forceCompleteAction != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _forceCompleteAction");
            }

            if (_forceCompleted)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _forceCompleted");
            }

            if (_instanceBasedCurrencyFlightSettings != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _instanceBasedCurrencyFlightSettings");
            }

            if (_instanceBased)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _instanceBased");
            }

            if (_instance != null)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _instance");
            }

            if (!_tweenersToKill.IsNullOrEmpty())
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _tweenersToKill");
            }

            if (_groupDelay > 0f)
            {
                BDebug.LogError(LogCat.Wallet, $"AnimateCurrencyCommand {_currencyUid} incorrect state of _groupDelay");
            }

            Reset();
        }

        public void SetupDurationMultiplier(float durationMultiplier, AnimationCurve translationTimeCurve)
        {
            _durationMultiplier = durationMultiplier;
            _translationTimeCurve = translationTimeCurve;
        }

        public override void Reset()
        {
            base.Reset();

            // We killing tweeners with completion, some of them relay on properties which reset below so we kill them firts
            foreach (var tweener in _tweenersToKill)
            {
                if (tweener.IsActive())
                {
                    tweener.Kill(true);
                }
            }

            _tweenersToKill.Clear();

            IsTimeConstraint = false;
            _currencyUid = null;
            _objProvider = null;
            _selectedSettings = null;
            _pathUid = null;
            _originalCount = 0;
            _renderCount = 0;
            _currencyVisualizationStartedCallback = null;
            _currencyVisualizationEndedCallback = null;
            _currencyCoinHitCallback = null;
            Completed = false;
            _forceCompleteAction = null;
            _forceCompleted = false;

            _instanceBasedCurrencyFlightSettings = null;
            _instanceBased = false;
            _replacedInstanceBased = false;
            _instance = null;
            _groupDelay = 0f;
        }

        private void StartRunning()
        {
            BDebug.Log(LogCat.Wallet, $"StartRunning: {_currencyUid}, {_pathUid}, {Time.time}, {Time.frameCount}");
            _forceCompleteAction = null;
            _forceCompleted = false;

            Vector3? startPosition = null;
            if (_instanceBased && _instance != null)
            {
                startPosition = _instance.transform.position;
            }

            _followingTransform = TransformDrivenPathsRegister.GetFollowingTransform(_pathUid, startPosition);
            var path = TransformDrivenPathsRegister.GetPositions(_pathUid);

            var blocked = TransformDrivenPathsRegister.IsBlocked(_pathUid);
            if ((_followingTransform == null && (path == null || path.Length == 0)) || blocked)
            {
                if (blocked)
                {
                    BDebug.LogWarning(LogCat.Wallet, $"path is blocked for {_currencyUid}");
                }
                else if (path == null || path.Length == 0)
                {
                    BDebug.LogError(LogCat.Wallet, $"path is null or empty for {_currencyUid}");
                }

                CurrentStatus = CommandStatus.Failure;
                Complete();
                return;
            }

            if (_instanceBased)
            {
                var originalStartPosition = path[0];
                var targetStartPosition = _instance.transform.position;
                var delta = targetStartPosition - originalStartPosition;

                var zeroOffsetIndex = path.Length - 1;
                for (var j = 0; j < zeroOffsetIndex; j++)
                {
                    path[j] += delta * ((float)(zeroOffsetIndex - j) / zeroOffsetIndex);
                }
            }

            var data = new DelegateDataInstance
            {
                CountFinished = 0,
                CurrencyUid = _currencyUid,
                OriginalCount = _originalCount,
                CurrencyCoinHitCallback = _currencyCoinHitCallback,
                CurrencyVisualizationStartedCallback = _currencyVisualizationStartedCallback,
                Instance = _instance
            };

            CurrentStatus = CommandStatus.Running;

            var pauseTime = _selectedSettings.PauseTime;

            if (_selectedSettings.HideCurrencyIcon)
            {
                if (pauseTime <= float.Epsilon)
                {
                    CurrentStatus = CommandStatus.Success;
                }
                else
                {
                    data.PauseTimeTweener = Rx.Invoke(pauseTime, _ => { CurrentStatus = CommandStatus.Success; });
                }

                var duration = _selectedSettings.GetRandomDuration() * _durationMultiplier;
                data.CurrencyVisualizationStartedCallback?.Invoke(data.CurrencyUid, data.OriginalCount, duration);
                _tweenersToKill.Add(Rx.Invoke(duration, _ => { CompleteHiddenCurrencyIcon(); }));

                _forceCompleteAction -= CompleteHiddenCurrencyIcon;
                _forceCompleteAction += CompleteHiddenCurrencyIcon;

                return;

                void CompleteHiddenCurrencyIcon()
                {
                    _forceCompleteAction -= CompleteHiddenCurrencyIcon;

                    try
                    {
                        data.PauseTimeTweener?.Kill(true);

                        var finalPosition = path is { Length: > 0 }
                            ? path[^1]
                            : Vector3.zero;
                        data.CurrencyCoinHitCallback?.Invoke(_pathUid, data.CountFinished, 0, finalPosition);
                    }
                    catch (Exception e)
                    {
                        //printing additional data of anything that possibly can be broken
                        var dataMessage = $"Hidden currency " +
                                          $"data = {data}, " +
                                          $"_selectedSettings = {_selectedSettings}";
                        var message = $"Exception: {e.Message}, Data: {dataMessage}, {e.StackTrace}";
                        BDebug.LogError(LogCat.Wallet, message);
                    }

                    if (_selectedSettings != null && !_forceCompleted)
                    {
                        _tweenersToKill.Add(Rx.Invoke(_selectedSettings.CompletionDelay, _ => { Complete(); }));
                    }
                    else
                    {
                        Complete();
                    }
                }
            }

            if (_instanceBased)
            {
                _replacedInstanceBased = TryToReplaceInstance(data);
                if (_replacedInstanceBased)
                {
                    pauseTime = _selectedSettings.ReplacedPauseTime;
                }
            }

            if (pauseTime <= float.Epsilon)
            {
                CurrentStatus = CommandStatus.Success;
            }
            else
            {
                data.PauseTimeTweener = Rx.Invoke(pauseTime, _ => { CurrentStatus = CommandStatus.Success; });
            }

            if (_instanceBased && !_replacedInstanceBased)
            {
                RenderWithInstance(data, path);
            }
            else
            {
                for (var i = 0; i < _renderCount; i++)
                {
                    var result = RenderWithCurrencyObject(data, i, path);
                    if (!result)
                        break;
                }
            }
        }

        private bool TryToReplaceInstance(DelegateDataInstance data)
        {
            if (_instanceBasedCurrencyFlightSettings.CurrenciesToReplaceInstance.Contains(_currencyUid))
            {
                var scaleSequence = DOTween.Sequence();

                var currencyTransform = data.Instance.transform;
                var targetScale = currencyTransform.localScale;
                var scaleTween = GetScaleTween(_instanceBasedCurrencyFlightSettings.NotReplacedInstanceHidingDuration, currencyTransform, targetScale,
                    _instanceBasedCurrencyFlightSettings.NotReplacedInstanceHidingScaleCurve);

                scaleSequence.Append(scaleTween);

                scaleSequence.AppendCallback(() =>
                {
                    if (data.Instance != null)
                    {
                        Object.Destroy(data.Instance);
                    }
                });

                return true;
            }

            return false;
        }

        private GameObject GetCurrencyObject(out Animator animator, out SpriteArrayAnimator spriteArrayAnimator)
        {
            animator = null;
            spriteArrayAnimator = null;
            var currencyObject = _objProvider.GetCurrencyObject(_currencyUid);

            if (currencyObject == null)
                return null;

            currencyObject.transform.SetAsLastSibling();
            currencyObject.gameObject.name = $"{_currencyUid}, {_renderCount}, {_originalCount}";

            spriteArrayAnimator = currencyObject.GetComponent<SpriteArrayAnimator>();
            spriteArrayAnimator.StartAnimation();

            animator = currencyObject.GetComponent<Animator>();
            var animTriggers = _selectedSettings.AnimTriggers;

            if (animator != null && animTriggers != null)
            {
                foreach (var namedNumber in animTriggers)
                {
                    var triggerName = namedNumber.Name;
                    var waitTime = namedNumber.Number;

                    var lambdaAnimator = animator;
                    _tweenersToKill.Add(Rx.Invoke(waitTime, _ => { lambdaAnimator.SetTrigger(triggerName); }));
                }
            }

            return currencyObject;
        }

        private float GetComposedTranslationCurve(float time, float duration, float overshootOrAmplitude, float period)
        {
            var normalizedTime = time / duration;
            var adjustedTime = _translationTimeCurve.Evaluate(normalizedTime);
            return _selectedSettings.TranslationCurve.Evaluate(adjustedTime);
        }

        private bool RenderWithCurrencyObject(DelegateDataInstance data, int i, Vector3[] primaryPath)
        {
            var translationSequence = DOTween.Sequence();
            var scaleSequence = DOTween.Sequence();
            var rotationSequence = DOTween.Sequence();
            var alphaSequence = DOTween.Sequence();
            var embeddedNumberAlphaSequence = DOTween.Sequence();

            var preTranslationDuration = _selectedSettings.RouteSettings.StartFromCenterButFlyToRadius ? _selectedSettings.RouteSettings.FlyToRadiusTranslationDuration : 0f;
            var timeOffset = _selectedSettings.GetStartTimeOffset(i, _renderCount);
            if (timeOffset > 0f)
            {
                translationSequence.AppendInterval(timeOffset);
                scaleSequence.AppendInterval(timeOffset);
                rotationSequence.AppendInterval(timeOffset);
                alphaSequence.AppendInterval(timeOffset);
                embeddedNumberAlphaSequence.AppendInterval(timeOffset);
            }

            var modifiedPath = _selectedSettings.ModifyOffsetPositions(primaryPath, i);
            var duration = _selectedSettings.GetRandomDuration() * _durationMultiplier + preTranslationDuration;

            var currencyObject = GetCurrencyObject(out var animator, out var spriteArrayAnimator);
            if (currencyObject == null)
            {
                CurrentStatus = CommandStatus.Failure;
                Complete();
                return false;
            }

            var genericFlyingCurrencyItem = currencyObject.GetComponent<GenericFlyingCurrencyItem>();

            var currencyTransform = currencyObject.transform;
            var renderCount = _renderCount;
            var effectRenderer = _effectRenderer;
            var delayBeforeShowNumber = _selectedSettings.DelayBeforeShowNumber;
            var shouldShowNumber = _selectedSettings.ShouldShowNumber;
            var shouldShowEmbeddedNumber = _selectedSettings.ShouldShowEmbeddedNumber;

            if (genericFlyingCurrencyItem != null)
            {
                genericFlyingCurrencyItem.SetEmbeddedCurrencyNumber(shouldShowEmbeddedNumber ? data.OriginalCount.ToString() : string.Empty);
                if (shouldShowEmbeddedNumber)
                {
                    var embeddedNumberAlphaTween = DOTween.To(() => 0f,
                            value => { genericFlyingCurrencyItem.SetEmbeddedCurrencyNumberAlpha(_selectedSettings.EmbeddedNumberAlpha.Evaluate(value)); }, 1f, duration)
                        .SetEase(Ease.Linear);
                    embeddedNumberAlphaSequence.Append(embeddedNumberAlphaTween);
                }
            }

            currencyTransform.position = modifiedPath[0];
            var impactFxPrefab = _selectedSettings.ImpactFxPrefab;
            var targetScale = currencyTransform.localScale;
            var targetAngles = currencyTransform.localRotation.eulerAngles;
            var startAngleZ = _selectedSettings.RotationCurveZ.Evaluate(0f);
            var startAngles = new Vector3(targetAngles.x, targetAngles.y, startAngleZ);
            currencyTransform.localRotation = Quaternion.Euler(startAngles);
            var startScale = _selectedSettings.ScaleCurve.Evaluate(0f);
            currencyTransform.localScale = new Vector3(startScale, startScale, startScale);

            var targetAlpha = spriteArrayAnimator.Alpha;
            var startAlpha = _selectedSettings.AlphaCurve.Evaluate(0f);
            spriteArrayAnimator.SetAlpha(startAlpha);

            Tween translationTween;
            Vector3 position;

            if (_followingTransform != null)
            {
                translationTween = DOTween.To(() => 0f, _ => { currencyTransform.position = _followingTransform.Transform.position; }, 1f, duration)
                    .OnStart(() =>
                    {
                        OnStart();
                        _followingTransform.Start(duration);
                    })
                    .OnComplete(() =>
                    {
                        OnComplete();
                        _followingTransform.Finish();
                    });

                position = _followingTransform.Transform.position;
            }
            else
            {
                var translationDuration = duration;
                if (_selectedSettings.RouteSettings.StartFromCenterButFlyToRadius)
                {
                    var startPoint = primaryPath[0];
                    var endPoint = modifiedPath[0];

                    var preTranslationTween = DOTween.To(() => 0f, value => { currencyTransform.position = Vector3.LerpUnclamped(startPoint, endPoint, value); },
                            1f, _selectedSettings.RouteSettings.FlyToRadiusTranslationDuration)
                        .SetEase(_selectedSettings.RouteSettings.FlyToRadiusTranslationCurve);

                    translationSequence.Append(preTranslationTween);
                    translationDuration -= preTranslationDuration;
                }

                if (_selectedSettings.UseUnclampedTween)
                {
                    var startPoint = modifiedPath[0];
                    var endPoint = modifiedPath[^1];

                    translationTween = DOTween.To(() => 0f, value => { currencyTransform.position = Vector3.LerpUnclamped(startPoint, endPoint, value); }, 1f, translationDuration)
                        .SetEase(GetComposedTranslationCurve)
                        .OnStart(OnStart)
                        .OnComplete(OnComplete);
                }
                else
                {
                    translationTween = currencyTransform.DOPath(modifiedPath, translationDuration,
                            PathType.CatmullRom,
                            PathMode.Ignore, resolution: 10, gizmoColor: Color.red)
                        .SetEase(GetComposedTranslationCurve)
                        .OnStart(OnStart)
                        .OnComplete(OnComplete);
                }

                position = modifiedPath[0];
            }

            if (i == 0)
            {
                TriggerAppearingFX(_selectedSettings.AppearingFxPrefab, position);
                translationSequence.AppendCallback(TranslationStartedHandler);
            }

            translationSequence.Append(translationTween);

            var scaleTween = GetScaleTween(duration, currencyTransform, targetScale, _selectedSettings.ScaleCurve);
            scaleSequence.Append(scaleTween);

            var rotationTween = DOTween.To(() => currencyTransform.localRotation.eulerAngles.z,
                value => currencyTransform.localRotation = Quaternion.Euler(targetAngles.x, targetAngles.y, value),
                FullCircleDegrees,
                duration).SetEase(_selectedSettings.RotationCurveZ);

            rotationSequence.Append(rotationTween);

            var alphaTween = DOTween.To(() => spriteArrayAnimator.Alpha,
                    value => spriteArrayAnimator.SetAlpha(value), targetAlpha, duration)
                .SetEase(_selectedSettings.AlphaCurve);

            alphaSequence.Append(alphaTween);

            _forceCompleteAction -= OnComplete;
            _forceCompleteAction += OnComplete;
            return true;

            void OnComplete()
            {
                _forceCompleteAction -= OnComplete;
                try
                {
                    if (spriteArrayAnimator != null)
                    {
                        spriteArrayAnimator.StopAnimation();
                    }

                    if (animator != null)
                    {
                        animator.Rebind();
                    }

                    translationSequence.Kill(true);
                    scaleSequence.Kill(true);
                    rotationSequence.Kill(true);
                    alphaSequence.Kill(true);
                    embeddedNumberAlphaSequence.Kill(true);

                    var finalPosition = modifiedPath is { Length: > 0 }
                        ? modifiedPath[^1]
                        : Vector3.zero;
                    data.CurrencyCoinHitCallback?.Invoke(_pathUid, data.CountFinished, renderCount, finalPosition);
                }
                catch (Exception e)
                {
                    //printing additional data of anything that possibly can be broken
                    var dataMessage = $"translationSequence = {translationSequence}, " +
                                      $"scaleSequence = {scaleSequence}, " +
                                      $"rotationSequence = {rotationSequence}," +
                                      $"alphaSequence = {alphaSequence}, " +
                                      $"data = {data}, " +
                                      $"_selectedSettings = {_selectedSettings}";
                    var message = $"Exception: {e.Message}, Data: {dataMessage}, {e.StackTrace}";
                    BDebug.LogError(LogCat.Wallet, message);
                }
                finally
                {
                    currencyObject.Release();
                }

                // moving out to guarantee that completion is getting tracked correctly
                data.CountFinished++;
                if (data.CountFinished >= renderCount)
                {
                    data.PauseTimeTweener?.Kill(true);

                    if (_selectedSettings != null && !_forceCompleted)
                    {
                        _tweenersToKill.Add(Rx.Invoke(_selectedSettings.CompletionDelay, _ => { Complete(); }));
                    }
                    else
                    {
                        Complete();
                    }
                }
            }

            void TranslationStartedHandler()
            {
                try
                {
                    if (effectRenderer != null && renderCount > 1 && shouldShowNumber)
                    {
                        var firstPosition = (modifiedPath.Length > 0) ? modifiedPath[0] : Vector3.zero;
                        var parameters = new IncrementNumberTextParams
                        {
                            CurrencyUid = data.CurrencyUid,
                            Number = data.OriginalCount
                        };

                        effectRenderer.SpawnDelayedEffect(this, delayBeforeShowNumber, OverlayEffect.IncrementNumberText, firstPosition, parameters);
                    }

                    data.CurrencyVisualizationStartedCallback?.Invoke(data.CurrencyUid, data.OriginalCount, duration);
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.Wallet, $"Exception: {e.Message} {e.StackTrace}");
                }
            }

            void OnStart()
            {
                TriggerImpactFx(impactFxPrefab);
            }
        }

        private void TriggerAppearingFX(GameObject appearingFxPrefab, Vector3 position)
        {
            if (appearingFxPrefab == null)
                return;

            _appearingFxInstance = _objProvider.GetImpactFx(appearingFxPrefab);
            if (_appearingFxInstance != null)
            {
                _appearingFxInstance.SetActive(false);
                _tweenersToKill.Add(Rx.Invoke(_selectedSettings.AppearingFxDelay, _ =>
                {
                    if (_appearingFxInstance == null)
                        return;

                    if (!_forceCompleted)
                    {
                        _appearingFxInstance.transform.position = position;
                        _appearingFxInstance.SetActive(true);
                    }
                }));

                _tweenersToKill.Add(Rx.Invoke(_selectedSettings.AppearingFxDelay + _selectedSettings.AppearingFxLifetime, _ =>
                {
                    if (_appearingFxInstance == null)
                        return;

                    _appearingFxInstance.SetActive(false);
                    _appearingFxInstance.Release();
                }));
            }
        }

        private void TriggerImpactFx(GameObject impactFxPrefab)
        {
            if (impactFxPrefab == null)
                return;

            _impactFxInstance = _objProvider.GetImpactFx(impactFxPrefab);
            if (_impactFxInstance != null)
            {
                _impactFxInstance.SetActive(false);
                _tweenersToKill.Add(Rx.Invoke(_selectedSettings.ImpactFxDelay, _ =>
                {
                    if (_impactFxInstance == null)
                        return;

                    if (!_forceCompleted)
                    {
                        var path = TransformDrivenPathsRegister.GetPositions(_pathUid);
                        _impactFxInstance.transform.position = path[^1];
                        _impactFxInstance.SetActive(true);
                    }
                }));

                _tweenersToKill.Add(Rx.Invoke(_selectedSettings.ImpactFxDelay + _selectedSettings.ImpactFxLifetime, _ =>
                {
                    if (_impactFxInstance == null)
                        return;

                    _impactFxInstance.SetActive(false);
                    _impactFxInstance.Release();
                }));
            }
        }

        private void CancelImpactAnimation()
        {
            if (_impactFxInstance == null)
                return;

            _impactFxInstance.SetActive(false);
            _impactFxInstance.Release();
            _impactFxInstance = null;
        }

        private void Complete()
        {
            if (Completed)
                return;

            Completed = true;
            _currencyVisualizationEndedCallback.SafeInvoke(_currencyUid);
        }

        private void RenderWithInstance(DelegateDataInstance data, Vector3[] path)
        {
            var translationSequence = DOTween.Sequence();
            var scaleSequence = DOTween.Sequence();
            var alphaSequence = DOTween.Sequence();
            var rotationSequence = DOTween.Sequence();
            var curCoinHitCallback = data.CurrencyCoinHitCallback;

            var duration = _selectedSettings.GetRandomDuration() * _durationMultiplier;
            var currencyTransform = data.Instance.transform;
            var targetScale = currencyTransform.localScale;

            Tween translationTween;

            if (_followingTransform != null)
            {
                translationTween = DOTween.To(() => 0f, _ => { currencyTransform.position = _followingTransform.Transform.position; }, 1f, duration)
                    .OnStart(() => { _followingTransform.Start(duration); })
                    .OnComplete(() =>
                    {
                        CompletedHandler();
                        _followingTransform.Finish();
                    });
            }
            else
            {
                if (_selectedSettings.UseUnclampedTween)
                {
                    var startPoint = path[0];
                    var endPoint = path[^1];

                    translationTween = DOTween.To(() => 0f, value => { currencyTransform.position = Vector3.LerpUnclamped(startPoint, endPoint, value); }, 1f, duration)
                        .SetEase(GetComposedTranslationCurve)
                        .OnComplete(CompletedHandler);
                }
                else
                {
                    translationTween = currencyTransform.DOPath(path, duration,
                            PathType.CatmullRom,
                            PathMode.Ignore, resolution: 10, gizmoColor: Color.red)
                        .SetEase(GetComposedTranslationCurve)
                        .OnComplete(CompletedHandler);
                }
            }

            translationSequence.AppendCallback(data.Instance.transform.SetAsLastSibling);
            translationSequence.Append(translationTween);

            TriggerAppearingFX(_selectedSettings.AppearingFxPrefab, currencyTransform.position);
            TriggerImpactFx(_selectedSettings.ImpactFxPrefab);
            var scaleTween = GetScaleTween(duration, currencyTransform, targetScale, _instanceBasedCurrencyFlightSettings.ScaleCurve);
            scaleSequence.Append(scaleTween);

            var targetAngles = currencyTransform.localRotation.eulerAngles;
            var startAngleZ = _selectedSettings.RotationCurveZ.Evaluate(0f);
            var startAngles = new Vector3(targetAngles.x, targetAngles.y, startAngleZ);
            currencyTransform.localRotation = Quaternion.Euler(startAngles);

            var rotationTween = DOTween.To(() => currencyTransform.localRotation.eulerAngles.z,
                value => currencyTransform.localRotation = Quaternion.Euler(targetAngles.x, targetAngles.y, value),
                FullCircleDegrees,
                duration).SetEase(_selectedSettings.RotationCurveZ);

            rotationSequence.Append(rotationTween);

            data.CurrencyVisualizationStartedCallback?.Invoke(data.CurrencyUid, data.OriginalCount, duration);
            _forceCompleteAction -= CompletedHandler;
            _forceCompleteAction += CompletedHandler;
            return;

            void CompletedHandler()
            {
                _forceCompleteAction -= CompletedHandler;
                try
                {
                    if (data.Instance != null)
                    {
                        Object.Destroy(data.Instance);
                    }

                    translationSequence.Kill(true);
                    scaleSequence.Kill(true);
                    rotationSequence.Kill(true);
                    alphaSequence.Kill(true);
                    data.PauseTimeTweener?.Kill(true);

                    var finalPosition = path[^1];
                    data.CountFinished++;
                    curCoinHitCallback?.Invoke(_pathUid, data.CountFinished, 1, finalPosition);
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.Wallet, $"Exception: {e.Message} {e.StackTrace}");
                }
                finally
                {
                    if (_selectedSettings != null && !_forceCompleted)
                    {
                        _tweenersToKill.Add(Rx.Invoke(_selectedSettings.CompletionDelay, _ => { Complete(); }));
                    }
                    else
                    {
                        Complete();
                    }
                }
            }
        }

        public void ForceComplete()
        {
            BDebug.Log(LogCat.Wallet, $"ForceComplete: {_currencyUid}, {_pathUid}");
            _forceCompleted = true;
            _forceCompleteAction?.Invoke();
            CancelImpactAnimation();
            _appearingFxInstance = null;
            CurrentStatus = CommandStatus.Success;
        }

        public override void Execute(IContext context = null)
        {
            if (CurrentStatus == CommandStatus.Pending)
                StartRunning();

            base.Execute(context);
        }

        public float GetDelayBeforeNextCurrency()
        {
            return _replacedInstanceBased ? 0f : _selectedSettings.DelayBeforeNextCurrency;
        }

        // DOScale doesn't work well because it requires starting state of the transform to be 0, which leads to sometimes for a frame transform to disappear which is not intended
        private static Tween GetScaleTween(float duration, Transform targetTransform, Vector3 targetScale, AnimationCurve easeCurve)
        {
            return GetNormalizedTween(duration, value => { targetTransform.localScale = targetScale * easeCurve.Evaluate(value); });
        }

        private static Tween GetNormalizedTween(float duration, DOSetter<float> callback)
        {
            return DOTween.To(() => 0f, callback, 1f, duration);
        }
    }
}