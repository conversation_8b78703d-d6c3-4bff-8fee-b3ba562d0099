using BBB;
using BBB.Wallet;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.MMVibrations;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Core.CustomAnimators;
using BBB.MMVibrations.Plugins;
using BBB.UI.Core;
using UnityEngine;

namespace GameAssets.Scripts.Wallet.Visualizing
{
    public class CurrencyImpactReceiver : ContextedUiBehaviour
    {
        [SerializeField] private ImpactAnimator _animator;
        [SerializeField] private string _pathUid;
        [SerializeField] private string _soundId = GenericSoundIds.CurrencyFlyEnd;
        [SerializeField] private OverlayEffect _effectToSpawn = OverlayEffect.CurrencyHit;
        [SerializeField] private ImpactPreset _hapticType = ImpactPreset.LightImpact;

        private ICurrencyImpactController _impactController;
        private OverlayEffectRenderer _effectRenderer;
        private IVibrationsWrapper _vibrationsWrapper;

        public string PathUid => _pathUid;

        protected override void InitWithContextInternal(IContext context)
        {
            _impactController = context.Resolve<IUIWalletManager>().ImpactController;
            _effectRenderer = context.Resolve<OverlayEffectRenderer>();
            _vibrationsWrapper = context.Resolve<IVibrationsWrapper>();
            _impactController.AddReceiver(this);
        }

        private void Start()
        {
            LazyInit();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
        }

        public void Receive(Vector3 position)
        {
            LazyInit();

            if (_animator != null)
                _animator.Run();

            AudioProxy.PlaySound(_soundId);

            BDebug.Log(LogCat.Vibration, $"Playing haptic feedback {_hapticType} on currency impact: {_pathUid}");
            _vibrationsWrapper.PlayHaptic(_hapticType);

            if (_effectToSpawn != OverlayEffect.None)
            {
                _effectRenderer.SpawnEffect(_effectToSpawn, position);
            }
        }

        public void ResetState()
        {
            _effectRenderer?.ResetState();
            Unsubscribe();
        }

        private void Unsubscribe()
        {
            _impactController?.RemoveReceiver(this);
        }
    }
}