using System;
using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Wallet.Visualizing
{
    public class OverlayEffectController : BbbMonoBehaviour, IPoolItem
    {
        private PoolItem _poolItem;
        private float _timer = -1f;

        private RectTransform _selfTransform;
        private Action<OverlayEffectController> _releaseCallback;
        private bool _watingForForceRelease;

        private RectTransform SelfTransform
        {
            get
            {
                if (_selfTransform == null)
                {
                    _selfTransform = GetComponent<RectTransform>();
                }

                return _selfTransform;
            }
        }

        public void Launch(float releaseTime, Vector3 position, Action<OverlayEffectController> releaseCallback)
        {
            _watingForForceRelease = false;
            SetReleaseTime(releaseTime);
            SelfTransform.position = position;
            _releaseCallback = releaseCallback;
        }

        public void ForceRelease()
        {
            _watingForForceRelease = true;
        }

        private void SetReleaseTime(float releaseTime)
        {
            if (_poolItem == null)
            {
                _poolItem = GetComponent<PoolItem>();
            }

            if (_poolItem == null)
            {
                Debug.LogError("OverlayEffectController can't find pool item component on attached object", gameObject);
                _timer = 0;
                enabled = false;
                return;
            }

            _timer = releaseTime;
        }

        private void Update()
        {
            if (_poolItem == null)
            {
                // This may happen if Launch was never called for this instance.
                // Usually this means that this component was forgotten to be removed from some prefab.
                enabled = false;
                return;
            }

            if (_timer >= 0f || _watingForForceRelease)
            {
                _timer -= Time.deltaTime;

                if (_timer <= 0f || _watingForForceRelease)
                {
                    var pool = _poolItem.Pool;
                    pool.Release(gameObject);
                    _watingForForceRelease = false;
                }
            }
        }

        protected override void OnDisable()
        {
            _timer = 0;
            _watingForForceRelease = false;
            
            if (_poolItem == null)
                return;
            var pool = _poolItem.Pool;
            pool.Release(gameObject);
        }

        public void OnInstantiate()
        {
            gameObject.SetActive(false);    
        }

        public void OnSpawn()
        {
            gameObject.SetActive(true); 
        }

        public void OnRelease()
        {
            gameObject.SetActive(false);
            _releaseCallback.SafeInvoke(this);
            _releaseCallback = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _releaseCallback = null;
        }
    }
}