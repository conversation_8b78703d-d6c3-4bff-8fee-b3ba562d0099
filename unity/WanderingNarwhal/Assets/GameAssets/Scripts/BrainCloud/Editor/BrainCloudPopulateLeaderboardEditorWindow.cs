using UnityEditor;
using UnityEngine;

namespace BBB.BrainCloud.Editor
{
    public class BrainCloudPopulateLeaderboardEditorWindow : BrainCloudBaseEditorWindow
    {
        private int _userCount = 100;
        private string _leaderboardId = "fennecoscars_Fire";
        private int _minScore = 1;
        private int _maxScore = 1000;

        [MenuItem("brainCloud/Custom/Populate Leaderboard")]
        public static void ShowWindow()
        {
            var window = GetWindow<BrainCloudPopulateLeaderboardEditorWindow>("Populate Leaderboard");
            window.InitDev();
        }

        private void OnGUI()
        {
            _userCount = EditorGUILayout.IntField("User Count", _userCount);
            _leaderboardId = EditorGUILayout.TextField("Leaderboard Id", _leaderboardId);
            _minScore = EditorGUILayout.IntField("Min Score", _minScore);
            _maxScore = EditorGUILayout.IntField("Max Score", _maxScore);


            if (GUILayout.But<PERSON>("Submit"))
            {
                OnSubmit();
            }
            DrawProdDevSwitchButton();
        }


        private void OnSubmit()
        {
            BrainCloudWrapper.ScriptService.RunScript("debug/PopulateLeaderboard", "{\"localLeaderboardId\":\"" + _leaderboardId + "\",\"minScore\":" + _minScore + ",\"maxScore\":" + _maxScore + ",\"userCount\":" + _userCount + "}", PopulateLeaderboardCallback);

            void PopulateLeaderboardCallback(string response, object cbObject)
            {
                Debug.Log("Response: " + response);
            }
        }
    }
}
