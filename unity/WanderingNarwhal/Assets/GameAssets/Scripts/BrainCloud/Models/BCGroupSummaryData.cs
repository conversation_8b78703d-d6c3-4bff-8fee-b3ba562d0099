using System;
using Realms;

namespace BBB.BrainCloud
{
    [Serializable]
    public class BCGroupSummaryData : EmbeddedObject
    {
        public string Description { get; set; }
        public int RequiredLevel { get; set; }
        public string Icon { get; set; }
        public string Country { get; set; }
        public int GroupActivity { get; set; }
        
        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || (obj is BCGroupSummaryData groupData &&
                   Description == groupData.Description && RequiredLevel == groupData.RequiredLevel &&
                   Icon == groupData.Icon &&
                   Country == groupData.Country &&
                   GroupActivity == groupData.GroupActivity);
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(Description, RequiredLevel, Icon, Country, GroupActivity);
        }
    }
}