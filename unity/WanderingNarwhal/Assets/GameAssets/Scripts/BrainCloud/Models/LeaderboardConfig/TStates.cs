using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public class TStates
    {
        [JsonProperty("enrolMins")]
        public int EnrolMins { get; set; }

        [JsonProperty("disallowMins")]
        public int DisallowMins { get; set; }

        [JsonProperty("minMins")]
        public int MinMins { get; set; }

        [JsonProperty("compMins")]
        public int CompMins { get; set; }

        [JsonProperty("bufferMins")]
        public int BufferMins { get; set; }

        [JsonProperty("announcementMins")]
        public int AnnouncementMins { get; set; }
    }
}