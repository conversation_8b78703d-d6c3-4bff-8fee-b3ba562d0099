using System;
using System.Collections.Generic;
using UnityEngine.Serialization;

namespace BBB.BrainCloud
{
    [System.Serializable]
    public class BCRaceEventResponse : BCRunScriptResponse<BCUserRaceData>
    {
        
    }
    

    [System.Serializable]
    public class BCUserRaceData : BCBasicResponseData
    {
        public string uid;
        public bool joined;
        public int ownScore;
        public double ownScoreTimestamp;
        public int joinedTimestamp;
        public List<BCOpponentData> opponents;
        public int raceStatus;
        public string raceId;
    }

    [System.Serializable]
    public class BCOpponentData
    {
        public string uid;
        public int score;
        public double scoreTimestamp;
        public string avatarUrl;
        public string name;
        public string country;
    }
}