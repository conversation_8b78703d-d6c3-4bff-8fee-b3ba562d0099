using System;
using BBB.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BBB.BrainCloud
{
    [Serializable]
    public class BCRunScriptResponse : BCRunScriptResponse<BCBasicResponseData> {}

    [Serializable]
    public class BCRunScriptResponse<TData> : BCResponse<BCRunScriptResponseData<TData>> where TData : BCBasicResponseData
    {
        public TData Response => _data.Response;
        public override bool IsSuccess => _data.Success && base.IsSuccess;
        public override int ReasonCode => _data.ReasonCode == 0 ? base.ReasonCode : _data.ReasonCode;
        public override int Status => !base.IsSuccess ? base.Status : _data.Status;
        public override string DebugLog => _data.DebugLog;
        public override string Message => !base.IsSuccess ? base.Message : _data.ErrorMessage;
    }
    
    [Serializable]
    public struct BCRunScriptResponseData<TResponse> where TResponse : BCBasicResponseData
    {
        // We have to serialize to the JToken instead of directly serialize it to the TResponse, because it will be string in case of script error
        [JsonProperty("response")] 
        private JToken _response;

        [JsonProperty("success")] 
        private bool _success;

        [JsonProperty("reasonCode")] 
        private JToken _reasonCode;
        
        [JsonIgnore]
        private TResponse _parsedResponse;

        public TResponse Response
        {
            get
            {
                if (!_success)
                    return null;

                try
                {
                    _parsedResponse ??= _response?.ToObject<TResponse>();
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.General, $"Failed to parse response: {_response}\n{e}");
                }

                return _parsedResponse;
            }
        }
        public bool Success => _success && Response.Status is 200 or 0;
        public int ReasonCode => _success ? Response.ReasonCode : _reasonCode.ToInt();
        public int Status => _success ? (Response.Status == 0 ? 200 : Response.Status) : 400;
        public string DebugLog => Response?.DebugLog;
        public string ErrorMessage => _success ? null : _response.ToString();
    }

    [Serializable]
    public class BCBasicResponseData
    {
        [JsonProperty("status")] 
        public int Status { get; private set; }
        [JsonProperty("reasonCode")] 
        public int ReasonCode { get; private set; }
        [JsonProperty("debugLog")]
        public string DebugLog { get; set; }
    }

    [Serializable]
    public class BCBasicResponseData<TData> : BCBasicResponseData
    {
        [JsonProperty("data")]
        public TData Data { get; private set; }
    }
}