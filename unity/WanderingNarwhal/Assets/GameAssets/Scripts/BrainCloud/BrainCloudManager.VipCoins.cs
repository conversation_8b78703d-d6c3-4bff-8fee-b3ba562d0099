using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    public enum ClaimVipLocationResponseCode
    {
        NoError = 0,
        LocationAlreadyClaimed = 1,
    }
    [Serializable]
    public class BCClaimVipWondersResponse : BCRunScriptResponse<BCBasicResponseData>
    {
    }

    [Serializable]
    public class BCClaimVipLocationResponse : BCRunScriptResponse<BCBasicResponseData>
    {
    }

    [Serializable]
    public class BCExchangeItemVipResponse : BCRunScriptResponse<BCBasicResponseData>
    {
    }

    [Serializable]
    public struct BCClaimVIpLocationData
    {
        public string locationUid;
    }

    [Serializable]
    public struct BCExchangeItemData
    {
        public string itemUid;
    }

    [Serializable]
    public struct BCClaimVIpWondersData
    {
        public List<string> wonderUids;
    }

    public partial class BrainCloudManager
    {
        public void ClaimVipForWonders(List<string> wonderUids, Action<BCClaimVipWondersResponse> onSuccess, Action onFailed)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                onFailed.SafeInvoke();
                return;
            }

            var data = new BCClaimVIpWondersData
            {
                wonderUids = wonderUids
            };
            _brainCloudWrapper.RunScript("/vip_coins/ClaimCoinsForWonder", JsonConvert.SerializeObject(data), onSuccess, onFailed);
        }

        public void ClaimVipForLocation(string locationUid, Action<BCClaimVipLocationResponse> onSuccess, Action onFailed)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                onFailed.SafeInvoke();
                return;
            }

            var data = new BCClaimVIpLocationData
            {
                locationUid = locationUid
            };
            _brainCloudWrapper.RunScript("/vip_coins/ClaimCoinsForLocation", JsonConvert.SerializeObject(data), onSuccess, onFailed);
        }

        public void ExchangeItemForVipCoins(string itemUid, Action<BCExchangeItemVipResponse> onSuccess, Action onFailed)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                onFailed.SafeInvoke();
                return;
            }

            var data = new BCExchangeItemData
            {
                itemUid = itemUid
            };
            _brainCloudWrapper.RunScript("/vip_coins/ExchangeItem", JsonConvert.SerializeObject(data), onSuccess, onFailed);
        }
    }
}