using System.Collections.Generic;
using BBB;
using BBB.DI;
using BBB.Social;
using BBB.UI;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FBConfig;
using GameAssets.Scripts.ChallengeModal;
using JetBrains.Annotations;
using PBGame;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.ChallengeTrivia
{
    public class ChallengeTriviaHeader : BbbMonoBehaviour
    {
        private const string RewardMultiplierTextMask = "{0}x";
        private const string CountryPrefix = "COUNTRY_";

        [SerializeField] private AsyncAvatar _asyncAvatar;
        [SerializeField] private Transform _rewardsMultiplierRoot;
        [SerializeField] private GameObject _rewardMultiplierPrefab;

        [SerializeField] private Transform _progressBarStartHolder;
        [SerializeField] private Transform _progressBarEndHolder;

        [SerializeField] private Transform _pinTransform;

        [SerializeField] private Image _fillImage;
        [SerializeField] private float _progressBarFillValueOffset = -0.02f;

        [SerializeField] private float _fillingDelay = 0.3f;
        [SerializeField] private float _fillingTime = 1f;
        [SerializeField] private Ease _fillingEase = Ease.InOutSine;

        [SerializeField] private GenericSpeechBubble _speechBubble;
        [SerializeField] private LocalizedTextPro _speechBubbleText;

        private ILocalizationManager _localizationManager;
        private ChallengeTriviaManager _challengeTriviaManager;

        private int _lastProgressValue = 0;

        private TweenerCore<float, float, FloatOptions> _fillingTweener;
        private IDictionary<string, ChallengeLocationConfig> _challengeLocationConfigs;
        private readonly Dictionary<string, ChallengeLocationConfig> _challengeLocationConfigByCountryCode = new();

        public void Init(IContext context)
        {
            _challengeTriviaManager = context.Resolve<ChallengeTriviaManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();

            _challengeLocationConfigs = context.Resolve<IConfig>().Get<ChallengeLocationConfig>();
            foreach (var keyValuePair in _challengeLocationConfigs)
            {
                _challengeLocationConfigByCountryCode[keyValuePair.Value.CountryCode] = keyValuePair.Value;
            }

            _rewardsMultiplierRoot.RemoveAllActiveChilden();
            for (var i = 0; i < _challengeTriviaManager.ChallengeTriviaMaxProgress / 2; i++)
            {
                var go = Instantiate(_rewardMultiplierPrefab, _rewardsMultiplierRoot);
                var text = go.GetComponent<TextMeshProUGUI>();
                text.text = string.Format(RewardMultiplierTextMask, i + 1);

                go.SetActive(true);
            }

            _speechBubble.HideSpeechBubble();
        }

        public void Setup(ActiveChallengeData activeChallengeData, PBChallengeTriviaCache triviaCache)
        {
            _asyncAvatar.Setup(new AvatarInfo(activeChallengeData.AvatarUrl, activeChallengeData.CountryCode));

            var country = activeChallengeData.CountryCode;

            if (_challengeLocationConfigByCountryCode.TryGetValue(country, out var locationConfig))
            {
                if (locationConfig.KnownForLength > 0)
                {
                    int knowForIndex;
                    if (triviaCache.KnownForIndex >= 0 && triviaCache.KnownForIndex < locationConfig.KnownForLength)
                    {
                        knowForIndex = triviaCache.KnownForIndex;
                    }
                    else
                    {
                        knowForIndex = Random.Range(0, locationConfig.KnownForLength);
                        triviaCache.KnownForIndex = knowForIndex;
                    }

                    var countryName = _localizationManager.getLocalizedText(CountryPrefix + locationConfig.CountryCode);
                    var knownForText = locationConfig.KnownFor(knowForIndex);
                    var knowForLocalized = _localizationManager.getLocalizedText(knownForText);

                    _speechBubbleText.FormatSetArgs(activeChallengeData.PlayerName, countryName, knowForLocalized);
                    _speechBubble.ShowSpeechBubble();
                }
                else
                {
                    _speechBubble.HideSpeechBubble();
                }
            }
        }

        public void SetupProgressBar(int currentProgress, bool instant)
        {
            if (_fillingTweener != null)
            {
                _fillingTweener.Kill();
                _fillingTweener = null;
            }

            var targetFillValue = GetFillAmount(currentProgress);

            if (instant || _lastProgressValue == currentProgress)
            {
                _fillImage.fillAmount = targetFillValue;
                SetTipPosition(targetFillValue);
            }
            else
            {
                var startFillValue = GetFillAmount(_lastProgressValue);
                _fillImage.fillAmount = startFillValue;
                SetTipPosition(startFillValue);

                _fillingTweener = DOTween.To(() => startFillValue, (x) =>
                    {
                        _fillImage.fillAmount = x;
                        SetTipPosition(x);
                    },
                    targetFillValue, _fillingTime).SetDelay(_fillingDelay).SetEase(_fillingEase);
            }

            _lastProgressValue = currentProgress;
        }

        private float GetFillAmount(int currentProgress)
        {
            var maxValue = _challengeTriviaManager.ChallengeTriviaMaxProgress;
            var fillValue = (float)currentProgress / maxValue;
            if (currentProgress > 0 && currentProgress < maxValue)
            {
                fillValue += _progressBarFillValueOffset;
            }

            return fillValue;
        }

        private void SetTipPosition(float fillValue)
        {
            _pinTransform.position = Vector3.Lerp(_progressBarStartHolder.position, _progressBarEndHolder.position, fillValue);
        }
    }
}