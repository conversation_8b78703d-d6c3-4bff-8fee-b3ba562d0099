using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BebopBee.Core.UI;
using BebopBee.Social;
using GameAssets.Scripts.Utils;

namespace GameAssets.Scripts.Deeplink
{
    public enum DeepLinkType
    {
        None,
        Promo,
        Invite,
        BuddyGift,
        GenericGift,
        PoiShare,
        Modal
    }

    /// <summary>
    /// Generates DeepLinkData from Url
    /// </summary>
    public static class DeepLinkFactory
    {
        public const string ImmediateShowModeParameter = "immediate";
        public const string ScreenParameter = "screen";
        public const string Modal = "modal";
        private const string ActionBasedStringFormatMask = "{0}://{1}?{2}";

        public static string GenerateUrl(string action, string actionParams)
        {
            return string.Format(ActionBasedStringFormatMask, GameConstants.UrlSchema, action, actionParams);
        }

        public static DeepLinkData GenerateFromUrl(string url)
        {
            if (url.IsNullOrEmpty())
                return default;

            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                BDebug.Log(LogCat.DeepLink, $"Invalid deep link URL: {url}");
                return default;
            }

            BDebug.Log(LogCat.DeepLink, $"GenerateFromUrl uri:{uri} host:{uri.Host} query:{uri.Query}");

            try
            {
                var linkType = GetTypeFromPath(uri.Host);
                var linkParams = uri.Query.GetDictionaryFromQueryString();

                if (linkType == DeepLinkType.None && linkParams.TryGetValue("link_type", out var linkTypeParam))
                {
                    linkType = GetTypeFromPath(linkTypeParam.ToString());
                }

                if (linkType == DeepLinkType.None && linkParams.TryGetValue("feature", out var featureParam))
                {
                    linkType = GetTypeFromPath(featureParam.ToString());
                }

                return new DeepLinkData(url, linkType, linkParams);
            }
            catch (Exception e)
            {
                BDebug.Log(LogCat.DeepLink, $"Failed to generate deeplink from: {url}, {e.Message}");
                return default;
            }
        }

        public static Dictionary<string, object> GetParamsFromUrl(string url)
        {
            if (Uri.TryCreate(url, UriKind.Absolute, out var uri))
                return uri.Query.GetDictionaryFromQueryString();

            BDebug.Log(LogCat.DeepLink, $"Failed to get params from URL: {url}");
            return null;
        }

        private static DeepLinkType GetTypeFromPath(string path)
        {
            return path.ToLowerInvariant() switch
            {
                "promo" => DeepLinkType.Promo,
                SocialRequestTypes.Invite => DeepLinkType.Invite,
                SocialRequestTypes.PoiShare => DeepLinkType.PoiShare,
                SocialRequestTypes.BuddyGift => DeepLinkType.BuddyGift,
                SocialRequestTypes.GenericGift => DeepLinkType.GenericGift,
                Modal => DeepLinkType.Modal,
                _ => DeepLinkType.None
            };
        }

        public static ShowMode GetShowMode(this Dictionary<string, string> parameters)
        {
            return parameters.GetSafe(ImmediateShowModeParameter) == "true"
                ? ShowMode.Immediate
                : ShowMode.Delayed;
        }
    }
}