using RPC.Stealing;

namespace Bebopbee.Core.Systems.RpcCommandManager.Diagnostics
{
    public class ClientIssueCommand : RpcCommand<ClientIssue>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.Message = (string) args[0];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 1;
        }

        protected override void OnSuccess()
        {
        }

        protected override void OnFailure()
        {
        }

        protected override void OnNoInternet()
        {
        }
    }
}