using System.Collections.Generic;
using RPC.Social;

namespace Bebopbee.Core.Systems.RpcCommandManager.Social
{
    public class JoinRaceCommand : RpcCommand<JoinRace>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.EventUid = (string) args[0];
            Dto.StageUid = (string) args[1];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 2;
        }

        protected override void OnSuccess()
        {
        }

        protected override void OnFailure()
        {
        }

        protected override void OnNoInternet()
        {
        }
    }
}