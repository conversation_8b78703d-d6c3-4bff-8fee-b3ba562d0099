using System.Collections.Generic;
using RPC.Levels;

namespace Bebopbee.Core.Systems.RpcCommandManager.Levels
{
    public class GetFriendLevelResultsCommand : RpcCommand<GetFriendLevelResults>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.LevelIds = (List<string>) args[0];
        }
    
        protected override int GetExpectedArgumentsCount()
        {
            return 1;
        }
    }
}