using System.Text;
using BBB.Match3.Logic;
using RPC.Social;

namespace Bebopbee.Core.Systems.RpcCommandManager.Social
{
    public class GetLeaderboardsCommand : RpcCommand<RPC.Social.GetLeaderboards>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.Types = (RPC.Social.LeaderboardType) args[0];
            Dto.Categories = (RPC.Social.LeaderboardCategory) args[1];
            Dto.LastSubmittedWeeklyScore = (int) args[2];
            Dto.LocalEndDateOfLastWeeklyStarted = (string) args[3];
        }
        public string ToSpecialString()
        {
            var builder = new StringBuilder();
            builder.Append("ClassName : ").AppendLine(GetType().Name)
                .Append("Id : ").AppendLine(Dto.Id.ToString())
                .Append("TimeCreated : ").AppendLine(Dto.TimeCreated.ToString())
                .Append("ResponseStatus.Status : ").AppendLine(Dto.ResponseStatus.Status.ToString())
                .Append("ResponseStatus.Message : ").AppendLine(Dto.ResponseStatus.Message)
                .Append("ResponseStatus.ErrorCode : ").AppendLine(Dto.ResponseStatus.ErrorCode.ToString())
                .Append("LeaderboardWeeklyWorldData : ").AppendLine(LeaderboardDataToString(Dto.LeaderboardWeeklyWorldData))
                .Append("LeaderboardWeeklyCountryData : ").AppendLine(LeaderboardDataToString(Dto.LeaderboardWeeklyCountryData))
                .Append("LeaderboardWeeklyFriendsData : ").AppendLine(LeaderboardDataToString(Dto.LeaderboardWeeklyFriendsData));

            return builder.ToString();
        }

        private static string LeaderboardDataToString(LeaderboardData ld)
        {
            if (ld == null)
            {
                return string.Empty;
            }

            var sb = new StringBuilder();
            sb.Append("UserTrophies=").Append(ld.UserTrophies)
                .Append(" UserPosition=").Append(ld.UserPosition)
                .Append(" IndexCount=").Append(ld.Indexes?.Count ?? 0);

            return sb.ToString();
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 4;
        }

        protected override void OnSuccess()
        {
        }

        protected override void OnFailure()
        {
        }

        protected override void OnNoInternet()
        {
        }
    }
}