using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace Bebopbee.Core.Systems.RpcCommandManager
{
    public class RpcCommandsTesterWindow : EditorWindow
    {
        private const string ScriptFolderPath = "GameAssets/_Sandbox/TesterScripts/";

        private RpcCommandsTester _tester;

        private bool _loaded;
        private string _scriptName = "";

        private readonly Queue<float> _progressBarQueue = new Queue<float>();

        [MenuItem("BebopBee/Rpc Commands Tester")]
        public static void ShowWindow()
        {
            var window = (RpcCommandsTesterWindow)GetWindow(typeof(RpcCommandsTesterWindow));
            window.titleContent.text = "Rpc Commands Tester"; 
            window.Setup();
            window.Show();
        }

        private void Setup()
        {
              _tester = new RpcCommandsTester(); 
        }

        private void OnGUI()
        {
            if (_tester == null)
            {
                _tester = new RpcCommandsTester(); 
                _loaded = false;
                _scriptName = "";
                _progressBarQueue.Clear();
            }


            if (_loaded)
            {
                foreach (var scriptName in _tester.AllScriptNames)
                {
                    GUILayout.Label(scriptName);
                }

                if (GUILayout.Button("Reload Scripts"))
                {
                    _tester.Load(Path.Combine(Application.dataPath, ScriptFolderPath));
                }

                _scriptName = GUILayout.TextField(_scriptName);

                if (_tester.HasScript(_scriptName) && GUILayout.Button("Run Script"))
                {
                    EditorUtility.DisplayProgressBar($"{_scriptName}","", 0f);
                    _tester.RunScript(_scriptName, PortionDoneCallback, CompleteCallback, ExceptionCallback);
                }
            }
            else
            {
                if (GUILayout.Button("Load Scripts"))
                {
                    _tester.Load(Path.Combine(Application.dataPath, ScriptFolderPath));
                    _loaded = true;
                }
            }

            lock (_progressBarQueue)
            {
                if (_progressBarQueue.Count > 0)
                {
                    var value = _progressBarQueue.Dequeue();
                    if (value > 0)
                    {
                        EditorUtility.DisplayProgressBar($"{_scriptName}","", value);
                    }
                    else
                    { 
                        EditorUtility.ClearProgressBar();
                    }
                }
            }
        }

        private void ExceptionCallback()
        {
            lock (_progressBarQueue)
            {
                _progressBarQueue.Enqueue(-1f);
            }
        }

        private void CompleteCallback()
        {
            lock (_progressBarQueue)
            {
                _progressBarQueue.Enqueue(-1f);
            }
           
        }

        private void PortionDoneCallback(float val)
        { 
            lock (_progressBarQueue)
            {
                _progressBarQueue.Enqueue(val);
            }
        }
    }
}