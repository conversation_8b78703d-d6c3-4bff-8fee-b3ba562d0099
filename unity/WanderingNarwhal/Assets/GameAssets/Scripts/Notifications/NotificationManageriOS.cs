#if UNITY_IOS
using System.Runtime.InteropServices;
using System.Collections.Generic;
using UnityEngine;
using System;
using BBB.Core;
using Unity.Notifications.iOS;

namespace BBB
{
    public class NotificationManageriOS : INotificationManager
    {
        private string _lastIntentDataId;

#region DllImports

        [DllImport("__Internal")]
        private static extern void _Request_Notifications_Authorization(string callbackId);

        [DllImport("__Internal")]
        private static extern void _Schedule_Notification(string identifier, string notificationType, string title, string message, int seconds);

        [DllImport("__Internal")]
        private static extern void _Schedule_Notification_Json(string json);

        [DllImport("__Internal")]
        private static extern bool _Should_Use_New_Notifications();

        [DllImport("__Internal")]
        private static extern bool _Remove_Pending_Notification(string identifier);

        [DllImport("__Internal")]
        private static extern bool _Remove_All_Pending_Notifications();

#endregion

        public void RequestNotificationsAuthorization(string callbackId)
        {
            _Request_Notifications_Authorization(callbackId);
        }

        public void ScheduleNotification(string notificationId, string notificationType, string title, string message, int time, int badgeNumber, string largeIconName)
        {
            _Schedule_Notification(notificationId, notificationType, title, message, time);
        }

        public void ScheduleCustomNotification(string notificationId, string layout, int time, List<Dictionary<string, string>> resources)
        {
            UnityEngine.Debug.LogError("Custom notifications not supported on iOS");
        }

        public void RemoveAllPendingNotifications()
        {
            _Remove_All_Pending_Notifications();
        }

        public void SetNotificationToken(string token, string userId)
        {
        }

        public string GetNotificationToken()
        {
            return string.Empty;
        }

        public void Schedule(Notification notification)
        {
            _Schedule_Notification_Json(notification.SerializeToJson());
        }

        public void Initialize(IRPCService rpcService, INotificationFilter notificationsFilter, FBConfig.LocalPushNotificationsTimingConfig notifTimingConfig)
        {
        }

        public void SetPushNotificationEnabled(bool b)
        {
            PlayerPrefs.SetInt(NotificationManager.NotificationPermissions, b ? 1 : 0);
            PlayerPrefs.Save();
        }

        public bool GetNotificationStatus()
        {
            return PlayerPrefs.GetInt(NotificationManager.NotificationPermissions, 1) == 1;
        }

        public void TrackMessageReceived(NotificationReceived notification)
        {
            // Do nothing. Already handled in generic way in NotificationManager
        }

        public void OnApplicationInForeground(Action<NotificationReceived> trackingCallback = null)
        {
            var iosNotification = iOSNotificationCenter.GetLastRespondedNotification();
            if (iosNotification == null)
            {
                BDebug.Log(LogCat.Notification, $"App was not opened through a local notification");
                return;
            }

            try
            {
                var notification = NotificationReceived.FromIosNotification(iosNotification);
                if (_lastIntentDataId == notification.Uuid)
                {
                    BDebug.Log(LogCat.Notification, $"We've already reported this intent id={_lastIntentDataId}");
                    return;
                }

                _lastIntentDataId = notification.Uuid;
                BDebug.Log(LogCat.Notification, $"App was opened through a local notification id={_lastIntentDataId} data={iosNotification.Data}");
                trackingCallback.SafeInvoke(notification);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogException(e);
            }
        }
    }
}
#endif