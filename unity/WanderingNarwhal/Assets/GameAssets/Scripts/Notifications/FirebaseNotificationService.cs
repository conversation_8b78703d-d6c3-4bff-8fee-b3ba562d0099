using System;
using System.Collections.Generic;
using BBB.Core.Analytics;
using BBB.DI;
using BebopBee;
using Beebopbee.Core.Extensions;
using Firebase.Extensions;
using Firebase.Messaging;
using Newtonsoft.Json;
using UnityEngine;

namespace BBB.Core.Notifications
{
    public class FirebaseNotificationServiceGo : BbbMonoBehaviour
    {
        public event Action<string> OnNotificationTokenEvent = delegate { };

        public void OnNotificationToken(string token)
        {
            OnNotificationTokenEvent.SafeInvoke(token);
        }

        public void OnLog(string log)
        {
            BDebug.Log(LogCat.General, log);
        }
    }

    /// <summary>
    /// Wrapper on top of Firebase Notification (Messaging) system
    /// in order to fetch token and notifications
    /// </summary>
    public class FirebaseNotificationService : INotificationService
    {
        private Action<bool, string, string> _tokenCallback;
        private Action<FirebaseMessage> _messageCallback;

        /// <summary>
        /// Wrapper to receive iOS Notification token
        /// </summary>
        private FirebaseNotificationServiceGo _ctrl;

        /// <summary>
        /// Flag used to control when to subscribe to FirebaseMessaging.MessageReceived
        /// for iOS if we subscribe right away it's going to prompt permission modal
        /// </summary>
        private bool _isPermissionCalled;

        private bool _requestingToken;

        /// <summary>
        /// iOS Notification token
        /// </summary>
        private string _cachedToken;

        /// <summary>
        /// FCM Notification token used by Firebase
        /// </summary>
        private string _fcmToken;

        /// <summary>
        /// Cached of _isPermissionCalled
        /// </summary>
        private const string PermissionPrefKey = "notification/permission_modal";

        /// <summary>
        /// iOS Notification token cached
        /// </summary>
        private const string CachedTokenKey = "notification/cached_token";

        /// <summary>
        /// FCM iOS/Android notification token cache
        /// </summary>
        private const string CachedFcmTokenKey = "notification/cached_fcm_token";

        private const string AndroidPermissionPostNotifications = "android.permission.POST_NOTIFICATIONS";

        public void InitializeByContext(IContext context)
        {
            var go = new GameObject("FirebaseNotificationServiceGo");
            _ctrl = go.AddComponent<FirebaseNotificationServiceGo>();
            _ctrl.OnNotificationTokenEvent += OniOSTokenReceived;
            _isPermissionCalled = PlayerPrefs.GetInt(PermissionPrefKey, 0) == 1;
            if (AppDefinesConverter.UnityAndroid)
                _isPermissionCalled = true;
            _cachedToken = PlayerPrefs.GetString(CachedTokenKey);
            _fcmToken = PlayerPrefs.GetString(CachedFcmTokenKey);
        }

        /// <summary>
        /// Register a callback to receive a notification token when it's fetch from Platform
        /// </summary>
        /// <param name="callback"></param>
        public void RegisterNotificationToken(Action<bool, string, string> callback)
        {
            _tokenCallback = callback;
        }

        /// <summary>
        /// Register callback to receive Notifications that arrive when game is running
        /// </summary>
        /// <param name="callback"></param>
        public void RegisterNotificationMessage(Action<FirebaseMessage> callback)
        {
            _messageCallback = callback;
            // Any call done to FirebaseMessaging.MessageReceived will trigger Push Notification permission
            // modal on iOS but we need it in order to get Notification arrives information
            // so this will be call after Permission modal is call
            if (_isPermissionCalled)
            {
                if (FirebaseInitializer.IsInitialized)
                    OnFirebaseInitialized();
                else
                    FirebaseInitializer.OnInitialized += OnFirebaseInitialized;
            }
        }

        private void OnFirebaseInitialized()
        {
            BDebug.Log(LogCat.Notification, $"OnFirebaseInitialized _isPermissionCalled:{_isPermissionCalled}  _cachedToken:{_cachedToken} _fcmToken:{_fcmToken}");
            FirebaseInitializer.OnInitialized -= OnFirebaseInitialized;
            if (_isPermissionCalled)
            {
                FirebaseMessaging.MessageReceived -= OnMessageReceived;
                FirebaseMessaging.MessageReceived += OnMessageReceived;
            }
        }

        public void UnRegisterNotificationToken()
        {
            if (FirebaseInitializer.IsInitialized)
                FirebaseMessaging.TokenReceived -= OnTokenReceived;
        }

        public void UnRegisterNotificationMessage()
        {
            if (FirebaseInitializer.IsInitialized)
                FirebaseMessaging.MessageReceived -= OnMessageReceived;
        }

        public void SetUserId(string userId)
        {
        }

        public void RefreshNotificationToken()
        {
            BDebug.Log(LogCat.Notification, $"RefreshNotificationToken _cachedToken:{_cachedToken} _fcmToken:{_fcmToken}");
            CallRequestToken();
        }

        public void Release()
        {
            if (_ctrl != null)
                _ctrl.OnNotificationTokenEvent -= OniOSTokenReceived;
        }

        private void CallRequestToken()
        {
            if (_requestingToken)
                return;
            _requestingToken = true;
            BDebug.Log(LogCat.Notification, $"CallRequestToken _requestingToken:{_requestingToken}  _cachedToken:{_cachedToken} _fcmToken:{_fcmToken}");

            // checking if token is already cached then we make the call to callback for processing
            var cachedTokenNotNullOrEmpty = !_cachedToken.IsNullOrEmpty();
            var fcmTokenNotNullOrEmpty = !_fcmToken.IsNullOrEmpty();
            if (cachedTokenNotNullOrEmpty || fcmTokenNotNullOrEmpty)
            {
                BDebug.Log(LogCat.Notification, $"CallRequestToken using cache _fcmToken:{_fcmToken} _cachedToken:{_cachedToken}");
                // FCM Token is a token used by Firebase to send notifications
                // For Android FCMToken is the only token we get
                // For iOS there's iOS token as _cachedToken and FCMToken
                if (AppDefinesConverter.UnityAndroid)
                {
                    _tokenCallback.SafeInvoke(fcmTokenNotNullOrEmpty, _fcmToken, _fcmToken);
                }
                else
                {
                    _tokenCallback.SafeInvoke(cachedTokenNotNullOrEmpty, _cachedToken, _fcmToken);
                }
            }

            RequestPermissions();
        }

        private void RequestPermissions()
        {
            BDebug.Log(LogCat.Notification, $"RequestPermissions");
            if (AppDefinesConverter.UnityAndroid)
            {
                RequestPermissionAndroid();
            } 
            else if (AppDefinesConverter.UnityIos)
            {
                RequestPermissionUniversal();
            }
        }

        private void RequestPermissionUniversal()
        {
            // iOS Order execution
            //  - First OniOSTokenReceived
            //  - Second RequestPermissionAsync
            // Android not clear
            FirebaseMessaging.TokenReceived -= OnTokenReceived;
            FirebaseMessaging.TokenReceived += OnTokenReceived;
            FirebaseMessaging.MessageReceived -= OnMessageReceived;
            FirebaseMessaging.MessageReceived += OnMessageReceived;
            FirebaseMessaging.DeliveryMetricsExportedToBigQueryEnabled = true;
            FirebaseMessaging.RequestPermissionAsync().ContinueWithOnMainThread(task =>
            {
                UnityEngine.Profiling.Profiler.BeginSample($"FirebaseMessaging RequestPermissionAsync");
                _requestingToken = false;
                PlayerPrefs.SetInt(PermissionPrefKey, 1);
                PlayerPrefs.Save();
                BDebug.Log(LogCat.Notification, $"RequestPermissionAsync completed:{task} ");
                if (!_cachedToken.IsNullOrEmpty())
                {
                    BDebug.Log(LogCat.Notification, $"RequestPermissionAsync calling callback Notification token:{_cachedToken} fcmToken:{_fcmToken}");
                    _tokenCallback.SafeInvoke(true, _cachedToken, _fcmToken);
                }
                else
                {
                    BDebug.Log(LogCat.Notification, $"RequestPermissionAsync token empty:{_cachedToken} fcmToken:{_fcmToken}");
                }

                UnityEngine.Profiling.Profiler.EndSample();
            });
            FirebaseMessaging.GetTokenAsync().ContinueWithOnMainThread(task => { SetFcmToken(task.Result); });
        }

        private void RequestPermissionAndroid()
        {
            if (!AndroidPermissionsController.Instance.HasPermission(AndroidPermissionPostNotifications))
            {
                AndroidPermissionsController.Instance.RequestPermission(AndroidPermissionPostNotifications, status =>
                {
                    if (status == AndroidPermissionsController.PermissionStatus.Granted)
                    {
                        RequestPermissionUniversal();
                    }
                });
            }
            else
            {
                RequestPermissionUniversal();
            }
        }

        private void OnMessageReceived(object sender, MessageReceivedEventArgs e)
        {
#if BBB_LOG
            // converting IDictionary to Dictionary in order to avoid a possible exception being thrown by JsonConvert.SerializeObject
            var messageData = new Dictionary<string, string>(e.Message.Data);
            BDebug.Log(LogCat.Notification, $"OnMessageReceived id:{e.Message.MessageId} opened:{e.Message.NotificationOpened} raw:{JsonConvert.SerializeObject(messageData)}");
#endif

            _messageCallback.SafeInvoke(e.Message);
        }

        private void OnTokenReceived(object sender, TokenReceivedEventArgs e)
        {
            SetFcmToken(e.Token);
        }

        private void SetFcmToken(string token)
        {
            _fcmToken = token;
            PlayerPrefs.SetString(CachedFcmTokenKey, _fcmToken);
            PlayerPrefs.Save();
            BDebug.Log(LogCat.Notification, $"OnTokenReceived token:{token}");
            if (AppDefinesConverter.UnityAndroid)
            {
                _tokenCallback.SafeInvoke(!token.IsNullOrEmpty(), token, string.Empty);
            }
        }

        private void OniOSTokenReceived(string token)
        {
            PlayerPrefs.SetString(CachedTokenKey, token);
            PlayerPrefs.Save();
            BDebug.Log(LogCat.Notification, $"OniOSTokenReceived token:{token}");
            _cachedToken = token;
        }
    }
}