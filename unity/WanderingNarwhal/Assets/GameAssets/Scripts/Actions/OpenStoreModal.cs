using System.Collections.Generic;
using BBB.DI;
using BBB.UI.IAP.Controllers;
using BebopBee.Core.UI;
using RSG;

namespace BBB.Actions
{
    public class OpenStoreModal : ISimpleAction
    {
        public IPromise Execute(IContext context, Dictionary<string, string> actionParams)
        {
            var modalsBuilder = context.Resolve<IModalsBuilder>();
            var tag = actionParams == null || !actionParams.TryGetValue("tag", out var param) ? ModalsTags.Iap : param;
            var item = actionParams == null || !actionParams.TryGetValue("item", out var actionParam) ? string.Empty : actionParam;
            var category = actionParams == null || !actionParams.ContainsKey("category") ? StoreCategory.Regular : actionParams["category"].ToEnum<StoreCategory>();

            var storeModalController = modalsBuilder.CreateModalView<StoreModalController>(ModalsType.Store);
            PurchasePath.Append(PurchaseStep.Action);

            if (!item.IsNullOrEmpty())
            {
                storeModalController.Show(item, false, ShowMode.Delayed, tag);
            }
            else
            {
                storeModalController.Show(category, ShowMode.Delayed, tag);
            }

            return Promise.Resolved();
        }
    }
}