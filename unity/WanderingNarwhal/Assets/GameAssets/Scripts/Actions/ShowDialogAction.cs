using System;
using System.Collections;
using System.Collections.Generic;
using BBB.DI;
using BBB.Narrative;
using BBB.Narrative.Controllers;
using BebopBee.Core.UI;
using PBConfig;
using RSG;

namespace BBB.Actions
{
    public class ShowDialogAction : ISimpleAction
    {
        private Promise _promise;
        private DialogModalController _dialogModal;

        public IPromise Execute(IContext context, Dictionary<string, string> actionParams)
        {
            if (actionParams == null || actionParams.Count == 0) return Promise.Resolved();
            _promise = new Promise();

            var dialogUid = actionParams.GetSafe("dialogUid");
            var tag = actionParams.GetSafe("tag");
            if (tag.IsNullOrEmpty())
            {
                tag = ModalsTags.Quest;
            }

            if (dialogUid.IsNullOrEmpty())
            {
                return Promise.Resolved();
            }

            var config = context.Resolve<IConfig>();
            var configDict = config.Get<NarrativeDialogConfig>();
            if (DialogUtility.IsValid(configDict, dialogUid))
            {
                ShowMode showMode = actionParams.GetSafe("immediate") == "true" ? ShowMode.Immediate : ShowMode.Delayed;
                var modalsBuilder = context.Resolve<IModalsBuilder>();
                _dialogModal = modalsBuilder.CreateModalView<DialogModalController>(ModalsType.DialogModal);
                var setupParams = new DialogModalSetupParams(dialogUid, PromiseResolver);
                context.Resolve<ICoroutineExecutor>().StartCoroutine(WaitForModal(_dialogModal,
                    () =>
                    {
                        _dialogModal.ShowModal(showMode, tag: tag, setupParams: setupParams);
                    }));

                return _promise;
            }

            return Promise.Resolved();
        }

        private IEnumerator WaitForModal(DialogModalController modal, Action callback)
        {
            while (!modal.IsReady())
            {
                yield return null;
            }

            callback();
        }

        private void PromiseResolver()
        {
            _promise.Resolve();
        }
    }
}