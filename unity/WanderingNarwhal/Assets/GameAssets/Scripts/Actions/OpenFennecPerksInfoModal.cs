using System.Collections.Generic;
using BBB.DI;
using BebopBee.Core.UI;
using RSG;

namespace BBB.Actions
{
    public class OpenFennecPerksInfoModal : ISimpleAction
    {
        public IPromise Execute(IContext context, Dictionary<string, string> actionParams)
        {
            var butlerGiftManager = context.Resolve<IButlerGiftManager>();
            butlerGiftManager.ShowInfoModal(ShowMode.Delayed);
            return Promise.Resolved();
        }
    }
}