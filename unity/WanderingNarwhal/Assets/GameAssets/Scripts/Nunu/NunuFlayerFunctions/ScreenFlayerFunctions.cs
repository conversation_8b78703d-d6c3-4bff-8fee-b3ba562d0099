#if USE_NUNU_SDK && BBB_DEBUG
using BBB;
using BBB.DI;
using Nunu.Flayer;
using Nunu.Network.Logging;
using UnityEngine.Scripting;

namespace GameAssets.Scripts.Nunu.NunuFlayerFunctions
{
    public static class ScreenFlayerFunctions
    {
        private static IScreensManager _screensManager;

        public static void InitGameContext(IContext context)
        {
            _screensManager = context.Resolve<IScreensManager>();
        }

        [FlayerFunction("current_screen", "Returns the screen that's currently being shown")]
        [Preserve]
        public static string CurrentScreen(PacketLogger packetLogger)
        {
            if (_screensManager == null)
            {
                packetLogger.Log("No screens manager", sendToAI: true);
                return string.Empty;
            }

            if (_screensManager.IsTransitionInProgress)
            {
                return $"{_screensManager.GetPreviousScreenType()}->{_screensManager.GetCurrentScreenType()}";
            }

            return _screensManager.GetCurrentScreenType().ToString();
        }
    }
}
#endif