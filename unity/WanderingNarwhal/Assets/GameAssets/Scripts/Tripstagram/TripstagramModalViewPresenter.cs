using System;
using System.Collections.Generic;
using BBB;
using BBB.Screens;
using Bebopbee.Core.Utility;
using BebopBee.UnityEngineExtensions;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Map;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Tripstagram
{
    public class TripstagramModalViewPresenter : ModalsViewPresenter, ITripstagramModalViewPresenter
    {
        private const float OutroAnimationLength = 0.27f;
        private const string TripstagramBuildButtonPush = "Tripstagram_BuildButtonPush";
        private static readonly int ShowId = Animator.StringToHash("Show");
        private static readonly int HideId = Animator.StringToHash("Hide");

        [Header("Scroll View")]
        [SerializeField] private ScrollRect _scrollView;
        [SerializeField] private Transform _scrollContent;
        [SerializeField] private TripstagramSceneItem _sceneItemPrefab;
        [SerializeField] private Transform _lockedSceneItem;
        [SerializeField] private Animator _lockedSpeechBubbleAnimator;
        [SerializeField] private Animator _animator;
        [SerializeField] private RectTransform _topBar;

        private readonly Dictionary<string, TripstagramSceneItem> _activeSceneItems = new();
        private IEpisodicScenesManager _episodicScenesManager;
        private GenericHudManager _genericHudManager;
        private bool _isSpeechBubbleShown;
        private string _buildSceneUid;
        private Transform _currentSceneItem;

        public event Action<string> OnScenePlayClicked;
        public event Action<string> OnSceneViewClicked;
        public event Action<string> OnSceneBuildClicked;
        public event Action<string> OnSceneIconClicked;
        public event Action OnSceneBuildPreClicked;

        public void Setup(TripstagramModalViewModel tripstagramModalViewModel)
        {
            var allScenes = tripstagramModalViewModel.AllScenes;
            var index = 0;
            foreach (var (uid, scene) in allScenes)
            {
                var tripstagramSceneItem = GetSceneItem(uid);
                tripstagramSceneItem.Init(scene);
                if (scene.TripstagramItemState == TripstagramItemState.InProgress)
                {
                    _currentSceneItem = tripstagramSceneItem.transform;
                }

                if (++index == allScenes.Count)
                {
                    tripstagramSceneItem.HideSeparator();
                }
            }

            _lockedSceneItem.SetAsLastSibling();

            _scrollView.onValueChanged.RemoveListener(OnScroll);
            _scrollView.onValueChanged.AddListener(OnScroll);

            ScrollToCurrent();
            ShowSpeechBubble();
        }

        private void ScrollToCurrent()
        {
            var current = _currentSceneItem?.transform;
            if (current == null)
            {
                current = _scrollContent.GetChild(_scrollContent.childCount - 1);
            }

            Canvas.ForceUpdateCanvases();
            var targetHeight = ((RectTransform)current).rect.height * 2f; //2x to show half of the next item
            var position =
                (Vector2)_scrollView.transform.InverseTransformPoint(_scrollContent.position)
                - (Vector2)_scrollView.transform.InverseTransformPoint(current.position);
            position.x = _scrollContent.RectTransform().anchoredPosition.x;
            position.y += targetHeight * 0.5f;
            _scrollContent.RectTransform().anchoredPosition = position;
        }

        private void OnScroll(Vector2 _)
        {
            if (!_isSpeechBubbleShown && _lockedSceneItem.position.y < _topBar.position.y - _topBar.rect.height * 0.5f)
            {
                ShowSpeechBubble();
            }
            else if (_isSpeechBubbleShown && _lockedSceneItem.position.y >= _topBar.position.y - _topBar.rect.height * 0.5f)
            {
                HideSpeechBubble();
            }
        }

        private void ShowSpeechBubble()
        {
            _isSpeechBubbleShown = true;
            _lockedSpeechBubbleAnimator.ResetAllParameters();
            _lockedSpeechBubbleAnimator.SetTrigger(ShowId);
        }

        private void HideSpeechBubble()
        {
            _isSpeechBubbleShown = false;
            _lockedSpeechBubbleAnimator.ResetAllParameters();
            _lockedSpeechBubbleAnimator.SetTrigger(HideId);
        }

        private TripstagramSceneItem GetSceneItem(string uid)
        {
            if (_activeSceneItems.TryGetValue(uid, out var tripstagramSceneItem)) return tripstagramSceneItem;
            tripstagramSceneItem = Instantiate(_sceneItemPrefab, _scrollContent);
            SubscribeItemListeners(tripstagramSceneItem);
            _activeSceneItems.Add(uid, tripstagramSceneItem);
            return tripstagramSceneItem;
        }

        private void SubscribeItemListeners(TripstagramSceneItem sceneItem)
        {
            UnsubscribeItemListeners(sceneItem);
            sceneItem.OnPlayButtonClicked += PlayButtonClickHandler;
            sceneItem.OnViewButtonClicked += ViewButtonClickHandler;
            sceneItem.OnBuildButtonClicked += BuildButtonClickHandler;
            sceneItem.OnSceneIconClicked += SceneIconClickHandler;
        }

        private void UnsubscribeItemListeners(TripstagramSceneItem sceneItem)
        {
            sceneItem.OnPlayButtonClicked -= PlayButtonClickHandler;
            sceneItem.OnViewButtonClicked -= ViewButtonClickHandler;
            sceneItem.OnBuildButtonClicked -= BuildButtonClickHandler;
            sceneItem.OnSceneIconClicked -= SceneIconClickHandler;
        }

        private void PlayButtonClickHandler(string sceneUid)
        {
            OnScenePlayClicked.SafeInvoke(sceneUid);
        }

        private void ViewButtonClickHandler(string sceneUid)
        {
            OnSceneViewClicked.SafeInvoke(sceneUid);
        }

        private void BuildButtonClickHandler(string sceneUid)
        {
            _buildSceneUid = sceneUid;
            _animator.Play(TripstagramBuildButtonPush);
            OnSceneBuildPreClicked.SafeInvoke();
        }

        [UsedImplicitly]
        private async void OnSceneBuildAnimationComplete()
        {
            foreach (var tripstagramSceneItem in _activeSceneItems.Values)
            {
                tripstagramSceneItem.PlayOutroAnimation();
            }

            await UniTask.Delay((int)(OutroAnimationLength * MathUtility.SecondsToMillis));
            OnSceneBuildClicked.SafeInvoke(_buildSceneUid);
        }

        private void SceneIconClickHandler(string sceneUid)
        {
            OnSceneIconClicked.SafeInvoke(sceneUid);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            _activeSceneItems.Clear();
            _scrollView?.onValueChanged.RemoveListener(OnScroll);
        }
    }
}