using System;
using BBB;
using BBB.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Tripstagram
{
    public class TripstagramSceneItem : BbbMonoBehaviour
    {
        private const string SceneItemOutro = "SceneItem_Outro";
        private const string SceneItemIdle = "SceneItem_Idle";

        [SerializeField] private GameObject _completedRoot;
        [SerializeField] private GameObject _inProgressRoot;
        [SerializeField] private GameObject _lockedRoot;
        [SerializeField] private Image _snapshotImage;
        [SerializeField] private Image _lockImage;
        [SerializeField] private Button _playButton;
        [SerializeField] private Button _viewButton;
        [SerializeField] private Button _buildButton;
        [SerializeField] private Button _iconButton;
        [SerializeField] private LocalizedTextPro[] _cityTitles;
        [SerializeField] private MaterialSwapper _materialSwapper;
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField] private Image _separator;
        [Serialize<PERSON>ield] private Animator _animator;
        [SerializeField] private Image _bottomLine;

        private Sprite _lockedSprite;
        private TripstagramSceneItemModel _tripstagramSceneItemModel;

        public event Action<string> OnPlayButtonClicked;
        public event Action<string> OnViewButtonClicked;
        public event Action<string> OnBuildButtonClicked;
        public event Action<string> OnSceneIconClicked;

        private void Awake()
        {
            CacheInitialValues();
        }

        public void PlayOutroAnimation()
        {
            _animator.Play(SceneItemOutro);
        }

        private void CacheInitialValues()
        {
            _materialSwapper.StoreOriginalValues();
            _lockedSprite ??= _snapshotImage.sprite;
        }

        public void Init(TripstagramSceneItemModel tripstagramSceneItemModel)
        {
            _animator.Play(SceneItemIdle);
            _tripstagramSceneItemModel = tripstagramSceneItemModel;
            InitButtons();
            InitBottomBar();
            InitIcon();
            InitNames();
            InitProgress();
        }

        private void InitProgress()
        {
            _progressBar.SetProgress((float)_tripstagramSceneItemModel.SceneProgress / _tripstagramSceneItemModel.SceneTaskCount);
            _progressText.text = $"{_tripstagramSceneItemModel.SceneProgress}/{_tripstagramSceneItemModel.SceneTaskCount}";
        }

        private void InitButtons()
        {
            _playButton.ReplaceOnClick(() => OnPlayButtonClicked.SafeInvoke(_tripstagramSceneItemModel?.SceneUid));
            _viewButton.ReplaceOnClick(() => OnViewButtonClicked.SafeInvoke(_tripstagramSceneItemModel?.SceneUid));
            _buildButton.ReplaceOnClick(() => OnBuildButtonClicked.SafeInvoke(_tripstagramSceneItemModel?.SceneUid));
            _iconButton.ReplaceOnClick(() =>
            {
                var state = _tripstagramSceneItemModel.TripstagramItemState;
                if (state is TripstagramItemState.InProgress)
                {
                    OnBuildButtonClicked.SafeInvoke(_tripstagramSceneItemModel?.SceneUid);
                }
                else
                {
                    OnSceneIconClicked.SafeInvoke(_tripstagramSceneItemModel?.SceneUid);
                }
            });
        }

        private void InitBottomBar()
        {
            var state = _tripstagramSceneItemModel.TripstagramItemState;
            _completedRoot.SetActive(state is TripstagramItemState.Completed);
            _inProgressRoot.SetActive(state is TripstagramItemState.InProgress);

            var isLocked = state is TripstagramItemState.Locked;
            _bottomLine.enabled = !isLocked;
            _lockImage.enabled = isLocked;
            _materialSwapper.SetValue(isLocked);
        }

        private void InitIcon()
        {
            _snapshotImage.sprite = _tripstagramSceneItemModel.SceneIcon ?? _lockedSprite;
        }

        private void InitNames()
        {
            foreach (var cityTitle in _cityTitles)
            {
                var textArgs = new object[]
                {
                    _tripstagramSceneItemModel.SceneOrder + 1,
                    _tripstagramSceneItemModel.SceneCityName,
                    _tripstagramSceneItemModel.SceneCountryName
                };
                cityTitle.SetTextId(_tripstagramSceneItemModel.SceneCityItemTitle, textArgs);
            }
        }

        protected override void OnDestroy()
        {
            _tripstagramSceneItemModel = null;
            OnPlayButtonClicked = null;
            OnViewButtonClicked = null;
            OnBuildButtonClicked = null;
            OnSceneIconClicked = null;
        }

        public void HideSeparator()
        {
            _separator.gameObject.SetActive(false);
        }
    }
}