using System;
using System.Collections.Generic;
using PBConfig;
using RSG;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class GenericNarrativeMessage : BbbMonoBehaviour
    {
        [SerializeField] private Transform _tipHolderTransform;
        [SerializeField] private Transform _tagHolderTransform;
        [SerializeField] private Transform _tagTextTransform;
        [SerializeField] private GlobalNarrativeSettings _narrativeSettings;
        [SerializeField] private LocalizedTextPro _tagText;
        [SerializeField] private LocalizedTextPro _messageText;
        [SerializeField] private Image _tagBackground;
        [SerializeField] private Image _tagShadow;
        [SerializeField] private Image _tagOutline;
        [SerializeField] private Animator _animator;

        private Promise _showingPromise;
        private Promise _hidingPromise;

        public void Setup(NarrativeCharacterWidget characterWidget, Phrase phrase)
        {
            var type = characterWidget.SpeechBubbleType;
            var colorPalette = characterWidget.SpeechBubbleColorPalette;

            switch (type)
            {
                case SpeechBubbleType.LeftHandGeneric:
                    SetFlip(1f);
                    break;
                case SpeechBubbleType.RightHandGeneric:
                    SetFlip(-1f);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(type), type, null);
            }

            var colorSettings = _narrativeSettings.GetSettings(colorPalette);
            _tagBackground.color = colorSettings.TagBackgroundColor;
            _tagShadow.color = colorSettings.TagShadowColor;
            _tagOutline.color = colorSettings.TagOutlineColor;
            _messageText.Text.color = colorSettings.MessageTextColor;

            transform.localPosition = characterWidget.MessagePosition;

            _tagText.SetTextId(phrase.NameTag);
            _messageText.SetTextId(phrase.Text);
        }

        public IPromise Show()
        {
            _showingPromise = new Promise();
            _animator.Rebind();
            gameObject.SetActive(true);
            _animator.SetTrigger("Show");
            return _showingPromise;
        }

        public IPromise Hide()
        {
            _hidingPromise = new Promise();
            _animator.SetTrigger("Hide");
            return _hidingPromise;
        }

        private void OnShownAnimationFinished()
        {
            _showingPromise?.Resolve();
            _showingPromise = null;
        }

        private void OnHiddenAnimationFinished()
        {
            _hidingPromise?.Resolve();
            _hidingPromise = null;
            gameObject.SetActive(false);
        }

        private void SetFlip(float flipScale)
        {
            var tipScale = _tipHolderTransform.localScale;
            tipScale.x = flipScale;
            _tipHolderTransform.localScale = tipScale;
            var tagScale = _tagHolderTransform.localScale;
            tagScale.x = flipScale;
            _tagHolderTransform.localScale = tagScale;
            var tagTextScale = _tagTextTransform.localScale;
            tagTextScale.x = flipScale;
            _tagTextTransform.localScale = tagTextScale;
        }

        public void Reset()
        {
            gameObject.SetActive(false);
            _animator.Rebind();
            _hidingPromise = null;
            _showingPromise = null;
        }

        public void FastForward()
        {
            _animator.ResetTrigger("Show");
            _animator.ResetTrigger("Hide");
            _animator.Play("Idle");
            OnShownAnimationFinished();
        }
    }
}