using BBB;
using BBB.DI;
using BBB.MMVibrations;
using BebopBee;

namespace Loading.Commands
{
    public class SetupUserRelatedCommand : CommandBase
    {

        public override void Execute(IContext context)
        {
            var incrementalContext = (IIncrementalContext)context;

            var vibrations = context.Resolve<IVibrationsWrapper>();

            var userSettings = new UserSettings(vibrations);
            incrementalContext.AddServiceToRegister<UserSettings>(userSettings);
            
            incrementalContext.RegisterContext();
            
            CurrentStatus = CommandStatus.Success;
        }
    }
}