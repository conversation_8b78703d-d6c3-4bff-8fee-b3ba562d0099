using BBB;
using BBB.DI;
using BebopBee;

namespace Loading.Commands
{
    public class WaitingForChangeScreenCommand : CommandBase
    {
        private IScreensManager _screensManager;
        public override float Progress => IsFinished() ? 1f : _screensManager?.TransitionProgress ?? 0f;

        protected override void CommandExecutionStart(IContext context)
        {
            MaxExecutionTimeInternal = 120;
            _screensManager = context.Resolve<IScreensManager>();
            _screensManager.OnScreenTransitionComplete -= OnScreenTransitionComplete;
            _screensManager.OnScreenTransitionComplete += OnScreenTransitionComplete;
            base.CommandExecutionStart(context);
        }

        private void OnScreenTransitionComplete(ScreenType screenType)
        {
            _screensManager.OnScreenTransitionComplete -= OnScreenTransitionComplete;
            CurrentStatus = CommandStatus.Success;
        }
    }
}