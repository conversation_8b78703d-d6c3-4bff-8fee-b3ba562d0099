using System;
using BBB;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BBB.Generic.Modal;
using BebopBee;

namespace Loading.Commands
{
    public class MigrationCheckCommand : CommandBase
    {
        private readonly Action<bool> _action;
        private IAccountManager _accountManager;
        private BrainCloudManager _brainCloudManager;
        private GenericModalFactory _genericModalFactory;
        private ILocalizationManager _localizationManager;
        private bool _promptMigration;
        private bool _isFirstLogin;

        public MigrationCheckCommand(Action<bool> action)
        {
            _action = action;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);
            _accountManager = context.Resolve<IAccountManager>();
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _genericModalFactory = context.Resolve<GenericModalFactory>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _isFirstLogin = _accountManager.IsFirstLogin();
        }
        
        private bool IsAuthenticated()
        {
            return _brainCloudManager.IsAuthenticated && _accountManager.HasUpdatedPlayer();
        }
        
        public override void Execute(IContext context)
        {
            base.Execute(context);
            
            if (!_isFirstLogin)
            {
                _action?.Invoke(false);
                CurrentStatus = CommandStatus.Success;
                return;
            }
            
            if (IsAuthenticated() && !_promptMigration)
            {
                _promptMigration = true;
                if (_accountManager.HasExternalGameData())
                {
                    // It's mandatory so we don't need to wait for user's input
                    _accountManager.LoadExternalGameData();
                    _accountManager.SetJustMigrated(true);
                    _genericModalFactory.ShowWithOkButtonOnly(_localizationManager.getLocalizedText("MIGRATION_CHECK_TITLE"), _localizationManager.getLocalizedText("MIGRATION_CHECK_DESC"), (_) =>
                    {
                        _action?.Invoke(true);
                        CurrentStatus = CommandStatus.Success;
                    });
                    _brainCloudManager.MarkMigrationAsRestore();
                }
                else
                {
                    _action?.Invoke(false);
                    CurrentStatus = CommandStatus.Success;
                }
            }
            
        }

        protected override void OnCommandExpired()
        {
            base.OnCommandExpired();
            // Even if the command is expired, we still want to process the migration if the user has external game data
            _action?.Invoke(_promptMigration);
        }
    }
}