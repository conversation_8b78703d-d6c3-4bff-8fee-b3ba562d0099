using System.Collections;
using System.Collections.Generic;
using System.IO;
using BBB.Core;
using UnityEngine;
using UnityEngine.Networking;

namespace BBB
{
    /// <summary>
    /// Copy all files included in build for POI cache content
    /// </summary>
    public class PoiCacheFiles
    {
        private static readonly string BuildCachePath = Path.Combine(Application.streamingAssetsPath, "Cached");
        public static readonly string CacheManifest = Path.Combine(Application.persistentDataPath, "cache_manifest.txt");
        private static readonly string StreamingManifest = Path.Combine(BuildCachePath, "cache_manifest.txt");


        private readonly HashSet<string> _cacheManifest;
        private readonly HashSet<string> _streamingManifest;

        public PoiCacheFiles()
        {
            _cacheManifest = new HashSet<string>();
            _streamingManifest = new HashSet<string>();
        }

        /// <summary>
        /// Start files copy process in background one by one as a Coroutine
        /// </summary>
        /// <param name="executor"></param>
        public void Initialize(ICoroutineExecutor executor)
        {
            executor.StartCoroutine(CopyFiles());
        }

        /// <summary>
        /// Read and filter existing files already copied into Persistent data
        /// </summary>
        /// <returns></returns>
        private IEnumerator CopyFiles()
        {
            yield return LoadCacheManifest();
            yield return LoadStreamingCacheManifest();
            _streamingManifest.ExceptWith(_cacheManifest);
            foreach (var img in _streamingManifest)
            {
                var filePath = Path.Combine(BuildCachePath, img);
                var destPath = Path.Combine(Application.persistentDataPath, img);
                yield return _CopyFile(filePath, destPath);
            }

            if (_streamingManifest.Count > 0)
                yield return _CopyFile(StreamingManifest, CacheManifest);
        }

        /// <summary>
        /// Do the actual copy of File having into consideration Android way to read from streaming assets folder
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="destPath"></param>
        /// <returns></returns>
        private IEnumerator _CopyFile(string filePath, string destPath)
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                var uwr = new UnityWebRequest(filePath);
                uwr.downloadHandler = new DownloadHandlerBuffer();
                yield return uwr.Send();
                if (uwr.isNetworkError || uwr.isHttpError)
                {
                    BDebug.Log(LogCat.Poi, $"_CopyFile {filePath} Error: {uwr.error}");
                }
                else
                {
                    BDebug.Log(LogCat.Poi, $"Copy Cached files {filePath} to {destPath}");
                    UnityEngine.Profiling.Profiler.BeginSample($"POICache WriteAllBytes [{filePath}]");
                    File.WriteAllBytes(destPath, uwr.downloadHandler.data);
                    UnityEngine.Profiling.Profiler.EndSample();
                }
            }
            else
            {
                BDebug.Log(LogCat.Poi, $"Copy Cached files {filePath} to {destPath}");
                UnityEngine.Profiling.Profiler.BeginSample($"POICache Copy [{filePath}]");
                File.Copy(filePath, destPath, true);
                UnityEngine.Profiling.Profiler.EndSample();
            }

            yield return null;
        }

        /// <summary>
        /// Read Cache manifest already in player's device
        /// </summary>
        /// <returns></returns>
        private IEnumerator LoadCacheManifest()
        {
            var path = CacheManifest;

            if (File.Exists(path))
            {
                var bytes = File.ReadAllLines(path);
                BDebug.Log(LogCat.Poi, $"LoadCacheManifest {string.Join(",", bytes)}");
                _cacheManifest.UnionWith(bytes);
            }

            yield return null;
        }

        /// <summary>
        /// Read Cache manifest included in the build
        /// </summary>
        /// <returns></returns>
        private IEnumerator LoadStreamingCacheManifest()
        {
            var path = StreamingManifest;

            if (Application.platform == RuntimePlatform.Android)
            {
                var uwr = new UnityWebRequest(path);
                uwr.downloadHandler = new DownloadHandlerBuffer();
                yield return uwr.Send();
                if (uwr.isNetworkError || uwr.isHttpError)
                {
                    Debug.Log(uwr.error);
                }
                else
                {
                    BDebug.Log(LogCat.Poi, $"LoadStreamingCacheManifest {uwr.downloadHandler.text}");
                    _streamingManifest.UnionWith(uwr.downloadHandler.text.Split('\n'));
                }
            }
            else
            {
                if (File.Exists(path))
                    _streamingManifest.UnionWith(File.ReadAllLines(path));
            }
        }
    }
}