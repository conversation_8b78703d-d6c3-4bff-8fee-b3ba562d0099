using System.Text;
using System.Threading;
using Cysharp.Threading.Tasks;
using Spin<PERSON>;
using UnityEngine;
using XNode;

namespace BBB.ActionGraph.ActionNodes
{
    [Node.CreateNodeMenu("Actions/Spine/Event Listener")]
    public class SpineEventListenerActionNode : PlaySpineAnimationActionNode
    {
        [SerializeField] private string _eventName;
        
        private bool _isTriggered;

        protected override async UniTask<bool> SpineEventSubscription(CancellationToken cancellationToken)
        {
            _currentTrackEntry.Event -= OnEvent;
            _currentTrackEntry.Event += OnEvent;
            _isTriggered = false;

            await UniTask.WaitWhile(this, state => !state._isTriggered, cancellationToken: cancellationToken);
            return true;
        }

        private void OnEvent(TrackEntry trackEntry, Spine.Event @event)
        {
            if (@event.Data.Name != _eventName) return;
            
            trackEntry.Event -= OnEvent;
            _isTriggered = true;
        }

#if UNITY_EDITOR
        public override bool IsValid(out string errorMessage)
        {
            var stringBuilder = new StringBuilder();
            if (!base.IsValid(out errorMessage))
            {
                stringBuilder.AppendLine(errorMessage);
            }
            
            if (_eventName == null)
            {
                stringBuilder.AppendLine("Event name is null.");
            }

            errorMessage = stringBuilder.ToString();
            return stringBuilder.Length == 0;
        }
#endif

        protected override void OnCanceled()
        {
            RemoveListener();
            base.OnCanceled();
        }
        
        private void RemoveListener()
        {
            if (_currentTrackEntry != null)
            {
                _currentTrackEntry.Event -= OnEvent;
            }
        }
    }
}