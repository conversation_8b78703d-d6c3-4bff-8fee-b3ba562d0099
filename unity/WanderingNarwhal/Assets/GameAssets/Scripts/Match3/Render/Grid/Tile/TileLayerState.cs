using System;

namespace BBB.Match3.Renderer
{
    [Flags]
    public enum TileLayerState : ulong
    {
        None           = 0,
        Normal         = 1L, //1,
        HorizontalLb   = 1L << 1, //2,
        VerticalLb     = 1L << 2, //4,
        Bomb           = 1L << 3, //8,
        ColorBomb      = 1L << 4, //16,
        Sticker        = 1L << 5, //32,
        DropItem       = 1L << 6, //64,
        Litter         = 1L << 7, //128,
        Chained        = 1L << 8, //256,
        Blinking       = 1L << 9, //512,
        IceCube        = 1L << 10, //1024,
        Undefined      = 1L << 11, //2048,
        Sand           = 1L << 13, //8192,
        Pinata         = 1L << 19, //524288,
        Frame          = 1L << 20, //1048576,
        Animal         = 1L << 21, //2097152,
        ColorCrate     = 1L << 22, //4194304,
        Watermelon     = 1L << 23, //8388608,
        Vase           = 1L << 24, //16777216,
        MoneyBag       = 1L << 25, //33554432,
        <PERSON>        = 1L << 26, //67108864,
        Egg            = 1L << 27, //134217728,
        <PERSON>           = 1L << 28, //268435456,
        Sheep          = 1L << 29,
        Banana         = 1L << 30,
        Monkey         = 1L << 31,
        Skunk          = 1L << 32,
        GameEventLabel = 1L << 33,
        Hen            = 1L << 34,
        Chicken        = 1L << 35,
        Hive           = 1L << 36,
        Bee            = 1L << 37, //137438953472
        Mole           = 1L << 38,
        Squid          = 1L << 39,
        Toad           = 1L << 40,
        StealingHatLabel = 1L << 41,
        Propeller      = 1L << 42,
        Bowling        = 1L << 43,
        Bush           = 1L << 44,
        Soda           = 1L << 45,
        MagicHat       = 1L << 46,
        Safe           = 1L << 47,
        FlowerPot      = 1L << 48,
        IceBar         = 1L << 49,
        DynamiteBox    = 1L << 50,
        GiantPinata    = 1L << 51,
        MetalBar       = 1L << 52,
        Shelf          = 1L << 53,
        JellyFish      = 1L << 54,
        GoldenScarab   = 1L << 55,
        Gondola        = 1L << 56,
        TukTuk         = 1L << 57,
        FireWorks      = 1L << 58,
        SlotMachine    = 1L << 59
    }
}
