
using BBB.RaceEvents;

namespace BBB
{
    public interface IRaceEventMatch3Manager
    {
        void HandleDebugWin();
        void ProcessOnLevelWin();
        void Setup(ILevel level);
        void ProcessOnExit(bool isStartedLevelPlay);
        void ProcessOnShuffleFailed();
        void ProcessOnLevelLose();
        bool MakeEventsPenalizable();
        bool IsAnyEventStreakBroken();
        bool IsAnyRaceEventOfType(RaceEventType type, out string eventUid);
        void AddScore(int score, bool append = false);
        int GetCollectEventScore();
        void ProcessOnFirstMove();
        bool IsAnyEventDoubleScoreActive();
        int GetHighestScoreMultiplier(bool includeCurrent);
    }
}