using System.IO;
using System.Linq;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using CleanupUtility;
using UnityEditor;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Editor
{
    public class CopyLevelAssetsToMainProgression
    {
        private const int SplitAmount = 50;
        
        [MenuItem("BebopBee/Main Progression/Copy levels from locations")]
        private static void CopeLevels()
        {
            var levelsDirectory = Path.Combine(Application.dataPath, "Levels");
            ConfigLoaderTool.Load(config =>
            {
                var levelsConfigDict = config.Get<FBConfig.ProgressionLevelConfig>();
                var locationConfigDict = config.Get<FBConfig.LocationConfig>();
                var orderedLevels = levelsConfigDict.Values.OrderBy(c => c.SortOrder);
                int number = 1;
                string targetDirectory = null;
                foreach (var levelConfig in orderedLevels)
                {
                    if (targetDirectory == null)
                    {
                        targetDirectory = Path.Combine(levelsDirectory,
                            levelConfig.GetMainProgressionFolder(SplitAmount));
                        Directory.CreateDirectory(targetDirectory);
                    }

                    if (levelConfig.LocationUid.IsNullOrEmpty()
                        || !locationConfigDict.TryGetValue(levelConfig.LocationUid, out var locationConfig)
                        || locationConfig.IsSideMapLocation)
                        continue;

                    var fileName = levelConfig.TrueFileNames()[0];
                    var path = "Levels/" + Level.GetLevelFilePath(levelConfig) + "/" + fileName + ".bytes";
                    var levelFullPath = Path.Combine(Application.dataPath, path);
                    var newFileName =  $"level_{number:00}_01.M3L.bytes";
                    var targetPath =  Path.Combine(targetDirectory, newFileName);
                    File.Copy(levelFullPath, targetPath, true);

                    if (number % SplitAmount == 0)
                        targetDirectory = null;
                    number++;
                }
            });
        }
    }
}