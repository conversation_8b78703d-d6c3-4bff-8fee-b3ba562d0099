using System;
using System.Reflection;
using BBB;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Utils;

namespace GameAssets.Scripts.Messages
{
    public interface IMessageListener : IDisposable
    {
        bool IsAlive { get; }
        bool EqualsDelegate(Delegate @delegate);
        object Target { get; }
    }
    
    public class MessageListener<T> : IMessageListener where T : IEvent
    {
        public bool IsAlive => _targetReference.IsAlive;

        public object Target => _targetReference.Target;
        public IDisposable Subscription { private get; set; }

        private readonly UnityWeakReference _targetReference;
        private readonly MethodInfo _methodInfo;
        private readonly object[] _args = new object[1];

        public MessageListener(Action<T> listener) : this(listener.Method, listener.Target) { }

        public MessageListener(Func<T, UniTask> listener): this(listener.Method, listener.Target) { }

        private MessageListener(MethodInfo method, object target)
        {
            _methodInfo = method;
            _targetReference = new UnityWeakReference(target);
        }

        public void Invoke(T data)
        {
            if (_targetReference.IsAlive)
            {
                _args[0] = data;
                _methodInfo.Invoke(_targetReference.Target, _args);
            }
        }
        
        public async UniTask InvokeAsync(T data)
        {
            if (_targetReference.IsAlive)
            {
                var asyncMethod = (Func<T, UniTask>)_methodInfo.CreateDelegate(typeof(Func<T, UniTask>), _targetReference.Target);
                await asyncMethod.Invoke(data);
            }
        }

        public bool EqualsDelegate(Delegate @delegate)
        {
            return _targetReference.IsAlive && 
                   _methodInfo.Equals(@delegate.Method) &&
                   _targetReference.Target.Equals(@delegate.Target);
        }

        public void Dispose()
        {
            Subscription?.Dispose();
        }
    }
}
