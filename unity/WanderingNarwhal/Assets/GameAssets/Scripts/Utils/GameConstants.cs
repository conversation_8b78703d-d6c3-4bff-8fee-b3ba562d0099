using System;
using System.Collections.Generic;
using BebopBee;

namespace GameAssets.Scripts.Utils
{
    public static class GameConstants
    {
        public const string GameName = "Travel Crush 2.0";
        public const string IosAppIdentifier = "com.bebopbee.match3.travelcrush";
        public const string AndroidAppIdentifier = "com.bebopbee.match3.travelcrush";
        private const string AppleAppStoreAppId = "6742204576";
        public static readonly string GooglePlayStoreAppUrl = $"market://details?id={AndroidAppIdentifier}";
        public static readonly string AppleAppStoreAppUrl = $"itms-apps://itunes.apple.com/app/{AppleAppStoreAppId}";

        public const string RegionCheckUrl = "https://ipapi.co/country/";
        public static readonly string AppStoreAppPageUrl =
#if UNITY_IOS
            AppleAppStoreAppUrl;
#elif UNITY_ANDROID
            GooglePlayStoreAppUrl;
#else
            string.Empty;
#endif

        #region Environments & Endpoints

        public static readonly Dictionary<Environments, string> ConfigHosts = new()
        {
            { Environments.Dev, "http://dev.redlion.bebopbee.com/admin" },
            { Environments.Staging, "http://staging.redlion.bebopbee.com/admin" },
            { Environments.Prod, "https://prod.tc.bebopbee.com/admin" }
        };

        public static readonly Dictionary<Environments, string> BucketNames = new()
        {
            { Environments.Dev, "dev.bundles.redlion.bebopbee.com" },
            { Environments.Staging, "staging.bundles.redlion.bebopbee.com" },
            { Environments.Prod, "bundles.tc.bebopbee.com" }
        };

        public const string LocalServer = "http://localhost:8080/";
#if BBB_LOCALSERVER
        public const string DevServer = LocalServer;
#else
        public const string DevServer = "http://dev.redlion.bebopbee.com/";
#endif
        public const string StagingServer = "http://staging.redlion.bebopbee.com/";
        public const string ProdServer = "https://prod.tc.bebopbee.com/";

        private const string ConfigsCacheDevServer = "https://dev.configs.redlion.bebopbee.com/";
        private const string ConfigsCacheStagingServer = "https://staging.configs.redlion.bebopbee.com/";
        private const string ConfigsCacheProdServer = "https://prod.configs.tc.bebopbee.com/";

        public static string RPCServerURL => RPCUrl.Value;

        public static string ConfigsCacheUrl => ConfigsCacheUrlSetting.Value;

        public static StringSetting RPCUrl => AppDefinesConverter.Prod
            ? new StringSetting("Server/URL", ProdServer)
            : AppDefinesConverter.Staging
                ? new StringSetting("Server/URL", StagingServer)
                : new StringSetting("Server/URL", DevServer);

        private static readonly StringSetting ConfigsCacheUrlSetting = AppDefinesConverter.Prod
            ? new StringSetting("ConfigsCache/URL", ConfigsCacheProdServer)
            : (AppDefinesConverter.Staging
                ? new StringSetting("ConfigsCache/URL", ConfigsCacheStagingServer)
                : new StringSetting("ConfigsCache/URL", ConfigsCacheDevServer));

        public static string AssetBundlesBaseDownloadUrl =>
            AppDefinesConverter.Prod ? "https://bundles.tc.bebopbee.com/" :
            AppDefinesConverter.Staging ? "http://staging.bundles.redlion.bebopbee.com/" :
            "http://dev.bundles.redlion.bebopbee.com/";

        [Obsolete]
        public const string RemoteAssetBaseUrl = "http://assets.winsor.Bebopbee.com/";
        [Obsolete]
        public const string RemoteAssetBundleUrl = "http://bundles.winsor.Bebopbee.com/";

        #endregion

        #region IronSource Keys

        public const string IronSourceAndroidAppKey = "2121409c5";
        public const string IronSourceIosAppKey = "21214434d";

        #endregion

        #region BrainCloud

        public const string BrainCloudServerUrl = "https://api.braincloudservers.com/dispatcherv2";
        public const string TestBrainCloudAppId = "14664";
        public const string TestSecretKey = "11938989-7fc7-42c4-9d43-f61abe304ca2";
        public const string DevBrainCloudAppId = "14589";
        public const string DevSecretKey = "56ccfa0b-295b-4713-9a94-bbd486cbc885";
        public const string ProdBrainCloudAppId = "15159";
        public const string ProdSecretKey = "92414e8b-ee73-43f8-827c-03eb2a416a34";

#if BBB_PROD
        public static readonly string BrainCloudAppId = ProdBrainCloudAppId;
        public static readonly string BrainCloudSecretKey = ProdSecretKey;
#else
        public static readonly string BrainCloudAppId = AppDefinesConverter.UnityEditor ? DevBrainCloudAppId : TestBrainCloudAppId;
        public static readonly string BrainCloudSecretKey = AppDefinesConverter.UnityEditor ? DevSecretKey : TestSecretKey;
#endif

        public const string BrainCloudS2SUrl = "https://api.braincloudservers.com/s2sdispatcher";

        public static readonly Dictionary<BcEnvironment, string> BrainCloudGameIds = new()
        {
            { BcEnvironment.DevEditor, DevBrainCloudAppId },
            { BcEnvironment.Test, TestBrainCloudAppId },
            { BcEnvironment.Prod, ProdBrainCloudAppId },
        };

        public static readonly Dictionary<BcEnvironment, string> BrainCloudGameSecret = new()
        {
            { BcEnvironment.DevEditor, "c64062e4-a9a5-4376-aaca-4c1773d1e258" },
            { BcEnvironment.Test, "c64062e4-a9a5-4376-aaca-4c1773d1e258" },
            { BcEnvironment.Prod, string.Empty },
        };

        public static readonly Dictionary<BcEnvironment, string> BrainCloudSecret = new()
        {
            { BcEnvironment.DevEditor, DevSecretKey },
            { BcEnvironment.Test, TestSecretKey },
            { BcEnvironment.Prod, ProdSecretKey }
        };

        #endregion

        #region Bugsnag

        public const string BugsnagApiKey = "86bef787e72ff9f2fd007140c163b88c";

        #endregion

        #region LeanPlum

        public const string LeanPlumAppId = "app_VVVHFTcn5dppGnPcMWEbKePveWgDtDP3rSFIvBz4NPc";
        public const string LeanPlumDevelopmentKey = "dev_o8T0KJ7DGBUlx7XIUpRuTKzEaP3K83qXxtNF7DOCU3Q";
        public const string LeanPlumProductionKey = "prod_rJaQmHa8mQbAVbKcDjxNGZkUe29LPZZlHc1BjSdZkTI";

        #endregion

        #region Zendesk

        public const string ZendeskKeyPayerStatusId = "30390677271959";
        public const string ZendeskKeyUserId = "30481513007639";
        public const string ZendeskKeyLevelId = "30390695540503";
        public const string ZendeskKeyGameId = "30390717200919";
        public const string ZendeskKeyAppVersion = "360037612934";
        public const string ZendeskKeyPlatform = "31398190210839";
        public const string ZendeskChannelId = "eyJzZXR0aW5nc191cmwiOiJodHRwczovL2JlYm9wYmVlaGVscC56ZW5kZXNrLmNvbS9tb2JpbGVfc2RrX2FwaS9zZXR0aW5ncy8wMUpORU0xRFFOWVlKTjlKRTZQTldDQUgzVi5qc29uIn0=";
        #endregion

        #region Media & External URLs

        public const string FennecAvatarUrl = "https://s3-us-west-2.amazonaws.com/assets.tc.bebopbee.com/thumbnails/fennec_avatar.png";
        public const string DailyTriviaDefaultNotificationImageUrl = "https://assets.tc.bebopbee.com/notifications/Captain_519x519.png";
        public const string YoutubeStandardUrlPrefix = "https://www.youtube.com/watch?v=";
        public const string YoutubeUrlFormat = "https://www.youtube.com/embed/{0}?mute=1&autoplay=1&showinfo=0&fs=0&loop=1&rel=0&modestbranding=1&autohide=1";
        public const string DefaultAvatarUrl = "https://s3-us-west-2.amazonaws.com/assets.tc.bebopbee.com/dummy_profiles/177.png";
        public const string DynamicLinkDefaultImageUrl = "https://s3.us-west-2.amazonaws.com/assets.tc.bebopbee.com/notifications/Captain_1038x1038.png";
        public const string OldAutoAvatar = "https://s3-us-west-2.amazonaws.com/assets.tc.bebopbee.com/thumbnails/fennec_avatar.png";

        #endregion

        #region Social

        public const string UrlSchema = "travelcrush";
        public const string DynamicLinkDomain = "travelcrush.sng.link";
        public const string DynamicLinkBasePath = "Eq3rn/436r";
        public const string DynamicLinkGamesUrl = "https://www.bebopbee.com/#games";
        public const string IosAppStoreId = "**********";
        public const string ItunesProviderToken = "*********";

        #endregion

        #region Facebook

        public const string FacebookAppId = "985981733458317";
        public const string FacebookClientToken = "********************************";
        public const bool FacebookAutoLogAppEventsEnabled = false;
        public const bool FacebookAdvertiserIDCollectionEnabled = false;

        #endregion

        #region Nunu

        public static readonly string[] NunuSchemes = { "els", "elx", "el" };

        #endregion

        #region Bundle Configs

        public const string BundleInfoConfigSpreadsheetId = "1mP6NZvOGEDMK6SavIHo6f3u3HZhnC6po4NGYYx5db2c";
        public const string BundleIndexConfigSpreadsheetId = "1vgoh7g5F7ypBO4eaEgr2V9QPRObmKIAtmeWZs7WsT-E";
        public const string SpriteAtlasIndexConfigSpreadsheetId = "1q1wEvFtRaMh1lvdDOvBcmp6IlueU-JwFH8OyvoGfrkw";

        #endregion
    }

    public enum Environments
    {
        Dev = 0,
        Staging,
        Prod
    }

    public enum BcEnvironment
    {
        DevEditor,
        Test,
        Prod
    }
}