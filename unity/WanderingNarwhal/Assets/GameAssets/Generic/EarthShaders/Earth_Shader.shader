Shader "BBB/Unlit/Earth"
{
	Properties
    {
        _MapTexture("Map color (RGB)", 2D) = "black" {}
        _NightMapTexture("Night Map color (RGB)", 2D) = "black" {}
        _Color("Color", Color) = (0, 0, 0, 1)
        
        _AtmoColor("Atmosphere Color", Color) = (0.5, 0.5, 1.0, 1)
        _AtmoColorNight("Atmosphere Night Color", Color) = (0.07, 0.12, 0.28, 1)
        
        // Glow stuffs
        _Size("Size", Float) = 0.1
        _GlowBias("Glow Bias", Range(0.5, 1)) = 0.6
        _GlowAtNight("Glow At Night", Range(0, 6)) = 0.6
        _GlowTransparency("Glow Transparency", Float) = 15
        // Atmosphere
        _Falloff("Falloff", Range(0, 20)) = 9.7
        _FalloffPlanet("Falloff Planet", Range(0, 10)) = 5.9
        _TransparencyPlanet("Transparency Planet", Range(0, 2)) = 1.1

        // Night
        _DayNightSoftness("Day/Night Softness", Range(0, 1)) = 0.62
        _DawnColor("Dawn Color", Color) = (0.58, 0.33, 0.26)
        _DawnIntensity("Dawn Intensity", Range(0, 1)) = 0.12
        //Overlay
        _OverlayColor("Overlay Color", Color) = (0, 0, 0, 1)
        _OverlayOpacity("Overlay Opacity", Range(-1, 1)) = -1
        _Opacity("Opacity", Range(0, 1)) = 1
    }
 
	SubShader
    {
		Tags {"Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }
        Pass
        {
            Name "PlanetBase"
            Cull Back
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite On
            CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag
 
                #pragma fragmentoption ARB_fog_exp2
                #pragma fragmentoption ARB_precision_hint_fastest
 
                #include "UnityCG.cginc"
 
                uniform sampler2D _MapTexture;
                uniform sampler2D _NightMapTexture;
                uniform float4 _MapTexture_ST;
                uniform float4 _NightMapTexture_ST;

                uniform float4 _Color;
                uniform float4 _AtmoColor;
                uniform float4 _AtmoColorNight;
                uniform float _FalloffPlanet;
                uniform float _TransparencyPlanet;
                uniform float _DayNightSoftness;
                uniform float4 _DawnColor;
                uniform float _DawnIntensity;
                uniform float4 _OverlayColor;
                uniform float _OverlayOpacity;
                uniform float _Opacity;

                struct v2f
                {
                    float4 pos : SV_POSITION;
                    float3 normal : TEXCOORD0;
                    float3 worldvertpos : TEXCOORD1;
                    float2 texcoord : TEXCOORD2;
                    float2 extra : TEXCOORD3;
                };
 
                v2f vert(appdata_base v)
                {
                    v2f o;
 
                    o.pos = UnityObjectToClipPos(v.vertex);
                    o.normal = UnityObjectToWorldDir(v.normal);
                    o.worldvertpos = UnityObjectToWorldDir(v.vertex.xyz);
                    o.texcoord = TRANSFORM_TEX(v.texcoord, _MapTexture);

                    float normPosition = (o.worldvertpos.x) / _DayNightSoftness;
                    normPosition = 0.5 - (normPosition * 0.5);
                    // vTerminator
                    o.extra.x = clamp(1.0 - normPosition, 0.0, 1.0);
                    // Sunrise Terminator
                    o.extra.y = clamp(cos(-2.0 + (o.extra.x * (4.0))), 0.0, 1.0) * _DawnIntensity;
                    return o;
                }
 
                float4 frag(v2f i) : COLOR
                {
                    float3 viewdir = normalize(i.worldvertpos - _WorldSpaceCameraPos);
 
                    float fresnel = pow( abs(1.0 + dot(viewdir, i.normal)) , _FalloffPlanet );
                    float vFresnel = clamp(fresnel * _TransparencyPlanet, 0.0, 1.0);

                    float4 mapcolor = tex2D(_MapTexture, i.texcoord) * _Color;
                    // TODO: Enable Day/Night effect
                    // float4 nightcolor = tex2D(_NightMapTexture, i.texcoord);

                    // mapcolor.rgb = lerp(nightcolor.rgb, mapcolor.rgb, i.extra.x);

                    // mapcolor.rgb = lerp(mapcolor.rgb, _DawnColor.rgb, i.extra.y);

                    // float3 dayNightFresnelColor = lerp(_AtmoColorNight.rgb, _AtmoColor.rgb, i.extra.x);
                    // mapcolor.rgb = lerp(mapcolor.rgb, dayNightFresnelColor.rgb, vFresnel );
                    mapcolor.rgb  = lerp(mapcolor.rgb, _OverlayColor.rgb , clamp(_OverlayOpacity - 0.3, 0.0, 1.0));
                    mapcolor.a  = _Opacity;

                    return mapcolor;
                }
            ENDCG
        }

        Tags {"LightMode" = "ForwardBase" "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }
        Pass
        {
            Name "PlaneGlow"
            Cull Front
            Blend SrcAlpha One
			ZWrite Off
 
            CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag
 
                #pragma fragmentoption ARB_fog_exp2
                #pragma fragmentoption ARB_precision_hint_fastest
 
                #include "UnityCG.cginc"
 
                uniform float4 _DawnColor;
                uniform float4 _AtmoColor;
                uniform float _Size;
                uniform float _Falloff;
                uniform float _GlowTransparency;
                uniform float _GlowAtNight;
                uniform float _DawnIntensity;
                uniform float _GlowBias;
                uniform float _Opacity;
 
                struct v2f
                {
                    float4 pos : SV_POSITION;
                    float3 normal : TEXCOORD0;
                    float3 extra : TEXCOORD1;
                };
 
                v2f vert(appdata_base v)
                {
                    v2f o;

                    v.vertex.xyz += v.normal * _Size;
                    o.pos = UnityObjectToClipPos(v.vertex);
                    o.normal = UnityObjectToWorldDir(v.normal);
                    float3 worldvertpos = UnityObjectToWorldDir(v.vertex.xyz);
                    
                    float normPosition = (worldvertpos.x) / _GlowAtNight;
                    normPosition = 0.5 - (normPosition * 0.5);
                    // vTerminator
                    o.extra.x = clamp(1.0 - normPosition, 0.0, 1.0);
                    // Sunrise Terminator
                    o.extra.y = clamp(cos(-3.0 + (o.extra.x * (6.0))), 0.0, 1.0) * _DawnIntensity;

                    float3 viewdir = normalize(worldvertpos - _WorldSpaceCameraPos);
                    
                    // Glow intensity
                    o.extra.z = pow( _GlowBias + dot(o.normal, viewdir), _Falloff );
                    
                    return o;
                }
 
                float4 frag(v2f i) : COLOR
                {
                    float4 color = _AtmoColor * clamp(i.extra.z, 0.0, 5.0);
                    
                    float3 mixcolor = clamp(_DawnColor.rgb * i.extra.z * i.extra.y, 0.0, 1.0);
                    color.rgb = lerp(color.rgb, mixcolor.rgb, 0.5);
					color.a *= _GlowTransparency;
                    color.a *= i.extra.x;
                    color.a *= _Opacity;

                    return color;
                }
            ENDCG
        }
    }
}
