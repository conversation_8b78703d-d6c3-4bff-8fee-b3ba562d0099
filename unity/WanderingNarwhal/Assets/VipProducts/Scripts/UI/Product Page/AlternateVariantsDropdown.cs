using BBB;
using UnityEngine;

namespace VipProducts.UI
{
    public class AlternateVariantsDropdown : BbbMonoBehaviour
    {
        public VariantsDropdown MainDropdown;
        public LocalizedTextPro OptionText;
        public LocalizedTextPro OptionNameText;

        [Tooltip("Use this field if this dropdown belongs to a big screen layout")]
        public SelectionManagerBigScreen selectionManagerBigScreen;

        public void SelectValue()
        {
            if (selectionManagerBigScreen == null) return;
            if (selectionManagerBigScreen.IsOpen())
            {
                if (selectionManagerBigScreen.HideSelectionCurrentTest(MainDropdown))
                    return;
            }

            selectionManagerBigScreen.ShowSelection();
            selectionManagerBigScreen.InitOptions(
                MainDropdown.Options, MainDropdown.OptionName,
                MainDropdown, MainDropdown.AllDropdowns);
        }
    }
}