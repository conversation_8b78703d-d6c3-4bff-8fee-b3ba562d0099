using System;
using UnityEngine;

namespace CleanupUtility.Editor
{
    [Serializable]
    public struct BundleCategoryData
    {
        public string categoryType;
        public int loadingOrder;
        public string bundleNameFilterRegex;
        public string bundleAssetPathFilterRegex;
        public string[] explicitBundlesList;
        [Multiline]
        public string comment;
    }

    /// <summary>
    /// Rules container for Asset Bundles Dependcies test.
    /// </summary>
    /// <remarks>
    /// Each bundle usually loads at roughly same 'stage' in game in each session.
    /// This happens when, for example, screen is loading or some modal is opening.
    ///
    /// For bundles that are loaded roughly at same time we can define a 'category', which will allow any dependency between bundles in same category.
    /// Each category has it's own 'order' number, which roughly represents order at which bundles in category are loading in game.
    /// Bundles in category with some certain order are allowed to have dependencies on bundles inside category with previous order,
    /// because they should have been already loaded. -VK 
    /// </remarks>
    [Serializable]
    public class BundlesDependenciesConfigData
    {
        public BundleCategoryData[] bundlesCategories = new BundleCategoryData[]
        {
            new BundleCategoryData()
            {
                categoryType = "Loading",
                loadingOrder = 1,
                explicitBundlesList = new string[]
                {
                    "ab_ui_defaulttransition",
                    "spriteatlas_loadinggenericatlas",
                    "spriteatlas_mainsceneatlas",
                    "ab_ui_screen_loading",
                    "ab_ui_screens",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "Landing",
                loadingOrder = 2,
                explicitBundlesList = new string[]
                {
                    "shareds",
                    "spriteatlas_genericatlas",
                    "spriteatlas_hudatlas",
                    "spriteatlas_landingatlas",
                    "ab_ui_transitions",
                    "ab_ui_screens_landingscreen",
                    "ab_ui_screen_landing",
                    "ab_ui_genericmaterials",
                    "ab_ui_genericprefabs",
                    "ab_p_generic",
                    "ab_ui_genericatlases",
                    "ab_ui_generic",
                    "ab_ui_genericaudio",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "Globe",
                loadingOrder = 3,
                explicitBundlesList = new string[]
                {
                    "ab_ui_screen_earth",
                    "spriteatlas_map",
                    "spriteatlas_fennecoscars_spriteatlas",
                    "ab_ui_tutorial",
                    "ab_ui_hud",
                    "spriteatlas_earth_atlas",
                    "ab_ui_screens_earthscreen",
                    "ab_ui_m_earth",
                    "spriteatlas_genericflags_20",
                    "spriteatlas_globelocationmarkatlas",
                    "spriteatlas_city_previews",
                    "ab_ui_earth",
                    "ab_ui_iap",
                    "ab_ui_narrative",
                    "ab_ui_settings",
                    "ab_ui_social",
                    "ab_ui_gacha",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "POI",
                loadingOrder = 4,
                explicitBundlesList = new string[]
                {
                    "spriteatlas_poi",
                    "ab_ui_poi",
                    "ab_ui_screen_poi",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "MapTransition",
                loadingOrder = 5,
                explicitBundlesList = new string[]
                {
                    "ab_ui_screens_mapscreen",
                    "ab_ui_map",
                    "spriteatlas_leveliconatlas",
                    "ab_ui_screen_map",
                    "ab_ui_screens_sidemapscreen"
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "MapTransitionDownloadables",
                loadingOrder = 5,
                explicitBundlesList = new string[0],
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "\\/Downloadable\\/",
            },
            new BundleCategoryData()
            {
                categoryType = "Map",
                loadingOrder = 8,
                explicitBundlesList = new string[]
                {
                    "ab_ui_m_iap",
                    "ab_ui_m_social",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "LevelDesigns",
                loadingOrder = 8,
                explicitBundlesList = new string[0],
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "\\/Levels\\/",
            },
            new BundleCategoryData()
            {
                categoryType = "LevelTransition",
                loadingOrder = 9,
                explicitBundlesList = new string[]
                {
                    "spriteatlas_match3overlays",
                    "spriteatlas_match3ui",
                    "spriteatlas_match3tiles",
                    "spriteatlas_match3tilesmain",
                    "ab_ui_match3",
                    "ab_ui_m_match3",
                    "ab_ui_goals",
                    "ab_ui_screen_match3",
                },
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
            new BundleCategoryData()
            {
                categoryType = "LevelScreen",
                loadingOrder = 10,
                explicitBundlesList = new string[0],
                bundleNameFilterRegex = "",
                bundleAssetPathFilterRegex = "",
            },
        };
    }
}