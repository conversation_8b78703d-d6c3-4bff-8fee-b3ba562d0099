using System.Collections.Generic;
using System.Linq;
using Assets.UltimateIsometricToolkit.Scripts.External;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace CleanupUtility.Editor
{
    public static class PixelPerfectFinder
    {
        [MenuItem("Assets/Find Pixel Perfect Canvases", false, 30)]
        private static void FindPixelPerfectCanvases()
        {
            FindInScenes();
            FindInPrefabs();
        }

        private static void FindInScenes()
        {
            var scenesNumber = SceneManager.sceneCountInBuildSettings;
            Debug.Log("Total number of scenes in build: " + scenesNumber);

            for (int i = 0; i < scenesNumber; i++)
            {
                EditorUtility.DisplayProgressBar("Searching for canvases in scenes", "Iterating scenes...", (float)i / scenesNumber);
                var scene = SceneManager.GetSceneByBuildIndex(i);

                if (!scene.IsValid())
                {
                    Debug.Log("Skipping invalid scene: " + scene.name);
                    continue;
				}

                Debug.Log("Checking scene: " + scene.name);
                List<GameObject> rootObjects = new List<GameObject>();
                scene.GetRootGameObjects(rootObjects);

                foreach (var rootObject in rootObjects)
                {
                    var canvases = rootObject.GetComponentsInChildren<Canvas>(true);
                    canvases.Where(x => x.pixelPerfect).ForEach(x => Debug.Log($"Pixel Perfect On: {rootObject.name}:{x.gameObject.name}", x.gameObject));
                }
            }

            EditorUtility.ClearProgressBar();
        }

        private static void FindInPrefabs()
        {
            var prefabGuids = AssetDatabase.FindAssets("t:gameobject");
            var prefabAssets = prefabGuids.Select(AssetDatabase.GUIDToAssetPath)
                .Select(AssetDatabase.LoadMainAssetAtPath).ToArray();

            Debug.Log("Total number of prefabs to iterate: " + prefabAssets.Length);

            int index = 0;
            foreach (var prefabAsset in prefabAssets)
            {
                EditorUtility.DisplayProgressBar("Searching for canvases in prefabs", "Iterating prefabs...", (float)index / prefabAssets.Length);

                var prefab = prefabAsset as GameObject;

                if (prefab == null)
                {
                    Debug.LogError("Couldn't convert prefab to gameobject: " + prefab.name);
                }
                else
                {
                    var canvases = prefab.GetComponentsInChildren<Canvas>(true);
                    canvases.Where(x => x.pixelPerfect).ForEach(x => Debug.Log($"Pixel Perfect On: {prefab.name}:{x.gameObject.name}", x.gameObject));
                }


                index++;
            }

            EditorUtility.ClearProgressBar();
        }
    }
}