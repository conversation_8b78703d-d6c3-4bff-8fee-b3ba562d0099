using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;

namespace BBB.UI.LoopScrollRect
{
    /// <summary>
    /// Animations that scroll items may play by themselves.
    /// Used for allowing per-item customization of scroll animations. 
    /// </summary>
    public enum LoopScrollListItemAnimationType
    {
        ScaleUp,
        ScaleToNormal,
    }

    /// <summary>
    /// Interface that is mandatory (must by implemented in one of the components)
    /// for any prefab that will be used as scroll list item. 
    /// </summary>
    public interface ILoopScrollListItem
    {
        /// <summary>
        /// Size is used for correct positioning of all list items inside Content parent.
        /// </summary>
        float listItemSize { get; }

        /// <summary>
        /// Real list index of item (not same as Visible list index).
        /// </summary>
        int listIndex { get; }

        RectTransform rectTransform { get; }

        GameObject gameObject { get; }

        PooledItem pooledItem { get; }

        /// <summary>
        /// Allows to check if this list item gameObject was not destroyed yet. 
        /// </summary>
        bool isAlive { get; }

        /// <summary>
        /// Initialize item on spawn.
        /// </summary>
        /// <param name="index">List index.</param>
        /// <param name="itemModel">Object of list item data.</param>
        /// <remarks>
        /// Allows to setup item visual state from current state data which is passed as itemModel object (this is individual way).
        /// Item also can be initialized in different way - by subscribing to spawn event in scroll rect,
        /// which will be invoked for each spawned item and it will pass item reference as event argument. (this is centralized way).
        /// </remarks>
        void OnInit(int index, object itemModel);

        /// <summary>
        /// Play animation on list item when scroll list is playing some global animation.
        /// </summary>
        /// <remarks>
        /// Scroll list item may participate in scroll list animations, which may be customized for each scroll item.
        /// </remarks>
        Tweener PlayAnimation(LoopScrollListItemAnimationType animation, ScrollListAnimationSettings _settings, float delay, TweenCallback onDone);

        void OnDragEvent(PointerEventData eventData);

        void OnRelease();
    }

    /// <summary>
    /// Loop scroll list item component.
    /// Attach this component to list item prefab if you don't have any other scripts with manually implemented list item interface. 
    /// </summary>
    [DisallowMultipleComponent]
    [RequireComponent(typeof(RectTransform))]
    public class LoopScrollListItem : BbbMonoBehaviour, ILoopScrollListItem
    {
        public event Action OnReleaseEvent;

        [SerializeField]
        protected Transform _scalingRoot;

        public float listItemSize => GetComponent<RectTransform>().rect.size.y;

        public int listIndex { get; private set; }

        public RectTransform rectTransform
        {
            get
            {
                if (_rectTransform == null)
                {
                    _rectTransform = GetComponent<RectTransform>();
                }

                return _rectTransform;
            }
        }

        private RectTransform _rectTransform;

        public PooledItem pooledItem
        {
            get
            {
                if (_pooledItem == null)
                {
                    _pooledItem = GetComponent<PooledItem>();
                }

                return _pooledItem;
            }
        }

        private PooledItem _pooledItem;

        public bool isAlive => this != null;

        public virtual void OnInit(int index, object itemModel) 
        {
            listIndex = index;
        }

        public virtual Tweener PlayAnimation(LoopScrollListItemAnimationType animationType, ScrollListAnimationSettings settings, float delay, TweenCallback onDone)
        {
            return GetDefaultScrollListItemScaleAnimation(_scalingRoot == null ? transform : _scalingRoot, animationType, settings, delay, onDone);
        }

        public virtual void OnDragEvent(PointerEventData eventData)
        {
        }

        public virtual void OnRelease()
        {
            OnReleaseEvent?.Invoke();
        }

        /// <summary>
        /// Get default scroll animations tween.
        /// </summary>
        /// <remarks>
        /// Main animation type for scroll list items is Scaling animation,
        /// and some list items may scale differently depending on layout.
        /// For example, social modal list items scale not the parent object but child pivot transform,
        /// because all list items parents have pivot always in the corner (and this distorts any try of scaling of the parent transform). 
        /// </remarks>
        public static Tweener GetDefaultScrollListItemScaleAnimation(Transform item, LoopScrollListItemAnimationType animation, ScrollListAnimationSettings _settings, float delay, TweenCallback onDone)
        {
            switch (animation)
            {
                case LoopScrollListItemAnimationType.ScaleUp:
                {
                    var endScale = new Vector3(1f, 1f, 1f) * _settings.scaleTransformUp;
                    var result = item.DOScale(endScale, _settings.animatedScaleUpDuration)
                        .SetEase(_settings.animatedScaleUpEasingType, _settings.animatedScaleUpEasingValue)
                        .OnComplete(() => { onDone?.Invoke(); });

                    if (delay > 0)
                    {
                        result.SetDelay(delay);
                    }

                    return result;
                }
                case LoopScrollListItemAnimationType.ScaleToNormal:
                {
                    var endScale = new Vector3(1f, 1f, 1f);
                    var result = item.DOScale(endScale, _settings.animatedScaleToNormalDuration)
                        .SetEase(_settings.animatedScaleToNormalEasingType, _settings.animatedScaleToNormalEasingValue)
                        .OnComplete(onDone);

                    if (delay > 0)
                    {
                        result.SetDelay(delay);
                    }

                    return result;
                }
            }

            return null;
        }
    }
}