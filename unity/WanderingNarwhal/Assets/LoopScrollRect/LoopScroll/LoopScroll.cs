using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace BBB.UI.LoopScrollRect
{
    [Serializable]
    public struct ListItemData
    {
        /// <summary>
        /// Model data for list item.
        /// Assign data so it will be passed to ListItem component when it will be spawned.
        /// </summary>
        public object model;

        /// <summary>
        /// Prefab for list item.
        /// If not assigned then it will never be spawned.
        /// </summary>
        public ILoopScrollListItem prefab;
    }

    [Serializable]
    public class ListItemInstanceData
    {
        /// <summary>
        /// Original model data of list item.
        /// </summary>
        public object model;

        /// <summary>
        /// Original prefab of list item.
        /// </summary>
        public ILoopScrollListItem prefab;

        /// <summary>
        /// List item instance reference.
        /// </summary>
        public ILoopScrollListItem listItem;

        /// <summary>
        /// List index in model (not same as visible list index).
        /// </summary>
        /// <remarks>
        /// This index always correspond to item in 'contentModel' list.
        /// </remarks>
        public int index;

        /// <summary>
        /// Original size of item.
        /// </summary>
        public float size;

        /// <summary>
        /// Is item spawned and placed by proper layout.
        /// </summary>
        public bool isInitialized;

        /// <summary>
        /// Is currently animating.
        /// Used to determine if we can un-spawn this item during scroll or not.
        /// </summary>
        public bool isAnimating;
    }

    public enum ScrollRestrictionType
    {
        None,
        Clamped,
        Elastic,
    }

    /// <summary>
    /// Scroll rect for UI that supports Looping and reusing of list items. 
    /// </summary>
    [DisallowMultipleComponent]
    [RequireComponent(typeof(RectTransform))]
    public class LoopScroll : MonoBehaviour, IInitializePotentialDragHandler, IBeginDragHandler, IEndDragHandler, IDragHandler, IScrollHandler
    {
        /// <summary>
        /// First item index of spawned list item.
        /// </summary>
        /// <remarks>
        /// Start index is pointer to the start of the range of the list that is currently visible in the scroll rect.
        /// For example:
        /// The list has 13 elements.
        /// IndexStart is 4 and visibleItemsCount is 4.
        /// This means that currently scroll rect contains 4 list items: 4, 5, 6, and 7 (total range is [0..12]).
        ///
        /// When scroll rect is moved up or down then indexes will change
        /// and new items will be added from one side and removed from another side in scene hierarchy of this scroll rect.
        ///
        /// Edge-cases:
        /// When list is out of view rect bounds, the amount of spawned items will be 0 and the index will be equal to first or last element of model array.
        /// 
        /// </remarks>
        public int VisibleIndexStart { get; private set; }

        /// <summary>
        /// Amount of currently visible items in view rect.
        /// </summary>
        public int VisibleItemsCount { get; private set; }

        /// <summary>
        /// Indicates if view should be scrollable or not.
        /// </summary>
        public bool Interactable { get => _interactable; set => _interactable = value; }

        private bool _isDragging;
        private float _velocity;

        [SerializeField]
        private bool _interactable = true;

        [SerializeField]
        private float _deAccelerationRate = 0.135f;

        [SerializeField]
        private bool _inertia = true;

        private float _previousPosition;

        [SerializeField]
        private ScrollRestrictionType _restrictionType = ScrollRestrictionType.Elastic;

        [SerializeField]
        private float _elasticity = 0.1f;

        [SerializeField]
        private float _elasticicOffsetMlt = 1f;

        [SerializeField]
        private RectTransform _content;

        public RectTransform content
        {
            get { return _content; }
            set { _content = value; }
        }

        public float contentPosition 
        {
            get { return _content.anchoredPosition.y; }
            set { _content.anchoredPosition = new Vector2(_content.anchoredPosition.x, value); }
        }

        [SerializeField]
        private RectTransform _viewRect;

        public RectTransform viewRect {
            get { return _viewRect; }
            set { _viewRect = value; }
        }
        
        /// <summary>
        /// Scroll UI element (arrow) which should be visible only if list is scrollable down.
        /// </summary>
        [SerializeField]
        [Tooltip("Scroll UI element (arrow) which should be visible only if list is scrollable down.")]
        private GameObject _scrollStateObject;

        public GameObject ScrollStateObject
        {
            get { return _scrollStateObject; }
            set { _scrollStateObject = value; }
        }

        /// <summary>
        /// New items spawn distance multiplier. Base value is current max size of list item.
        /// </summary>
        /// <remarks>
        /// Spawn distance is controlling how far new items are spawning or despawning
        /// from top ViewBound or from bottom ViewBound line.
        /// </remarks>
        [SerializeField]
        [Range(1f, 3f)]
        private float _spawnDistanceMultiplier = 2.1f;

        private Bounds _viewBounds = new Bounds();
        public Bounds viewBounds => _viewBounds;

        private Bounds _contentBounds = new Bounds();

        public Bounds contentBounds => _contentBounds;

        private readonly Vector3[] _corners = new Vector3[4];
        private float _spawnDistance;
        private bool _isListLayoutDirty;
        private Vector2 _dragStartPos;
        private Vector3 _contentStartPos;
        private float _offsetAtStartPos;

        /// <summary>
        /// List content data. Add your data here to make trigger automatic spawning of list items (on next update).
        /// You can call refresh method manually if you want to spawn items immediately (at current frame).
        /// </summary>
        /// <remarks>
        /// Scroll rect will automatically spawn and despawn visible list items based on current model, view rect and content rect.
        /// Spawned items placed in listInstances list.
        /// </remarks>
        public List<ListItemData> contentModel = new List<ListItemData>();

        private int _lastContentSize;

        /// <summary>
        /// Items sizes of model items.
        /// Always includes all items of the model (not only visible instances).
        /// </summary>
        /// <remarks>
        /// Item size is determined by it's prefab, and each item may have different prefab,
        /// For this reason all sizes must be cached.
        /// </remarks>
        private List<float> _itemsSizes = new List<float>();

        /// <summary>
        /// Local positions of model items in Content parent object.
        /// Always includes all items of the model (not only visible instances). 
        /// </summary>
        /// <remarks>
        /// Origin point of content is always on top, so local positions are always negative.
        /// 
        /// Local positions calculated from items sizes.
        /// Each item size may be different so local positions must be cached on every model update.
        /// </remarks>
        private List<float> _itemsAnchoredPositions = new List<float>();

        private float _itemsSizesSum;
        private readonly Dictionary<ILoopScrollListItem, float> _prefabsSizes = new Dictionary<ILoopScrollListItem, float>();
        public event Action<ILoopScrollListItem, object, int> onItemSpawnedEvent;
        public event Action<ILoopScrollListItem> onItemReleasedEvent;
        public event Action<float> onContentSizeChangedEvent;
        public event Action onDragStarted;
        public event Action onScrollEnded;

        private float _contentEndOffset;
        private float _contentStartOffset;

        [SerializeField]
        private List<ListItemInstanceData> _listInstances = new List<ListItemInstanceData>();

        /// <summary>
        /// List of scroll list instances. List size always correspond to the size of model list.
        /// Each list item may be spawned or may be not spawned due to out of view bounds. To check that you can use isInitialized property.
        /// </summary>
        /// <remarks>
        /// List of instances always contains same amount of elements as model.
        /// To get range of initialized items inside this list
        /// you can use property 'visibleIndexStart' and 'visibleItemsCount'.
        /// Also you can iterate whole list and check isInitialized property on each item (this is not optimal). 
        /// </remarks>
        public List<ListItemInstanceData> listInstances => _listInstances;

#if UNITY_EDITOR
        [SerializeField]
        private bool _debugAllowNormalizedPositionSlider;

        [SerializeField]
        [Range(0f,1f)]
        private float _debugNormalizedPosition;
#endif

        #region Pointer Handler Interfaces implementation

        public void OnInitializePotentialDrag(PointerEventData eventData)
        {
        }

        public void OnBeginDrag(PointerEventData eventData)
        {
            if (!_interactable)
                return;

            RectTransformUtility.ScreenPointToLocalPointInRectangle(_viewRect, eventData.position, eventData.pressEventCamera, out _dragStartPos);
            _contentStartPos = _content.anchoredPosition;
            _offsetAtStartPos = Mathf.Sign(CalcContentOutOfBoundsOffset());
            _isDragging = true;
            onDragStarted?.Invoke();
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            _isDragging = false;
        }

        public void OnDrag(PointerEventData eventData)
        {
            if (!_interactable)
                return;

            Vector2 dragCurrentPos;
            if (!RectTransformUtility.ScreenPointToLocalPointInRectangle(_viewRect, eventData.position, eventData.pressEventCamera, out dragCurrentPos))
                return;

            var delta = dragCurrentPos - _dragStartPos;
            if (_restrictionType == ScrollRestrictionType.Elastic)
            {
                RefreshContentBounds();
                var offset = CalcContentOutOfBoundsOffset();
                var offsetAbs = Mathf.Abs(offset);
                if (_offsetAtStartPos != offsetAbs)
                {
                    _offsetAtStartPos = offsetAbs;
                    _contentStartPos = _content.anchoredPosition;
                    _dragStartPos = dragCurrentPos;
                    delta = new Vector2();
                }

                if (!IsAlmostZero(offset))
                {
                    var normalizedOffset = offsetAbs / _viewBounds.size.y;
                    var elasticOffset = normalizedOffset * _elasticicOffsetMlt * 12;
                    var sign = elasticOffset > 0 ? 1f : -1f;
                    elasticOffset *= sign;
                    var elasticOffsetMlt = 1f / (elasticOffset * 0.5f + 1f);
                    delta.y *= elasticOffsetMlt;
                }
            }

            if (!IsAlmostZero(delta.y))
            {
                var pos = _contentStartPos;
                pos.y += delta.y;
                _content.anchoredPosition = pos;
            }

            foreach (var item in _listInstances)
            {
                if (item.listItem != null)
                {
                    item.listItem.OnDragEvent(eventData);
                }
            }
        }

        public void OnScroll(PointerEventData eventData)
        {
            // OnScroll interface implementation, which is not used.
            // But this is required in order for OnBehinDrag or OnEndDrag to work.
        }

        #endregion

        private void SpawnNextItem(ListItemInstanceData instanceContainer, ILoopScrollListItem prefab, object model, int listIndex)
        {
            if (prefab == null)
            {
                // Missing prefab will make list item not appear in the list, but everything else should work same
                // (as if list item exist and have size 0). -VK
                instanceContainer.index = listIndex;
            }
            else
            {
                var itemGO = SimpleGOPool.Instance.GetObjectFromPool(prefab.gameObject);
                var item = itemGO.GetComponent<ILoopScrollListItem>();
                var itemRect = itemGO.GetComponent<RectTransform>();
                itemRect.localScale = new Vector3(1, 1, 1);
                itemRect.SetParent(_content, worldPositionStays: false);
                itemGO.SetActive(true);

                instanceContainer.listItem = item;
                instanceContainer.prefab = prefab;
                instanceContainer.model = model;
                instanceContainer.size = item.listItemSize;
                instanceContainer.index = listIndex;

                if (instanceContainer.size > _spawnDistance)
                {
                    _spawnDistance = instanceContainer.size * _spawnDistanceMultiplier;
                }

                var width = _viewBounds.size.x;
                InitializeItemLayoutAndPosition(instanceContainer, width);
            }
        }

        public void ReleaseInstance(ListItemInstanceData instance)
        {
            ReleaseListItem(instance.listItem);
            instance.isInitialized = false;
            instance.isAnimating = false;
            instance.listItem = null;
            instance.prefab = null;
            instance.model = null;
        }

        private void ReleaseListItem(ILoopScrollListItem item)
        {
            if (item == null) return;
            var pooledItem = ((MonoBehaviour)item).gameObject;
            SimpleGOPool.Instance.ReturnObjectToPool(pooledItem);
            onItemReleasedEvent?.Invoke(item);
        }

        private bool AddItemAtStart()
        {
            int itemsCount = contentModel.Count;
            if (itemsCount == 0) return false;
            if (VisibleIndexStart <= 0) return false;
            VisibleIndexStart--;
            VisibleItemsCount++;
            if (VisibleIndexStart >= itemsCount)
            {
                VisibleIndexStart = itemsCount - 1;
            }

            var index     = VisibleIndexStart;
            var itemModel = contentModel[index];

            var instance = _listInstances[index];
            if (instance.isInitialized && instance.listItem != null && instance.isAnimating)
            {
                return true;
            }

            ReleaseInstance(instance);

            SpawnNextItem(instance, itemModel.prefab, itemModel.model, listIndex: index);

            if (instance.listItem != null)
            {
                instance.listItem.OnInit(index, itemModel.model);
                onItemSpawnedEvent?.Invoke(instance.listItem, itemModel.model, index);
            }

            return true;
        }

        private bool AddItemAtEnd()
        {
            int modelCount = contentModel.Count;
            if (modelCount == 0) return false;

            var index = VisibleIndexStart + VisibleItemsCount;

            if (index >= modelCount) return false;
            VisibleItemsCount++;
            var itemModel = contentModel[index];
            AlignInstanceList();
            var instance  = _listInstances[index];
            ReleaseInstance(instance);
            SpawnNextItem(instance, itemModel.prefab, itemModel.model, listIndex: index);

            if (instance.listItem != null)
            {
                instance.listItem.OnInit(index, itemModel.model);
                onItemSpawnedEvent?.Invoke(instance.listItem, itemModel.model, index);
            }

            return true;
        }

        private bool DeleteItemAtStart()
        {
            if (VisibleItemsCount <= 0) return false;
            var instance = _listInstances[VisibleIndexStart];
            if (instance.isAnimating) return false;
            ReleaseInstance(instance);
            VisibleIndexStart++;
            VisibleItemsCount--;
            return true;
        }

        private bool DeleteItemAtEnd()
        {
            if (VisibleItemsCount <= 0) return false;
            int last = VisibleIndexStart + VisibleItemsCount - 1;
            var instance = _listInstances[last];
            if (instance.isAnimating) return false;
            ReleaseInstance(instance);
            VisibleItemsCount--;
            return true;
        }

        private void RefreshBounds()
        {
            var viewRect = _viewRect.rect;
            _viewBounds.SetMinMax(viewRect.min, viewRect.max);
            RefreshContentBounds();
        }

        [ContextMenu("RefreshContentBounds")]
        public void RefreshContentBounds()
        {
            var toLocal = _viewRect.worldToLocalMatrix;

            // World corners contains 4 corners in clockwise order starting from bottom left.
            _content.GetWorldCorners(_corners);
            Vector3 min = toLocal.MultiplyPoint3x4(_corners[0]);
            Vector3 max = toLocal.MultiplyPoint3x4(_corners[2]);
            _contentBounds.SetMinMax(min, max);
        }

        /// <summary>
        /// Calls initialization of every visible list item.
        /// Also, if prefab of list item doesn't correspond to model's prefab (due to external changes in model),
        /// then list item will be replaced with correct prefab instance to match the model.
        /// This will not update the positions of list items. Call layout refresh manually or wait for next auto-update.
        /// </summary>
        [ContextMenu("ReinitializeVisibleListItems")]
        public void ReinitializeVisibleListItems()
        {
            AlignInstanceList();
            for (int i = 0; i < _listInstances.Count; i++)
            {
                var instance = _listInstances[i];
                bool isInRange = i >= VisibleIndexStart && i < VisibleIndexStart + VisibleItemsCount;
                if (isInRange)
                {
                    var model = contentModel[i];
                    if (instance.prefab != model.prefab)
                    {
                        ReleaseInstance(instance);
                        SpawnNextItem(instance, model.prefab, model.model, i);
                    }
                    else if (!ReferenceEquals(instance.model, model.model))
                    {
                        instance.model = model.model;
                    }

                    if (instance.listItem != null)
                    {
                        instance.listItem.OnInit(i, instance.model);
                        onItemSpawnedEvent?.Invoke(instance.listItem, instance.model, i);
                    }
                }
                else
                {
                    if (instance.listItem != null || instance.isInitialized)
                    {
                        ReleaseInstance(instance);
                    }
                }
            }
        }

        [ContextMenu("ClearAllVisibleListItems")]
        public void ClearAllVisibleListItems()
        {
            foreach (var instance in _listInstances)
            {
                ReleaseInstance(instance);
            }

            VisibleIndexStart = 0;
            VisibleItemsCount = 0;
            _listInstances.Clear();

            if (content.childCount > 0)
            {
                Debug.LogError($"Found leaked list items '{content.childCount}' in the list", content);
                for (int i = 0; i < content.childCount; i++)
                {
                    var child = content.GetChild(i);
                    if (child != null)
                    {
                        if (child.GetComponent<PooledItem>() != null)
                        {
                            SimpleGOPool.Instance.ReturnObjectToPool(child.gameObject);
                        }
                        else
                        {
                            Destroy(child.gameObject);
                        }
                    }
                }
            }

            _contentStartOffset = 0f;
            _contentEndOffset = 0f;
            _content.anchoredPosition = new Vector2(_content.anchoredPosition.x, 0);
        }

        private void AlignInstanceList()
        {
            if (_listInstances.Count < contentModel.Count)
            {
                do
                {
                    // Add uninitialized slot.
                    _listInstances.Add(new ListItemInstanceData() {});
                } while (_listInstances.Count < contentModel.Count);
            }
            else if (_listInstances.Count > contentModel.Count)
            {
                do
                {
                    var last = _listInstances.Count - 1;
                    var instance = _listInstances[last];
                    _listInstances.RemoveAt(last);
                    if (instance.listItem != null || instance.isInitialized)
                    {
                        ReleaseInstance(instance);
                    }
                } while (_listInstances.Count > contentModel.Count);
            }
        }

        private void RefreshVisibleListItems()
        {
            AlignInstanceList();
            var contentTop = _contentBounds.max.y; 
            if (contentTop - _contentEndOffset > _viewBounds.min.y)
            {
                if (AddItemAtEnd())
                {
                    _isListLayoutDirty = true;
                }
            }
            else if (contentTop - _contentEndOffset < _viewBounds.min.y - _spawnDistance)
            {
                _isListLayoutDirty = DeleteItemAtEnd();
            }

            if (!_isListLayoutDirty)
            {
                if (contentTop - _contentStartOffset < _viewBounds.max.y)
                {
                    if (AddItemAtStart())
                    {
                        _isListLayoutDirty = true;
                    }
                }
                else if (contentTop - _contentStartOffset > _viewBounds.max.y + _spawnDistance)
                {
                    _isListLayoutDirty |= DeleteItemAtStart();
                }
            }

            if (_scrollStateObject != null)
            {
                bool isContentFit = viewBounds.size.y >= contentBounds.size.y;
                _scrollStateObject.SetActive(!isContentFit && viewBounds.min.y > contentBounds.min.y);
            }
        }

        private void Start()
        {
            if (_viewRect.pivot != new Vector2(0.5f, 0.5f))
            {
                Debug.Log("Loop Scroll ViewRect' pivot must be (0.5;0.5)", _viewRect);
                _viewRect.pivot = new Vector2(0.5f, 0.5f);
            }

            if (_content.pivot != new Vector2(0, 1f))
            {
                Debug.Log("LoopScroll Content' pivot must be (0, 1)", _content);
                _content.pivot = new Vector2(0, 1f);
            }

            if (_content.GetComponent<VerticalLayoutGroup>() != null)
            {
                Debug.LogError("LoopScroll Content should not contain layout group components", _content);
                Destroy(_content.GetComponent<VerticalLayoutGroup>());
            }

            if (_content.GetComponent<ContentSizeFitter>() != null)
            {
                Debug.LogError("LoopScroll Content should not contain size fitter component", _content);
                Destroy(_content.GetComponent<ContentSizeFitter>());
            }
        }

        private void LateUpdate()
        {
            RefreshBounds();
            UpdateContentVelocityStep();
            RefreshVisibleListItemsAndLayout();
#if UNITY_EDITOR
            if (_debugAllowNormalizedPositionSlider)
            {
                SetContentNormalizedPosition(_debugNormalizedPosition);
            }
#endif
        }

        [ContextMenu("RefreshVisibleListItemsAndLayout")]
        public void RefreshVisibleListItemsAndLayout()
        {
            if (_lastContentSize != contentModel.Count)
            {
                _lastContentSize = contentModel.Count;
                RefreshVisibleLayout();
            }

            bool itemAddedOrRemoved;
            do
            {
                RefreshVisibleListItems();
                itemAddedOrRemoved = _isListLayoutDirty;
                RefreshVisibleLayoutIfNeeded();
            } while (itemAddedOrRemoved);
        }

        private void UpdateContentVelocityStep()
        {
            var offset = CalcContentOutOfBoundsOffset();
            var pos = _content.anchoredPosition;
            var initialPos = pos;
            UpdateContentPositionFromVelocity(ref pos, offset);

            if (_restrictionType == ScrollRestrictionType.Clamped)
            {
                pos.y = _content.anchoredPosition.y - offset;
            }

            if (!Mathf.Approximately(pos.y, initialPos.y) || !Mathf.Approximately(pos.x, initialPos.x))
            {
                _content.anchoredPosition = pos;
            }

            if (_isDragging && _inertia)
            {
                var newVelocity = (_content.anchoredPosition.y - _previousPosition) / Time.deltaTime;
                _velocity = Mathf.Lerp(_velocity, newVelocity, Time.deltaTime * 10);
            }

            _previousPosition = pos.y;
        }

        private void UpdateContentPositionFromVelocity(ref Vector2 pos, float offset)
        {
            if (_isDragging) return;
            if (_restrictionType == ScrollRestrictionType.Elastic && !IsAlmostZero(v: offset))
            {
                pos.y = Mathf.SmoothDamp(
                    current: pos.y,
                    target: pos.y - offset,
                    currentVelocity: ref _velocity,
                    smoothTime: _elasticity,
                    maxSpeed: Mathf.Infinity,
                    deltaTime: Time.deltaTime);
            }
            else if (_inertia)
            {
                _velocity *= Mathf.Pow(_deAccelerationRate, Time.deltaTime);
                if (_velocity < 1 && _velocity > -1)
                {
                    _velocity = 0;
                    onScrollEnded?.Invoke();
                }

                pos.y += _velocity * Time.deltaTime;
            }
            else
            {
                _velocity = 0;
                onScrollEnded?.Invoke();
            }
        }

        [ContextMenu("RefreshVisibleLayoutIfNeeded")]
        public void RefreshVisibleLayoutIfNeeded()
        {
            if (!_isListLayoutDirty) return;
            RefreshVisibleLayout();
        }

        [ContextMenu("RefreshVisibleLayout")]
        public void RefreshVisibleLayout()
        {
            _isListLayoutDirty = false;
            RefreshPrefabsSizesCache();
            RefreshItemsSizesCache();
            RefreshContentRootSize();
            RefreshVisibleItemsLayoutTransformPositions();
            DetermineContentVisibleOffsetBounds();
        }

        private void RefreshPrefabsSizesCache()
        {
            ILoopScrollListItem lastProcessedPrefab = null;
            foreach (var modelItem in contentModel)
            {
                if (modelItem.prefab != lastProcessedPrefab)
                {
                    lastProcessedPrefab = modelItem.prefab;
                    if (modelItem.prefab != null)
                    {
                        _prefabsSizes[modelItem.prefab] = modelItem.prefab.listItemSize;
                    }
                }
            }
        }

        private void RefreshItemsSizesCache()
        {
            AlignSizesCache();
            int index = 0;
            _itemsSizesSum = 0f;
            _itemsAnchoredPositions.Clear();
            foreach (var item in contentModel)
            {
                float size = 0f;

                // First item starts at 0 and list goes down.
                _itemsAnchoredPositions.Add(-_itemsSizesSum);
                if (item.prefab != null)
                {
                    size = _prefabsSizes[item.prefab];
                    _itemsSizesSum += size;
                }

                _itemsSizes[index] = size;
                index++;
            }
        }

        private void RefreshContentRootSize()
        {
            if (!Mathf.Approximately(_content.sizeDelta.y, _itemsSizesSum))
            {
                _content.sizeDelta = new Vector2(_content.sizeDelta.x, _itemsSizesSum);
                onContentSizeChangedEvent?.Invoke(_itemsSizesSum);
            }
        }

        private void DetermineContentVisibleOffsetBounds()
        {
            var distance = 0f;

            if (VisibleItemsCount <= 0)
            {
                if (VisibleIndexStart >= contentModel.Count)
                {
                    _contentStartOffset = _itemsSizesSum;
                    _contentEndOffset = _itemsSizesSum;
                }
                else if (VisibleIndexStart == 0)
                {
                    _contentStartOffset = 0;
                    _contentEndOffset = 0f;
                }
                else
                {
                    for (int i = 0; i < contentModel.Count; i++)
                    {
                        if (i == VisibleIndexStart)
                        {
                            _contentStartOffset = distance;
                            _contentEndOffset = distance;
                        }

                        distance += _itemsSizes[i];
                    }
                }
            }
            else
            {
                var indexEnd = VisibleIndexStart + VisibleItemsCount - 1;
                for (int i = 0; i < contentModel.Count; i++)
                {
                    if (i == VisibleIndexStart)
                    {
                        _contentStartOffset = distance;
                    }

                    distance += _itemsSizes[i];
                    if (i == indexEnd)
                    {
                        _contentEndOffset = distance;
                        break;
                    }
                }
            }
        }

        private void RefreshVisibleItemsLayoutTransformPositions()
        {
            var width = _viewBounds.size.x;
            foreach (var instance in _listInstances)
            {
                InitializeItemLayoutAndPosition(instance, width);
            }
        }

        private void InitializeItemLayoutAndPosition(ListItemInstanceData instance, float width)
        {
            if (!instance.isInitialized && instance.listItem != null)
            {
                var rect = instance.listItem.rectTransform;
                rect.anchorMax        = new Vector2(0, 1);
                rect.anchorMin        = new Vector2(0, 1);
                rect.pivot            = new Vector2(0, 1);
                rect.anchoredPosition = GetListItemContentTargetAnchoredPosition(instance.index);
                rect.sizeDelta        = new Vector2(width, instance.size);
                rect.localRotation = Quaternion.identity;
                instance.isInitialized = true;
            }
        }

        /// <summary>
        /// Move item at some index to different position in the model list.
        /// Item will be removed from 'index' and inserted at target index.
        /// Corresponding visible item instance will be also moved same way in the instances list.
        /// </summary>
        /// <remarks>
        /// Visually list will not change instantly, you need to update visual state accordingly.
        /// Also other items positions may became displaced due to changed amount of items above,
        /// this must be handled properly when this method is called.
        /// </remarks>
        public void ChangeModelItemListIndex(int index, int targetIndex)
        {
            var count = contentModel.Count;
            if (index < 0 && index >= count)
            {
                Debug.LogError($"Assert! Model index out of range {index} (total count={count})");
                return;
            }

            if (targetIndex < 0 || targetIndex >= count)
            {
                Debug.LogError($"Assert! Model index out of range {targetIndex} (total count={count})");
                return;
            }

            var instanceToMove = _listInstances[index];
            var modelToMove = contentModel[index];

            RemoveListItemFromModel(index, releaseVisibleItemIfExists: false);
            InsertListItemToModel(targetIndex, modelToMove, instanceToMove);
            RefreshItemsSizesCache();
        }

        /// <summary>
        /// Remove item from the model list and visible instances list.
        /// </summary>
        /// <param name="index">Target index.</param>
        /// <param name="releaseVisibleItemIfExists">Option to release visible instance when it is removed. You can disable this and release list item manually.</param>
        public void RemoveListItemFromModel(int index, bool releaseVisibleItemIfExists)
        {
            if (index >= 0 && index < contentModel.Count)
            {
                contentModel.RemoveAt(index);
                RemoveVisibleListItem(index, releaseVisibleItemIfExists);
                RefreshItemsSizesCache();
            }
        }

        public void RemoveVisibleListItem(int listIndex, bool releaseItem)
        {
            if (listIndex < VisibleIndexStart)
            {
                VisibleIndexStart--;
            } else if (listIndex < VisibleIndexStart + VisibleItemsCount)
            {
                VisibleItemsCount--;
            }

            var instance = _listInstances[listIndex];

            if (releaseItem)
            {
                if (instance.isInitialized || instance.listItem != null)
                {
                    ReleaseInstance(instance);
                }
            }

            _listInstances.RemoveAt(listIndex);
        }

        public void InsertListItemToModel(int index, ListItemData modelData, ListItemInstanceData instance = null)
        {
            if (index < 0)
            {
                return;
            }

            var instanceToInsert = instance ?? new ListItemInstanceData();

            if (index >= contentModel.Count)
            {
                contentModel.Add(modelData);
                _listInstances.Add(instanceToInsert);
                if (index <= VisibleIndexStart + VisibleItemsCount)
                {
                    VisibleItemsCount++;
                }
            }
            else
            {
                contentModel.Insert(index, modelData);
                _listInstances.Insert(index, instanceToInsert);
                if (index <= VisibleIndexStart)
                {
                    VisibleIndexStart++;
                }
                else if (index <= VisibleIndexStart + VisibleItemsCount)
                {
                    VisibleItemsCount++;
                }
            }
        }

        public void ForceSpawnItem(int listIndex, bool reSpawnIfAlreadyExists = true)
        {
            if (listIndex < 0 || listIndex >= contentModel.Count)
            {
                Debug.LogError($"Assert! isntance index out of range '{listIndex}'");
                return;
            }

            var model = contentModel[listIndex];
            var instance = _listInstances[listIndex];
            if (instance.isInitialized || instance.listItem != null)
            {
                if (!reSpawnIfAlreadyExists)
                {
                    return;
                }

                ReleaseInstance(instance);
            }

            SpawnNextItem(instance, model.prefab, model.model, listIndex);

            if (instance.listItem != null)
            {
                instance.listItem.OnInit(listIndex, instance.model);
                onItemSpawnedEvent?.Invoke(instance.listItem, instance.model, instance.index);
            }
        }

        /// <summary>
        /// Calculate content local position when given specific list item at specific local position. 
        /// </summary>
        /// <param name="itemIndex">Target list item.</param>
        /// <param name="itemLocalPosition">Item local position relative to view transform.</param>
        /// <remarks>
        /// The returned value of this method allows to perform simple check to determine this:
        /// if we know current content local position and target list index,
        /// the question is: is list item above some coordinate or below it?
        /// </remarks>
        public float CalcContentLocalPositionWhenListItemAtPosition(int itemIndex, float itemLocalPosition)
        {
            var result = 0f;
            var itemContentRelativePosition = 0f;
            if (itemIndex >= 0 && itemIndex < _itemsAnchoredPositions.Count)
            {
                itemContentRelativePosition = _itemsAnchoredPositions[itemIndex];
            }

            result = -(itemLocalPosition + itemContentRelativePosition - _viewBounds.size.y * 0.5f);
            return result;
        }

        public Vector2 GetListItemContentTargetAnchoredPosition(int listIndex)
        {
            var itemsCount = _itemsAnchoredPositions.Count;
            if (listIndex < 0)
            {
                if (itemsCount > 0)
                {
                    return new Vector2(0, _itemsAnchoredPositions[0]);
                }
            } else if (listIndex >= itemsCount)
            {
                if (itemsCount > 0)
                {
                    return new Vector2(0, _itemsAnchoredPositions[itemsCount - 1]);
                }
            }
            else
            {
                return new Vector2(0, _itemsAnchoredPositions[listIndex]);
            }

            return new Vector2();
        }

        private void AlignSizesCache()
        {
            var totalCount = contentModel.Count;
            if (_itemsSizes.Count != totalCount)
            {
                while (_itemsSizes.Count < totalCount)
                {
                    _itemsSizes.Add(0);
                }

                while (_itemsSizes.Count > totalCount)
                {
                    _itemsSizes.RemoveAt(_itemsSizes.Count - 1);
                }
            }
        }

        private float CalcContentOutOfBoundsOffset()
        {
            var contentMax = _contentBounds.max.y;
            var viewMax = _viewBounds.max.y;
            var result = 0f;
            if (contentMax < viewMax)
            {
                result = contentMax - viewMax;
            }
            else
            {
                if (_contentBounds.size.y > _viewBounds.size.y)
                {
                    var contentMin = _contentBounds.min.y;
                    var viewMin = _viewBounds.min.y;
                    if (contentMin > viewMin)
                    {
                        result = contentMin - viewMin;
                    }
                }
                else
                {
                    result = contentMax - viewMax;
                }
            }

            return result;
        }

        public float CalcNormalizedItemPosition(int listIndex)
        {
            if (_itemsSizesSum <= 0f || _itemsSizes.Count == 0) return 0f;

            var viewSize = _viewBounds.size.y;
            var viewHalfSize = viewSize * 0.5f;
            if (viewHalfSize <= 0f || Mathf.Approximately(viewHalfSize, _itemsSizesSum)) return 0f;

            float distance = 0f;
            for (int i = 0; i < listIndex && i < _itemsSizes.Count; i++)
            {
                distance += _itemsSizes[i];
            }

            if (listIndex < _itemsSizes.Count)
            {
                distance += _itemsSizes[listIndex] * 0.5f;
            }

            var ratio = (distance - viewHalfSize) / (_itemsSizesSum - viewSize);
            var result = Mathf.Clamp01(ratio);
            return result;
        }

        public float CalcContentLocalPositionAtNormalizedPosition(float normalizedItemPosition)
        {
            var viewSize  = _viewBounds.size.y;
            var maxOffset = _itemsSizesSum - viewSize;
            if (maxOffset <= 0f) maxOffset = 0f;
            return maxOffset * normalizedItemPosition + viewSize * 0.5f;
        }

        public void SetContentNormalizedPosition(float normalizedPosition)
        {
            normalizedPosition = Mathf.Clamp01(normalizedPosition);
            var localPos = _content.localPosition;
            var posY = CalcContentLocalPositionAtNormalizedPosition(normalizedPosition);
            if (localPos.y != posY)
            {
                localPos.y = posY;
                _content.localPosition = localPos;
            }

            _velocity = 0;
        }

        public void SetVelocity(float velocity)
        {
            _velocity = velocity;
        }

        private static bool IsAlmostZero(float v)
        {
            // This precision is more than enough for the scroll rect' algorithms.
            return v < 0.01f && v > -0.01f;
        }

        private static bool IsAlmostZero(Vector2 v)
        {
            return IsAlmostZero(v.x) && IsAlmostZero(v.y);
        }

#if UNITY_EDITOR
        private void OnDrawGizmos()
        {
            var origin = transform.position;

            Gizmos.color = Color.blue;
            Gizmos.DrawLine(origin + new Vector3(_viewBounds.min.x, _viewBounds.min.y), origin + new Vector3(_viewBounds.min.x, _viewBounds.max.y));
            Gizmos.DrawLine(origin + new Vector3(_viewBounds.min.x, _viewBounds.max.y), origin + new Vector3(_viewBounds.max.x, _viewBounds.max.y));
            Gizmos.DrawLine(origin + new Vector3(_viewBounds.max.x, _viewBounds.max.y), origin + new Vector3(_viewBounds.max.x, _viewBounds.min.y));
            Gizmos.DrawLine(origin + new Vector3(_viewBounds.max.x, _viewBounds.min.y), origin + new Vector3(_viewBounds.min.x, _viewBounds.min.y));

            Gizmos.color = Color.red;
            Gizmos.DrawLine(origin + new Vector3(_contentBounds.min.x, _contentBounds.min.y), origin + new Vector3(_contentBounds.min.x, _contentBounds.max.y));
            Gizmos.DrawLine(origin + new Vector3(_contentBounds.min.x, _contentBounds.max.y), origin + new Vector3(_contentBounds.max.x, _contentBounds.max.y));
            Gizmos.DrawLine(origin + new Vector3(_contentBounds.max.x, _contentBounds.max.y), origin + new Vector3(_contentBounds.max.x, _contentBounds.min.y));
            Gizmos.DrawLine(origin + new Vector3(_contentBounds.max.x, _contentBounds.min.y), origin + new Vector3(_contentBounds.min.x, _contentBounds.min.y));

            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(
                origin + new Vector3(_viewBounds.min.x, _viewBounds.max.y + _spawnDistance), 
                origin + new Vector3(_viewBounds.max.x, _viewBounds.max.y + _spawnDistance));
            Gizmos.DrawLine(
                origin + new Vector3(_viewBounds.min.x, _viewBounds.min.y - _spawnDistance),
                origin + new Vector3(_viewBounds.max.x, _viewBounds.min.y - _spawnDistance));

            Gizmos.color = Color.green;
            Gizmos.DrawLine(
                origin + new Vector3(_viewBounds.min.x - 1, _contentBounds.max.y - _contentStartOffset),
                origin + new Vector3(_viewBounds.max.x + 1, _contentBounds.max.y - _contentStartOffset));
            Gizmos.DrawLine(
                origin + new Vector3(_viewBounds.min.x - 1, _contentBounds.max.y - _contentEndOffset),
                origin + new Vector3(_viewBounds.max.x + 1, _contentBounds.max.y - _contentEndOffset));
        }
#endif
        // public void ForAllVisibleItems(Action<ILoopScrollListItem> action)
        // {
        //     for (int i = visibleIndexStart; i < visibleIndexStart + visibleItemsCount; i++)
        //     {
        //         var instance = _listInstances[i];
        //         if (instance.isInitialized || instance.listItem != null)
        //         {
        //             action(instance.listItem);
        //         }
        //     }
        // }
    }
}