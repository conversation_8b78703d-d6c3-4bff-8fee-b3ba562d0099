using UnityEngine.Events;
using UnityEngine.EventSystems;
using System;
using System.Collections;
using System.Collections.Generic;
using BBB;
using BBB.UI;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using UniRx;
using UnityEngine.UI.LoopScrollRectAnimations;

namespace UnityEngine.UI
{
    [Serializable]
    public class SrollItemData
    {
        public GameObject Prefab;
        public List<int> IndexsToShow;
        public int OverridePoolSize;

        public SrollItemData(GameObject prefab, List<int> indexToShow = null, int overridePoolSize = -1)
        {
            Prefab = prefab;
            IndexsToShow = indexToShow == null ? new List<int>() : indexToShow;
            OverridePoolSize = overridePoolSize;
        }

        public void AddIndexToShow(int index)
        {
            IndexsToShow.Add(index);
        }
    }

    [AddComponentMenu("")]
    [DisallowMultipleComponent]
    [RequireComponent(typeof(RectTransform))]
    public abstract class LoopScrollRectAdvanced : UIBehaviour, 
        IInitializePotentialDragHandler, 
        IBeginDragHandler, 
        IEndDragHandler, 
        IDragHandler, 
        IScrollHandler, 
        ICanvasElement, 
        ILayoutElement, 
        ILayoutGroup, 
        IScrollableEvents
    {
        #region IScrollableEvents implementation

        public event Action<IScrollable, int> OnNextItemInit = delegate { };

        #endregion

        //==========LoopScrollRect==========
        public delegate string prefabNameDelegate(int idx);
        public delegate int prefabCountDelegate(int idx);
        [Tooltip("Prefabs Names in Resources")]
        public List<SrollItemData> PrefabsList;
        [HideInInspector]
        public prefabNameDelegate prefabNameFunc = null;
        [HideInInspector]
        public prefabCountDelegate prefabCountFunc = null;
        [Tooltip("Total count, negative means INFINITE mode")]
        public int totalCount;
        [HideInInspector]
        public int poolSize = 5;
        [HideInInspector]
        [NonSerialized]
        public object[] objectsToFill = null;
        [Tooltip("Threshold for preloading")]
        public float threshold = 100;
        [Tooltip("Reverse direction for dragging")]
        public bool reverseDirection = false;
        [Tooltip("Rubber scale for outside")]
        public float rubberScale = 1;

        [SerializeField]
        private ScrolListItemAnimationManager.ScrollListAnimationSettings _animationsSettings = new ScrolListItemAnimationManager.ScrollListAnimationSettings();

        public ScrolListItemAnimationManager.ScrollListAnimationSettings AnimationsSettings => _animationsSettings;

        private ScrolListItemAnimationManager _animationManager;

        private ScrolListItemAnimationManager.LisItemsPositions _listItemsCachedPositions = new ScrolListItemAnimationManager.LisItemsPositions();

        private static readonly List<Transform> _animMovedItemsCache = new List<Transform>();

        private bool _debugDisplayCachedPositions = false;
        public int TotalSpawnedItemsCount => itemTypeEnd - itemTypeStart;

        protected int itemTypeStart = 0;
        protected int itemTypeEnd = 0;

        public int SpawnedItemStartIndex => itemTypeStart;
        public int SpawnedItemEndIndex => itemTypeEnd;

        private ContentSizeFitter _contentSizeFitter;
        private LayoutGroup _contentLayoutGroup;

        protected abstract float GetSize(RectTransform item);
        protected abstract float GetDimension(Vector2 vector);
        protected abstract Vector2 GetVector(float value);
        protected int directionSign = 0;

        private float m_ContentSpacing = -1;
        protected GridLayoutGroup m_GridLayout = null;

        private bool _disableReturnToPoolOnScroll = false;

        public bool isEnabledScroll = true;

        private float _elementSizeY;

        public bool IsPlayingAnimation
        {
            get {return _animationManager.IsPlayingAnimation;}
        }

        protected float contentSpacing
        {
            get
            {
                if (m_ContentSpacing >= 0)
                {
                    return m_ContentSpacing;
                }
                m_ContentSpacing = 0;
                if (content != null)
                {
                    HorizontalOrVerticalLayoutGroup layout1 = content.GetComponent<HorizontalOrVerticalLayoutGroup>();
                    if (layout1 != null)
                    {
                        m_ContentSpacing = layout1.spacing;
                    }
                    m_GridLayout = content.GetComponent<GridLayoutGroup>();
                    if (m_GridLayout != null)
                    {
                        m_ContentSpacing = GetDimension(m_GridLayout.spacing);
                    }
                }
                return m_ContentSpacing;
            }
        }

        private int m_ContentConstraintCount = 0;
        protected int contentConstraintCount
        {
            get
            {
                if (m_ContentConstraintCount > 0)
                {
                    return m_ContentConstraintCount;
                }
                m_ContentConstraintCount = 1;
                if (content != null)
                {
                    GridLayoutGroup layout2 = content.GetComponent<GridLayoutGroup>();
                    if (layout2 != null)
                    {
                        if (layout2.constraint == GridLayoutGroup.Constraint.Flexible)
                        {
                            Debug.LogWarning("[LoopScrollRect] Flexible not supported yet");
                        }
                        m_ContentConstraintCount = layout2.constraintCount;
                    }
                }
                return m_ContentConstraintCount;
            }
        }

        protected virtual bool UpdateItems(Bounds viewBounds, Bounds contentBounds) { return false; }
        //==========LoopScrollRect==========

        public enum MovementType
        {
            Unrestricted, // Unrestricted movement -- can scroll forever
            Elastic, // Restricted but flexible -- can go past the edges, but springs back in place
            Clamped, // Restricted movement where it's not possible to go past the edges
        }

        public enum ScrollbarVisibility
        {
            Permanent,
            AutoHide,
            AutoHideAndExpandViewport,
        }

        [Serializable]
        public class ScrollRectEvent : UnityEvent<Vector2> { }

        [SerializeField]
        private RectTransform m_Content;
        public RectTransform content { get { return m_Content; } set { m_Content = value; } }

        [SerializeField]
        private bool m_Horizontal = true;
        public bool horizontal { get { return m_Horizontal; } set { m_Horizontal = value; } }

        [SerializeField]
        private bool m_Vertical = true;
        public bool vertical { get { return m_Vertical; } set { m_Vertical = value; } }

        [SerializeField]
        private MovementType m_MovementType = MovementType.Elastic;
        public MovementType movementType { get { return m_MovementType; } set { m_MovementType = value; } }

        [SerializeField]
        private float m_Elasticity = 0.1f; // Only used for MovementType.Elastic
        public float elasticity { get { return m_Elasticity; } set { m_Elasticity = value; } }

        [SerializeField]
        private bool m_Inertia = true;
        public bool inertia { get { return m_Inertia; } set { m_Inertia = value; } }

        [SerializeField]
        private float m_DecelerationRate = 0.135f; // Only used when inertia is enabled
        public float decelerationRate { get { return m_DecelerationRate; } set { m_DecelerationRate = value; } }

        [SerializeField]
        private float m_ScrollSensitivity = 1.0f;
        public float scrollSensitivity { get { return m_ScrollSensitivity; } set { m_ScrollSensitivity = value; } }

        [SerializeField]
        private RectTransform m_Viewport;
        public RectTransform viewport { get { return m_Viewport; } set { m_Viewport = value; SetDirtyCaching(); } }

        [SerializeField]
        private Scrollbar m_HorizontalScrollbar;
        public Scrollbar horizontalScrollbar
        {
            get
            {
                return m_HorizontalScrollbar;
            }
            set
            {
                if (m_HorizontalScrollbar)
                    m_HorizontalScrollbar.onValueChanged.RemoveListener(SetHorizontalNormalizedPosition);
                m_HorizontalScrollbar = value;
                if (m_HorizontalScrollbar)
                    m_HorizontalScrollbar.onValueChanged.AddListener(SetHorizontalNormalizedPosition);
                SetDirtyCaching();
            }
        }

        [SerializeField]
        private Scrollbar m_VerticalScrollbar;
        public Scrollbar verticalScrollbar
        {
            get
            {
                return m_VerticalScrollbar;
            }
            set
            {
                if (m_VerticalScrollbar)
                    m_VerticalScrollbar.onValueChanged.RemoveListener(SetVerticalNormalizedPosition);
                m_VerticalScrollbar = value;
                if (m_VerticalScrollbar)
                    m_VerticalScrollbar.onValueChanged.AddListener(SetVerticalNormalizedPosition);
                SetDirtyCaching();
            }
        }

        [SerializeField]
        private ScrollbarVisibility m_HorizontalScrollbarVisibility;
        public ScrollbarVisibility horizontalScrollbarVisibility { get { return m_HorizontalScrollbarVisibility; } set { m_HorizontalScrollbarVisibility = value; SetDirtyCaching(); } }

        [SerializeField]
        private ScrollbarVisibility m_VerticalScrollbarVisibility;
        public ScrollbarVisibility verticalScrollbarVisibility { get { return m_VerticalScrollbarVisibility; } set { m_VerticalScrollbarVisibility = value; SetDirtyCaching(); } }

        [SerializeField]
        private float m_HorizontalScrollbarSpacing;
        public float horizontalScrollbarSpacing { get { return m_HorizontalScrollbarSpacing; } set { m_HorizontalScrollbarSpacing = value; SetDirty(); } }

        [SerializeField]
        private float m_VerticalScrollbarSpacing;
        public float verticalScrollbarSpacing { get { return m_VerticalScrollbarSpacing; } set { m_VerticalScrollbarSpacing = value; SetDirty(); } }

        [SerializeField]
        private ScrollRectEvent m_OnValueChanged = new ScrollRectEvent();
        public ScrollRectEvent onValueChanged { get { return m_OnValueChanged; } set { m_OnValueChanged = value; } }

        // The offset from handle position to mouse down position
        private Vector2 m_PointerStartLocalCursor = Vector2.zero;
        private Vector2 m_ContentStartPosition = Vector2.zero;

        private RectTransform m_ViewRect;

        protected RectTransform viewRect
        {
            get
            {
                if (m_ViewRect == null)
                    m_ViewRect = m_Viewport;
                if (m_ViewRect == null)
                    m_ViewRect = (RectTransform)transform;
                return m_ViewRect;
            }
        }

        private Bounds m_ContentBounds;
        private Bounds m_ViewBounds;

        private Vector2 m_Velocity;
        public Vector2 velocity { get { return m_Velocity; } set { m_Velocity = value; } }

        private bool m_Dragging;

        private Vector2 m_PrevPosition = Vector2.zero;
        private Bounds m_PrevContentBounds;
        private Bounds m_PrevViewBounds;
        [NonSerialized]
        private bool m_HasRebuiltLayout = false;

        private bool m_HSliderExpand;
        private bool m_VSliderExpand;
        private float m_HSliderHeight;
        private float m_VSliderWidth;

        [System.NonSerialized]
        private RectTransform m_Rect;
        private RectTransform rectTransform
        {
            get
            {
                if (m_Rect == null)
                    m_Rect = GetComponent<RectTransform>();
                return m_Rect;
            }
        }

        private RectTransform m_HorizontalScrollbarRect;
        private RectTransform m_VerticalScrollbarRect;

        /// <summary>
        /// Cache of temporary object, which are created in animation, and needs to be cleared if animation was interrupted.
        /// </summary>
        private List<GameObject> _tempObjectsCache = new List<GameObject>();

        /// <summary>
        /// Cache of objects with temporary canvas group component (created for animation), which needs to be removed if animation was interrupted.
        /// </summary>
        private List<CanvasGroup> _tempCanvasGroupObjectsCache = new List<CanvasGroup>();

        private DrivenRectTransformTracker m_Tracker;

        protected LoopScrollRectAdvanced()
        {
            flexibleWidth = -1;
            _animationManager = new ScrolListItemAnimationManager(_animationsSettings, _listItemsCachedPositions);
        }

        //==========LoopScrollRect==========
        private void SendMessageToNewObject(Transform go, int idx)
        {
            go.SendMessage("ScrollCellIndex", idx);
            if (objectsToFill != null)
            {
                object o = null;
                if (idx >= 0 && idx < objectsToFill.Length)
                {
                    o = objectsToFill[idx];
                }
                go.SendMessage("ScrollCellContent", o, SendMessageOptions.DontRequireReceiver);
            }
        }

        private void SendMessageToNewObject(IScrollable item, int idx)
        {
            if (item == null)
            {
                Debug.LogError("You need to add IScrollable impementation to your scrollable item script");
            }

            item.InitItem(idx);
            if (OnNextItemInit != null)
                OnNextItemInit(item, idx);
        }

        private void ReturnObjectAndSendMessage(Transform go)
        {
            var scrollable = go.GetComponent<IScrollable>();
            if(scrollable != null)
                scrollable.UninitItem();
            SimpleGOPool.Instance.ReturnObjectToPool(go.gameObject);
        }

        public void ClearCells()
        {
            _animationManager.StopAllAnimations(disposeCallbacks: true);
            StopAllCoroutines();
            CleanTempObjects();
            _animationManager.SetActiveLayout(true);
            if (Application.isPlaying)
            {
                itemTypeStart = 0;
                itemTypeEnd = 0;
                totalCount = 0;
                objectsToFill = null;
                for (int i = content.childCount - 1; i >= 0; i--)
                {
                    ReturnObjectAndSendMessage(content.GetChild(i));
                }
            }
        }

        public void RefreshCells()
        {
            if (!Application.isPlaying || !this.isActiveAndEnabled)
            {
                return;
            }
            itemTypeEnd = itemTypeStart;

            // recycle items if we can
            for (int i = 0; i < content.childCount; i++)
            {
                if (itemTypeEnd < totalCount)
                {
                    var item = content.GetChild(i).GetComponent<IScrollable>();
                    SendMessageToNewObject(item, itemTypeEnd);
                    itemTypeEnd++;
                }
                else
                {
                    ReturnObjectAndSendMessage(content.GetChild(i));
                    i--;
                }
            }
        }

        public void RefillCellsFromEnd(int offset = 0)
        {
            //TODO: unsupported for Infinity or Grid yet
            if (!Application.isPlaying || totalCount < 0 || contentConstraintCount > 1 || PrefabsList.Count == 0)
                return;

            StopMovement();
            itemTypeEnd = reverseDirection ? offset : totalCount - offset;
            itemTypeStart = itemTypeEnd;

            for (int i = m_Content.childCount - 1; i >= 0; i--)
            {
                ReturnObjectAndSendMessage(m_Content.GetChild(i));
            }

            float sizeToFill = 0, sizeFilled = 0;
            if (directionSign == -1)
                sizeToFill = viewRect.rect.size.y;
            else
                sizeToFill = viewRect.rect.size.x;

            while (sizeToFill > sizeFilled)
            {
                float size = reverseDirection ? NewItemAtEnd() : NewItemAtStart();
                if (size <= 0) break;
                sizeFilled += size;
            }

            Vector2 pos = m_Content.anchoredPosition;
            float dist = Mathf.Max(0, sizeFilled - sizeToFill);
            if (reverseDirection)
                dist = -dist;
            if (directionSign == -1)
                pos.y = dist;
            else if (directionSign == 1)
                pos.x = dist;
            m_Content.anchoredPosition = pos;
        }

        /// <summary>
        /// Delete and create all cells to refresh everything;
        /// </summary>
        public void RefillCells(int offset = 0)
        {
            if (!Application.isPlaying || PrefabsList.Count == 0)
                return;

            StopMovement();
            itemTypeStart = reverseDirection ? totalCount - offset : offset;
            itemTypeEnd = itemTypeStart;

            // Don't `Canvas.ForceUpdateCanvases();` here, or it will new/delete cells to change itemTypeStart/End
            for (int i = m_Content.childCount - 1; i >= 0; i--)
            {
                ReturnObjectAndSendMessage(m_Content.GetChild(i));
            }

            float sizeToFill = 0, sizeFilled = 0;
            // m_ViewBounds may be not ready when RefillCells on Start
            if (directionSign == -1)
                sizeToFill = viewRect.rect.size.y;
            else
                sizeToFill = viewRect.rect.size.x;

            while (sizeToFill > sizeFilled)
            {
                float size = reverseDirection ? NewItemAtStart() : NewItemAtEnd();
                if (size <= 0) break;
                sizeFilled += size;
            }

            Vector2 pos = m_Content.anchoredPosition;
            if (directionSign == -1)
                pos.y = 0;
            else if (directionSign == 1)
                pos.x = 0;
            m_Content.anchoredPosition = pos;
        }

        protected float NewItemAtStart()
        {
            if (totalCount >= 0 && itemTypeStart - contentConstraintCount < 0 || PrefabsList.Count == 0)
            {
                return 0;
            }
            float size = 0;
            for (int i = 0; i < contentConstraintCount; i++)
            {
                itemTypeStart--;
                RectTransform newItem = InstantiateNextItem(itemTypeStart);
                newItem.SetAsFirstSibling();
                size = Mathf.Max(GetSize(newItem), size);
            }

            if (!reverseDirection)
            {
                Vector2 offset = GetVector(size);
                content.anchoredPosition += offset;
                m_PrevPosition += offset;
                m_ContentStartPosition += offset;
            }
            return size;
        }

        protected float DeleteItemAtStart()
        {
            if ((totalCount >= 0 && TotalSpawnedItemsCount < 1) || content.childCount == 0)
            {
                return 0;
            }

            float size = 0;
            for (int i = 0; i < contentConstraintCount; i++)
            {
                RectTransform oldItem = content.GetChild(0) as RectTransform;
                size = Mathf.Max(GetSize(oldItem), size);
                ReturnObjectAndSendMessage(oldItem);

                itemTypeStart++;

                if (content.childCount == 0)
                {
                    break;
                }
            }

            if (!reverseDirection)
            {
                Vector2 offset = GetVector(size);
                content.anchoredPosition -= offset;
                m_PrevPosition -= offset;
                m_ContentStartPosition -= offset;
            }
            return size;
        }


        protected float NewItemAtEnd()
        {
            if (totalCount >= 0 && itemTypeEnd >= totalCount || PrefabsList.Count == 0)
            {
                return 0;
            }
            float size = 0;
            // issue 4: fill lines to end first
            int count = contentConstraintCount - (content.childCount % contentConstraintCount);
            for (int i = 0; i < count; i++)
            {
                RectTransform newItem = InstantiateNextItem(itemTypeEnd);
                size = Mathf.Max(GetSize(newItem), Mathf.Max(size, newItem.sizeDelta.y));
                itemTypeEnd++;
                if (_elementSizeY < size)
                {
                    _elementSizeY = size;
                }

                if (totalCount >= 0 && itemTypeEnd >= totalCount)
                {
                    break;
                }
            }

            if (reverseDirection)
            {
                Vector2 offset = GetVector(size);
                content.anchoredPosition -= offset;
                m_PrevPosition -= offset;
                m_ContentStartPosition -= offset;
            }
            return size;
        }

        protected float DeleteItemAtEnd()
        {
            if ((totalCount >= 0 && TotalSpawnedItemsCount < contentConstraintCount) 
                || content.childCount == 0
                || _disableReturnToPoolOnScroll)
            {
                return 0;
            }

            float size = 0;
            for (int i = 0; i < contentConstraintCount; i++)
            {
                RectTransform oldItem = content.GetChild(content.childCount - 1) as RectTransform;
                size = Mathf.Max(GetSize(oldItem), size);
                ReturnObjectAndSendMessage(oldItem);

                itemTypeEnd--;
                if (itemTypeEnd % contentConstraintCount == 0 || content.childCount == 0)
                {
                    break;  //just delete the whole row
                }
            }

            if (reverseDirection)
            {
                Vector2 offset = GetVector(size);
                content.anchoredPosition += offset;
                m_PrevPosition += offset;
                m_ContentStartPosition += offset;
            }
            return size;
        }

        private RectTransform InstantiateNextItem(int itemIdx)
        {
            GameObject prefab;
            var currentItem = PrefabsList.Find(x => x.IndexsToShow.Contains(itemIdx));
            if (currentItem == null)
            {
                currentItem = PrefabsList.Find(x => x.IndexsToShow.Count == 0);
            }

            prefab = currentItem.Prefab;
            int targetPoolSize = currentItem.OverridePoolSize >= 0 ? currentItem.OverridePoolSize : poolSize;
            SimpleGOPool.Instance.PreWarmPool(prefab, targetPoolSize);
            RectTransform nextItem = SimpleGOPool.Instance.GetObjectFromPool(prefab).GetComponent<RectTransform>();
            nextItem.localScale = new Vector3(1f, 1f, 1f);
            nextItem.transform.SetParent(content, false);
            nextItem.gameObject.SetActive(true);
            var item = nextItem.GetComponent<IScrollable>();
            SendMessageToNewObject(item, itemIdx);
            //SendMessageToNewObject(nextItem, itemIdx);
            return nextItem;
        }

        //==========LoopScrollRect==========

        public virtual void Rebuild(CanvasUpdate executing)
        {
            if (executing == CanvasUpdate.Prelayout)
            {
                UpdateCachedData();
            }

            if (executing == CanvasUpdate.PostLayout)
            {
                UpdateBounds(false);
                UpdateScrollbars(Vector2.zero);
                UpdatePrevData();

                m_HasRebuiltLayout = true;
            }
        }

        public virtual void LayoutComplete()
        { }

        public virtual void GraphicUpdateComplete()
        { }

        void UpdateCachedData()
        {
            Transform transform = this.transform;
            m_HorizontalScrollbarRect = m_HorizontalScrollbar == null ? null : m_HorizontalScrollbar.transform as RectTransform;
            m_VerticalScrollbarRect = m_VerticalScrollbar == null ? null : m_VerticalScrollbar.transform as RectTransform;

            // These are true if either the elements are children, or they don't exist at all.
            bool viewIsChild = (viewRect.parent == transform);
            bool hScrollbarIsChild = (!m_HorizontalScrollbarRect || m_HorizontalScrollbarRect.parent == transform);
            bool vScrollbarIsChild = (!m_VerticalScrollbarRect || m_VerticalScrollbarRect.parent == transform);
            bool allAreChildren = (viewIsChild && hScrollbarIsChild && vScrollbarIsChild);

            m_HSliderExpand = allAreChildren && m_HorizontalScrollbarRect && horizontalScrollbarVisibility == ScrollbarVisibility.AutoHideAndExpandViewport;
            m_VSliderExpand = allAreChildren && m_VerticalScrollbarRect && verticalScrollbarVisibility == ScrollbarVisibility.AutoHideAndExpandViewport;
            m_HSliderHeight = (m_HorizontalScrollbarRect == null ? 0 : m_HorizontalScrollbarRect.rect.height);
            m_VSliderWidth = (m_VerticalScrollbarRect == null ? 0 : m_VerticalScrollbarRect.rect.width);
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            if (_contentLayoutGroup == null)
            {
                _contentLayoutGroup = content.GetComponent<VerticalLayoutGroup>();
            }
            if (_contentSizeFitter == null)
            {
                _contentSizeFitter = content.GetComponent<ContentSizeFitter>();
            }

            _animationManager.ProvideContentLayoutComponents(_contentSizeFitter, _contentLayoutGroup, content);

            if (m_HorizontalScrollbar)
                m_HorizontalScrollbar.onValueChanged.AddListener(SetHorizontalNormalizedPosition);
            if (m_VerticalScrollbar)
                m_VerticalScrollbar.onValueChanged.AddListener(SetVerticalNormalizedPosition);

            CanvasUpdateRegistry.RegisterCanvasElementForLayoutRebuild(this);
        }

        protected override void OnDisable()
        {
            CleanTempObjects();
            CanvasUpdateRegistry.UnRegisterCanvasElementForRebuild(this);

            if (m_HorizontalScrollbar)
                m_HorizontalScrollbar.onValueChanged.RemoveListener(SetHorizontalNormalizedPosition);
            if (m_VerticalScrollbar)
                m_VerticalScrollbar.onValueChanged.RemoveListener(SetVerticalNormalizedPosition);

            m_HasRebuiltLayout = false;
            m_Tracker.Clear();
            m_Velocity = Vector2.zero;
            LayoutRebuilder.MarkLayoutForRebuild(rectTransform);
            _animationManager.ResetAnimations();
            _disableReturnToPoolOnScroll = false;
            base.OnDisable();
        }

        /// <summary>
        /// Clean temporary object, which may be left from animations.
        /// </summary>
        /// <remarks>
        /// Animations usually clean temp objects on their own, but if something interrupted animation,
        /// then objects have to be cleaned manually.
        /// </remarks>
        private void CleanTempObjects()
        {
            foreach (var go in _tempObjectsCache)
            {
                if (go)
                {
                    if (go.GetComponent<PoolObject>() != null)
                    {
                        SimpleGOPool.Instance.ReturnObjectToPool(go);
                    }
                    else
                    {
                        Destroy(go);
                    }
                }
            }

            _tempObjectsCache.Clear();

            foreach (var canvasGroup in _tempCanvasGroupObjectsCache)
            {
                if (canvasGroup != null)
                {
                    Destroy(canvasGroup);
                }
            }

            _tempCanvasGroupObjectsCache.Clear();
        }

        public override bool IsActive()
        {
            return base.IsActive() && m_Content != null && !IsPlayingAnimation;
        }

        private void EnsureLayoutHasRebuilt()
        {
            if (!m_HasRebuiltLayout && !CanvasUpdateRegistry.IsRebuildingLayout())
                Canvas.ForceUpdateCanvases();
        }

        public virtual void StopMovement()
        {
            m_Velocity = Vector2.zero;
        }

        public virtual void OnScroll(PointerEventData data)
        {
            if (!isEnabledScroll) return;
            if (!IsActive())
                return;

            EnsureLayoutHasRebuilt();
            UpdateBounds();

            Vector2 delta = data.scrollDelta;
            // Down is positive for scroll events, while in UI system up is positive.
            delta.y *= -1;
            if (vertical && !horizontal)
            {
                if (Mathf.Abs(delta.x) > Mathf.Abs(delta.y))
                    delta.y = delta.x;
                delta.x = 0;
            }
            if (horizontal && !vertical)
            {
                if (Mathf.Abs(delta.y) > Mathf.Abs(delta.x))
                    delta.x = delta.y;
                delta.y = 0;
            }

            Vector2 position = m_Content.anchoredPosition;
            position += delta * m_ScrollSensitivity;
            if (m_MovementType == MovementType.Clamped)
                position += CalculateOffset(position - m_Content.anchoredPosition);

            SetContentAnchoredPosition(position);
            UpdateBounds();
        }

        public virtual void OnInitializePotentialDrag(PointerEventData eventData)
        {
            if (!isEnabledScroll) return;
            if (eventData.button != PointerEventData.InputButton.Left)
                return;

            m_Velocity = Vector2.zero;
        }

        public virtual void OnBeginDrag(PointerEventData eventData)
        {
            if (!isEnabledScroll) return;
            if (eventData.button != PointerEventData.InputButton.Left)
                return;

            if (!IsActive())
                return;

            UpdateBounds();

            m_PointerStartLocalCursor = Vector2.zero;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(viewRect, eventData.position, eventData.pressEventCamera, out m_PointerStartLocalCursor);
            m_ContentStartPosition = m_Content.anchoredPosition;
            m_Dragging = true;
        }

        public virtual void OnEndDrag(PointerEventData eventData)
        {
            if (!isEnabledScroll) return;
            if (eventData.button != PointerEventData.InputButton.Left)
                return;

            m_Dragging = false;
        }

        public virtual void OnDrag(PointerEventData eventData)
        {
            if (!isEnabledScroll) return;
            if (eventData.button != PointerEventData.InputButton.Left)
                return;

            if (!IsActive())
                return;

            Vector2 localCursor;
            if (!RectTransformUtility.ScreenPointToLocalPointInRectangle(viewRect, eventData.position, eventData.pressEventCamera, out localCursor))
                return;

            UpdateBounds();

            var pointerDelta = localCursor - m_PointerStartLocalCursor;
            Vector2 position = m_ContentStartPosition + pointerDelta;

            // Offset to get content into place in the view.
            Vector2 offset = CalculateOffset(position - m_Content.anchoredPosition);
            position += offset;
            if (m_MovementType == MovementType.Elastic)
            {
                //==========LoopScrollRect==========
                if (offset.x != 0)
                    position.x = position.x - RubberDelta(offset.x, m_ViewBounds.size.x) * rubberScale;
                if (offset.y != 0)
                    position.y = position.y - RubberDelta(offset.y, m_ViewBounds.size.y) * rubberScale;
                //==========LoopScrollRect==========
            }

            SetContentAnchoredPosition(position);
        }

        protected virtual void SetContentAnchoredPosition(Vector2 position)
        {
            if (!m_Horizontal)
                position.x = m_Content.anchoredPosition.x;
            if (!m_Vertical)
                position.y = m_Content.anchoredPosition.y;

            if (position != m_Content.anchoredPosition)
            {
                m_Content.anchoredPosition = position;
                UpdateBounds();
            }
        }

        protected virtual void LateUpdate()
        {
#if BBB_DEBUG
            DebugUpdate();
#endif
            if (!isEnabledScroll) return;
            if (!m_Content) return;
            if (_animationManager.IsPlayingAnimation) return;

            EnsureLayoutHasRebuilt();
            UpdateScrollbarVisibility();
            UpdateBounds();
            float deltaTime = Time.unscaledDeltaTime;
            Vector2 offset = CalculateOffset(Vector2.zero);
            if (!m_Dragging && (!IsAlmostZero(offset) || !IsAlmostZero(m_Velocity)))
            {
                Vector2 position = m_Content.anchoredPosition;
                for (int axis = 0; axis < 2; axis++)
                {
                    // Apply spring physics if movement is elastic and content has an offset from the view.
                    if (m_MovementType == MovementType.Elastic && !IsAlmostZero(offset[axis]))
                    {
                        float speed = m_Velocity[axis];
                        position[axis] = Mathf.SmoothDamp(m_Content.anchoredPosition[axis], m_Content.anchoredPosition[axis] + offset[axis], ref speed, m_Elasticity, Mathf.Infinity, deltaTime);
                        m_Velocity[axis] = speed;
                    }
                    // Else move content according to velocity with deceleration applied.
                    else if (m_Inertia)
                    {
                        m_Velocity[axis] *= Mathf.Pow(m_DecelerationRate, deltaTime);
                        if (Mathf.Abs(m_Velocity[axis]) < 1)
                            m_Velocity[axis] = 0;
                        position[axis] += m_Velocity[axis] * deltaTime;
                    }
                    // If we have neither elaticity or friction, there shouldn't be any velocity.
                    else
                    {
                        m_Velocity[axis] = 0;
                    }
                }

                if (!IsAlmostZero(m_Velocity))
                {
                    if (m_MovementType == MovementType.Clamped)
                    {
                        offset = CalculateOffset(position - m_Content.anchoredPosition);
                        position += offset;
                    }

                    SetContentAnchoredPosition(position);
                }
            }

            if (m_Dragging && m_Inertia)
            {
                Vector3 newVelocity = (m_Content.anchoredPosition - m_PrevPosition) / deltaTime;
                m_Velocity = Vector3.Lerp(m_Velocity, newVelocity, deltaTime * 10);
            }

            if (m_ViewBounds != m_PrevViewBounds || m_ContentBounds != m_PrevContentBounds || m_Content.anchoredPosition != m_PrevPosition)
            {
                UpdateScrollbars(offset);
                m_OnValueChanged.Invoke(normalizedPosition);
                UpdatePrevData();
            }
        }

#if BBB_DEBUG
        private int _debugLastIndex = 0;

        private void DebugUpdate()
        {
            if (Input.GetKeyDown(KeyCode.Z))
            {
                DebugScale(0f);
            }
            else if (Input.GetKeyDown(KeyCode.X))
            {
                DebugScale(0.3f);
            }
            else if (Input.GetKeyDown(KeyCode.C))
            {
                DebugScale(0.55f);
            }
            else if (Input.GetKeyDown(KeyCode.V))
            {
                DebugScale(0.7f);
            }
            else if (Input.GetKeyDown(KeyCode.B))
            {
                DebugScale(0.85f);
            }
            else if (Input.GetKeyDown(KeyCode.N))
            {
                DebugScale(1f);
            }

            if (Input.GetKeyDown(KeyCode.T))
            {
                Debug.Log("Key 'T' is pressed: Debug switch input lock for scroll view", gameObject);
                isEnabledScroll = !isEnabledScroll;
            }
        }

        private void DebugScale(float ratio)
        {
            int index = (int)((totalCount - 1) * ratio);
            Debug.Log("Debug key pressed, instant focus on list item: " + index);
            if (Input.GetKey(KeyCode.LeftShift))
            {
                SmoothScrollToListItem(_debugLastIndex, index);
            }
            else
            {
                _debugLastIndex = index;
                InstantFocusOnListItem(index);
            }
        }
#endif

        private static bool IsAlmostZero(float v)
        {
            return v < 0.001f && v > -0.001f;
        }

        private static bool IsAlmostZero(Vector2 v)
        {
            return IsAlmostZero(v.x) && IsAlmostZero(v.y);
        }

        private int ConvertListIndexToChildIndex(int listIndex)
        {
            return listIndex - itemTypeStart;
        }

        public void AnimatedSwapTwoListItems(int listItemFirst, int listItemSecond)
        {
            if (!_animationManager.IsPlayingAnimation)
            {
                UpdateCachedListItemsPositions();
            }
            if (listItemFirst == listItemSecond) return;

            int contentItemsCount = content.childCount;

            var childIndex = ConvertListIndexToChildIndex(listItemFirst);
            if (childIndex < 0 || childIndex >= contentItemsCount) return;

            var childIndexSecond = ConvertListIndexToChildIndex(listItemSecond);
            if (childIndexSecond < 0 || childIndexSecond >= contentItemsCount) return;

            var childItem = content.GetChild(childIndex);

            _animationManager.PlayAnimationForItemInQueue(childItem, ScrolListItemAnimationManager.AnimationActionType.Remove, 0);

            var childItemSecond = content.GetChild(childIndexSecond);
            _animationManager.PlayAnimationForItemInQueue(childItem, ScrolListItemAnimationManager.AnimationActionType.Appear, listItemSecond);

            bool isAlreadyMoving = _animationManager.IsPlayingActionForItem(childItemSecond, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, ScrolListItemAnimationManager.AnimationActionType.Delay);
            var moveAction = isAlreadyMoving ? ScrolListItemAnimationManager.AnimationActionType.MoveWithBump : ScrolListItemAnimationManager.AnimationActionType.Delay;
            _animationManager.PlayAnimationForItemInQueue(childItemSecond, moveAction, listItemFirst);
        }

        public void AnimatedMoveItemToPosition(int listIndex, int targetListIndex)
        {
            if (!_animationManager.IsPlayingAnimation)
            {
                UpdateCachedListItemsPositions();
            }
            if (listIndex == targetListIndex) return;

            int contentItemsCount = content.childCount;

            var childIndex = ConvertListIndexToChildIndex(listIndex);
            if (childIndex < 0 || childIndex >= contentItemsCount) return;

            var childItem = content.GetChild(childIndex);
            _animationManager.PlayAnimationForItemInQueue(childItem, ScrolListItemAnimationManager.AnimationActionType.Remove, 0);
            // Move every item by 1 position in opposite direction.
            var delta = targetListIndex > listIndex ? 1 : -1;
            for (int i = listIndex + delta; i <= targetListIndex && i < contentItemsCount; i += delta)
            {
                var childItemSecondary = content.GetChild(i);

                bool isAlreadyMoving = _animationManager.IsPlayingActionForItem(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, ScrolListItemAnimationManager.AnimationActionType.Delay);
                if (!isAlreadyMoving)
                {
                    _animationManager.PlayAnimationForItemInQueue(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.Delay, i - delta);
                }

                _animationManager.PlayAnimationForItemInQueue(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, i - delta);
            }
            var childIndexTarget = ConvertListIndexToChildIndex(targetListIndex);
            if (childIndexTarget < 0 || childIndexTarget >= contentItemsCount)
            {
                itemTypeEnd--;
                totalCount--;
                Observable.NextFrame().Subscribe(_ =>
                {
                    UnityEngine.Profiling.Profiler.BeginSample($"LoopScroll AnimatedMoveItemToPosition Subscribe");
                    RemoveItemFromList(childItem);
                    _tempObjectsCache.Add(childItem.gameObject);
                    UnityEngine.Profiling.Profiler.EndSample();
                });
            }
            else
            {
                InsertItemIntoList(childItem, ConvertListIndexToChildIndex(targetListIndex) + 1);
                _animationManager.PlayAnimationForItemInQueue(childItem, ScrolListItemAnimationManager.AnimationActionType.Appear, targetListIndex);
            }
        }

        public void AnimatedMoveItemUpBy(int listIndex, int moveUpBy, Action<Transform> onOriginalListItemHidden = null, Action<Transform> onTargetScaleUp = null, Action<Transform, int> onTargetScaleDown = null, Action<Transform> onOtherItemMoveDown = null, Action onDone = null)
        {
            StartCoroutine(AnimatedMoveItemUpByRoutine(listIndex, moveUpBy, onOriginalListItemHidden, onTargetScaleUp, onTargetScaleDown, onOtherItemMoveDown, onDone));
        }

        private IEnumerator AnimatedMoveItemUpByRoutine(int listIndex, int moveUpBy, Action<Transform> onOriginalListItemHidden = null, Action<Transform> onTargetScaleUp = null, Action<Transform, int> onTargetScaleDown = null, Action<Transform> onOtherItemMoveDown = null, Action onDone = null)
        {
            //1. focus on index position
            //2. scale item up
            //3. scroll up until target position is not under old item position OR end of scroll.
            //4. move item to target position. at same time move all items-in-between down by one position.
            //5. scale item down

            int endIndex = listIndex - moveUpBy < 0 ? 0 : listIndex - moveUpBy;

            if (endIndex == listIndex)
            {
                onDone?.Invoke();
                yield break;
            }

            InstantFocusOnListItem(listIndex);
            var childIndex        = ConvertListIndexToChildIndex(listIndex);
            int contentItemsCount = content.childCount;

            if (childIndex < 0 || childIndex >= contentItemsCount)
            {
                onDone?.Invoke();
                yield break;
            }

            yield return null;

            UpdateCachedListItemsPositions();
            var childItemOriginal = content.GetChild(childIndex);
            var childItemFake = Instantiate(childItemOriginal, parent: childItemOriginal.parent.parent);
            var fakePoolItem = childItemFake.GetComponent<PoolItem>();
            if (fakePoolItem != null)
            {
                Destroy(fakePoolItem);
            }

            childItemFake.SetAsLastSibling();
            childItemFake.GetComponent<RectTransform>().sizeDelta = childItemOriginal.GetComponent<RectTransform>().sizeDelta;
            childItemFake.position = childItemOriginal.position;
            _tempObjectsCache.Add(childItemFake.gameObject);
            if (_animationsSettings.useSeparateCanvasForAnimatedItem)
            {
                var fakeCanvas = childItemFake.gameObject.AddComponent<Canvas>();
                fakeCanvas.overrideSorting = true;
                fakeCanvas.sortingOrder = _animationsSettings.animatedOverrideSorting;
            }

            var canvasGroup = childItemOriginal.gameObject.AddComponent<CanvasGroup>();
            canvasGroup.alpha = 0f;
            _tempCanvasGroupObjectsCache.Add(canvasGroup);
            onOriginalListItemHidden?.Invoke(childItemOriginal);

            _disableReturnToPoolOnScroll = true;

            _animMovedItemsCache.Clear();
            onTargetScaleUp?.Invoke(childItemFake);
            _animationManager.PlayAnimationForItemInQueue(childItemFake, ScrolListItemAnimationManager.AnimationActionType.ScaleUp, listIndex, onDone: () =>
            {
                if (!gameObject.activeInHierarchy)
                {
                    onDone?.Invoke();
                    return;
                }

                UpdateCachedListItemsPositions();
                SmoothScrollToListItem(fromListIndex: listIndex, toListIndex: Mathf.Max(0, endIndex), onDone: () =>
                {
                    contentItemsCount = content.childCount;
                    int movedByOneCount = 0;
                    int moveByOneSkipped = 0;
                    for (int i = listIndex - 1; i >= endIndex; i--)
                    {
                        var secondaryChildIndex = ConvertListIndexToChildIndex(i);
                        if (secondaryChildIndex < 0 || secondaryChildIndex >= contentItemsCount)
                        {
                            moveByOneSkipped++;
                            continue;
                        }
                        movedByOneCount++;
                    }

                    var movedByOneDelay = movedByOneCount > 0 ? _animationsSettings.animatedMoveDuration / movedByOneCount : 0;

                    int moveIndex = 0;
                    for (int i = listIndex - 1; i >= endIndex; i--)
                    {
                        var secondaryChildIndex = ConvertListIndexToChildIndex(i);
                        if (secondaryChildIndex < 0 || secondaryChildIndex >= contentItemsCount)
                        {
                            continue;
                        }

                        var secondaryChild = content.GetChild(secondaryChildIndex);
                        _animationManager.PlayAnimationForItemInQueue(secondaryChild, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, i + 1, delay: moveIndex * movedByOneDelay);
                        _animMovedItemsCache.Add(secondaryChild);
                        moveIndex++;
                    }

                    if (movedByOneCount > 0 && onOtherItemMoveDown != null)
                    {
                        StartCoroutine(DelayedCallbacksForMovedItems(secondaryMovedItems: _animMovedItemsCache, delay: movedByOneDelay, callback: onOtherItemMoveDown)); 
                    }

                    InsertItemIntoList(childItemOriginal, endIndex);
                    _animationManager.PlayAnimationForItemInQueue(childItemFake, ScrolListItemAnimationManager.AnimationActionType.MoveSmooth, endIndex,
                        onDone: () => { 
                            onTargetScaleDown?.Invoke(childItemFake, endIndex - listIndex);
                            childItemOriginal.SetSiblingIndex(endIndex - itemTypeStart);
                        });
                    _animationManager.PlayAnimationForItemInQueue(childItemFake, ScrolListItemAnimationManager.AnimationActionType.ScaleToNormal, endIndex,
                        onDone: () =>
                        {
                            _animMovedItemsCache.Clear();
                            if (!gameObject.activeInHierarchy)
                            {
                                onDone?.Invoke();
                            }
                            else
                            {
                                StartCoroutine(DoAfterDelay(1f, () =>
                                {
                                    _disableReturnToPoolOnScroll = false;
                                    if (childItemFake != null)
                                    {
                                        var childItemFakeGo = childItemFake.gameObject;
                                        Destroy(childItemFakeGo);
                                        _tempObjectsCache.Remove(childItemFakeGo);
                                    }

                                    if (canvasGroup != null)
                                    {
                                        _tempCanvasGroupObjectsCache.Remove(canvasGroup);
                                        Destroy(canvasGroup);
                                    }
                                    m_Velocity[1] = 0;
                                    onDone?.Invoke();
                                }));
                            }
                        });
                });
            });
        }

        private IEnumerator DoAfterDelay(float delay, Action action)
        {
            float timer = 0f;
            while (timer <= delay)
            {
                timer += Time.deltaTime;
                yield return null;
            }

            action?.Invoke();
        }

        private IEnumerator DelayedCallbacksForMovedItems(List<Transform> secondaryMovedItems, float delay, Action<Transform> callback)
        {
            for (int i = 0; i < secondaryMovedItems.Count; i++)
            {
                callback.Invoke(secondaryMovedItems[i]);
                if (delay > 0 && !float.IsInfinity(delay))
                {
                    yield return WaitCache.Seconds(delay);
                }
                else
                {
                    yield return null;
                }
            }
        }

        private void MakeValidElementSize()
        {
            if (_elementSizeY <= 0 || float.IsNaN(_elementSizeY) || float.IsInfinity(_elementSizeY))
            {
                _elementSizeY = 0f;
                var prefabInfo = PrefabsList != null && PrefabsList.Count > 0 ? PrefabsList[0] : null;
                if (prefabInfo != null && prefabInfo.Prefab != null)
                {
                    var prefabRect = prefabInfo.Prefab.GetComponent<RectTransform>();
                    if (prefabRect != null)
                    {
                        _elementSizeY = prefabRect.sizeDelta.y;
                    }
                }
            }
            else
            {
                return;
            }

            if (_elementSizeY <= 0)
            {
                var currentSpawnedItemsCount = (itemTypeEnd - itemTypeStart);
                if (currentSpawnedItemsCount > 0)
                {
                    float elementSize = m_ContentBounds.size.y / currentSpawnedItemsCount;
                    if (!float.IsNaN(elementSize) && !float.IsInfinity(elementSize) && elementSize > 0)
                    {
                        _elementSizeY = elementSize;
                    }
                }
            }
            else
            {
                return;
            }

            if (_elementSizeY <= 0)
            {
                _elementSizeY = 115f;
            }
        }

        public void InstantFocusOnListItem(int listIndex)
        {
            MakeValidElementSize();
            const float listContentTopPosY = 0f;
            var viewSizeY = m_ViewBounds.size.y;
            int maxSpawnedElementsCount = Mathf.CeilToInt((viewSizeY + threshold * 2) / _elementSizeY);
            int targetSpawnItemIndexStart = Mathf.Max(
                0,
                listIndex - ((maxSpawnedElementsCount % 2 == 0) ? (maxSpawnedElementsCount / 2) : ((maxSpawnedElementsCount + 1) / 2)));
            int targetSpawnItemIndexEnd = Mathf.Min(
                totalCount,
                targetSpawnItemIndexStart + maxSpawnedElementsCount);
            bool isChanged = true;
            while (isChanged)
            {
                isChanged = false;
                if (itemTypeStart > targetSpawnItemIndexStart)
                {
                    if (NewItemAtStart() > 0)
                    {
                        isChanged = true;
                    }
                }
                else if (itemTypeStart < targetSpawnItemIndexStart)
                {
                    if (DeleteItemAtStart() > 0)
                    {
                        isChanged = true;
                    }
                }

                if (itemTypeEnd > targetSpawnItemIndexEnd)
                {
                    if (DeleteItemAtEnd() > 0)
                    {
                        isChanged = true;
                    }
                }
                else if (itemTypeEnd < targetSpawnItemIndexEnd)
                {
                    if (NewItemAtEnd() > 0)
                    {
                        isChanged = true;
                    }
                }
            }

            Canvas.ForceUpdateCanvases();
            UpdateBounds(false);
            var targetPosY = listContentTopPosY - viewSizeY * 0.5f + (listIndex - itemTypeStart) * _elementSizeY;
            if (targetPosY < listContentTopPosY)
            {
                targetPosY = listContentTopPosY;
            }

            var pos = m_Content.localPosition;
            pos.y = targetPosY;
            m_Content.localPosition = pos;
            UpdateBounds(false);
            m_Velocity[1] = 0;
        }

        public void SmoothScrollToListItem(int fromListIndex, int toListIndex, float minDistanceViewRectRatio = 0.5f, Action onDone = null)
        {
            StartCoroutine(SmoothScrollToListItemRoutine(fromListIndex, toListIndex, minDistanceViewRectRatio, onDone));
        }

        private IEnumerator SmoothScrollToListItemRoutine(int fromListIndex, int toListIndex, float minDistanceViewRectRatio = 0.5f, Action onDone = null)
        {
            InstantFocusOnListItem(fromListIndex);
            var contentPosY = m_Content.localPosition.y;
            const float listContentTopPosY = 0f;
            var startingOriginPosY = contentPosY + _elementSizeY * itemTypeStart;
            var viewSizeY = m_ViewBounds.size.y;
            var targetPosY = listContentTopPosY - viewSizeY * 0.5f;
            var targetOriginPosY = targetPosY + _elementSizeY * toListIndex;
            if (targetOriginPosY < listContentTopPosY)
            {
                targetOriginPosY = listContentTopPosY;
            }

            var duration = _animationsSettings.smoothFocusMotionDuration;
            bool fullMotionSkipped = false;

            if (startingOriginPosY - targetOriginPosY > 0 && startingOriginPosY - targetOriginPosY < viewSizeY * minDistanceViewRectRatio)
            {
                targetOriginPosY = startingOriginPosY;
                duration = 0.05f;
                fullMotionSkipped = true;
            }

            var t = 0f;
            while (t <= duration)
            {
                yield return null;

                var deltaTime = Time.deltaTime;
                t += deltaTime;
                var ratio = t / duration;
                var currentOriginPosY = Mathf.Lerp(startingOriginPosY, targetOriginPosY, ratio);
                var oldPos = m_Content.localPosition;
                oldPos.y = currentOriginPosY - itemTypeStart * _elementSizeY;
                m_Content.localPosition = oldPos;
                m_Velocity[1] = 0;
                int tmp = 0;
                bool itemChanged = false;
                do
                {
                    itemChanged = UpdateItems(m_ViewBounds, GetBounds());
                    if (itemChanged)
                    {

                        Canvas.ForceUpdateCanvases();
                        UpdateBounds(false);
                    }

                    tmp++;
                } while (itemChanged && tmp < 50);
            }

            yield return null;
            if (!fullMotionSkipped)
            {
                InstantFocusOnListItem(toListIndex);
            }

            UpdateCachedListItemsPositions();
            onDone?.Invoke();
        }

        private void OnDrawGizmos()
        {
            if (!_debugDisplayCachedPositions)
            {
                return;
            }

            for (int i = 0; i < _listItemsCachedPositions.positions.Count; i++)
            {
                var pos = new Vector3(-100, _listItemsCachedPositions.positions[i].y, 0);
                Gizmos.DrawLine(pos, pos + new Vector3(500, 0, 0));
            }

            Gizmos.color = Color.gray;
            for (int i = 0; i < _listItemsCachedPositions.startIndex - 1; i++)
            {
                var pos = new Vector3(-100, _animationManager.GetWorldPositionOfListItem(i).y, 0);
                Gizmos.DrawLine(pos, pos + new Vector3(500, 0, 0));
            }
        }

        public void AnimatedRemoveItem(int listIndex)
        {
            if (!_animationManager.IsPlayingAnimation)
            {
                UpdateCachedListItemsPositions();
            }
            int contentItemsCount = content.childCount;

            var childIndex = ConvertListIndexToChildIndex(listIndex);
            if (childIndex < 0 || childIndex >= contentItemsCount) return;

            var childItem = content.GetChild(childIndex);
            _animationManager.PlayAnimationForItemInQueue(childItem, ScrolListItemAnimationManager.AnimationActionType.Remove, 0);

            Observable.NextFrame().Subscribe(_ =>
            {
                UnityEngine.Profiling.Profiler.BeginSample($"LoopScroll AnimatedRemoveItem Subscribe");
                RemoveItemFromList(childItem);
                _tempObjectsCache.Add(childItem.gameObject);
                UnityEngine.Profiling.Profiler.EndSample();
            });
            itemTypeEnd--;
            totalCount--;

            var lastVisibleIndex = itemTypeEnd;

            var childIndexTarget = ConvertListIndexToChildIndex(lastVisibleIndex);
            if (childIndexTarget < 0 || childIndexTarget >= contentItemsCount) return;

            // Move every item by 1 position in opposite direction.
            for (int i = childIndex + 1; i <= childIndexTarget; i++)
            {
                var childItemSecondary = content.GetChild(i);

                bool isAlreadyMoving = _animationManager.IsPlayingActionForItem(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, ScrolListItemAnimationManager.AnimationActionType.Delay);
                if (!isAlreadyMoving)
                {
                    _animationManager.PlayAnimationForItemInQueue(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.Delay, i - 1);
                }

                _animationManager.PlayAnimationForItemInQueue(childItemSecondary, ScrolListItemAnimationManager.AnimationActionType.MoveWithBump, i - 1);
            }
        }

        private void RemoveItemFromList(Transform item)
        {
            if (item == null || item.parent != content) return;
            item.SetParent(content.parent);
            item.SetAsLastSibling();
        }

        private void InsertItemIntoList(Transform item, int childIndex)
        {
            if (item.parent != content)
            {
                item.SetParent(content);
            }
            item.SetSiblingIndex(childIndex);
        }

        /// <summary>
        /// Store current list items positions in cached object. Should be called before any animation play.
        /// </summary>
        /// <remarks>
        /// Positions cached allowing to convert list item index to position vector for animation purposes.
        /// </remarks>
        [ContextMenu("Update cached items positions")]
        private void UpdateCachedListItemsPositions()
        {
            _listItemsCachedPositions.startIndex = itemTypeStart;
            _listItemsCachedPositions.positions.Clear();
            foreach (Transform child in content)
            {
                _listItemsCachedPositions.positions.Add(child.position);
            }

            Comparison<Vector3> comparison = (a, b) => {return a.y < b.y ? 1 : (a.y > b.y ? -1 : 0);};
            _listItemsCachedPositions.positions.Sort(comparison);
        }

        private void UpdateCachedListItemsPositions(float delta)
        {
            for (int i = 0; i < _listItemsCachedPositions.positions.Count; i++)
            {
                var pos = _listItemsCachedPositions.positions[i];
                pos.y                                  += delta;
                _listItemsCachedPositions.positions[i] =  pos;
            }
        }

        private void UpdatePrevData()
        {
            if (m_Content == null)
                m_PrevPosition = Vector2.zero;
            else
                m_PrevPosition = m_Content.anchoredPosition;
            m_PrevViewBounds = m_ViewBounds;
            m_PrevContentBounds = m_ContentBounds;
        }

        private void UpdateScrollbars(Vector2 offset)
        {
            if (m_HorizontalScrollbar)
            {
                //==========LoopScrollRect==========
                if (m_ContentBounds.size.x > 0 && totalCount > 0)
                {
                    m_HorizontalScrollbar.size = Mathf.Clamp01((m_ViewBounds.size.x - Mathf.Abs(offset.x)) / m_ContentBounds.size.x * (itemTypeEnd - itemTypeStart) / totalCount);
                }
                //==========LoopScrollRect==========
                else
                    m_HorizontalScrollbar.size = 1;

                m_HorizontalScrollbar.value = horizontalNormalizedPosition;
            }

            if (m_VerticalScrollbar)
            {
                //==========LoopScrollRect==========
                if (m_ContentBounds.size.y > 0 && totalCount > 0)
                {
                    m_VerticalScrollbar.size = Mathf.Clamp01((m_ViewBounds.size.y - Mathf.Abs(offset.y)) / m_ContentBounds.size.y * (itemTypeEnd - itemTypeStart) / totalCount);
                }
                //==========LoopScrollRect==========
                else
                    m_VerticalScrollbar.size = 1;

                m_VerticalScrollbar.value = verticalNormalizedPosition;
            }
        }

        public Vector2 normalizedPosition
        {
            get
            {
                return new Vector2(horizontalNormalizedPosition, verticalNormalizedPosition);
            }
            set
            {
                SetNormalizedPosition(value.x, 0);
                SetNormalizedPosition(value.y, 1);
            }
        }

        public float horizontalNormalizedPosition
        {
            get
            {
                UpdateBounds();
                //==========LoopScrollRect==========
                if (totalCount > 0 && itemTypeEnd > itemTypeStart)
                {
                    //TODO: consider contentSpacing
                    float elementSize = _elementSizeY;// m_ContentBounds.size.x / (itemTypeEnd - itemTypeStart);
                    float totalSize   = elementSize * totalCount;
                    float offset      = m_ContentBounds.min.x - elementSize * itemTypeStart;

                    if (totalSize <= m_ViewBounds.size.x)
                        return (m_ViewBounds.min.x > offset) ? 1 : 0;
                    return (m_ViewBounds.min.x - offset) / (totalSize - m_ViewBounds.size.x);
                }
                else
                    return 0.5f;

                //==========LoopScrollRect==========
            }
            set
            {
                if (!_animationManager.IsPlayingAnimation)
                {
                    SetNormalizedPosition(value, 0);
                }
            }
        }

        public float verticalNormalizedPosition
        {
            get
            {
                UpdateBounds();
                //==========LoopScrollRect==========
                if (totalCount > 0 && itemTypeEnd > itemTypeStart)
                {
                    //TODO: consider contentSpacinge
                    float elementSize = _elementSizeY; // m_ContentBounds.size.y / (itemTypeEnd - itemTypeStart);
                    float totalSize   = elementSize * totalCount;
                    float offset      = m_ContentBounds.max.y + elementSize * itemTypeStart;

                    if (totalSize <= m_ViewBounds.size.y)
                        return (offset > m_ViewBounds.max.y) ? 1 : 0;
                    return (offset - m_ViewBounds.max.y) / (totalSize - m_ViewBounds.size.y);
                }
                else
                    return 0.5f;

                //==========LoopScrollRect==========
            }
            set
            {
                if (!_animationManager.IsPlayingAnimation)
                {
                    SetNormalizedPosition(value, 1);
                }
            }
        }

        private void SetHorizontalNormalizedPosition(float value) {
            if (!_animationManager.IsPlayingAnimation)
            {
                SetNormalizedPosition(value, 0);
            }
        }

        private void SetVerticalNormalizedPosition(float value)
        {
            if (!_animationManager.IsPlayingAnimation)
            {
                SetNormalizedPosition(value, 1);
            }
        }

        private void SetNormalizedPosition(float value, int axis)
        {
            //==========LoopScrollRect==========
            if (totalCount <= 0 || itemTypeEnd <= itemTypeStart)
                return;
            //==========LoopScrollRect==========

            EnsureLayoutHasRebuilt();
            UpdateBounds();

            //==========LoopScrollRect==========
            Vector3 localPosition = m_Content.localPosition;
            float newLocalPosition = localPosition[axis];
            if (axis == 0)
            {
                float elementSize = _elementSizeY; // m_ContentBounds.size.x / (itemTypeEnd - itemTypeStart);
                float totalSize = elementSize * totalCount;
                float offset = m_ContentBounds.min.x - elementSize * itemTypeStart;

                newLocalPosition += m_ViewBounds.min.x - value * (totalSize - m_ViewBounds.size[axis]) - offset;
            }
            else if (axis == 1)
            {
                float elementSize = _elementSizeY; // m_ContentBounds.size.y / (itemTypeEnd - itemTypeStart);
                float totalSize = elementSize * totalCount;
                float offset = m_ContentBounds.max.y + elementSize * itemTypeStart;

                newLocalPosition -= offset - value * (totalSize - m_ViewBounds.size.y) - m_ViewBounds.max.y;
            }
            //==========LoopScrollRect==========

            if (Mathf.Abs(localPosition[axis] - newLocalPosition) > 0.01f)
            {
                localPosition[axis] = newLocalPosition;
                m_Content.localPosition = localPosition;
                m_Velocity[axis] = 0;
                UpdateBounds();
            }
        }

        private static float RubberDelta(float overStretching, float viewSize)
        {
            return (1 - (1 / ((Mathf.Abs(overStretching) * 0.55f / viewSize) + 1))) * viewSize * Mathf.Sign(overStretching);
        }

        protected override void OnRectTransformDimensionsChange()
        {
            SetDirty();
        }

        private bool hScrollingNeeded
        {
            get
            {
                if (Application.isPlaying)
                    return m_ContentBounds.size.x > m_ViewBounds.size.x + 0.01f;
                return true;
            }
        }
        private bool vScrollingNeeded
        {
            get
            {
                if (Application.isPlaying)
                    return m_ContentBounds.size.y > m_ViewBounds.size.y + 0.01f;
                return true;
            }
        }

        public virtual void CalculateLayoutInputHorizontal() { }
        public virtual void CalculateLayoutInputVertical() { }

        public virtual float minWidth { get { return -1; } }
        public virtual float preferredWidth { get { return -1; } }
        public virtual float flexibleWidth { get; private set; }

        public virtual float minHeight { get { return -1; } }
        public virtual float preferredHeight { get { return -1; } }
        public virtual float flexibleHeight { get { return -1; } }

        public virtual int layoutPriority { get { return -1; } }

        public virtual void SetLayoutHorizontal()
        {
            m_Tracker.Clear();

            if (m_HSliderExpand || m_VSliderExpand)
            {
                m_Tracker.Add(this, viewRect,
                    DrivenTransformProperties.Anchors |
                    DrivenTransformProperties.SizeDelta |
                    DrivenTransformProperties.AnchoredPosition);

                // Make view full size to see if content fits.
                viewRect.anchorMin = Vector2.zero;
                viewRect.anchorMax = Vector2.one;
                viewRect.sizeDelta = Vector2.zero;
                viewRect.anchoredPosition = Vector2.zero;

                // Recalculate content layout with this size to see if it fits when there are no scrollbars.
                LayoutRebuilder.ForceRebuildLayoutImmediate(content);
                m_ViewBounds = new Bounds(viewRect.rect.center, viewRect.rect.size);
                m_ContentBounds = GetBounds();
            }

            // If it doesn't fit vertically, enable vertical scrollbar and shrink view horizontally to make room for it.
            if (m_VSliderExpand && vScrollingNeeded)
            {
                viewRect.sizeDelta = new Vector2(-(m_VSliderWidth + m_VerticalScrollbarSpacing), viewRect.sizeDelta.y);

                // Recalculate content layout with this size to see if it fits vertically
                // when there is a vertical scrollbar (which may reflowed the content to make it taller).
                LayoutRebuilder.ForceRebuildLayoutImmediate(content);
                m_ViewBounds = new Bounds(viewRect.rect.center, viewRect.rect.size);
                m_ContentBounds = GetBounds();
            }

            // If it doesn't fit horizontally, enable horizontal scrollbar and shrink view vertically to make room for it.
            if (m_HSliderExpand && hScrollingNeeded)
            {
                viewRect.sizeDelta = new Vector2(viewRect.sizeDelta.x, -(m_HSliderHeight + m_HorizontalScrollbarSpacing));
                m_ViewBounds = new Bounds(viewRect.rect.center, viewRect.rect.size);
                m_ContentBounds = GetBounds();
            }

            // If the vertical slider didn't kick in the first time, and the horizontal one did,
            // we need to check again if the vertical slider now needs to kick in.
            // If it doesn't fit vertically, enable vertical scrollbar and shrink view horizontally to make room for it.
            if (m_VSliderExpand && vScrollingNeeded && viewRect.sizeDelta.x == 0 && viewRect.sizeDelta.y < 0)
            {
                viewRect.sizeDelta = new Vector2(-(m_VSliderWidth + m_VerticalScrollbarSpacing), viewRect.sizeDelta.y);
            }
        }

        public virtual void SetLayoutVertical()
        {
            UpdateScrollbarLayout();
            m_ViewBounds = new Bounds(viewRect.rect.center, viewRect.rect.size);
            m_ContentBounds = GetBounds();
        }

        void UpdateScrollbarVisibility()
        {
            if (m_VerticalScrollbar && m_VerticalScrollbarVisibility != ScrollbarVisibility.Permanent && m_VerticalScrollbar.gameObject.activeSelf != vScrollingNeeded)
                m_VerticalScrollbar.gameObject.SetActive(vScrollingNeeded);

            if (m_HorizontalScrollbar && m_HorizontalScrollbarVisibility != ScrollbarVisibility.Permanent && m_HorizontalScrollbar.gameObject.activeSelf != hScrollingNeeded)
                m_HorizontalScrollbar.gameObject.SetActive(hScrollingNeeded);
        }

        void UpdateScrollbarLayout()
        {
            if (m_VSliderExpand && m_HorizontalScrollbar)
            {
                m_Tracker.Add(this, m_HorizontalScrollbarRect,
                          DrivenTransformProperties.AnchorMinX |
                          DrivenTransformProperties.AnchorMaxX |
                          DrivenTransformProperties.SizeDeltaX |
                          DrivenTransformProperties.AnchoredPositionX);
                m_HorizontalScrollbarRect.anchorMin = new Vector2(0, m_HorizontalScrollbarRect.anchorMin.y);
                m_HorizontalScrollbarRect.anchorMax = new Vector2(1, m_HorizontalScrollbarRect.anchorMax.y);
                m_HorizontalScrollbarRect.anchoredPosition = new Vector2(0, m_HorizontalScrollbarRect.anchoredPosition.y);
                if (vScrollingNeeded)
                    m_HorizontalScrollbarRect.sizeDelta = new Vector2(-(m_VSliderWidth + m_VerticalScrollbarSpacing), m_HorizontalScrollbarRect.sizeDelta.y);
                else
                    m_HorizontalScrollbarRect.sizeDelta = new Vector2(0, m_HorizontalScrollbarRect.sizeDelta.y);
            }

            if (m_HSliderExpand && m_VerticalScrollbar)
            {
                m_Tracker.Add(this, m_VerticalScrollbarRect,
                          DrivenTransformProperties.AnchorMinY |
                          DrivenTransformProperties.AnchorMaxY |
                          DrivenTransformProperties.SizeDeltaY |
                          DrivenTransformProperties.AnchoredPositionY);
                m_VerticalScrollbarRect.anchorMin = new Vector2(m_VerticalScrollbarRect.anchorMin.x, 0);
                m_VerticalScrollbarRect.anchorMax = new Vector2(m_VerticalScrollbarRect.anchorMax.x, 1);
                m_VerticalScrollbarRect.anchoredPosition = new Vector2(m_VerticalScrollbarRect.anchoredPosition.x, 0);
                if (hScrollingNeeded)
                    m_VerticalScrollbarRect.sizeDelta = new Vector2(m_VerticalScrollbarRect.sizeDelta.x, -(m_HSliderHeight + m_HorizontalScrollbarSpacing));
                else
                    m_VerticalScrollbarRect.sizeDelta = new Vector2(m_VerticalScrollbarRect.sizeDelta.x, 0);
            }
        }

        private void UpdateBounds(bool updateItems = true)
        {
            m_ViewBounds = new Bounds(viewRect.rect.center, viewRect.rect.size);
            m_ContentBounds = GetBounds();

            if (m_Content == null)
                return;

            // ============LoopScrollRect============
            // Don't do this in Rebuild
            if (Application.isPlaying && updateItems && UpdateItems(m_ViewBounds, m_ContentBounds))
            {
                Canvas.ForceUpdateCanvases();
                m_ContentBounds = GetBounds();
            }

            // ============LoopScrollRect============

            // Make sure content bounds are at least as large as view by adding padding if not.
            // One might think at first that if the content is smaller than the view, scrolling should be allowed.
            // However, that's not how scroll views normally work.
            // Scrolling is *only* possible when content is *larger* than view.
            // We use the pivot of the content rect to decide in which directions the content bounds should be expanded.
            // E.g. if pivot is at top, bounds are expanded downwards.
            // This also works nicely when ContentSizeFitter is used on the content.
            Vector3 contentSize = m_ContentBounds.size;
            Vector3 contentPos = m_ContentBounds.center;
            Vector3 excess = m_ViewBounds.size - contentSize;
            if (excess.x > 0)
            {
                contentPos.x -= excess.x * (m_Content.pivot.x - 0.5f);
                contentSize.x = m_ViewBounds.size.x;
            }
            if (excess.y > 0)
            {
                contentPos.y -= excess.y * (m_Content.pivot.y - 0.5f);
                contentSize.y = m_ViewBounds.size.y;
            }

            m_ContentBounds.size = contentSize;
            m_ContentBounds.center = contentPos;
        }

        private readonly Vector3[] m_Corners = new Vector3[4];
        private Bounds GetBounds()
        {
            if (m_Content == null)
                return new Bounds();

            var vMin = new Vector3(float.MaxValue, float.MaxValue, float.MaxValue);
            var vMax = new Vector3(float.MinValue, float.MinValue, float.MinValue);

            var toLocal = viewRect.worldToLocalMatrix;
            m_Content.GetWorldCorners(m_Corners);
            for (int j = 0; j < 4; j++)
            {
                Vector3 v = toLocal.MultiplyPoint3x4(m_Corners[j]);
                vMin = Vector3.Min(v, vMin);
                vMax = Vector3.Max(v, vMax);
            }

            var bounds = new Bounds(vMin, Vector3.zero);
            bounds.Encapsulate(vMax);
            return bounds;
        }

        private Vector2 CalculateOffset(Vector2 delta)
        {
            Vector2 offset = Vector2.zero;

            if (m_MovementType == MovementType.Unrestricted)
            {
                return offset;
            }

            Vector2 min = m_ContentBounds.min;
            Vector2 max = m_ContentBounds.max;

            if (m_Horizontal)
            {
                min.x += delta.x;
                max.x += delta.x;
                if (min.x > m_ViewBounds.min.x)
                    offset.x = m_ViewBounds.min.x - min.x;
                else if (max.x < m_ViewBounds.max.x)
                    offset.x = m_ViewBounds.max.x - max.x;
            }

            if (m_Vertical)
            {
                min.y += delta.y;
                max.y += delta.y;
                if (max.y < m_ViewBounds.max.y)
                    offset.y = m_ViewBounds.max.y - max.y;
                else if (min.y > m_ViewBounds.min.y)
                    offset.y = m_ViewBounds.min.y - min.y;
            }

            return offset;
        }

        protected void SetDirty()
        {
            if (!IsActive())
                return;

            LayoutRebuilder.MarkLayoutForRebuild(rectTransform);
        }

        protected void SetDirtyCaching()
        {
            if (!IsActive())
                return;

            CanvasUpdateRegistry.RegisterCanvasElementForLayoutRebuild(this);
            LayoutRebuilder.MarkLayoutForRebuild(rectTransform);
        }

#if UNITY_EDITOR
        protected override void OnValidate()
        {
            SetDirtyCaching();
        }

        private int _debugPosStart;
        private int _debugPosEnd;

#endif

        /// <summary>
        /// Prewarms pools 
        /// </summary>
        public void PrewarmPools()
        {
            foreach (var item in PrefabsList)
            {
                var prefab = item.Prefab;
                var autoCreateCount = item.OverridePoolSize < 0 ? poolSize : item.OverridePoolSize;
                SimpleGOPool.Instance.PreWarmPool(prefab, autoCreateCount);  
            }
        }
    }
}