<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Google.Apis.Sheets.v4</name>
    </assembly>
    <members>
        <member name="T:Google.Apis.Sheets.v4.SheetsService">
            <summary>The Sheets Service.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Version">
            <summary>The API version.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.DiscoveryVersionUsed">
            <summary>The discovery version used to generate this service.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SheetsService.#ctor">
            <summary>Constructs a new service.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SheetsService.#ctor(Google.Apis.Services.BaseClientService.Initializer)">
            <summary>Constructs a new service.</summary>
            <param name="initializer">The service initializer.</param>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.Features">
            <summary>Gets the service supported features.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.Name">
            <summary>Gets the service name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.BaseUri">
            <summary>Gets the service base URI.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.BasePath">
            <summary>Gets the service base path.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.BatchUri">
            <summary>Gets the batch base URI; <c>null</c> if unspecified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.BatchPath">
            <summary>Gets the batch base path; <c>null</c> if unspecified.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SheetsService.Scope">
            <summary>Available OAuth 2.0 scopes for use with the Google Sheets API.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Scope.Drive">
            <summary>See, edit, create, and delete all of your Google Drive files</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Scope.DriveFile">
            <summary>View and manage Google Drive files and folders that you have opened or created with this
            app</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Scope.DriveReadonly">
            <summary>See and download all your Google Drive files</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Scope.Spreadsheets">
            <summary>See, edit, create, and delete your spreadsheets in Google Drive</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.Scope.SpreadsheetsReadonly">
            <summary>View your Google Spreadsheets</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SheetsService.ScopeConstants">
            <summary>Available OAuth 2.0 scope constants for use with the Google Sheets API.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.ScopeConstants.Drive">
            <summary>See, edit, create, and delete all of your Google Drive files</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.ScopeConstants.DriveFile">
            <summary>View and manage Google Drive files and folders that you have opened or created with this
            app</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.ScopeConstants.DriveReadonly">
            <summary>See and download all your Google Drive files</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.ScopeConstants.Spreadsheets">
            <summary>See, edit, create, and delete your spreadsheets in Google Drive</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsService.ScopeConstants.SpreadsheetsReadonly">
            <summary>View your Google Spreadsheets</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsService.Spreadsheets">
            <summary>Gets the Spreadsheets resource.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1">
            <summary>A base abstract class for Sheets requests.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new SheetsBaseServiceRequest instance.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.Xgafv">
            <summary>V1 error format.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.XgafvEnum">
            <summary>V1 error format.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.XgafvEnum.Value1">
            <summary>v1 error format</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.XgafvEnum.Value2">
            <summary>v2 error format</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.AccessToken">
            <summary>OAuth access token.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.Alt">
            <summary>Data format for response.</summary>
            [default: json]
        </member>
        <member name="T:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.AltEnum">
            <summary>Data format for response.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.AltEnum.Json">
            <summary>Responses with Content-Type of application/json</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.AltEnum.Media">
            <summary>Media download with context-dependent Content-Type</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.AltEnum.Proto">
            <summary>Responses with Content-Type of application/x-protobuf</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.Callback">
            <summary>JSONP</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.Fields">
            <summary>Selector specifying which fields to include in a partial response.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.Key">
            <summary>API key. Your API key identifies your project and provides you with API access, quota, and reports.
            Required unless you provide an OAuth 2.0 token.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.OauthToken">
            <summary>OAuth 2.0 token for the current user.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.PrettyPrint">
            <summary>Returns response with indentations and line breaks.</summary>
            [default: true]
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.QuotaUser">
            <summary>Available to use for quota purposes for server-side applications. Can be any arbitrary string
            assigned to a user, but should not exceed 40 characters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.UploadType">
            <summary>Legacy upload protocol for media (e.g. "media", "multipart").</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.UploadProtocol">
            <summary>Upload protocol for media (e.g. "raw", "multipart").</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SheetsBaseServiceRequest`1.InitParameters">
            <summary>Initializes Sheets parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource">
            <summary>The "spreadsheets" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SpreadsheetsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadata">
            <summary>Gets the DeveloperMetadata resource.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource">
            <summary>The "developerMetadata" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.Get(System.String,System.Int32)">
            <summary>Returns the developer metadata with the specified ID. The caller must specify the spreadsheet
            ID and the developer metadata's unique metadataId.</summary>
            <param name="spreadsheetId">The ID of the spreadsheet to retrieve metadata from.</param>
            <param
            name="metadataId">The ID of the developer metadata to retrieve.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest">
            <summary>Returns the developer metadata with the specified ID. The caller must specify the spreadsheet
            ID and the developer metadata's unique metadataId.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.Int32)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to retrieve metadata from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.MetadataId">
            <summary>The ID of the developer metadata to retrieve.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.Search(Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataRequest,System.String)">
            <summary>Returns all developer metadata matching the specified DataFilter. If the provided DataFilter
            represents a DeveloperMetadataLookup object, this will return all DeveloperMetadata entries selected by
            it. If the DataFilter represents a location in a spreadsheet, this will return all developer metadata
            associated with locations intersecting that region.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to retrieve metadata from.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest">
            <summary>Returns all developer metadata matching the specified DataFilter. If the provided DataFilter
            represents a DeveloperMetadataLookup object, this will return all DeveloperMetadata entries selected by
            it. If the DataFilter represents a location in a spreadsheet, this will return all developer metadata
            associated with locations intersecting that region.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataRequest,System.String)">
            <summary>Constructs a new Search request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to retrieve metadata from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.DeveloperMetadataResource.SearchRequest.InitParameters">
            <summary>Initializes Search parameter list.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.Sheets">
            <summary>Gets the Sheets resource.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource">
            <summary>The "sheets" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyTo(Google.Apis.Sheets.v4.Data.CopySheetToAnotherSpreadsheetRequest,System.String,System.Int32)">
            <summary>Copies a single sheet from a spreadsheet to another spreadsheet. Returns the properties of the
            newly created sheet.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet containing the sheet to copy.</param>
            <param
            name="sheetId">The ID of the sheet to copy.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest">
            <summary>Copies a single sheet from a spreadsheet to another spreadsheet. Returns the properties of the
            newly created sheet.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.CopySheetToAnotherSpreadsheetRequest,System.String,System.Int32)">
            <summary>Constructs a new CopyTo request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet containing the sheet to copy.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.SheetId">
            <summary>The ID of the sheet to copy.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.SheetsResource.CopyToRequest.InitParameters">
            <summary>Initializes CopyTo parameter list.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.Values">
            <summary>Gets the Values resource.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource">
            <summary>The "values" collection of methods.</summary>
        </member>
        <member name="F:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.service">
            <summary>The service which this resource belongs to.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.#ctor(Google.Apis.Services.IClientService)">
            <summary>Constructs a new resource.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.Append(Google.Apis.Sheets.v4.Data.ValueRange,System.String,System.String)">
             <summary>Appends values to a spreadsheet. The input range is used to search for existing data and find a
             "table" within that range. Values will be appended to the next row of the table, starting with the first
             column of the table. See the [guide](/sheets/api/guides/values#appending_values) and [sample
             code](/sheets/api/samples/writing#append_values) for specific details of how tables are detected and
             data is appended.
            
             The caller must specify the spreadsheet ID, range, and a valueInputOption.  The `valueInputOption` only
             controls how the input data will be added to the sheet (column-wise or row-wise), it does not influence
             what cell the data starts being written to.</summary>
             <param name="body">The body of the request.</param>
             <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
             <param name="range">The A1 notation
             of a range to search for a logical table of data. Values will be appended after the last row of the
             table.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest">
             <summary>Appends values to a spreadsheet. The input range is used to search for existing data and find a
             "table" within that range. Values will be appended to the next row of the table, starting with the first
             column of the table. See the [guide](/sheets/api/guides/values#appending_values) and [sample
             code](/sheets/api/samples/writing#append_values) for specific details of how tables are detected and
             data is appended.
            
             The caller must specify the spreadsheet ID, range, and a valueInputOption.  The `valueInputOption` only
             controls how the input data will be added to the sheet (column-wise or row-wise), it does not influence
             what cell the data starts being written to.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.ValueRange,System.String,System.String)">
            <summary>Constructs a new Append request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.Range">
            <summary>The A1 notation of a range to search for a logical table of data. Values will be appended
            after the last row of the table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ResponseValueRenderOption">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ResponseValueRenderOptionEnum">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOption">
            <summary>How the input data should be inserted.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOptionEnum">
            <summary>How the input data should be inserted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOption">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ResponseDateTimeRenderOption">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is
            ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.ResponseDateTimeRenderOptionEnum">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is
            ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.IncludeValuesInResponse">
            <summary>Determines if the update response should include the values of the cells that were
            appended. By default, responses do not include the updated values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.AppendRequest.InitParameters">
            <summary>Initializes Append parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClear(Google.Apis.Sheets.v4.Data.BatchClearValuesRequest,System.String)">
            <summary>Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet
            ID and one or more ranges. Only values are cleared -- all other properties of the cell (such as
            formatting, data validation, etc..) are kept.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest">
            <summary>Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet
            ID and one or more ranges. Only values are cleared -- all other properties of the cell (such as
            formatting, data validation, etc..) are kept.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchClearValuesRequest,System.String)">
            <summary>Constructs a new BatchClear request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearRequest.InitParameters">
            <summary>Initializes BatchClear parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilter(Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterRequest,System.String)">
            <summary>Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet
            ID and one or more DataFilters. Ranges matching any of the specified data filters will be cleared.  Only
            values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are
            kept.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest">
            <summary>Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet
            ID and one or more DataFilters. Ranges matching any of the specified data filters will be cleared.  Only
            values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are
            kept.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterRequest,System.String)">
            <summary>Constructs a new BatchClearByDataFilter request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchClearByDataFilterRequest.InitParameters">
            <summary>Initializes BatchClearByDataFilter parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGet(System.String)">
            <summary>Returns one or more ranges of values from a spreadsheet. The caller must specify the
            spreadsheet ID and one or more ranges.</summary>
            <param name="spreadsheetId">The ID of the spreadsheet to retrieve data from.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest">
            <summary>Returns one or more ranges of values from a spreadsheet. The caller must specify the
            spreadsheet ID and one or more ranges.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new BatchGet request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to retrieve data from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.ValueRenderOption">
            <summary>How values should be represented in the output. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.ValueRenderOptionEnum">
            <summary>How values should be represented in the output. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.DateTimeRenderOption">
            <summary>How dates, times, and durations should be represented in the output. This is ignored if
            value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.DateTimeRenderOptionEnum">
            <summary>How dates, times, and durations should be represented in the output. This is ignored if
            value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.Ranges">
            <summary>The A1 notation of the values to retrieve.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.MajorDimension">
             <summary>The major dimension that results should use.
            
             For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting
             `range=A1:B2,majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas requesting
             `range=A1:B2,majorDimension=COLUMNS` will return `[[1,3],[2,4]]`.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.MajorDimensionEnum">
             <summary>The major dimension that results should use.
            
             For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting
             `range=A1:B2,majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas requesting
             `range=A1:B2,majorDimension=COLUMNS` will return `[[1,3],[2,4]]`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetRequest.InitParameters">
            <summary>Initializes BatchGet parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilter(Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest,System.String)">
            <summary>Returns one or more ranges of values that match the specified data filters. The caller must
            specify the spreadsheet ID and one or more DataFilters.  Ranges that match any of the data filters in
            the request will be returned.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to retrieve data from.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest">
            <summary>Returns one or more ranges of values that match the specified data filters. The caller must
            specify the spreadsheet ID and one or more DataFilters.  Ranges that match any of the data filters in
            the request will be returned.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest,System.String)">
            <summary>Constructs a new BatchGetByDataFilter request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to retrieve data from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchGetByDataFilterRequest.InitParameters">
            <summary>Initializes BatchGetByDataFilter parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdate(Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest,System.String)">
            <summary>Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID,
            a valueInputOption, and one or more ValueRanges.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest">
            <summary>Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID,
            a valueInputOption, and one or more ValueRanges.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest,System.String)">
            <summary>Constructs a new BatchUpdate request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateRequest.InitParameters">
            <summary>Initializes BatchUpdate parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilter(Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest,System.String)">
            <summary>Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID,
            a valueInputOption, and one or more DataFilterValueRanges.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest">
            <summary>Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID,
            a valueInputOption, and one or more DataFilterValueRanges.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest,System.String)">
            <summary>Constructs a new BatchUpdateByDataFilter request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.BatchUpdateByDataFilterRequest.InitParameters">
            <summary>Initializes BatchUpdateByDataFilter parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.Clear(Google.Apis.Sheets.v4.Data.ClearValuesRequest,System.String,System.String)">
            <summary>Clears values from a spreadsheet. The caller must specify the spreadsheet ID and range. Only
            values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are
            kept.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
            <param name="range">The A1 notation
            of the values to clear.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest">
            <summary>Clears values from a spreadsheet. The caller must specify the spreadsheet ID and range. Only
            values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are
            kept.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.ClearValuesRequest,System.String,System.String)">
            <summary>Constructs a new Clear request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.Range">
            <summary>The A1 notation of the values to clear.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.ClearRequest.InitParameters">
            <summary>Initializes Clear parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.Get(System.String,System.String)">
            <summary>Returns a range of values from a spreadsheet. The caller must specify the spreadsheet ID and a
            range.</summary>
            <param name="spreadsheetId">The ID of the spreadsheet to retrieve data from.</param>
            <param name="range">The
            A1 notation of the values to retrieve.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest">
            <summary>Returns a range of values from a spreadsheet. The caller must specify the spreadsheet ID and a
            range.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to retrieve data from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.Range">
            <summary>The A1 notation of the values to retrieve.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.ValueRenderOption">
            <summary>How values should be represented in the output. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.ValueRenderOptionEnum">
            <summary>How values should be represented in the output. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.DateTimeRenderOption">
            <summary>How dates, times, and durations should be represented in the output. This is ignored if
            value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.DateTimeRenderOptionEnum">
            <summary>How dates, times, and durations should be represented in the output. This is ignored if
            value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.MajorDimension">
             <summary>The major dimension that results should use.
            
             For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting
             `range=A1:B2,majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas requesting
             `range=A1:B2,majorDimension=COLUMNS` will return `[[1,3],[2,4]]`.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.MajorDimensionEnum">
             <summary>The major dimension that results should use.
            
             For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting
             `range=A1:B2,majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas requesting
             `range=A1:B2,majorDimension=COLUMNS` will return `[[1,3],[2,4]]`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.Update(Google.Apis.Sheets.v4.Data.ValueRange,System.String,System.String)">
            <summary>Sets values in a range of a spreadsheet. The caller must specify the spreadsheet ID, range, and
            a valueInputOption.</summary>
            <param name="body">The body of the request.</param>
            <param name="spreadsheetId">The ID of the spreadsheet to update.</param>
            <param name="range">The A1 notation
            of the values to update.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest">
            <summary>Sets values in a range of a spreadsheet. The caller must specify the spreadsheet ID, range, and
            a valueInputOption.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.ValueRange,System.String,System.String)">
            <summary>Constructs a new Update request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.SpreadsheetId">
            <summary>The ID of the spreadsheet to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.Range">
            <summary>The A1 notation of the values to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ResponseValueRenderOption">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ResponseValueRenderOptionEnum">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOption">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ResponseDateTimeRenderOption">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is
            ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            DateTimeRenderOption.SERIAL_NUMBER.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.ResponseDateTimeRenderOptionEnum">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is
            ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            DateTimeRenderOption.SERIAL_NUMBER.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.IncludeValuesInResponse">
            <summary>Determines if the update response should include the values of the cells that were updated.
            By default, responses do not include the updated values. If the range to write was larger than than
            the range actually written, the response will include all values in the requested range (excluding
            trailing empty rows and columns).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.ValuesResource.UpdateRequest.InitParameters">
            <summary>Initializes Update parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdate(Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest,System.String)">
             <summary>Applies one or more updates to the spreadsheet.
            
             Each request is validated before being applied. If any request is not valid then the entire request will
             fail and nothing will be applied.
            
             Some requests have replies to give you some information about how they are applied. The replies will mirror
             the requests.  For example, if you applied 4 updates and the 3rd one had a reply, then the response will
             have 2 empty replies, the actual reply, and another empty reply, in that order.
            
             Due to the collaborative nature of spreadsheets, it is not guaranteed that the spreadsheet will reflect
             exactly your changes after this completes, however it is guaranteed that the updates in the request will be
             applied together atomically. Your changes may be altered with respect to collaborator changes. If there are
             no collaborators, the spreadsheet should reflect your changes.</summary>
             <param name="body">The body of the request.</param>
             <param name="spreadsheetId">The spreadsheet to apply the updates to.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest">
             <summary>Applies one or more updates to the spreadsheet.
            
             Each request is validated before being applied. If any request is not valid then the entire request will
             fail and nothing will be applied.
            
             Some requests have replies to give you some information about how they are applied. The replies will mirror
             the requests.  For example, if you applied 4 updates and the 3rd one had a reply, then the response will
             have 2 empty replies, the actual reply, and another empty reply, in that order.
            
             Due to the collaborative nature of spreadsheets, it is not guaranteed that the spreadsheet will reflect
             exactly your changes after this completes, however it is guaranteed that the updates in the request will be
             applied together atomically. Your changes may be altered with respect to collaborator changes. If there are
             no collaborators, the spreadsheet should reflect your changes.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest,System.String)">
            <summary>Constructs a new BatchUpdate request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.SpreadsheetId">
            <summary>The spreadsheet to apply the updates to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.BatchUpdateRequest.InitParameters">
            <summary>Initializes BatchUpdate parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.Create(Google.Apis.Sheets.v4.Data.Spreadsheet)">
            <summary>Creates a spreadsheet, returning the newly created spreadsheet.</summary>
            <param name="body">The body of the request.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest">
            <summary>Creates a spreadsheet, returning the newly created spreadsheet.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.Spreadsheet)">
            <summary>Constructs a new Create request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.CreateRequest.InitParameters">
            <summary>Initializes Create parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.Get(System.String)">
             <summary>Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID.
            
             By default, data within grids will not be returned. You can include grid data one of two ways:
            
             * Specify a field mask listing your desired fields using the `fields` URL parameter in HTTP
            
             * Set the includeGridData URL parameter to true.  If a field mask is set, the `includeGridData` parameter is
             ignored
            
             For large spreadsheets, it is recommended to retrieve only the specific fields of the spreadsheet that you
             want.
            
             To retrieve only subsets of the spreadsheet, use the ranges URL parameter. Multiple ranges can be specified.
             Limiting the range will return only the portions of the spreadsheet that intersect the requested ranges.
             Ranges are specified using A1 notation.</summary>
             <param name="spreadsheetId">The spreadsheet to request.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest">
             <summary>Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID.
            
             By default, data within grids will not be returned. You can include grid data one of two ways:
            
             * Specify a field mask listing your desired fields using the `fields` URL parameter in HTTP
            
             * Set the includeGridData URL parameter to true.  If a field mask is set, the `includeGridData` parameter is
             ignored
            
             For large spreadsheets, it is recommended to retrieve only the specific fields of the spreadsheet that you
             want.
            
             To retrieve only subsets of the spreadsheet, use the ranges URL parameter. Multiple ranges can be specified.
             Limiting the range will return only the portions of the spreadsheet that intersect the requested ranges.
             Ranges are specified using A1 notation.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.#ctor(Google.Apis.Services.IClientService,System.String)">
            <summary>Constructs a new Get request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.SpreadsheetId">
            <summary>The spreadsheet to request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.Ranges">
            <summary>The ranges to retrieve from the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.IncludeGridData">
            <summary>True if grid data should be returned. This parameter is ignored if a field mask was set in the
            request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetRequest.InitParameters">
            <summary>Initializes Get parameter list.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilter(Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest,System.String)">
             <summary>Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID.
            
             This method differs from GetSpreadsheet in that it allows selecting which subsets of spreadsheet data to
             return by specifying a dataFilters parameter. Multiple DataFilters can be specified.  Specifying one or more
             data filters will return the portions of the spreadsheet that intersect ranges matched by any of the
             filters.
            
             By default, data within grids will not be returned. You can include grid data one of two ways:
            
             * Specify a field mask listing your desired fields using the `fields` URL parameter in HTTP
            
             * Set the includeGridData parameter to true.  If a field mask is set, the `includeGridData` parameter is
             ignored
            
             For large spreadsheets, it is recommended to retrieve only the specific fields of the spreadsheet that you
             want.</summary>
             <param name="body">The body of the request.</param>
             <param name="spreadsheetId">The spreadsheet to request.</param>
        </member>
        <member name="T:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest">
             <summary>Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID.
            
             This method differs from GetSpreadsheet in that it allows selecting which subsets of spreadsheet data to
             return by specifying a dataFilters parameter. Multiple DataFilters can be specified.  Specifying one or more
             data filters will return the portions of the spreadsheet that intersect ranges matched by any of the
             filters.
            
             By default, data within grids will not be returned. You can include grid data one of two ways:
            
             * Specify a field mask listing your desired fields using the `fields` URL parameter in HTTP
            
             * Set the includeGridData parameter to true.  If a field mask is set, the `includeGridData` parameter is
             ignored
            
             For large spreadsheets, it is recommended to retrieve only the specific fields of the spreadsheet that you
             want.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.#ctor(Google.Apis.Services.IClientService,Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest,System.String)">
            <summary>Constructs a new GetByDataFilter request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.SpreadsheetId">
            <summary>The spreadsheet to request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.Body">
            <summary>Gets or sets the body of this request.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.GetBody">
            <summary>Returns the body of the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.MethodName">
            <summary>Gets the method name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.HttpMethod">
            <summary>Gets the HTTP method.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.RestPath">
            <summary>Gets the REST path.</summary>
        </member>
        <member name="M:Google.Apis.Sheets.v4.SpreadsheetsResource.GetByDataFilterRequest.InitParameters">
            <summary>Initializes GetByDataFilter parameter list.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddBandingRequest">
            <summary>Adds a new banded range to the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddBandingRequest.BandedRange">
            <summary>The banded range to add. The bandedRangeId field is optional; if one is not set, an id will be
            randomly generated. (It is an error to specify the ID of a range that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddBandingRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddBandingResponse">
            <summary>The result of adding a banded range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddBandingResponse.BandedRange">
            <summary>The banded range that was added.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddBandingResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddChartRequest">
            <summary>Adds a chart to a sheet in the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddChartRequest.Chart">
            <summary>The chart that should be added to the spreadsheet, including the position where it should be
            placed. The chartId field is optional; if one is not set, an id will be randomly generated. (It is an error
            to specify the ID of an embedded object that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddChartRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddChartResponse">
            <summary>The result of adding a chart to a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddChartResponse.Chart">
            <summary>The newly added chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddChartResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddConditionalFormatRuleRequest">
            <summary>Adds a new conditional format rule at the given index. All subsequent rules' indexes are
            incremented.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddConditionalFormatRuleRequest.Index">
            <summary>The zero-based index where the rule should be inserted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddConditionalFormatRuleRequest.Rule">
            <summary>The rule to add.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddConditionalFormatRuleRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddDimensionGroupRequest">
             <summary>Creates a group over the specified range.
            
             If the requested range is a superset of the range of an existing group G, then the depth of G is incremented and
             this new group G' has the depth of that group. For example, a group [C:D, depth 1] + [B:E] results in groups
             [B:E, depth 1] and [C:D, depth 2]. If the requested range is a subset of the range of an existing group G, then
             the depth of the new group G' becomes one greater than the depth of G. For example, a group [B:E, depth 1] +
             [C:D] results in groups [B:E, depth 1] and [C:D, depth 2]. If the requested range starts before and ends within,
             or starts within and ends after, the range of an existing group G, then the range of the existing group G
             becomes the union of the ranges, and the new group G' has depth one greater than the depth of G and range as the
             intersection of the ranges. For example, a group [B:D, depth 1] + [C:E] results in groups [B:E, depth 1] and
             [C:D, depth 2].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddDimensionGroupRequest.Range">
            <summary>The range over which to create a group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddDimensionGroupRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddDimensionGroupResponse">
            <summary>The result of adding a group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddDimensionGroupResponse.DimensionGroups">
            <summary>All groups of a dimension after adding a group to that dimension.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddDimensionGroupResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddFilterViewRequest">
            <summary>Adds a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddFilterViewRequest.Filter">
            <summary>The filter to add. The filterViewId field is optional; if one is not set, an id will be randomly
            generated. (It is an error to specify the ID of a filter that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddFilterViewRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddFilterViewResponse">
            <summary>The result of adding a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddFilterViewResponse.Filter">
            <summary>The newly added filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddFilterViewResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddNamedRangeRequest">
            <summary>Adds a named range to the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddNamedRangeRequest.NamedRange">
            <summary>The named range to add. The namedRangeId field is optional; if one is not set, an id will be
            randomly generated. (It is an error to specify the ID of a range that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddNamedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddNamedRangeResponse">
            <summary>The result of adding a named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddNamedRangeResponse.NamedRange">
            <summary>The named range to add.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddNamedRangeResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddProtectedRangeRequest">
            <summary>Adds a new protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddProtectedRangeRequest.ProtectedRange">
            <summary>The protected range to be added. The protectedRangeId field is optional; if one is not set, an id
            will be randomly generated. (It is an error to specify the ID of a range that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddProtectedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddProtectedRangeResponse">
            <summary>The result of adding a new protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddProtectedRangeResponse.ProtectedRange">
            <summary>The newly added protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddProtectedRangeResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddSheetRequest">
            <summary>Adds a new sheet. When a sheet is added at a given index, all subsequent sheets' indexes are
            incremented. To add an object sheet, use AddChartRequest instead and specify EmbeddedObjectPosition.sheetId or
            EmbeddedObjectPosition.newSheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSheetRequest.Properties">
            <summary>The properties the new sheet should have. All properties are optional. The sheetId field is
            optional; if one is not set, an id will be randomly generated. (It is an error to specify the ID of a sheet
            that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSheetRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddSheetResponse">
            <summary>The result of adding a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSheetResponse.Properties">
            <summary>The properties of the newly added sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSheetResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddSlicerRequest">
            <summary>Adds a slicer to a sheet in the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSlicerRequest.Slicer">
            <summary>The slicer that should be added to the spreadsheet, including the position where it should be
            placed. The slicerId field is optional; if one is not set, an id will be randomly generated. (It is an error
            to specify the ID of a slicer that already exists.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSlicerRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AddSlicerResponse">
            <summary>The result of adding a slicer to a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSlicerResponse.Slicer">
            <summary>The newly added slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AddSlicerResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AppendCellsRequest">
            <summary>Adds new cells after the last row with data in a sheet, inserting new rows into the sheet if
            necessary.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendCellsRequest.Fields">
            <summary>The fields of CellData that should be updated. At least one field must be specified. The root is
            the CellData; 'row.values.' should not be specified. A single `"*"` can be used as short-hand for listing
            every field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendCellsRequest.Rows">
            <summary>The data to append.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendCellsRequest.SheetId">
            <summary>The sheet ID to append the data to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendCellsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AppendDimensionRequest">
            <summary>Appends rows or columns to the end of a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendDimensionRequest.Dimension">
            <summary>Whether rows or columns should be appended.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendDimensionRequest.Length">
            <summary>The number of rows or columns to append.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendDimensionRequest.SheetId">
            <summary>The sheet to append rows or columns to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendDimensionRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AppendValuesResponse">
            <summary>The response when updating a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendValuesResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendValuesResponse.TableRange">
            <summary>The range (in A1 notation) of the table that values are being appended to (before the values were
            appended). Empty if no table was found.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendValuesResponse.Updates">
            <summary>Information about the updates that were applied.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AppendValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AutoFillRequest">
            <summary>Fills in more data based on existing data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoFillRequest.Range">
            <summary>The range to autofill. This will examine the range and detect the location that has data and
            automatically fill that data in to the rest of the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoFillRequest.SourceAndDestination">
            <summary>The source and destination areas to autofill. This explicitly lists the source of the autofill and
            where to extend that data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoFillRequest.UseAlternateSeries">
            <summary>True if we should generate data with the "alternate" series. This differs based on the type and
            amount of source data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoFillRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.AutoResizeDimensionsRequest">
            <summary>Automatically resizes one or more dimensions based on the contents of the cells in that
            dimension.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoResizeDimensionsRequest.Dimensions">
            <summary>The dimensions to automatically resize.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.AutoResizeDimensionsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BandedRange">
            <summary>A banded (alternating colors) range in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandedRange.BandedRangeId">
            <summary>The id of the banded range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandedRange.ColumnProperties">
            <summary>Properties for column bands. These properties are applied on a column- by-column basis throughout
            all the columns in the range. At least one of row_properties or column_properties must be
            specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandedRange.Range">
            <summary>The range over which these properties are applied.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandedRange.RowProperties">
            <summary>Properties for row bands. These properties are applied on a row-by-row basis throughout all the
            rows in the range. At least one of row_properties or column_properties must be specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandedRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BandingProperties">
             <summary>Properties referring a single dimension (either row or column). If both BandedRange.row_properties and
             BandedRange.column_properties are set, the fill colors are applied to cells according to the following rules:
            
             * header_color and footer_color take priority over band colors. * first_band_color takes priority over
             second_band_color. * row_properties takes priority over column_properties.
            
             For example, the first row color takes priority over the first column color, but the first column color takes
             priority over the second row color. Similarly, the row header takes priority over the column header in the top
             left cell, but the column header takes priority over the first row color if the row header is not set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandingProperties.FirstBandColor">
            <summary>The first color that is alternating. (Required)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandingProperties.FooterColor">
            <summary>The color of the last row or column. If this field is not set, the last row or column will be
            filled with either first_band_color or second_band_color, depending on the color of the previous row or
            column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandingProperties.HeaderColor">
            <summary>The color of the first row or column. If this field is set, the first row or column will be filled
            with this color and the colors will alternate between first_band_color and second_band_color starting from
            the second row or column. Otherwise, the first row or column will be filled with first_band_color and the
            colors will proceed to alternate as they normally would.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandingProperties.SecondBandColor">
            <summary>The second color that is alternating. (Required)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BandingProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BaselineValueFormat">
            <summary>Formatting options for baseline value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.ComparisonType">
            <summary>The comparison type of key value with baseline value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.Description">
            <summary>Description which is appended after the baseline value. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.NegativeColor">
            <summary>Color to be used, in case baseline value represents a negative change for key value. This field is
            optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.Position">
            <summary>Specifies the horizontal text positioning of baseline value. This field is optional. If not
            specified, default positioning is used.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.PositiveColor">
            <summary>Color to be used, in case baseline value represents a positive change for key value. This field is
            optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.TextFormat">
            <summary>Text formatting options for baseline value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BaselineValueFormat.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BasicChartAxis">
            <summary>An axis of the chart. A chart may not have more than one axis per axis position.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.Format">
            <summary>The format of the title. Only valid if the axis is not associated with the domain.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.Position">
            <summary>The position of this axis.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.Title">
            <summary>The title of this axis. If set, this overrides any title inferred from headers of the
            data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.TitleTextPosition">
            <summary>The axis title text position.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.ViewWindowOptions">
            <summary>The view window options for this axis.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartAxis.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BasicChartDomain">
            <summary>The domain of a chart. For example, if charting stock prices over time, this would be the
            date.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartDomain.Domain">
            <summary>The data of the domain. For example, if charting stock prices over time, this is the data
            representing the dates.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartDomain.Reversed">
            <summary>True to reverse the order of the domain values (horizontal axis).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartDomain.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BasicChartSeries">
            <summary>A single series of data in a chart. For example, if charting stock prices over time, multiple series
            may exist, one for the "Open Price", "High Price", "Low Price" and "Close Price".</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.Color">
            <summary>The color for elements (i.e. bars, lines, points) associated with this series.  If empty, a default
            color is used.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.LineStyle">
            <summary>The line style of this series. Valid only if the chartType is AREA, LINE, or SCATTER. COMBO charts
            are also supported if the series chart type is AREA or LINE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.Series">
            <summary>The data being visualized in this chart series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.TargetAxis">
            <summary>The minor axis that will specify the range of values for this series. For example, if charting
            stocks over time, the "Volume" series may want to be pinned to the right with the prices pinned to the left,
            because the scale of trading volume is different than the scale of prices. It is an error to specify an axis
            that isn't a valid minor axis for the chart's type.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.Type">
            <summary>The type of this series. Valid only if the chartType is COMBO. Different types will change the way
            the series is visualized. Only LINE, AREA, and COLUMN are supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSeries.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BasicChartSpec">
            <summary>The specification for a basic chart.  See BasicChartType for the list of charts this
            supports.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.Axis">
            <summary>The axis on the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.ChartType">
            <summary>The type of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.CompareMode">
            <summary>The behavior of tooltips and data highlighting when hovering on data and chart area.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.Domains">
            <summary>The domain of data this is charting. Only a single domain is supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.HeaderCount">
             <summary>The number of rows or columns in the data that are "headers". If not set, Google Sheets will guess
             how many rows are headers based on the data.
            
             (Note that BasicChartAxis.title may override the axis title inferred from the header values.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.InterpolateNulls">
            <summary>If some values in a series are missing, gaps may appear in the chart (e.g, segments of lines in a
            line chart will be missing).  To eliminate these gaps set this to true. Applies to Line, Area, and Combo
            charts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.LegendPosition">
            <summary>The position of the chart legend.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.LineSmoothing">
            <summary>Gets whether all lines should be rendered smooth or straight by default. Applies to Line
            charts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.Series">
            <summary>The data this chart is visualizing.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.StackedType">
            <summary>The stacked type for charts that support vertical stacking. Applies to Area, Bar, Column, Combo,
            and Stepped Area charts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.ThreeDimensional">
            <summary>True to make the chart 3D. Applies to Bar and Column charts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BasicFilter">
            <summary>The default filter associated with a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicFilter.Criteria">
            <summary>The criteria for showing/hiding values per column. The map's key is the column index, and the value
            is the criteria for that column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicFilter.Range">
            <summary>The range the filter covers.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicFilter.SortSpecs">
            <summary>The sort order per column. Later specifications are used when values are equal in the earlier
            specifications.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BasicFilter.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterRequest">
            <summary>The request for clearing more than one range selected by a DataFilter in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterRequest.DataFilters">
            <summary>The DataFilters used to determine which ranges to clear.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterResponse">
            <summary>The response when clearing a range of values selected with DataFilters in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterResponse.ClearedRanges">
            <summary>The ranges that were cleared, in A1 notation. (If the requests were for an unbounded range or a
            ranger larger than the bounds of the sheet, this will be the actual ranges that were cleared, bounded to the
            sheet's limits.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesByDataFilterResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchClearValuesRequest">
            <summary>The request for clearing more than one range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesRequest.Ranges">
            <summary>The ranges to clear, in A1 notation.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchClearValuesResponse">
            <summary>The response when clearing a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesResponse.ClearedRanges">
            <summary>The ranges that were cleared, in A1 notation. (If the requests were for an unbounded range or a
            ranger larger than the bounds of the sheet, this will be the actual ranges that were cleared, bounded to the
            sheet's limits.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchClearValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest">
            <summary>The request for retrieving a range of values in a spreadsheet selected by a set of
            DataFilters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest.DataFilters">
            <summary>The data filters used to match the ranges of values to retrieve.  Ranges that match any of the
            specified data filters will be included in the response.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest.DateTimeRenderOption">
            <summary>How dates, times, and durations should be represented in the output. This is ignored if
            value_render_option is FORMATTED_VALUE. The default dateTime render option is
            [DateTimeRenderOption.SERIAL_NUMBER].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest.MajorDimension">
             <summary>The major dimension that results should use.
            
             For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then a request that selects that range and
             sets `majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas a request that sets `majorDimension=COLUMNS`
             will return `[[1,3],[2,4]]`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest.ValueRenderOption">
            <summary>How values should be represented in the output. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterResponse">
            <summary>The response when retrieving more than one range of values in a spreadsheet selected by
            DataFilters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterResponse.SpreadsheetId">
            <summary>The ID of the spreadsheet the data was retrieved from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterResponse.ValueRanges">
            <summary>The requested values with the list of data filters that matched them.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesByDataFilterResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchGetValuesResponse">
            <summary>The response when retrieving more than one range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesResponse.SpreadsheetId">
            <summary>The ID of the spreadsheet the data was retrieved from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesResponse.ValueRanges">
            <summary>The requested values. The order of the ValueRanges is the same as the order of the requested
            ranges.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchGetValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest">
            <summary>The request for updating any aspect of a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest.IncludeSpreadsheetInResponse">
            <summary>Determines if the update response should include the spreadsheet resource.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest.Requests">
            <summary>A list of updates to apply to the spreadsheet. Requests will be applied in the order they are
            specified. If any request is not valid, no requests will be applied.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest.ResponseIncludeGridData">
            <summary>True if grid data should be returned. Meaningful only if if include_spreadsheet_in_response is
            'true'. This parameter is ignored if a field mask was set in the request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest.ResponseRanges">
            <summary>Limits the ranges included in the response spreadsheet. Meaningful only if
            include_spreadsheet_response is 'true'.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetResponse">
            <summary>The reply for batch updating a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetResponse.Replies">
            <summary>The reply of the updates.  This maps 1:1 with the updates, although replies to some requests may be
            empty.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetResponse.UpdatedSpreadsheet">
            <summary>The spreadsheet after updates were applied. This is only set if
            [BatchUpdateSpreadsheetRequest.include_spreadsheet_in_response] is `true`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest">
            <summary>The request for updating more than one range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.Data">
            <summary>The new values to apply to the spreadsheet.  If more than one range is matched by the specified
            DataFilter the specified values will be applied to all of those ranges.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.IncludeValuesInResponse">
            <summary>Determines if the update response should include the values of the cells that were updated. By
            default, responses do not include the updated values. The `updatedData` field within each of the
            BatchUpdateValuesResponse.responses will contain the updated values. If the range to write was larger than
            than the range actually written, the response will include all values in the requested range (excluding
            trailing empty rows and columns).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.ResponseDateTimeRenderOption">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is ignored if
            response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            DateTimeRenderOption.SERIAL_NUMBER.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.ResponseValueRenderOption">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.ValueInputOption">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse">
            <summary>The response when updating a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.Responses">
            <summary>The response for each range updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.TotalUpdatedCells">
            <summary>The total number of cells updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.TotalUpdatedColumns">
            <summary>The total number of columns where at least one cell in the column was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.TotalUpdatedRows">
            <summary>The total number of rows where at least one cell in the row was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.TotalUpdatedSheets">
            <summary>The total number of sheets where at least one cell in the sheet was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesByDataFilterResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest">
            <summary>The request for updating more than one range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.Data">
            <summary>The new values to apply to the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.IncludeValuesInResponse">
            <summary>Determines if the update response should include the values of the cells that were updated. By
            default, responses do not include the updated values. The `updatedData` field within each of the
            BatchUpdateValuesResponse.responses will contain the updated values. If the range to write was larger than
            than the range actually written, the response will include all values in the requested range (excluding
            trailing empty rows and columns).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.ResponseDateTimeRenderOption">
            <summary>Determines how dates, times, and durations in the response should be rendered. This is ignored if
            response_value_render_option is FORMATTED_VALUE. The default dateTime render option is
            DateTimeRenderOption.SERIAL_NUMBER.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.ResponseValueRenderOption">
            <summary>Determines how values in the response should be rendered. The default render option is
            ValueRenderOption.FORMATTED_VALUE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.ValueInputOption">
            <summary>How the input data should be interpreted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse">
            <summary>The response when updating a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.Responses">
            <summary>One UpdateValuesResponse per requested range, in the same order as the requests appeared.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.TotalUpdatedCells">
            <summary>The total number of cells updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.TotalUpdatedColumns">
            <summary>The total number of columns where at least one cell in the column was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.TotalUpdatedRows">
            <summary>The total number of rows where at least one cell in the row was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.TotalUpdatedSheets">
            <summary>The total number of sheets where at least one cell in the sheet was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BatchUpdateValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BooleanCondition">
            <summary>A condition that can evaluate to true or false. BooleanConditions are used by conditional formatting,
            data validation, and the criteria in filters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BooleanCondition.Type">
            <summary>The type of condition.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BooleanCondition.Values">
            <summary>The values of the condition. The number of supported values depends on the condition type.  Some
            support zero values, others one or two values, and ConditionType.ONE_OF_LIST supports an arbitrary number of
            values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BooleanCondition.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BooleanRule">
            <summary>A rule that may or may not match, depending on the condition.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BooleanRule.Condition">
            <summary>The condition of the rule. If the condition evaluates to true, the format is applied.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Google.Apis.Sheets.v4.Data.BooleanRule.Format" -->
        <member name="P:Google.Apis.Sheets.v4.Data.BooleanRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Border">
            <summary>A border along a cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Border.Color">
            <summary>The color of the border.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Border.Style">
            <summary>The style of the border.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Border.Width">
            <summary>The width of the border, in pixels. Deprecated; the width is determined by the "style"
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Border.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Borders">
            <summary>The borders of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Borders.Bottom">
            <summary>The bottom border of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Borders.Left">
            <summary>The left border of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Borders.Right">
            <summary>The right border of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Borders.Top">
            <summary>The top border of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Borders.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.BubbleChartSpec">
            <summary>A bubble chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleBorderColor">
            <summary>The bubble border color.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleLabels">
            <summary>The data containing the bubble labels.  These do not need to be unique.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleMaxRadiusSize">
            <summary>The max radius size of the bubbles, in pixels. If specified, the field must be a positive
            value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleMinRadiusSize">
            <summary>The minimum radius size of the bubbles, in pixels. If specific, the field must be a positive
            value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleOpacity">
            <summary>The opacity of the bubbles between 0 and 1.0. 0 is fully transparent and 1 is fully
            opaque.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleSizes">
            <summary>The data contianing the bubble sizes.  Bubble sizes are used to draw the bubbles at different sizes
            relative to each other. If specified, group_ids must also be specified.  This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.BubbleTextStyle">
            <summary>The format of the text inside the bubbles. Underline and Strikethrough are not supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.Domain">
            <summary>The data containing the bubble x-values.  These values locate the bubbles in the chart
            horizontally.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.GroupIds">
            <summary>The data containing the bubble group IDs. All bubbles with the same group ID are drawn in the same
            color. If bubble_sizes is specified then this field must also be specified but may contain blank values.
            This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.LegendPosition">
            <summary>Where the legend of the chart should be drawn.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.Series">
            <summary>The data contianing the bubble y-values.  These values locate the bubbles in the chart
            vertically.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.BubbleChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CandlestickChartSpec">
            <summary>A candlestick chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickChartSpec.Data">
            <summary>The Candlestick chart data. Only one CandlestickData is supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickChartSpec.Domain">
            <summary>The domain data (horizontal axis) for the candlestick chart.  String data will be treated as
            discrete labels, other data will be treated as continuous values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CandlestickData">
            <summary>The Candlestick chart data, each containing the low, open, close, and high values for a
            series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickData.CloseSeries">
            <summary>The range data (vertical axis) for the close/final value for each candle. This is the top of the
            candle body.  If greater than the open value the candle will be filled.  Otherwise the candle will be
            hollow.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickData.HighSeries">
            <summary>The range data (vertical axis) for the high/maximum value for each candle. This is the top of the
            candle's center line.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickData.LowSeries">
            <summary>The range data (vertical axis) for the low/minimum value for each candle. This is the bottom of the
            candle's center line.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickData.OpenSeries">
            <summary>The range data (vertical axis) for the open/initial value for each candle. This is the bottom of
            the candle body.  If less than the close value the candle will be filled.  Otherwise the candle will be
            hollow.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CandlestickDomain">
            <summary>The domain of a CandlestickChart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickDomain.Data">
            <summary>The data of the CandlestickDomain.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickDomain.Reversed">
            <summary>True to reverse the order of the domain values (horizontal axis).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickDomain.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CandlestickSeries">
            <summary>The series of a CandlestickData.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickSeries.Data">
            <summary>The data of the CandlestickSeries.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CandlestickSeries.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CellData">
            <summary>Data about a specific cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.DataValidation">
             <summary>A data validation rule on the cell, if any.
            
             When writing, the new data validation rule will overwrite any prior rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.EffectiveFormat">
            <summary>The effective format being used by the cell. This includes the results of applying any conditional
            formatting and, if the cell contains a formula, the computed number format. If the effective format is the
            default format, effective format will not be written. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.EffectiveValue">
            <summary>The effective value of the cell. For cells with formulas, this is the calculated value.  For cells
            with literals, this is the same as the user_entered_value. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.FormattedValue">
            <summary>The formatted value of the cell. This is the value as it's shown to the user. This field is read-
            only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.Hyperlink">
            <summary>A hyperlink this cell points to, if any. This field is read-only.  (To set it, use a `=HYPERLINK`
            formula in the userEnteredValue.formulaValue field.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.Note">
            <summary>Any note on the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.PivotTable">
            <summary>A pivot table anchored at this cell. The size of pivot table itself is computed dynamically based
            on its data, grouping, filters, values, etc. Only the top-left cell of the pivot table contains the pivot
            table definition. The other cells will contain the calculated values of the results of the pivot in their
            effective_value fields.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.TextFormatRuns">
             <summary>Runs of rich text applied to subsections of the cell.  Runs are only valid on user entered strings,
             not formulas, bools, or numbers. Runs start at specific indexes in the text and continue until the next run.
             Properties of a run will continue unless explicitly changed in a subsequent run (and properties of the first
             run will continue the properties of the cell unless explicitly changed).
            
             When writing, the new runs will overwrite any prior runs.  When writing a new user_entered_value, previous
             runs are erased.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.UserEnteredFormat">
             <summary>The format the user entered for the cell.
            
             When writing, the new format will be merged with the existing format.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.UserEnteredValue">
            <summary>The value the user entered in the cell. e.g, `1234`, `'Hello'`, or `=NOW()` Note: Dates, Times and
            DateTimes are represented as doubles in serial number format.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CellFormat">
            <summary>The format of a cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.BackgroundColor">
            <summary>The background color of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.Borders">
            <summary>The borders of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.HorizontalAlignment">
            <summary>The horizontal alignment of the value in the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.HyperlinkDisplayType">
            <summary>How a hyperlink, if it exists, should be displayed in the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.NumberFormat">
            <summary>A format describing how number values should be represented to the user.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.Padding">
            <summary>The padding of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.TextDirection">
            <summary>The direction of the text in the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.TextFormat">
            <summary>The format of the text in the cell (unless overridden by a format run).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.TextRotation">
            <summary>The rotation applied to text in a cell</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.VerticalAlignment">
            <summary>The vertical alignment of the value in the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.WrapStrategy">
            <summary>The wrap strategy for the value in the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CellFormat.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ChartAxisViewWindowOptions">
            <summary>The options that define a "view window" for a chart (such as the visible values in an axis).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartAxisViewWindowOptions.ViewWindowMax">
            <summary>The maximum numeric value to be shown in this view window. If unset, will automatically determine a
            maximum value that looks good for the data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartAxisViewWindowOptions.ViewWindowMin">
            <summary>The minimum numeric value to be shown in this view window. If unset, will automatically determine a
            minimum value that looks good for the data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartAxisViewWindowOptions.ViewWindowMode">
            <summary>The view window's mode.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartAxisViewWindowOptions.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ChartCustomNumberFormatOptions">
            <summary>Custom number formatting options for chart attributes.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartCustomNumberFormatOptions.Prefix">
            <summary>Custom prefix to be prepended to the chart attribute. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartCustomNumberFormatOptions.Suffix">
            <summary>Custom suffix to be appended to the chart attribute. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartCustomNumberFormatOptions.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ChartData">
            <summary>The data included in a domain or series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartData.SourceRange">
            <summary>The source ranges of the data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ChartSourceRange">
            <summary>Source ranges for a chart.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Google.Apis.Sheets.v4.Data.ChartSourceRange.Sources" -->
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSourceRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ChartSpec">
            <summary>The specifications of a chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.AltText">
            <summary>The alternative text that describes the chart.  This is often used for accessibility.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.BackgroundColor">
            <summary>The background color of the entire chart. Not applicable to Org charts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.BasicChart">
            <summary>A basic chart specification, can be one of many kinds of charts. See BasicChartType for the list of
            all charts this supports.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.BubbleChart">
            <summary>A bubble chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.CandlestickChart">
            <summary>A candlestick chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.FontName">
            <summary>The name of the font to use by default for all chart text (e.g. title, axis labels, legend).  If a
            font is specified for a specific part of the chart it will override this font name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.HiddenDimensionStrategy">
            <summary>Determines how the charts will use hidden rows or columns.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.HistogramChart">
            <summary>A histogram chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.Maximized">
            <summary>True to make a chart fill the entire space in which it's rendered with minimum padding.  False to
            use the default padding. (Not applicable to Geo and Org charts.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.OrgChart">
            <summary>An org chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.PieChart">
            <summary>A pie chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.ScorecardChart">
            <summary>A scorecard chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.Subtitle">
            <summary>The subtitle of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.SubtitleTextFormat">
            <summary>The subtitle text format. Strikethrough and underline are not supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.SubtitleTextPosition">
            <summary>The subtitle text position. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.Title">
            <summary>The title of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.TitleTextFormat">
            <summary>The title text format. Strikethrough and underline are not supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.TitleTextPosition">
            <summary>The title text position. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.TreemapChart">
            <summary>A treemap chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.WaterfallChart">
            <summary>A waterfall chart specification.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ClearBasicFilterRequest">
            <summary>Clears the basic filter, if any exists on the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearBasicFilterRequest.SheetId">
            <summary>The sheet ID on which the basic filter should be cleared.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearBasicFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ClearValuesRequest">
            <summary>The request for clearing a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearValuesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ClearValuesResponse">
            <summary>The response when clearing a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearValuesResponse.ClearedRange">
            <summary>The range (in A1 notation) that was cleared. (If the request was for an unbounded range or a ranger
            larger than the bounds of the sheet, this will be the actual range that was cleared, bounded to the sheet's
            limits.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearValuesResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ClearValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Google.Apis.Sheets.v4.Data.Color" -->
        <member name="P:Google.Apis.Sheets.v4.Data.Color.Alpha">
             <summary>The fraction of this color that should be applied to the pixel. That is, the final pixel color is
             defined by the equation:
            
             pixel color = alpha * (this color) + (1.0 - alpha) * (background color)
            
             This means that a value of 1.0 corresponds to a solid color, whereas a value of 0.0 corresponds to a
             completely transparent color. This uses a wrapper message rather than a simple float scalar so that it is
             possible to distinguish between a default value and the value being unset. If omitted, this color object is
             to be rendered as a solid color (as if the alpha value had been explicitly given with a value of
             1.0).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Color.Blue">
            <summary>The amount of blue in the color as a value in the interval [0, 1].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Color.Green">
            <summary>The amount of green in the color as a value in the interval [0, 1].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Color.Red">
            <summary>The amount of red in the color as a value in the interval [0, 1].</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Color.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ConditionValue">
            <summary>The value of the condition.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionValue.RelativeDate">
             <summary>A relative date (based on the current date). Valid only if the type is DATE_BEFORE, DATE_AFTER,
             DATE_ON_OR_BEFORE or DATE_ON_OR_AFTER.
            
             Relative dates are not supported in data validation. They are supported only in conditional formatting and
             conditional filters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionValue.UserEnteredValue">
            <summary>A value the condition is based on. The value is parsed as if the user typed into a cell. Formulas
            are supported (and must begin with an `=` or a '+').</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionValue.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ConditionalFormatRule">
            <summary>A rule describing a conditional format.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionalFormatRule.BooleanRule">
            <summary>The formatting is either "on" or "off" according to the rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionalFormatRule.GradientRule">
            <summary>The formatting will vary based on the gradients in the rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionalFormatRule.Ranges">
            <summary>The ranges that are formatted if the condition is true. All the ranges must be on the same
            grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ConditionalFormatRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CopyPasteRequest">
            <summary>Copies data from the source to the destination.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopyPasteRequest.Destination">
            <summary>The location to paste to. If the range covers a span that's a multiple of the source's height or
            width, then the data will be repeated to fill in the destination range. If the range is smaller than the
            source range, the entire source data will still be copied (beyond the end of the destination
            range).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopyPasteRequest.PasteOrientation">
            <summary>How that data should be oriented when pasting.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopyPasteRequest.PasteType">
            <summary>What kind of data to paste.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopyPasteRequest.Source">
            <summary>The source range to copy.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopyPasteRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CopySheetToAnotherSpreadsheetRequest">
            <summary>The request to copy a sheet across spreadsheets.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopySheetToAnotherSpreadsheetRequest.DestinationSpreadsheetId">
            <summary>The ID of the spreadsheet to copy the sheet to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CopySheetToAnotherSpreadsheetRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataRequest">
            <summary>A request to create developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataRequest.DeveloperMetadata">
            <summary>The developer metadata to create.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataResponse">
            <summary>The response from creating developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataResponse.DeveloperMetadata">
            <summary>The developer metadata that was created.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CreateDeveloperMetadataResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.CutPasteRequest">
            <summary>Moves data from the source to the destination.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CutPasteRequest.Destination">
            <summary>The top-left coordinate where the data should be pasted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CutPasteRequest.PasteType">
            <summary>What kind of data to paste.  All the source data will be cut, regardless of what is
            pasted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CutPasteRequest.Source">
            <summary>The source data to cut.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.CutPasteRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DataFilter">
            <summary>Filter that describes what data should be selected or returned from a request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilter.A1Range">
            <summary>Selects data that matches the specified A1 range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilter.DeveloperMetadataLookup">
            <summary>Selects data associated with the developer metadata matching the criteria described by this
            DeveloperMetadataLookup.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilter.GridRange">
            <summary>Selects data that matches the range described by the GridRange.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilter.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DataFilterValueRange">
            <summary>A range of values whose location is specified by a DataFilter.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilterValueRange.DataFilter">
            <summary>The data filter describing the location of the values in the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilterValueRange.MajorDimension">
            <summary>The major dimension of the values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilterValueRange.Values">
            <summary>The data to be written.  If the provided values exceed any of the ranges matched by the data filter
            then the request will fail.  If the provided values are less than the matched ranges only the specified
            values will be written, existing values in the matched ranges will remain unaffected.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataFilterValueRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DataValidationRule">
            <summary>A data validation rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataValidationRule.Condition">
            <summary>The condition that data in the cell must match.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataValidationRule.InputMessage">
            <summary>A message to show the user when adding data to the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataValidationRule.ShowCustomUi">
            <summary>True if the UI should be customized based on the kind of condition. If true, "List" conditions will
            show a dropdown.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataValidationRule.Strict">
            <summary>True if invalid data should be rejected.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DataValidationRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DateTimeRule">
             <summary>Allows you to organize the date-time values in a source data column into buckets based on selected
             parts of their date or time values. For example, consider a pivot table showing sales transactions by date:
            
             +----------+--------------+ | Date     | SUM of Sales | +----------+--------------+ | 1/1/2017 |      $621.14 |
             | 2/3/2017 |      $708.84 | | 5/8/2017 |      $326.84 | ... +----------+--------------+ Applying a date-time
             group rule with a DateTimeRuleType of YEAR_MONTH results in the following pivot table.
            
             +--------------+--------------+ | Grouped Date | SUM of Sales | +--------------+--------------+ | 2017-Jan     |
             $53,731.78 | | 2017-Feb     |   $83,475.32 | | 2017-Mar     |   $94,385.05 | ...
             +--------------+--------------+</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DateTimeRule.Type">
            <summary>The type of date-time grouping to apply.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DateTimeRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteBandingRequest">
            <summary>Removes the banded range with the given ID from the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteBandingRequest.BandedRangeId">
            <summary>The ID of the banded range to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteBandingRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleRequest">
            <summary>Deletes a conditional format rule at the given index. All subsequent rules' indexes are
            decremented.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleRequest.Index">
            <summary>The zero-based index of the rule to be deleted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleRequest.SheetId">
            <summary>The sheet the rule is being deleted from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleResponse">
            <summary>The result of deleting a conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleResponse.Rule">
            <summary>The rule that was deleted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteConditionalFormatRuleResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataRequest">
            <summary>A request to delete developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataRequest.DataFilter">
            <summary>The data filter describing the criteria used to select which developer metadata entry to
            delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataResponse">
            <summary>The response from deleting developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataResponse.DeletedDeveloperMetadata">
            <summary>The metadata that was deleted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDeveloperMetadataResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupRequest">
             <summary>Deletes a group over the specified range by decrementing the depth of the dimensions in the range.
            
             For example, assume the sheet has a depth-1 group over B:E and a depth-2 group over C:D. Deleting a group over
             D:E leaves the sheet with a depth-1 group over B:D and a depth-2 group over C:C.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupRequest.Range">
            <summary>The range of the group to be deleted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupResponse">
            <summary>The result of deleting a group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupResponse.DimensionGroups">
            <summary>All groups of a dimension after deleting a group from that dimension.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionGroupResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDimensionRequest">
            <summary>Deletes the dimensions from the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionRequest.Range">
            <summary>The dimensions to delete from the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDimensionRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDuplicatesRequest">
             <summary>Removes rows within this range that contain values in the specified columns that are duplicates of
             values in any previous row. Rows with identical values but different letter cases, formatting, or formulas are
             considered to be duplicates.
            
             This request also removes duplicate rows hidden from view (for example, due to a filter). When removing
             duplicates, the first instance of each duplicate row scanning from the top downwards is kept in the resulting
             range. Content outside of the specified range isn't removed, and rows considered duplicates do not have to be
             adjacent to each other in the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDuplicatesRequest.ComparisonColumns">
            <summary>The columns in the range to analyze for duplicate values. If no columns are selected then all
            columns are analyzed for duplicates.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDuplicatesRequest.Range">
            <summary>The range to remove duplicates rows from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDuplicatesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteDuplicatesResponse">
            <summary>The result of removing duplicates in a range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDuplicatesResponse.DuplicatesRemovedCount">
            <summary>The number of duplicate rows removed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteDuplicatesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteEmbeddedObjectRequest">
            <summary>Deletes the embedded object with the given ID.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteEmbeddedObjectRequest.ObjectId">
            <summary>The ID of the embedded object to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteEmbeddedObjectRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteFilterViewRequest">
            <summary>Deletes a particular filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteFilterViewRequest.FilterId">
            <summary>The ID of the filter to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteFilterViewRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteNamedRangeRequest">
            <summary>Removes the named range with the given ID from the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteNamedRangeRequest.NamedRangeId">
            <summary>The ID of the named range to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteNamedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteProtectedRangeRequest">
            <summary>Deletes the protected range with the given ID.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteProtectedRangeRequest.ProtectedRangeId">
            <summary>The ID of the protected range to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteProtectedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteRangeRequest">
            <summary>Deletes a range of cells, shifting other cells into the deleted area.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteRangeRequest.Range">
            <summary>The range of cells to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteRangeRequest.ShiftDimension">
            <summary>The dimension from which deleted cells will be replaced with. If ROWS, existing cells will be
            shifted upward to replace the deleted cells. If COLUMNS, existing cells will be shifted left to replace the
            deleted cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeleteSheetRequest">
            <summary>Deletes the requested sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteSheetRequest.SheetId">
            <summary>The ID of the sheet to delete.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeleteSheetRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeveloperMetadata">
            <summary>Developer metadata associated with a location or object in a spreadsheet. Developer metadata may be
            used to associate arbitrary data with various parts of a spreadsheet and will remain associated at those
            locations as they move around and the spreadsheet is edited.  For example, if developer metadata is associated
            with row 5 and another row is then subsequently inserted above row 5, that original metadata will still be
            associated with the row it was first associated with (what is now row 6). If the associated object is deleted
            its metadata is deleted too.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.Location">
            <summary>The location where the metadata is associated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.MetadataId">
            <summary>The spreadsheet-scoped unique ID that identifies the metadata. IDs may be specified when metadata
            is created, otherwise one will be randomly generated and assigned. Must be positive.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.MetadataKey">
            <summary>The metadata key. There may be multiple metadata in a spreadsheet with the same key.  Developer
            metadata must always have a key specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.MetadataValue">
            <summary>Data associated with the metadata's key.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.Visibility">
            <summary>The metadata visibility.  Developer metadata must always have a visibility specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadata.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation">
            <summary>A location where metadata may be associated in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation.DimensionRange">
            <summary>Represents the row or column when metadata is associated with a dimension. The specified
            DimensionRange must represent a single row or column; it cannot be unbounded or span multiple rows or
            columns.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation.LocationType">
            <summary>The type of location this object represents.  This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation.SheetId">
            <summary>The ID of the sheet when metadata is associated with an entire sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation.Spreadsheet">
            <summary>True when metadata is associated with an entire spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLocation.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup">
            <summary>Selects DeveloperMetadata that matches all of the specified fields.  For example, if only a metadata ID
            is specified this considers the DeveloperMetadata with that particular unique ID. If a metadata key is
            specified, this considers all developer metadata with that key.  If a key, visibility, and location type are all
            specified, this considers all developer metadata with that key and visibility that are associated with a
            location of that type.  In general, this selects all DeveloperMetadata that matches the intersection of all the
            specified fields; any field or combination of fields may be specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.LocationMatchingStrategy">
            <summary>Determines how this lookup matches the location.  If this field is specified as EXACT, only
            developer metadata associated on the exact location specified is matched.  If this field is specified to
            INTERSECTING, developer metadata associated on intersecting locations is also matched.  If left unspecified,
            this field assumes a default value of INTERSECTING. If this field is specified, a metadataLocation must also
            be specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.LocationType">
            <summary>Limits the selected developer metadata to those entries which are associated with locations of the
            specified type.  For example, when this field is specified as ROW this lookup only considers developer
            metadata associated on rows.  If the field is left unspecified, all location types are considered.  This
            field cannot be specified as SPREADSHEET when the locationMatchingStrategy is specified as INTERSECTING or
            when the metadataLocation is specified as a non-spreadsheet location: spreadsheet metadata cannot intersect
            any other developer metadata location.  This field also must be left unspecified when the
            locationMatchingStrategy is specified as EXACT.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.MetadataId">
            <summary>Limits the selected developer metadata to that which has a matching
            DeveloperMetadata.metadata_id.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.MetadataKey">
            <summary>Limits the selected developer metadata to that which has a matching
            DeveloperMetadata.metadata_key.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.MetadataLocation">
            <summary>Limits the selected developer metadata to those entries associated with the specified location.
            This field either matches exact locations or all intersecting locations according the specified
            locationMatchingStrategy.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.MetadataValue">
            <summary>Limits the selected developer metadata to that which has a matching
            DeveloperMetadata.metadata_value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.Visibility">
            <summary>Limits the selected developer metadata to that which has a matching DeveloperMetadata.visibility.
            If left unspecified, all developer metadata visibile to the requesting project is considered.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DeveloperMetadataLookup.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DimensionGroup">
            <summary>A group over an interval of rows or columns on a sheet, which can contain or be contained within other
            groups. A group can be collapsed or expanded as a unit on the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionGroup.Collapsed">
             <summary>This field is true if this group is collapsed. A collapsed group remains collapsed if an
             overlapping group at a shallower depth is expanded.
            
             A true value does not imply that all dimensions within the group are hidden, since a dimension's visibility
             can change independently from this group property. However, when this property is updated, all dimensions
             within it are set to hidden if this field is true, or set to visible if this field is false.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionGroup.Depth">
            <summary>The depth of the group, representing how many groups have a range that wholly contains the range of
            this group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionGroup.Range">
            <summary>The range over which this group exists.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionGroup.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DimensionProperties">
            <summary>Properties about a dimension.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionProperties.DeveloperMetadata">
            <summary>The developer metadata associated with a single row or column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionProperties.HiddenByFilter">
            <summary>True if this dimension is being filtered. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionProperties.HiddenByUser">
            <summary>True if this dimension is explicitly hidden.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionProperties.PixelSize">
            <summary>The height (if a row) or width (if a column) of the dimension in pixels.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DimensionRange">
            <summary>A range along a single dimension on a sheet. All indexes are zero-based. Indexes are half open: the
            start index is inclusive and the end index is exclusive. Missing indexes indicate the range is unbounded on that
            side.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionRange.Dimension">
            <summary>The dimension of the span.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionRange.EndIndex">
            <summary>The end (exclusive) of the span, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionRange.SheetId">
            <summary>The sheet this span is on.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionRange.StartIndex">
            <summary>The start (inclusive) of the span, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DimensionRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DuplicateFilterViewRequest">
            <summary>Duplicates a particular filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateFilterViewRequest.FilterId">
            <summary>The ID of the filter being duplicated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateFilterViewRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DuplicateFilterViewResponse">
            <summary>The result of a filter view being duplicated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateFilterViewResponse.Filter">
            <summary>The newly created filter.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateFilterViewResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest">
            <summary>Duplicates the contents of a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest.InsertSheetIndex">
            <summary>The zero-based index where the new sheet should be inserted. The index of all sheets after this are
            incremented.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest.NewSheetId">
            <summary>If set, the ID of the new sheet. If not set, an ID is chosen. If set, the ID must not conflict with
            any existing sheet ID. If set, it must be non-negative.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest.NewSheetName">
            <summary>The name of the new sheet.  If empty, a new name is chosen for you.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest.SourceSheetId">
            <summary>The sheet to duplicate.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.DuplicateSheetResponse">
            <summary>The result of duplicating a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetResponse.Properties">
            <summary>The properties of the duplicate sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.DuplicateSheetResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Editors">
            <summary>The editors of a protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Editors.DomainUsersCanEdit">
            <summary>True if anyone in the document's domain has edit access to the protected range.  Domain protection
            is only supported on documents within a domain.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Editors.Groups">
            <summary>The email addresses of groups with edit access to the protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Editors.Users">
            <summary>The email addresses of users with edit access to the protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Editors.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.EmbeddedChart">
            <summary>A chart embedded in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedChart.ChartId">
            <summary>The ID of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedChart.Position">
            <summary>The position of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedChart.Spec">
            <summary>The specification of the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedChart.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.EmbeddedObjectPosition">
            <summary>The position of an embedded object such as a chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedObjectPosition.NewSheet">
            <summary>If true, the embedded object is put on a new sheet whose ID is chosen for you. Used only when
            writing.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedObjectPosition.OverlayPosition">
            <summary>The position at which the object is overlaid on top of a grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedObjectPosition.SheetId">
            <summary>The sheet this is on. Set only if the embedded object is on its own sheet. Must be non-
            negative.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.EmbeddedObjectPosition.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ErrorValue">
            <summary>An error in a cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ErrorValue.Message">
            <summary>A message with more information about the error (in the spreadsheet's locale).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ErrorValue.Type">
            <summary>The type of error.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ErrorValue.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ExtendedValue">
            <summary>The kinds of value that a cell in a spreadsheet can have.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.BoolValue">
            <summary>Represents a boolean value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.ErrorValue">
            <summary>Represents an error. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.FormulaValue">
            <summary>Represents a formula.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.NumberValue">
            <summary>Represents a double value. Note: Dates, Times and DateTimes are represented as doubles in "serial
            number" format.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.StringValue">
            <summary>Represents a string value. Leading single quotes are not included. For example, if the user typed
            `'123` into the UI, this would be represented as a `stringValue` of `"123"`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ExtendedValue.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.FilterCriteria">
            <summary>Criteria for showing/hiding rows in a filter or filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterCriteria.Condition">
            <summary>A condition that must be true for values to be shown. (This does not override hiddenValues -- if a
            value is listed there, it will still be hidden.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterCriteria.HiddenValues">
            <summary>Values that should be hidden.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterCriteria.VisibleBackgroundColor">
            <summary>The background fill color to filter by; only cells with this fill color are shown. Mutually
            exclusive with all other filter criteria. Requests to set this field will fail with a 400 error if any other
            filter criteria field is set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterCriteria.VisibleForegroundColor">
            <summary>The text color to filter by; only cells with this text color are shown. Mutually exclusive with all
            other filter criteria. Requests to set this field will fail with a 400 error if any other filter criteria
            field is set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterCriteria.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.FilterView">
            <summary>A filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.Criteria">
            <summary>The criteria for showing/hiding values per column. The map's key is the column index, and the value
            is the criteria for that column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.FilterViewId">
            <summary>The ID of the filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.NamedRangeId">
             <summary>The named range this filter view is backed by, if any.
            
             When writing, only one of range or named_range_id may be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.Range">
             <summary>The range this filter view covers.
            
             When writing, only one of range or named_range_id may be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.SortSpecs">
            <summary>The sort order per column. Later specifications are used when values are equal in the earlier
            specifications.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.Title">
            <summary>The name of the filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FilterView.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.FindReplaceRequest">
            <summary>Finds and replaces data in cells over a range, sheet, or all sheets.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.AllSheets">
            <summary>True to find/replace over all sheets.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.Find">
            <summary>The value to search.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.IncludeFormulas">
            <summary>True if the search should include cells with formulas. False to skip cells with formulas.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.MatchCase">
            <summary>True if the search is case sensitive.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.MatchEntireCell">
            <summary>True if the find value should match the entire cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.Range">
            <summary>The range to find/replace over.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.Replacement">
            <summary>The value to use as the replacement.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.SearchByRegex">
            <summary>True if the find value is a regex. The regular expression and replacement should follow Java regex
            rules at https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html. The replacement string is
            allowed to refer to capturing groups. For example, if one cell has the contents `"Google Sheets"` and
            another has `"Google Docs"`, then searching for `"o.* (.*)"` with a replacement of `"$1 Rocks"` would change
            the contents of the cells to `"GSheets Rocks"` and `"GDocs Rocks"` respectively.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.SheetId">
            <summary>The sheet to find/replace over.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.FindReplaceResponse">
            <summary>The result of the find/replace.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.FormulasChanged">
            <summary>The number of formula cells changed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.OccurrencesChanged">
            <summary>The number of occurrences (possibly multiple within a cell) changed. For example, if replacing
            `"e"` with `"o"` in `"Google Sheets"`, this would be `"3"` because `"Google Sheets"` -> `"Googlo
            Shoots"`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.RowsChanged">
            <summary>The number of rows changed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.SheetsChanged">
            <summary>The number of sheets changed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.ValuesChanged">
            <summary>The number of non-formula cells changed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.FindReplaceResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest">
            <summary>The request for retrieving a Spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest.DataFilters">
            <summary>The DataFilters used to select which ranges to retrieve from the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest.IncludeGridData">
            <summary>True if grid data should be returned. This parameter is ignored if a field mask was set in the
            request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GetSpreadsheetByDataFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GradientRule">
            <summary>A rule that applies a gradient color scale format, based on the interpolation points listed. The format
            of a cell will vary based on its contents as compared to the values of the interpolation points.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GradientRule.Maxpoint">
            <summary>The final interpolation point.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GradientRule.Midpoint">
            <summary>An optional midway interpolation point.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GradientRule.Minpoint">
            <summary>The starting interpolation point.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GradientRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GridCoordinate">
            <summary>A coordinate in a sheet. All indexes are zero-based.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridCoordinate.ColumnIndex">
            <summary>The column index of the coordinate.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridCoordinate.RowIndex">
            <summary>The row index of the coordinate.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridCoordinate.SheetId">
            <summary>The sheet this coordinate is on.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridCoordinate.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GridData">
            <summary>Data in the grid, as well as metadata about the dimensions.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.ColumnMetadata">
            <summary>Metadata about the requested columns in the grid, starting with the column in
            start_column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.RowData">
            <summary>The data in the grid, one entry per row, starting with the row in startRow. The values in RowData
            will correspond to columns starting at start_column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.RowMetadata">
            <summary>Metadata about the requested rows in the grid, starting with the row in start_row.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.StartColumn">
            <summary>The first column this GridData refers to, zero-based.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.StartRow">
            <summary>The first row this GridData refers to, zero-based.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GridProperties">
            <summary>Properties of a grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.ColumnCount">
            <summary>The number of columns in the grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.ColumnGroupControlAfter">
            <summary>True if the column grouping control toggle is shown after the group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.FrozenColumnCount">
            <summary>The number of columns that are frozen in the grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.FrozenRowCount">
            <summary>The number of rows that are frozen in the grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.HideGridlines">
            <summary>True if the grid isn't showing gridlines in the UI.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.RowCount">
            <summary>The number of rows in the grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.RowGroupControlAfter">
            <summary>True if the row grouping control toggle is shown after the group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.GridRange">
             <summary>A range on a sheet. All indexes are zero-based. Indexes are half open, e.g the start index is inclusive
             and the end index is exclusive -- [start_index, end_index). Missing indexes indicate the range is unbounded on
             that side.
            
             For example, if `"Sheet1"` is sheet ID 0, then:
            
             `Sheet1!A1:A1 == sheet_id: 0, start_row_index: 0, end_row_index: 1, start_column_index: 0, end_column_index: 1`
            
             `Sheet1!A3:B4 == sheet_id: 0, start_row_index: 2, end_row_index: 4, start_column_index: 0, end_column_index: 2`
            
             `Sheet1!A:B == sheet_id: 0, start_column_index: 0, end_column_index: 2`
            
             `Sheet1!A5:B == sheet_id: 0, start_row_index: 4, start_column_index: 0, end_column_index: 2`
            
             `Sheet1 == sheet_id:0`
            
             The start index must always be less than or equal to the end index. If the start index equals the end index,
             then the range is empty. Empty ranges are typically not meaningful and are usually rendered in the UI as
             `#REF!`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.EndColumnIndex">
            <summary>The end column (exclusive) of the range, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.EndRowIndex">
            <summary>The end row (exclusive) of the range, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.SheetId">
            <summary>The sheet this range is on.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.StartColumnIndex">
            <summary>The start column (inclusive) of the range, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.StartRowIndex">
            <summary>The start row (inclusive) of the range, or not set if unbounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.GridRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.HistogramChartSpec">
            <summary>A histogram chart. A histogram chart groups data items into bins, displaying each bin as a column of
            stacked items.  Histograms are used to display the distribution of a dataset.  Each column of items represents a
            range into which those items fall.  The number of bins can be chosen automatically or specified
            explicitly.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.BucketSize">
            <summary>By default the bucket size (the range of values stacked in a single column) is chosen
            automatically, but it may be overridden here. E.g., A bucket size of 1.5 results in buckets from 0 - 1.5,
            1.5 - 3.0, etc. Cannot be negative. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.LegendPosition">
            <summary>The position of the chart legend.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.OutlierPercentile">
            <summary>The outlier percentile is used to ensure that outliers do not adversely affect the calculation of
            bucket sizes.  For example, setting an outlier percentile of 0.05 indicates that the top and bottom 5% of
            values when calculating buckets.  The values are still included in the chart, they will be added to the
            first or last buckets instead of their own buckets. Must be between 0.0 and 0.5.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.Series">
            <summary>The series for a histogram may be either a single series of values to be bucketed or multiple
            series, each of the same length, containing the name of the series followed by the values to be bucketed for
            that series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.ShowItemDividers">
            <summary>Whether horizontal divider lines should be displayed between items in each column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Google.Apis.Sheets.v4.Data.HistogramRule" -->
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramRule.End">
            <summary>The maximum value at which items are placed into buckets of constant size. Values above end are
            lumped into a single bucket. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramRule.Interval">
            <summary>The size of the buckets that are created. Must be positive.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramRule.Start">
            <summary>The minimum value at which items are placed into buckets of constant size. Values below start are
            lumped into a single bucket. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.HistogramSeries">
            <summary>A histogram series containing the series color and data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramSeries.BarColor">
            <summary>The color of the column representing this series in each bucket. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramSeries.Data">
            <summary>The data for this histogram series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.HistogramSeries.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.InsertDimensionRequest">
            <summary>Inserts rows or columns in a sheet at a particular index.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertDimensionRequest.InheritFromBefore">
             <summary>Whether dimension properties should be extended from the dimensions before or after the newly
             inserted dimensions. True to inherit from the dimensions before (in which case the start index must be
             greater than 0), and false to inherit from the dimensions after.
            
             For example, if row index 0 has red background and row index 1 has a green background, then inserting 2 rows
             at index 1 can inherit either the green or red background.  If `inheritFromBefore` is true, the two new rows
             will be red (because the row before the insertion point was red), whereas if `inheritFromBefore` is false,
             the two new rows will be green (because the row after the insertion point was green).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertDimensionRequest.Range">
            <summary>The dimensions to insert.  Both the start and end indexes must be bounded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertDimensionRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.InsertRangeRequest">
            <summary>Inserts cells into a range, shifting the existing cells over or down.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertRangeRequest.Range">
            <summary>The range to insert new cells into.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertRangeRequest.ShiftDimension">
            <summary>The dimension which will be shifted when inserting cells. If ROWS, existing cells will be shifted
            down. If COLUMNS, existing cells will be shifted right.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InsertRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.InterpolationPoint">
            <summary>A single interpolation point on a gradient conditional format. These pin the gradient color scale
            according to the color, type and value chosen.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InterpolationPoint.Color">
            <summary>The color this interpolation point should use.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InterpolationPoint.Type">
            <summary>How the value should be interpreted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InterpolationPoint.Value">
            <summary>The value this interpolation point uses.  May be a formula. Unused if type is MIN or MAX.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.InterpolationPoint.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.IterativeCalculationSettings">
            <summary>Settings to control how circular dependencies are resolved with iterative calculation.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.IterativeCalculationSettings.ConvergenceThreshold">
            <summary>When iterative calculation is enabled and successive results differ by less than this threshold
            value, the calculation rounds stop.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.IterativeCalculationSettings.MaxIterations">
            <summary>When iterative calculation is enabled, the maximum number of calculation rounds to
            perform.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.IterativeCalculationSettings.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.KeyValueFormat">
            <summary>Formatting options for key value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.KeyValueFormat.Position">
            <summary>Specifies the horizontal text positioning of key value. This field is optional. If not specified,
            default positioning is used.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.KeyValueFormat.TextFormat">
            <summary>Text formatting options for key value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.KeyValueFormat.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.LineStyle">
            <summary>Properties that describe the style of a line.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.LineStyle.Type">
            <summary>The dash type of the line.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.LineStyle.Width">
            <summary>The thickness of the line, in px.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.LineStyle.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ManualRule">
             <summary>Allows you to manually organize the values in a source data column into buckets with names of your
             choosing. For example, a pivot table that aggregates population by state:
            
             +-------+-------------------+ | State | SUM of Population | +-------+-------------------+ | AK    |
             0.7 | | AL    |               4.8 | | AR    |               2.9 | ... +-------+-------------------+ could be
             turned into a pivot table that aggregates population by time zone by providing a list of groups (for example,
             groupName = 'Central', items = ['AL', 'AR', 'IA', ...]) to a manual group rule. Note that a similar effect could
             be achieved by adding a time zone column to the source data and adjusting the pivot table.
            
             +-----------+-------------------+ | Time Zone | SUM of Population | +-----------+-------------------+ | Central
             |             106.3 | | Eastern   |             151.9 | | Mountain  |              17.4 | ...
             +-----------+-------------------+</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ManualRule.Groups">
            <summary>The list of group names and the corresponding items from the source data that map to each group
            name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ManualRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ManualRuleGroup">
            <summary>A group name and a list of items from the source data that should be placed in the group with this
            name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ManualRuleGroup.GroupName">
            <summary>The group name, which must be a string. Each group in a given ManualRule must have a unique group
            name.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ManualRuleGroup.Items">
            <summary>The items in the source data that should be placed into this group. Each item may be a string,
            number, or boolean. Items may appear in at most one group within a given ManualRule. Items that do not
            appear in any group will appear on their own.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ManualRuleGroup.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.MatchedDeveloperMetadata">
            <summary>A developer metadata entry and the data filters specified in the original request that matched
            it.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedDeveloperMetadata.DataFilters">
            <summary>All filters matching the returned developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedDeveloperMetadata.DeveloperMetadata">
            <summary>The developer metadata matching the specified filters.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedDeveloperMetadata.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.MatchedValueRange">
            <summary>A value range that was matched by one or more data filers.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedValueRange.DataFilters">
            <summary>The DataFilters from the request that matched the range of values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedValueRange.ValueRange">
            <summary>The values matched by the DataFilter.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MatchedValueRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.MergeCellsRequest">
            <summary>Merges all cells in the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MergeCellsRequest.MergeType">
            <summary>How the cells should be merged.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MergeCellsRequest.Range">
            <summary>The range of cells to merge.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MergeCellsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.MoveDimensionRequest">
            <summary>Moves one or more rows or columns.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MoveDimensionRequest.DestinationIndex">
             <summary>The zero-based start index of where to move the source data to, based on the coordinates *before*
             the source data is removed from the grid.  Existing data will be shifted down or right (depending on the
             dimension) to make room for the moved dimensions. The source dimensions are removed from the grid, so the
             the data may end up in a different index than specified.
            
             For example, given `A1..A5` of `0, 1, 2, 3, 4` and wanting to move `"1"` and `"2"` to between `"3"` and
             `"4"`, the source would be `ROWS [1..3)`,and the destination index would be `"4"` (the zero-based index of
             row 5). The end result would be `A1..A5` of `0, 3, 1, 2, 4`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MoveDimensionRequest.Source">
            <summary>The source dimensions to move.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.MoveDimensionRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.NamedRange">
            <summary>A named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NamedRange.Name">
            <summary>The name of the named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NamedRange.NamedRangeId">
            <summary>The ID of the named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NamedRange.Range">
            <summary>The range this represents.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NamedRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.NumberFormat">
            <summary>The number format of a cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NumberFormat.Pattern">
            <summary>Pattern string used for formatting.  If not set, a default pattern based on the user's locale will
            be used if necessary for the given type. See the [Date and Number Formats guide](/sheets/api/guides/formats)
            for more information about the supported patterns.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NumberFormat.Type">
            <summary>The type of the number format. When writing, this field must be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.NumberFormat.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.OrgChartSpec">
             <summary>An org chart. Org charts require a unique set of labels in labels and may optionally include
             parent_labels and tooltips. parent_labels contain, for each node, the label identifying the parent node.
             tooltips contain, for each node, an optional tooltip.
            
             For example, to describe an OrgChart with Alice as the CEO, Bob as the President (reporting to Alice) and Cathy
             as VP of Sales (also reporting to Alice), have labels contain "Alice", "Bob", "Cathy", parent_labels contain "",
             "Alice", "Alice" and tooltips contain "CEO", "President", "VP Sales".</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.Labels">
            <summary>The data containing the labels for all the nodes in the chart.  Labels must be unique.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.NodeColor">
            <summary>The color of the org chart nodes.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.NodeSize">
            <summary>The size of the org chart nodes.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.ParentLabels">
            <summary>The data containing the label of the parent for the corresponding node. A blank value indicates
            that the node has no parent and is a top-level node. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.SelectedNodeColor">
            <summary>The color of the selected org chart nodes.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.Tooltips">
            <summary>The data containing the tooltip for the corresponding node.  A blank value results in no tooltip
            being displayed for the node. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OrgChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.OverlayPosition">
            <summary>The location an object is overlaid on top of a grid.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.AnchorCell">
            <summary>The cell the object is anchored to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.HeightPixels">
            <summary>The height of the object, in pixels. Defaults to 371.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.OffsetXPixels">
            <summary>The horizontal offset, in pixels, that the object is offset from the anchor cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.OffsetYPixels">
            <summary>The vertical offset, in pixels, that the object is offset from the anchor cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.WidthPixels">
            <summary>The width of the object, in pixels. Defaults to 600.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.OverlayPosition.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Padding">
            <summary>The amount of padding around the cell, in pixels. When updating padding, every field must be
            specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Padding.Bottom">
            <summary>The bottom padding of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Padding.Left">
            <summary>The left padding of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Padding.Right">
            <summary>The right padding of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Padding.Top">
            <summary>The top padding of the cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Padding.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PasteDataRequest">
            <summary>Inserts data into the spreadsheet starting at the specified coordinate.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.Coordinate">
            <summary>The coordinate at which the data should start being inserted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.Data">
            <summary>The data to insert.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.Delimiter">
            <summary>The delimiter in the data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.Html">
            <summary>True if the data is HTML.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.Type">
            <summary>How the data should be pasted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PasteDataRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PieChartSpec">
            <summary>A pie chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.Domain">
            <summary>The data that covers the domain of the pie chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.LegendPosition">
            <summary>Where the legend of the pie chart should be drawn.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.PieHole">
            <summary>The size of the hole in the pie chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.Series">
            <summary>The data that covers the one and only series of the pie chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.ThreeDimensional">
            <summary>True if the pie is three dimensional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PieChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotFilterCriteria">
            <summary>Criteria for showing/hiding rows in a pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotFilterCriteria.VisibleValues">
            <summary>Values that should be included.  Values not listed here are excluded.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotFilterCriteria.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotGroup">
            <summary>A single grouping (either row or column) in a pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.GroupRule">
            <summary>The group rule to apply to this row/column group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.Label">
             <summary>The labels to use for the row/column groups which can be customized. For example, in the following
             pivot table, the row label is `Region` (which could be renamed to `State`) and the column label is `Product`
             (which could be renamed `Item`). Pivot tables created before December 2017 do not have header labels. If
             you'd like to add header labels to an existing pivot table, please delete the existing pivot table and then
             create a new pivot table with same parameters.
            
             +--------------+---------+-------+ | SUM of Units | Product |       | | Region       | Pen     | Paper |
             +--------------+---------+-------+ | New York     |     345 |    98 | | Oregon       |     234 |   123 | |
             Tennessee    |     531 |   415 | +--------------+---------+-------+ | Grand Total  |    1110 |   636 |
             +--------------+---------+-------+</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.RepeatHeadings">
             <summary>True if the headings in this pivot group should be repeated. This is only valid for row groupings
             and is ignored by columns.
            
             By default, we minimize repitition of headings by not showing higher level headings where they are the same.
             For example, even though the third row below corresponds to "Q1 Mar", "Q1" is not shown because it is
             redundant with previous rows. Setting repeat_headings to true would cause "Q1" to be repeated for "Feb" and
             "Mar".
            
             +--------------+ | Q1     | Jan | |        | Feb | |        | Mar | +--------+-----+ | Q1 Total     |
             +--------------+</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.ShowTotals">
            <summary>True if the pivot table should include the totals for this grouping.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.SortOrder">
            <summary>The order the values in this group should be sorted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.SourceColumnOffset">
             <summary>The column offset of the source range that this grouping is based on.
            
             For example, if the source was `C10:E15`, a `sourceColumnOffset` of `0` means this group refers to column
             `C`, whereas the offset `1` would refer to column `D`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.ValueBucket">
            <summary>The bucket of the opposite pivot group to sort by. If not specified, sorting is alphabetical by
            this group's values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.ValueMetadata">
            <summary>Metadata about values in the grouping.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroup.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotGroupRule">
            <summary>An optional setting on a PivotGroup that defines buckets for the values in the source data column
            rather than breaking out each individual value. Only one PivotGroup with a group rule may be added for each
            column in the source data, though on any given column you may add both a PivotGroup that has a rule and a
            PivotGroup that does not.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupRule.DateTimeRule">
            <summary>A DateTimeRule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupRule.HistogramRule">
            <summary>A HistogramRule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupRule.ManualRule">
            <summary>A ManualRule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupRule.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotGroupSortValueBucket">
            <summary>Information about which values in a pivot group should be used for sorting.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Google.Apis.Sheets.v4.Data.PivotGroupSortValueBucket.Buckets" -->
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupSortValueBucket.ValuesIndex">
            <summary>The offset in the PivotTable.values list which the values in this grouping should be sorted
            by.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupSortValueBucket.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotGroupValueMetadata">
            <summary>Metadata about a value in a pivot grouping.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupValueMetadata.Collapsed">
            <summary>True if the data corresponding to the value is collapsed.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupValueMetadata.Value">
            <summary>The calculated value the metadata corresponds to. (Note that formulaValue is not valid, because the
            values will be calculated.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotGroupValueMetadata.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotTable">
            <summary>A pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.Columns">
            <summary>Each column grouping in the pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.Criteria">
             <summary>An optional mapping of filters per source column offset.
            
             The filters are applied before aggregating data into the pivot table. The map's key is the column offset of
             the source range that you want to filter, and the value is the criteria for that column.
            
             For example, if the source was `C10:E15`, a key of `0` will have the filter for column `C`, whereas the key
             `1` is for column `D`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.Rows">
            <summary>Each row grouping in the pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.Source">
            <summary>The range the pivot table is reading data from.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.ValueLayout">
            <summary>Whether values should be listed horizontally (as columns) or vertically (as rows).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.Values">
            <summary>A list of values to include in the pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotTable.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.PivotValue">
            <summary>The definition of how a value in a pivot table should be calculated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.CalculatedDisplayType">
            <summary>If specified, indicates that pivot values should be displayed as the result of a calculation with
            another pivot value. For example, if calculated_display_type is specified as PERCENT_OF_GRAND_TOTAL, all the
            pivot values are displayed as the percentage of the grand total. In the Sheets UI, this is referred to as
            "Show As" in the value section of a pivot table.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.Formula">
            <summary>A custom formula to calculate the value.  The formula must start with an `=` character.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.Name">
            <summary>A name to use for the value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.SourceColumnOffset">
             <summary>The column offset of the source range that this value reads from.
            
             For example, if the source was `C10:E15`, a `sourceColumnOffset` of `0` means this value refers to column
             `C`, whereas the offset `1` would refer to column `D`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.SummarizeFunction">
            <summary>A function to summarize the value. If formula is set, the only supported values are SUM and CUSTOM.
            If sourceColumnOffset is set, then `CUSTOM` is not supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.PivotValue.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ProtectedRange">
            <summary>A protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.Description">
            <summary>The description of this protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.Editors">
            <summary>The users and groups with edit access to the protected range. This field is only visible to users
            with edit access to the protected range and the document. Editors are not supported with warning_only
            protection.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.NamedRangeId">
             <summary>The named range this protected range is backed by, if any.
            
             When writing, only one of range or named_range_id may be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.ProtectedRangeId">
            <summary>The ID of the protected range. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.Range">
             <summary>The range that is being protected. The range may be fully unbounded, in which case this is
             considered a protected sheet.
            
             When writing, only one of range or named_range_id may be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.RequestingUserCanEdit">
            <summary>True if the user who requested this protected range can edit the protected area. This field is
            read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.UnprotectedRanges">
            <summary>The list of unprotected ranges within a protected sheet. Unprotected ranges are only supported on
            protected sheets.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.WarningOnly">
             <summary>True if this protected range will show a warning when editing. Warning-based protection means that
             every user can edit data in the protected range, except editing will prompt a warning asking the user to
             confirm the edit.
            
             When writing: if this field is true, then editors is ignored. Additionally, if this field is changed from
             true to false and the `editors` field is not set (nor included in the field mask), then the editors will be
             set to all the editors in the document.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ProtectedRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.RandomizeRangeRequest">
            <summary>Randomizes the order of the rows in a range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RandomizeRangeRequest.Range">
            <summary>The range to randomize.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RandomizeRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.RepeatCellRequest">
             <summary>Updates all cells in the range to the values in the given Cell object. Only the fields listed in the
             fields field are updated; others are unchanged.
            
             If writing a cell with a formula, the formula's ranges will automatically increment for each field in the range.
             For example, if writing a cell with formula `=A1` into range B2:C4, B2 would be `=A1`, B3 would be `=A2`, B4
             would be `=A3`, C2 would be `=B1`, C3 would be `=B2`, C4 would be `=B3`.
            
             To keep the formula's ranges static, use the `$` indicator. For example, use the formula `=$A$1` to prevent both
             the row and the column from incrementing.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RepeatCellRequest.Cell">
            <summary>The data to write.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RepeatCellRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `cell` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RepeatCellRequest.Range">
            <summary>The range to repeat the cell in.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RepeatCellRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Request">
            <summary>A single kind of update to apply to a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddBanding">
            <summary>Adds a new banded range</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddChart">
            <summary>Adds a chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddConditionalFormatRule">
            <summary>Adds a new conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddDimensionGroup">
            <summary>Creates a group over the specified range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddFilterView">
            <summary>Adds a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddNamedRange">
            <summary>Adds a named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddProtectedRange">
            <summary>Adds a protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddSheet">
            <summary>Adds a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AddSlicer">
            <summary>Adds a slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AppendCells">
            <summary>Appends cells after the last row with data in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AppendDimension">
            <summary>Appends dimensions to the end of a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AutoFill">
            <summary>Automatically fills in more data based on existing data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.AutoResizeDimensions">
            <summary>Automatically resizes one or more dimensions based on the contents of the cells in that
            dimension.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.ClearBasicFilter">
            <summary>Clears the basic filter on a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.CopyPaste">
            <summary>Copies data from one area and pastes it to another.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.CreateDeveloperMetadata">
            <summary>Creates new developer metadata</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.CutPaste">
            <summary>Cuts data from one area and pastes it to another.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteBanding">
            <summary>Removes a banded range</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteConditionalFormatRule">
            <summary>Deletes an existing conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteDeveloperMetadata">
            <summary>Deletes developer metadata</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteDimension">
            <summary>Deletes rows or columns in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteDimensionGroup">
            <summary>Deletes a group over the specified range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteDuplicates">
            <summary>Removes rows containing duplicate values in specified columns of a cell range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteEmbeddedObject">
            <summary>Deletes an embedded object (e.g, chart, image) in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteFilterView">
            <summary>Deletes a filter view from a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteNamedRange">
            <summary>Deletes a named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteProtectedRange">
            <summary>Deletes a protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteRange">
            <summary>Deletes a range of cells from a sheet, shifting the remaining cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DeleteSheet">
            <summary>Deletes a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DuplicateFilterView">
            <summary>Duplicates a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.DuplicateSheet">
            <summary>Duplicates a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.FindReplace">
            <summary>Finds and replaces occurrences of some text with other text.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.InsertDimension">
            <summary>Inserts new rows or columns in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.InsertRange">
            <summary>Inserts new cells in a sheet, shifting the existing cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.MergeCells">
            <summary>Merges cells together.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.MoveDimension">
            <summary>Moves rows or columns to another location in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.PasteData">
            <summary>Pastes data (HTML or delimited) into a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.RandomizeRange">
            <summary>Randomizes the order of the rows in a range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.RepeatCell">
            <summary>Repeats a single cell across a range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.SetBasicFilter">
            <summary>Sets the basic filter on a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.SetDataValidation">
            <summary>Sets data validation for one or more cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.SortRange">
            <summary>Sorts data in a range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.TextToColumns">
            <summary>Converts a column of text into many columns of text.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.TrimWhitespace">
            <summary>Trims cells of whitespace (such as spaces, tabs, or new lines).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UnmergeCells">
            <summary>Unmerges merged cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateBanding">
            <summary>Updates a banded range</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateBorders">
            <summary>Updates the borders in a range of cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateCells">
            <summary>Updates many cells at once.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateChartSpec">
            <summary>Updates a chart's specifications.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateConditionalFormatRule">
            <summary>Updates an existing conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateDeveloperMetadata">
            <summary>Updates an existing developer metadata entry</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateDimensionGroup">
            <summary>Updates the state of the specified group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateDimensionProperties">
            <summary>Updates dimensions' properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateEmbeddedObjectPosition">
            <summary>Updates an embedded object's (e.g. chart, image) position.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateFilterView">
            <summary>Updates the properties of a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateNamedRange">
            <summary>Updates a named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateProtectedRange">
            <summary>Updates a protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateSheetProperties">
            <summary>Updates a sheet's properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateSlicerSpec">
            <summary>Updates a slicer's specifications.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.UpdateSpreadsheetProperties">
            <summary>Updates the spreadsheet's properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Request.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Response">
            <summary>A single response from an update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddBanding">
            <summary>A reply from adding a banded range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddChart">
            <summary>A reply from adding a chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddDimensionGroup">
            <summary>A reply from adding a dimension group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddFilterView">
            <summary>A reply from adding a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddNamedRange">
            <summary>A reply from adding a named range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddProtectedRange">
            <summary>A reply from adding a protected range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddSheet">
            <summary>A reply from adding a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.AddSlicer">
            <summary>A reply from adding a slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.CreateDeveloperMetadata">
            <summary>A reply from creating a developer metadata entry.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DeleteConditionalFormatRule">
            <summary>A reply from deleting a conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DeleteDeveloperMetadata">
            <summary>A reply from deleting a developer metadata entry.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DeleteDimensionGroup">
            <summary>A reply from deleting a dimension group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DeleteDuplicates">
            <summary>A reply from removing rows containing duplicate values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DuplicateFilterView">
            <summary>A reply from duplicating a filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.DuplicateSheet">
            <summary>A reply from duplicating a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.FindReplace">
            <summary>A reply from doing a find/replace.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.TrimWhitespace">
            <summary>A reply from trimming whitespace.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.UpdateConditionalFormatRule">
            <summary>A reply from updating a conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.UpdateDeveloperMetadata">
            <summary>A reply from updating a developer metadata entry.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.UpdateEmbeddedObjectPosition">
            <summary>A reply from updating an embedded object's position.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Response.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.RowData">
            <summary>Data about each cell in a row.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RowData.Values">
            <summary>The values in the row, one per column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.RowData.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ScorecardChartSpec">
            <summary>A scorecard chart. Scorecard charts are used to highlight key performance indicators, known as KPIs, on
            the spreadsheet. A scorecard chart can represent things like total sales, average cost, or a top selling item.
            You can specify a single data value, or aggregate over a range of data. Percentage or absolute difference from a
            baseline value can be highlighted, like changes over time.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.AggregateType">
            <summary>The aggregation type for key and baseline chart data in scorecard chart. This field is
            optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.BaselineValueData">
            <summary>The data for scorecard baseline value. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.BaselineValueFormat">
            <summary>Formatting options for baseline value. This field is needed only if baseline_value_data is
            specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.CustomFormatOptions">
            <summary>Custom formatting options for numeric key/baseline values in scorecard chart. This field is used
            only when number_format_source is set to CUSTOM. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.KeyValueData">
            <summary>The data for scorecard key value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.KeyValueFormat">
            <summary>Formatting options for key value.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.NumberFormatSource">
            <summary>The number format source used in the scorecard chart. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.ScaleFactor">
            <summary>Value to scale scorecard key and baseline value. For example, a factor of 10 can be used to divide
            all values in the chart by 10. This field is optional.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ScorecardChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataRequest">
            <summary>A request to retrieve all developer metadata matching the set of specified criteria.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataRequest.DataFilters">
            <summary>The data filters describing the criteria used to determine which DeveloperMetadata entries to
            return.  DeveloperMetadata matching any of the specified filters will be included in the response.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataResponse">
            <summary>A reply to a developer metadata search request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataResponse.MatchedDeveloperMetadata">
            <summary>The metadata matching the criteria of the search request.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SearchDeveloperMetadataResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SetBasicFilterRequest">
            <summary>Sets the basic filter associated with a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SetBasicFilterRequest.Filter">
            <summary>The filter to set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SetBasicFilterRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SetDataValidationRequest">
            <summary>Sets a data validation rule to every cell in the range. To clear validation in a range, call this with
            no rule specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SetDataValidationRequest.Range">
            <summary>The range the data validation rule should apply to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SetDataValidationRequest.Rule">
            <summary>The data validation rule to set on each cell in the range, or empty to clear the data validation in
            the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SetDataValidationRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Sheet">
            <summary>A sheet in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.BandedRanges">
            <summary>The banded (alternating colors) ranges on this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.BasicFilter">
            <summary>The filter on this sheet, if any.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.Charts">
            <summary>The specifications of every chart on this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.ColumnGroups">
            <summary>All column groups on this sheet, ordered by increasing range start index, then by group
            depth.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.ConditionalFormats">
            <summary>The conditional format rules in this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.Data">
            <summary>Data in the grid, if this is a grid sheet. The number of GridData objects returned is dependent on
            the number of ranges requested on this sheet. For example, if this is representing `Sheet1`, and the
            spreadsheet was requested with ranges `Sheet1!A1:C10` and `Sheet1!D15:E20`, then the first GridData will
            have a startRow/startColumn of `0`, while the second one will have `startRow 14` (zero-based row 15), and
            `startColumn 3` (zero-based column D).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.DeveloperMetadata">
            <summary>The developer metadata associated with a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.FilterViews">
            <summary>The filter views in this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.Merges">
            <summary>The ranges that are merged together.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.Properties">
            <summary>The properties of the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.ProtectedRanges">
            <summary>The protected ranges in this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.RowGroups">
            <summary>All row groups on this sheet, ordered by increasing range start index, then by group
            depth.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.Slicers">
            <summary>The slicers on this sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Sheet.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SheetProperties">
            <summary>Properties of a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.GridProperties">
            <summary>Additional properties of the sheet if this sheet is a grid. (If the sheet is an object sheet,
            containing a chart or image, then this field will be absent.) When writing it is an error to set any grid
            properties on non-grid sheets.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.Hidden">
            <summary>True if the sheet is hidden in the UI, false if it's visible.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.Index">
            <summary>The index of the sheet within the spreadsheet. When adding or updating sheet properties, if this
            field is excluded then the sheet is added or moved to the end of the sheet list. When updating sheet indices
            or inserting sheets, movement is considered in "before the move" indexes. For example, if there were 3
            sheets (S1, S2, S3) in order to move S1 ahead of S2 the index would have to be set to 2. A sheet index
            update request is ignored if the requested index is identical to the sheets current index or if the
            requested new index is equal to the current sheet index + 1.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.RightToLeft">
            <summary>True if the sheet is an RTL sheet instead of an LTR sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.SheetId">
            <summary>The ID of the sheet. Must be non-negative. This field cannot be changed once set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.SheetType">
            <summary>The type of sheet. Defaults to GRID. This field cannot be changed once set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.TabColor">
            <summary>The color of the tab in the UI.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.Title">
            <summary>The name of the sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SheetProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Slicer">
            <summary>A slicer in a sheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Slicer.Position">
            <summary>The position of the slicer. Note that slicer can be positioned only on existing sheet. Also, width
            and height of slicer can be automatically adjusted to keep it within permitted limits.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Slicer.SlicerId">
            <summary>The ID of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Slicer.Spec">
            <summary>The specification of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Slicer.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SlicerSpec">
            <summary>The specifications of a slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.ApplyToPivotTables">
            <summary>True if the filter should apply to pivot tables. If not set, default to `True`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.BackgroundColor">
            <summary>The background color of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.ColumnIndex">
            <summary>The column index in the data table on which the filter is applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.DataRange">
            <summary>The data range of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.FilterCriteria">
            <summary>The filtering criteria of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.HorizontalAlignment">
            <summary>The horizontal alignment of title in the slicer. If unspecified, defaults to `LEFT`</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.TextFormat">
            <summary>The text format of title in the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.Title">
            <summary>The title of the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SlicerSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SortRangeRequest">
            <summary>Sorts data in rows based on a sort order per column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortRangeRequest.Range">
            <summary>The range to sort.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortRangeRequest.SortSpecs">
            <summary>The sort order per column. Later specifications are used when values are equal in the earlier
            specifications.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SortSpec">
            <summary>A sort order associated with a specific column or row.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortSpec.BackgroundColor">
            <summary>The background fill color to sort by. Mutually exclusive with sorting by text color. Requests to
            set this field will fail with a 400 error if foreground color is also set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortSpec.DimensionIndex">
            <summary>The dimension the sort should be applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortSpec.ForegroundColor">
            <summary>The text color to sort by. Mutually exclusive with sorting by background fill color. Requests to
            set this field will fail with a 400 error if background color is also set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortSpec.SortOrder">
            <summary>The order data should be sorted.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SortSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SourceAndDestination">
            <summary>A combination of a source range and how to extend that source.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SourceAndDestination.Dimension">
            <summary>The dimension that data should be filled into.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SourceAndDestination.FillLength">
            <summary>The number of rows or columns that data should be filled into. Positive numbers expand beyond the
            last row or last column of the source.  Negative numbers expand before the first row or first column of the
            source.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SourceAndDestination.Source">
            <summary>The location of the data to use as the source of the autofill.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SourceAndDestination.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.Spreadsheet">
            <summary>Resource that represents a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.DeveloperMetadata">
            <summary>The developer metadata associated with a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.NamedRanges">
            <summary>The named ranges defined in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.Properties">
            <summary>Overall properties of a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.Sheets">
            <summary>The sheets that are part of a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.SpreadsheetId">
            <summary>The ID of the spreadsheet. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.SpreadsheetUrl">
            <summary>The url of the spreadsheet. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.Spreadsheet.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.SpreadsheetProperties">
            <summary>Properties of a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.AutoRecalc">
            <summary>The amount of time to wait before volatile functions are recalculated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.DefaultFormat">
            <summary>The default format of all cells in the spreadsheet. CellData.effectiveFormat will not be set if the
            cell's format is equal to this default format. This field is read-only.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.IterativeCalculationSettings">
            <summary>Determines whether and how circular references are resolved with iterative calculation.  Absence of
            this field means that circular references will result in calculation errors.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.Locale">
             <summary>The locale of the spreadsheet in one of the following formats:
            
             * an ISO 639-1 language code such as `en`
            
             * an ISO 639-2 language code such as `fil`, if no 639-1 code exists
            
             * a combination of the ISO language code and country code, such as `en_US`
            
             Note: when updating this field, not all locales/languages are supported.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.TimeZone">
            <summary>The time zone of the spreadsheet, in CLDR format such as `America/New_York`. If the time zone isn't
            recognized, this may be a custom time zone such as `GMT-07:00`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.Title">
            <summary>The title of the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.SpreadsheetProperties.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TextFormat">
            <summary>The format of a run of text in a cell. Absent values indicate that the field isn't specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.Bold">
            <summary>True if the text is bold.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.FontFamily">
            <summary>The font family.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.FontSize">
            <summary>The size of the font.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.ForegroundColor">
            <summary>The foreground color of the text.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.Italic">
            <summary>True if the text is italicized.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.Strikethrough">
            <summary>True if the text has a strikethrough.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.Underline">
            <summary>True if the text is underlined.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormat.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TextFormatRun">
            <summary>A run of a text format. The format of this run continues until the start index of the next run. When
            updating, all fields must be set.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormatRun.Format">
            <summary>The format of this run.  Absent values inherit the cell's format.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormatRun.StartIndex">
            <summary>The character index where this run starts.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextFormatRun.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TextPosition">
            <summary>Position settings for text.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextPosition.HorizontalAlignment">
            <summary>Horizontal alignment setting for the piece of text.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextPosition.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TextRotation">
            <summary>The rotation applied to text in a cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextRotation.Angle">
             <summary>The angle between the standard orientation and the desired orientation. Measured in degrees. Valid
             values are between -90 and 90. Positive angles are angled upwards, negative are angled downwards.
            
             Note: For LTR text direction positive angles are in the counterclockwise direction, whereas for RTL they are
             in the clockwise direction</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextRotation.Vertical">
             <summary>If true, text reads top to bottom, but the orientation of individual characters is unchanged. For
             example:
            
             | V | | e | | r | | t | | i | | c | | a | | l |</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextRotation.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TextToColumnsRequest">
            <summary>Splits a column of text into multiple columns, based on a delimiter in each cell.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextToColumnsRequest.Delimiter">
            <summary>The delimiter to use. Used only if delimiterType is CUSTOM.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextToColumnsRequest.DelimiterType">
            <summary>The delimiter type to use.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextToColumnsRequest.Source">
            <summary>The source data range.  This must span exactly one column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TextToColumnsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TreemapChartColorScale">
            <summary>A color scale for a treemap chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartColorScale.MaxValueColor">
            <summary>The background color for cells with a color value greater than or equal to maxValue. Defaults to
            #109618 if not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartColorScale.MidValueColor">
            <summary>The background color for cells with a color value at the midpoint between minValue and maxValue.
            Defaults to #efe6dc if not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartColorScale.MinValueColor">
            <summary>The background color for cells with a color value less than or equal to minValue. Defaults to
            #dc3912 if not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartColorScale.NoDataColor">
            <summary>The background color for cells that have no color data associated with them. Defaults to #000000 if
            not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartColorScale.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TreemapChartSpec">
            <summary>A Treemap chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.ColorData">
            <summary>The data that determines the background color of each treemap data cell. This field is optional. If
            not specified, size_data is used to determine background colors. If specified, the data is expected to be
            numeric. color_scale will determine how the values in this data map to data cell background
            colors.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.ColorScale">
            <summary>The color scale for data cells in the treemap chart. Data cells are assigned colors based on their
            color values. These color values come from color_data, or from size_data if color_data is not specified.
            Cells with color values less than or equal to min_value will have minValueColor as their background color.
            Cells with color values greater than or equal to max_value will have maxValueColor as their background
            color. Cells with color values between min_value and max_value will have background colors on a gradient
            between minValueColor and maxValueColor, the midpoint of the gradient being midValueColor. Cells with
            missing or non-numeric color values will have noDataColor as their background color.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.HeaderColor">
            <summary>The background color for header cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.HideTooltips">
            <summary>True to hide tooltips.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.HintedLevels">
            <summary>The number of additional data levels beyond the labeled levels to be shown on the treemap chart.
            These levels are not interactive and are shown without their labels. Defaults to 0 if not
            specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.Labels">
            <summary>The data that contains the treemap cell labels.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.Levels">
            <summary>The number of data levels to show on the treemap chart. These levels are interactive and are shown
            with their labels. Defaults to 2 if not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.MaxValue">
            <summary>The maximum possible data value. Cells with values greater than this will have the same color as
            cells with this value. If not specified, defaults to the actual maximum value from color_data, or the
            maximum value from size_data if color_data is not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.MinValue">
            <summary>The minimum possible data value. Cells with values less than this will have the same color as cells
            with this value. If not specified, defaults to the actual minimum value from color_data, or the minimum
            value from size_data if color_data is not specified.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.ParentLabels">
            <summary>The data the contains the treemap cells' parent labels.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.SizeData">
            <summary>The data that determines the size of each treemap data cell. This data is expected to be numeric.
            The cells corresponding to non-numeric or missing data will not be rendered. If color_data is not specified,
            this data is used to determine data cell background colors as well.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.TextFormat">
            <summary>The text format for all labels on the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TreemapChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TrimWhitespaceRequest">
            <summary>Trims the whitespace (such as spaces, tabs, or new lines) in every cell in the specified range. This
            request removes all whitespace from the start and end of each cell's text, and reduces any subsequence of
            remaining whitespace characters to a single space. If the resulting trimmed text starts with a '+' or '='
            character, the text remains as a string value and isn't interpreted as a formula.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TrimWhitespaceRequest.Range">
            <summary>The range whose cells to trim.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TrimWhitespaceRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.TrimWhitespaceResponse">
            <summary>The result of trimming whitespace in cells.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TrimWhitespaceResponse.CellsChangedCount">
            <summary>The number of cells that were trimmed of whitespace.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.TrimWhitespaceResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UnmergeCellsRequest">
            <summary>Unmerges cells in the given range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UnmergeCellsRequest.Range">
            <summary>The range within which all cells should be unmerged. If the range spans multiple merges, all will
            be unmerged. The range must not partially span any merge.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UnmergeCellsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateBandingRequest">
            <summary>Updates properties of the supplied banded range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBandingRequest.BandedRange">
            <summary>The banded range to update with the new properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBandingRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `bandedRange` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBandingRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateBordersRequest">
             <summary>Updates the borders of a range. If a field is not set in the request, that means the border remains as-
             is. For example, with two subsequent UpdateBordersRequest:
            
             1. range: A1:A5 `{ top: RED, bottom: WHITE }` 2. range: A1:A5 `{ left: BLUE }`
            
             That would result in A1:A5 having a borders of `{ top: RED, bottom: WHITE, left: BLUE }`. If you want to clear a
             border, explicitly set the style to NONE.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.Bottom">
            <summary>The border to put at the bottom of the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.InnerHorizontal">
            <summary>The horizontal border to put within the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.InnerVertical">
            <summary>The vertical border to put within the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.Left">
            <summary>The border to put at the left of the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.Range">
            <summary>The range whose borders should be updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.Right">
            <summary>The border to put at the right of the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.Top">
            <summary>The border to put at the top of the range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateBordersRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateCellsRequest">
            <summary>Updates all cells in a range with new data.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateCellsRequest.Fields">
            <summary>The fields of CellData that should be updated. At least one field must be specified. The root is
            the CellData; 'row.values.' should not be specified. A single `"*"` can be used as short-hand for listing
            every field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateCellsRequest.Range">
             <summary>The range to write data to.
            
             If the data in rows does not cover the entire requested range, the fields matching those set in fields will
             be cleared.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateCellsRequest.Rows">
            <summary>The data to write.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateCellsRequest.Start">
            <summary>The coordinate to start writing data at. Any number of rows and columns (including a different
            number of columns per row) may be written.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateCellsRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateChartSpecRequest">
            <summary>Updates a chart's specifications. (This does not move or resize a chart. To move or resize a chart, use
            UpdateEmbeddedObjectPositionRequest.)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateChartSpecRequest.ChartId">
            <summary>The ID of the chart to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateChartSpecRequest.Spec">
            <summary>The specification to apply to the chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateChartSpecRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest">
            <summary>Updates a conditional format rule at the given index, or moves a conditional format rule to another
            index.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest.Index">
            <summary>The zero-based index of the rule that should be replaced or moved.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest.NewIndex">
            <summary>The zero-based new index the rule should end up at.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest.Rule">
            <summary>The rule that should replace the rule at the given index.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest.SheetId">
            <summary>The sheet of the rule to move.  Required if new_index is set, unused otherwise.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse">
            <summary>The result of updating a conditional format rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse.NewIndex">
            <summary>The index of the new rule.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse.NewRule">
            <summary>The new rule that replaced the old rule (if replacing), or the rule that was moved (if
            moved)</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse.OldIndex">
            <summary>The old index of the rule. Not set if a rule was replaced (because it is the same as
            new_index).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse.OldRule">
            <summary>The old (deleted) rule. Not set if a rule was moved (because it is the same as new_rule).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateConditionalFormatRuleResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataRequest">
            <summary>A request to update properties of developer metadata. Updates the properties of the developer metadata
            selected by the filters to the values provided in the DeveloperMetadata resource.  Callers must specify the
            properties they wish to update in the fields parameter, as well as specify at least one DataFilter matching the
            metadata they wish to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataRequest.DataFilters">
            <summary>The filters matching the developer metadata entries to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataRequest.DeveloperMetadata">
            <summary>The value that all metadata matched by the data filters will be updated to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root
            `developerMetadata` is implied and should not be specified. A single `"*"` can be used as short-hand for
            listing every field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataResponse">
            <summary>The response from updating developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataResponse.DeveloperMetadata">
            <summary>The updated developer metadata.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDeveloperMetadataResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateDimensionGroupRequest">
            <summary>Updates the state of the specified group.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionGroupRequest.DimensionGroup">
            <summary>The group whose state should be updated. The range and depth of the group should specify a valid
            group on the sheet, and all other fields updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionGroupRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `dimensionGroup`
            is implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionGroupRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateDimensionPropertiesRequest">
            <summary>Updates properties of dimensions within the specified range.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionPropertiesRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `properties` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionPropertiesRequest.Properties">
            <summary>Properties to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionPropertiesRequest.Range">
            <summary>The rows or columns to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateDimensionPropertiesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionRequest">
            <summary>Update an embedded object's position (such as a moving or resizing a chart or image).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionRequest.Fields">
            <summary>The fields of OverlayPosition that should be updated when setting a new position. Used only if
            newPosition.overlayPosition is set, in which case at least one field must be specified.  The root
            `newPosition.overlayPosition` is implied and should not be specified. A single `"*"` can be used as short-
            hand for listing every field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionRequest.NewPosition">
            <summary>An explicit position to move the embedded object to. If newPosition.sheetId is set, a new sheet
            with that ID will be created. If newPosition.newSheet is set to true, a new sheet will be created with an ID
            that will be chosen for you.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionRequest.ObjectId">
            <summary>The ID of the object to moved.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionResponse">
            <summary>The result of updating an embedded object's position.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionResponse.Position">
            <summary>The new position of the embedded object.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateEmbeddedObjectPositionResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateFilterViewRequest">
            <summary>Updates properties of the filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateFilterViewRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `filter` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateFilterViewRequest.Filter">
            <summary>The new properties of the filter view.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateFilterViewRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateNamedRangeRequest">
            <summary>Updates properties of the named range with the specified namedRangeId.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateNamedRangeRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `namedRange` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateNamedRangeRequest.NamedRange">
            <summary>The named range to update with the new properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateNamedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateProtectedRangeRequest">
            <summary>Updates an existing protected range with the specified protectedRangeId.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateProtectedRangeRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `protectedRange`
            is implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateProtectedRangeRequest.ProtectedRange">
            <summary>The protected range to update with the new properties.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateProtectedRangeRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateSheetPropertiesRequest">
            <summary>Updates properties of the sheet with the specified sheetId.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSheetPropertiesRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `properties` is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSheetPropertiesRequest.Properties">
            <summary>The properties to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSheetPropertiesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateSlicerSpecRequest">
            <summary>Updates a slicer’s specifications. (This does not move or resize a slicer. To move or resize a slicer
            use UpdateEmbeddedObjectPositionRequest.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSlicerSpecRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root `SlicerSpec` is
            implied and should not be specified. A single "*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSlicerSpecRequest.SlicerId">
            <summary>The id of the slicer to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSlicerSpecRequest.Spec">
            <summary>The specification to apply to the slicer.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSlicerSpecRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateSpreadsheetPropertiesRequest">
            <summary>Updates properties of a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSpreadsheetPropertiesRequest.Fields">
            <summary>The fields that should be updated.  At least one field must be specified. The root 'properties' is
            implied and should not be specified. A single `"*"` can be used as short-hand for listing every
            field.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSpreadsheetPropertiesRequest.Properties">
            <summary>The properties to update.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateSpreadsheetPropertiesRequest.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse">
            <summary>The response when updating a range of values by a data filter in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.DataFilter">
            <summary>The data filter that selected the range that was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.UpdatedCells">
            <summary>The number of cells updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.UpdatedColumns">
            <summary>The number of columns where at least one cell in the column was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.UpdatedData">
            <summary>The values of the cells in the range matched by the dataFilter after all updates were applied. This
            is only included if the request's `includeValuesInResponse` field was `true`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.UpdatedRange">
            <summary>The range (in A1 notation) that updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.UpdatedRows">
            <summary>The number of rows where at least one cell in the row was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesByDataFilterResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.UpdateValuesResponse">
            <summary>The response when updating a range of values in a spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.SpreadsheetId">
            <summary>The spreadsheet the updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.UpdatedCells">
            <summary>The number of cells updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.UpdatedColumns">
            <summary>The number of columns where at least one cell in the column was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.UpdatedData">
            <summary>The values of the cells after updates were applied. This is only included if the request's
            `includeValuesInResponse` field was `true`.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.UpdatedRange">
            <summary>The range (in A1 notation) that updates were applied to.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.UpdatedRows">
            <summary>The number of rows where at least one cell in the row was updated.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.UpdateValuesResponse.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.ValueRange">
            <summary>Data within a range of the spreadsheet.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ValueRange.MajorDimension">
             <summary>The major dimension of the values.
            
             For output, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting
             `range=A1:B2,majorDimension=ROWS` will return `[[1,2],[3,4]]`, whereas requesting
             `range=A1:B2,majorDimension=COLUMNS` will return `[[1,3],[2,4]]`.
            
             For input, with `range=A1:B2,majorDimension=ROWS` then `[[1,2],[3,4]]` will set `A1=1,B1=2,A2=3,B2=4`. With
             `range=A1:B2,majorDimension=COLUMNS` then `[[1,2],[3,4]]` will set `A1=1,B1=3,A2=2,B2=4`.
            
             When writing, if this field is not set, it defaults to ROWS.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ValueRange.Range">
            <summary>The range the values cover, in A1 notation. For output, this range indicates the entire requested
            range, even though the values will exclude trailing rows and columns. When appending values, this field
            represents the range to search for a table, after which values will be appended.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ValueRange.Values">
             <summary>The data that was read or to be written.  This is an array of arrays, the outer array representing
             all the data and each inner array representing a major dimension. Each item in the inner array corresponds
             with one cell.
            
             For output, empty trailing rows and columns will not be included.
            
             For input, supported value types are: bool, string, and double. Null values will be skipped. To set a cell
             to an empty value, set the string value to an empty string.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.ValueRange.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.WaterfallChartColumnStyle">
            <summary>Styles for a waterfall chart column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartColumnStyle.Color">
            <summary>The color of the column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartColumnStyle.Label">
            <summary>The label of the column's legend.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartColumnStyle.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.WaterfallChartCustomSubtotal">
            <summary>A custom subtotal column for a waterfall chart series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartCustomSubtotal.DataIsSubtotal">
            <summary>True if the data point at subtotal_index is the subtotal. If false, the subtotal will be computed
            and appear after the data point.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartCustomSubtotal.Label">
            <summary>A label for the subtotal column.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartCustomSubtotal.SubtotalIndex">
            <summary>The 0-based index of a data point within the series. If data_is_subtotal is true, the data point at
            this index is the subtotal. Otherwise, the subtotal appears after the data point with this index. A series
            can have multiple subtotals at arbitrary indices, but subtotals do not affect the indices of the data
            points. For example, if a series has three data points, their indices will always be 0, 1, and 2, regardless
            of how many subtotals exist on the series or what data points they are associated with.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartCustomSubtotal.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.WaterfallChartDomain">
            <summary>The domain of a waterfall chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartDomain.Data">
            <summary>The data of the WaterfallChartDomain.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartDomain.Reversed">
            <summary>True to reverse the order of the domain values (horizontal axis).</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartDomain.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.WaterfallChartSeries">
            <summary>A single series of data for a waterfall chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.CustomSubtotals">
            <summary>Custom subtotal columns appearing in this series. The order in which subtotals are defined is not
            significant. Only one subtotal may be defined for each data point.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.Data">
            <summary>The data being visualized in this series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.HideTrailingSubtotal">
            <summary>True to hide the subtotal column from the end of the series. By default, a subtotal column will
            appear at the end of each series. Setting this field to true will hide that subtotal column for this
            series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.NegativeColumnsStyle">
            <summary>Styles for all columns in this series with negative values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.PositiveColumnsStyle">
            <summary>Styles for all columns in this series with positive values.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.SubtotalColumnsStyle">
            <summary>Styles for all subtotal columns in this series.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSeries.ETag">
            <summary>The ETag of the item.</summary>
        </member>
        <member name="T:Google.Apis.Sheets.v4.Data.WaterfallChartSpec">
            <summary>A waterfall chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.ConnectorLineStyle">
            <summary>The line style for the connector lines.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.Domain">
            <summary>The domain data (horizontal axis) for the waterfall chart.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.FirstValueIsTotal">
            <summary>True to interpret the first value as a total.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.HideConnectorLines">
            <summary>True to hide connector lines between columns.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.Series">
            <summary>The data this waterfall chart is visualizing.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.StackedType">
            <summary>The stacked type.</summary>
        </member>
        <member name="P:Google.Apis.Sheets.v4.Data.WaterfallChartSpec.ETag">
            <summary>The ETag of the item.</summary>
        </member>
    </members>
</doc>
